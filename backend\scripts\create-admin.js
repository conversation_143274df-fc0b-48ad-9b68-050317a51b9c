/**
 * 创建初始化管理员用户的脚本
 */
const bcrypt = require('bcrypt');
const db = require('../database/db');
const { logger } = require('../utils/logger');

const createAdmin = async () => {
  try {
    // 检查数据库连接
    await db.testConnection();

    // 创建管理员用户
    const adminData = {
      username: 'admin',
      password: await bcrypt.hash('admin123', 10),
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      full_name: '系统管理员'
    };

    // 检查是否已存在管理员用户
    const [existingAdmin] = await db.query(
      'SELECT * FROM users WHERE username = ?',
      [adminData.username]
    );

    if (existingAdmin.length > 0) {
      logger.info(`管理员用户 (${adminData.username}) 已存在，跳过创建`);
      process.exit(0);
    }

    // 创建管理员用户
    const [result] = await db.query(
      `INSERT INTO users 
       (username, password, email, full_name, role, status, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        adminData.username,
        adminData.password,
        adminData.email,
        adminData.full_name,
        adminData.role,
        adminData.status,
        new Date()
      ]
    );

    logger.info(`管理员用户 (${adminData.username}) 创建成功，ID: ${result.insertId}`);

    // 测试登录凭据
    logger.info('请使用以下凭据登录:');
    logger.info(`用户名: ${adminData.username}`);
    logger.info(`密码: admin123`);

    process.exit(0);
  } catch (error) {
    logger.error(`创建管理员用户失败: ${error.message}`);
    process.exit(1);
  }
};

// 执行创建
createAdmin(); 