# Token 管理系统

基于 Vue 3 开发的前端页面和Node.js后端API，用于管理和维护 Token 数据。

## 功能模块

### 首页 (Dashboard)

首页展示系统核心指标：
- Token 总数量
- 备份数量
- 最近到期时间
- Token 状态分布图表
- 最近操作记录

### Token 重置与订单管理

此模块提供以下功能：

**Token管理功能：**
- 导入 Token 数据（支持 Excel 和 TXT 格式）
- 显示 UID 和 Token 信息
- 支持批量操作：重置、删除、导出
- 搜索筛选功能
- 查询Token在线状态（通过API验证Token有效性）
- 查询Token的用户头像和昵称

**订单管理功能：**
- 查询关联Token的订单信息
- 显示订单详情（订单号、商品、金额、状态等）
- 删除已评价的订单（符合平台要求的可删除订单）
- 批量删除订单功能
- 订单状态筛选和分类展示
- 订单删除进度和结果实时显示
- 商家信息显示（商家名称、订单关联信息）

### Token 备份

Token 备份页面提供以下功能：
- 创建新备份
- 恢复备份
- 查看备份详情
- 自动备份设置
- 下载备份
- 关联ID显示（支持"重置前"和"重置后"备份关联显示）
- 双备份标识（同一关联ID同时有"重置前"和"重置后"备份时显示）
- 状态过滤功能

### 系统设置

设置页面提供以下功能：
- 基本设置（系统名称、主题、语言等）
- 用户设置（密码修改等）
- 通知设置（邮件通知、到期提醒等）
- 数据管理（导入导出、清理）

## 技术栈

### 前端
- Vue 3 (Composition API)
- Vue Router
- Pinia 状态管理
- Element Plus UI 组件库
- Vite 构建工具

### 后端
- Node.js
- Express.js
- MySQL
- JWT认证
- Winston日志系统

## 代码规范与组织

### 目录结构规范
- 保持清晰的目录结构，将相关功能模块归类存放
- 遵循"关注点分离"原则，将不同职责的代码分离到不同文件中

### 代码质量要求
- 每个功能文件代码行数不超过1000行，避免文件过大难以维护
- 单一职责原则：每个文件和函数应该只负责一个明确的功能
- 代码应包含适当的注释，特别是复杂逻辑部分
- 使用TypeScript类型定义提高代码的可读性和可维护性

### 模块化设计
- 按照功能划分服务模块，如tokenService、onlineCheckService等
- 共享功能抽取为公共组件或工具函数
- 统一错误处理和状态管理

## 本地开发

### 安装依赖

```bash
# 前端
cd token-management
npm install

# 后端
cd backend
npm install
```

### 启动开发服务器

```bash
# 前端
cd token-management
npm run dev

# 后端
cd backend
npm run dev
```

### 构建生产版本

```bash
# 前端
cd token-management
npm run build

# 后端
cd backend
npm run build
```

### 预览生产构建

```bash
# 前端
cd token-management
npm run serve

# 后端
cd backend
npm start
```

## 目录结构

```
/token-management/      # 前端项目
├── public/            # 静态资源
├── src/
│   ├── assets/        # 项目资源文件
│   ├── components/    # 公共组件
│   ├── router/        # 路由配置
│   ├── store/         # Pinia 状态管理
│   ├── views/         # 页面组件
│   │   ├── Dashboard.vue       # 首页/仪表盘
│   │   ├── TokenReset.vue      # Token重置/订单管理
│   │   ├── TokenBackup.vue     # Token备份
│   │   ├── TokenAutoReset.vue  # 自动重置
│   │   ├── Settings.vue        # 系统设置
│   │   └── Login.vue           # 登录页
│   ├── services/      # API服务模块
│   │   ├── tokenService.js     # Token相关API服务
│   │   ├── onlineCheckService.js # Token在线状态检查服务
│   │   └── api.js              # 通用API服务
│   ├── utils/         # 工具函数
│   │   ├── tokenParser.js      # Token数据解析工具
│   │   ├── helpers.js          # 通用辅助函数
│   │   └── formatters.js       # 格式化工具
│   ├── App.vue        # 根组件
│   └── main.js        # 入口文件
├── index.html         # HTML 模板
├── vite.config.js     # Vite 配置
└── package.json       # 项目依赖
```

## 模块说明

### 服务模块 (services)

服务模块主要负责与后端API的交互，实现前后端数据的对接。

#### tokenService.js
包含Token管理的所有API调用:
- 导入Token数据 (支持JSON和FormData两种方式)
- 获取Token列表
- 重置Token
- 批量重置Token
- 查询Token信息
- 查询在线状态
- 创建备份
- 删除Token
- 查询订单信息
- 删除订单
- 批量删除订单

#### onlineCheckService.js
专门处理Token在线状态检查:
- 单个Token在线状态检查
- 批量并发检查多个Token状态
- 优化的并发控制，避免请求过多导致性能问题
- 实时进度反馈

### 工具模块 (utils)

工具模块提供各种辅助函数，用于数据处理、格式化等操作。

#### tokenParser.js
负责解析不同格式的Token数据:
- 解析Excel文件 (.xlsx, .xls)
- 解析文本文件 (.txt)
- 智能识别数据格式和表头
- 支持多种分隔符的文本数据处理

#### helpers.js
提供通用的辅助函数:
- 日期时间格式化
- 数据验证
- 文件大小格式化
- 剪贴板操作
- 防抖和节流函数
- 深拷贝

## 主要组件说明

### TokenReset.vue
Token重置和订单管理的主要组件:
- Token列表展示和管理
- 批量操作功能
- 在线状态查询
- 用户信息查询
- 订单信息查询和展示
- 订单删除功能
  - 单个订单删除
  - 批量订单删除
  - 删除进度和结果显示
  - 商家名称展示

## 最近更新

### v1.4.0 (2025-04-02)
- 完善订单管理功能
- 优化订单删除API集成
- 修复订单删除API处理逻辑
- 增强错误处理和用户提示
- 改进批量删除订单功能
- 添加商家名称显示在订单列表中
- 优化删除进度和结果显示
- 解决重复变量声明问题

### v1.3.0 (2023-12-20)
- 添加 Token 状态管理功能
- 实现完整的认证和权限系统
- 完善后端 API 架构
- 添加重置记录管理功能
- 增强数据统计和可视化功能

### v1.2.0 (2023-10-12)
- 添加 Token 状态筛选功能
- 优化备份管理界面
- 添加 Token 批量操作功能
- 修复已知 BUG

### 后端API文档

后端API提供以下主要模块的接口：

#### Token订单管理 (/api/tokens/)
- `/query/order` - 查询Token关联的订单
- `/delete-order` - 删除单个订单
- `/batch-delete-orders` - 批量删除订单

## 更新记录

### [v1.0.0] - 2023-06-15
- 初始版本发布

### [v1.1.0] - 2023-08-01
- 添加备份功能
- 改进Token重置功能
- 优化UI界面

### [v1.2.0] - 2023-10-12
- 添加Token状态过滤
- 优化备份管理
- 修复已知BUG

### [v1.3.0] - 2023-12-20
- 添加双备份标识功能
- 优化备份关联ID显示
- 添加备份类型过滤功能
- 完善API错误处理机制
- 添加Token批量导入导出功能

### [v1.4.0] - 2024-03-31
- 添加Token在线状态检查功能
- 优化代码结构，实现功能模块化
- 限制单个文件代码不超过1000行，提高可维护性
- 增加并发控制，优化批量请求性能 