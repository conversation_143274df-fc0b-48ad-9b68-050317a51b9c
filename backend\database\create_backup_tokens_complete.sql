-- 创建一个新的完整备份表，使用更直观的字段名
DROP TABLE IF EXISTS backup_tokens_complete;

CREATE TABLE backup_tokens_complete (
  id INT AUTO_INCREMENT PRIMARY KEY,
  backup_id INT NOT NULL,
  uid VARCHAR(255) NOT NULL,
  token VARCHAR(1000) NOT NULL,
  username VA<PERSON><PERSON><PERSON>(255),
  nickname VA<PERSON><PERSON><PERSON>(255),
  avatar VA<PERSON>HA<PERSON>(255),
  status VARCHAR(50),
  platform VARCHAR(50),
  purchase_time DATETIME,
  expiry_time DATETIME,
  order_id VARCHAR(255),
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE
);

-- 添加索引
CREATE INDEX idx_backup_tokens_complete_backup_id ON backup_tokens_complete(backup_id);
CREATE INDEX idx_backup_tokens_complete_uid ON backup_tokens_complete(uid);
