const pool = require('../database/db');
const { logger } = require('../utils/logger');

class Token {
  // 获取所有Token
  static async getAll(page = 1, limit = 20, filters = {}) {
    try {
      const offset = (page - 1) * limit;

      // 构建基础查询
      let query = 'SELECT * FROM tokens WHERE deleted_at IS NULL';
      const params = [];

      // 添加筛选条件
      if (filters.uid) {
        query += ' AND uid LIKE ?';
        params.push(`%${filters.uid}%`);
      }

      if (filters.status) {
        query += ' AND status = ?';
        params.push(filters.status);
      }

      if (filters.platform) {
        query += ' AND platform = ?';
        params.push(filters.platform);
      }

      // 添加分页
      query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      // 执行查询
      const [tokens] = await pool.execute(query, params);

      // 获取总数
      let countQuery = 'SELECT COUNT(*) as total FROM tokens WHERE deleted_at IS NULL';
      const countParams = [];

      if (filters.uid) {
        countQuery += ' AND uid LIKE ?';
        countParams.push(`%${filters.uid}%`);
      }

      if (filters.status) {
        countQuery += ' AND status = ?';
        countParams.push(filters.status);
      }

      if (filters.platform) {
        countQuery += ' AND platform = ?';
        countParams.push(filters.platform);
      }

      const [countResult] = await pool.execute(countQuery, countParams);
      const total = countResult[0].total;

      return {
        tokens,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('获取Token列表失败:', error);
      throw error;
    }
  }

  // 获取单个Token
  static async getById(id) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM tokens WHERE id = ? AND deleted_at IS NULL',
        [id]
      );

      return rows[0];
    } catch (error) {
      logger.error(`获取Token(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 获取单个Token根据UID
  static async getByUid(uid) {
    try {
      logger.info(`开始根据UID查找Token: ${uid}`);

      // 使用query而不是execute方法
      const [rows] = await pool.query(
        'SELECT * FROM tokens WHERE uid = ? AND deleted_at IS NULL',
        [uid]
      );

      if (rows.length > 0) {
        logger.info(`成功找到UID为 ${uid} 的Token: ${JSON.stringify(rows[0])}`);
      } else {
        logger.warn(`未找到UID为 ${uid} 的Token`);
      }

      return rows[0];
    } catch (error) {
      logger.error(`获取Token(UID: ${uid})失败:`, error);
      logger.error(`错误详情: ${error.stack}`);
      throw error;
    }
  }

  // 创建Token
  static async create(tokenData, userId) {
    try {
      const {
        uid,
        token_value,
        username,
        nickname,
        avatar_url,
        platform,
        purchase_time,
        expiry_date,
        status,
        account_id,
        remarks
      } = tokenData;

      const [result] = await pool.execute(
        `INSERT INTO tokens (
          uid, token_value, username, nickname, avatar_url, platform,
          purchase_time, expiry_date, status, account_id, created_by,
          updated_by, remarks
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          uid,
          token_value,
          username,
          nickname,
          avatar_url,
          platform,
          purchase_time,
          expiry_date,
          status,
          account_id,
          userId,
          userId,
          remarks
        ]
      );

      return { id: result.insertId };
    } catch (error) {
      logger.error('创建Token失败:', error);
      throw error;
    }
  }

  // 批量创建Token
  static async bulkCreate(tokensData, userId) {
    try {
      // 开始事务
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        const insertedIds = [];

        for (const tokenData of tokensData) {
          const {
            uid,
            token_value,
            username,
            nickname,
            avatar_url,
            platform,
            purchase_time,
            expiry_date,
            status,
            account_id,
            remarks
          } = tokenData;

          const [result] = await connection.execute(
            `INSERT INTO tokens (
              uid, token_value, username, nickname, avatar_url, platform,
              purchase_time, expiry_date, status, account_id, created_by,
              updated_by, remarks
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              uid,
              token_value,
              username,
              nickname,
              avatar_url,
              platform,
              purchase_time || null,
              expiry_date || null,
              status || '正常',
              account_id || null,
              userId,
              userId,
              remarks || null
            ]
          );

          insertedIds.push(result.insertId);
        }

        // 提交事务
        await connection.commit();
        connection.release();

        return { ids: insertedIds, count: insertedIds.length };
      } catch (error) {
        // 回滚事务
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      logger.error('批量创建Token失败:', error);
      throw error;
    }
  }

  // 更新Token
  static async update(id, tokenData, userId) {
    try {
      const {
        uid,
        token_value,
        username,
        nickname,
        avatar_url,
        platform,
        purchase_time,
        expiry_date,
        status,
        account_id,
        remarks
      } = tokenData;

      const [result] = await pool.execute(
        `UPDATE tokens SET
          uid = ?,
          token_value = ?,
          username = ?,
          nickname = ?,
          avatar_url = ?,
          platform = ?,
          purchase_time = ?,
          expiry_date = ?,
          status = ?,
          account_id = ?,
          updated_by = ?,
          remarks = ?
        WHERE id = ? AND deleted_at IS NULL`,
        [
          uid,
          token_value,
          username,
          nickname,
          avatar_url,
          platform,
          purchase_time,
          expiry_date,
          status,
          account_id,
          userId,
          remarks,
          id
        ]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`更新Token(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 软删除Token
  static async delete(id, userId) {
    try {
      const [result] = await pool.execute(
        'UPDATE tokens SET deleted_at = NOW(), updated_by = ? WHERE id = ? AND deleted_at IS NULL',
        [userId, id]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`删除Token(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 批量软删除Token
  static async bulkDelete(ids, userId) {
    try {
      const placeholders = ids.map(() => '?').join(',');
      const params = [...ids, userId];

      const [result] = await pool.execute(
        `UPDATE tokens SET deleted_at = NOW(), updated_by = ?
         WHERE id IN (${placeholders}) AND deleted_at IS NULL`,
        [userId, ...ids]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error('批量删除Token失败:', error);
      throw error;
    }
  }

  // 重置Token
  static async reset(id, newTokenValue, userId) {
    try {
      const [result] = await pool.execute(
        `UPDATE tokens SET
          token_value = ?,
          status = '正常',
          updated_by = ?,
          updated_at = NOW()
        WHERE id = ? AND deleted_at IS NULL`,
        [newTokenValue, userId, id]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`重置Token(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 批量重置Token
  static async bulkReset(ids, newTokenValues, userId) {
    try {
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        let affected = 0;

        for (let i = 0; i < ids.length; i++) {
          const [result] = await connection.execute(
            `UPDATE tokens SET
              token_value = ?,
              status = '正常',
              updated_by = ?,
              updated_at = NOW()
            WHERE id = ? AND deleted_at IS NULL`,
            [newTokenValues[i], userId, ids[i]]
          );

          affected += result.affectedRows;
        }

        await connection.commit();
        connection.release();

        return { affected };
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      logger.error('批量重置Token失败:', error);
      throw error;
    }
  }

  // 获取Token统计信息
  static async getStats() {
    try {
      // 获取总数
      const [totalResult] = await pool.execute(
        'SELECT COUNT(*) as total FROM tokens WHERE deleted_at IS NULL'
      );

      // 获取状态分布
      const [statusResult] = await pool.execute(
        'SELECT status, COUNT(*) as count FROM tokens WHERE deleted_at IS NULL GROUP BY status'
      );

      // 获取平台分布
      const [platformResult] = await pool.execute(
        'SELECT platform, COUNT(*) as count FROM tokens WHERE deleted_at IS NULL GROUP BY platform'
      );

      // 获取最近到期时间
      const [expiryResult] = await pool.execute(
        'SELECT MIN(expiry_date) as next_expiry FROM tokens WHERE expiry_date > NOW() AND deleted_at IS NULL'
      );

      return {
        total: totalResult[0].total,
        statusDistribution: statusResult,
        platformDistribution: platformResult,
        nextExpiry: expiryResult[0].next_expiry
      };
    } catch (error) {
      logger.error('获取Token统计信息失败:', error);
      throw error;
    }
  }
}

module.exports = Token;