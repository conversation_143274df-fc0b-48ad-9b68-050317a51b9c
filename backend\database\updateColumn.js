// 用于添加缺失的token_count字段的一次性脚本
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量
dotenv.config();

async function updateDatabase() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'qq@666666',
    database: process.env.DB_NAME || 'token_management'
  });

  try {
    console.log('连接到数据库成功，开始执行更新...');

    // 添加token_count字段
    try {
      await connection.query(`
        ALTER TABLE backups 
        ADD COLUMN IF NOT EXISTS token_count INT DEFAULT 0 AFTER task_id
      `);
      console.log('成功添加 token_count 字段到 backups 表');
    } catch (error) {
      // 检查是否是字段已存在的错误
      if (error.code === 'ER_DUPLICATE_COLUMN') {
        console.log('token_count 字段已存在，无需添加');
      } else {
        console.error('添加 token_count 字段时出错:', error.message);
        // 尝试使用替代语法
        try {
          // 检查字段是否存在
          const [columns] = await connection.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
            AND TABLE_NAME = 'backups' 
            AND COLUMN_NAME = 'token_count'
          `);
          
          if (columns.length === 0) {
            // 如果字段不存在，添加它
            await connection.query(`
              ALTER TABLE backups 
              ADD COLUMN token_count INT DEFAULT 0 AFTER task_id
            `);
            console.log('使用替代方法成功添加 token_count 字段');
          } else {
            console.log('token_count 字段已存在，无需添加');
          }
        } catch (altError) {
          console.error('使用替代方法添加字段时出错:', altError.message);
        }
      }
    }

    // 添加其他可能缺失的字段
    try {
      await connection.query(`
        ALTER TABLE backups
        ADD COLUMN IF NOT EXISTS size VARCHAR(20) AFTER token_count,
        ADD COLUMN IF NOT EXISTS type VARCHAR(20) AFTER size,
        ADD COLUMN IF NOT EXISTS remark TEXT AFTER description,
        ADD COLUMN IF NOT EXISTS delete_status BOOLEAN DEFAULT FALSE AFTER status,
        ADD COLUMN IF NOT EXISTS scheduled_delete_time DATETIME AFTER delete_status,
        ADD COLUMN IF NOT EXISTS backup_time DATETIME AFTER scheduled_delete_time
      `);
      console.log('成功添加其他可能缺失的字段');
    } catch (error) {
      console.error('添加其他字段时出错:', error.message);
      // 这里不再尝试替代方法，因为主要的token_count字段已经尝试添加
    }

    console.log('数据库更新完成！');
  } catch (error) {
    console.error('执行数据库更新时出错:', error.message);
  } finally {
    // 关闭连接
    await connection.end();
    console.log('数据库连接已关闭');
  }
}

// 执行更新
updateDatabase().catch(err => {
  console.error('更新脚本执行失败:', err);
  process.exit(1);
}); 