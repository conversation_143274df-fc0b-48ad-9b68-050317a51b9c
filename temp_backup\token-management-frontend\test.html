<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <h1>API测试工具</h1>
    
    <div class="test-section">
        <h2>测试API连接</h2>
        <button id="testApi">测试API连接</button>
        <div id="testResult"></div>
    </div>
    
    <div class="test-section">
        <h2>发送Token订单</h2>
        <button id="sendTokens">发送Token订单</button>
        <div id="tokenResult"></div>
    </div>
    
    <script>
        // 测试API连接按钮
        document.getElementById('testApi').addEventListener('click', async () => {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '正在连接...';
            
            try {
                const response = await fetch('http://localhost:3001/api/test');
                const data = await response.json();
                
                resultDiv.innerHTML = `<p>状态: 成功</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p>状态: 失败</p><pre>错误: ${error.message}</pre>`;
            }
        });
        
        // 发送Token订单按钮
        document.getElementById('sendTokens').addEventListener('click', async () => {
            const resultDiv = document.getElementById('tokenResult');
            resultDiv.innerHTML = '正在发送...';
            
            // 测试数据
            const testData = {
                tokens: [
                    {
                        username: "testuser",
                        UID: "12345",
                        token: "test-token-123"
                    }
                ]
            };
            
            try {
                const response = await fetch('http://localhost:3001/api/tokens/place-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `<p>状态: ${response.ok ? '成功' : '失败'}</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                resultDiv.innerHTML = `<p>状态: 失败</p><pre>错误: ${error.message}</pre>`;
            }
        });
    </script>
</body>
</html> 