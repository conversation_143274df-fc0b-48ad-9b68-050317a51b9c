@echo off
chcp 65001 > nul
echo 正在启动 Token 管理系统...
echo ======================================

echo 启动后端服务...
start cmd /k "cd backend && node server.js"

echo 启动前端服务...
start cmd /k "cd token-management && npm run dev"

echo.
echo 服务已启动！
echo ======================================
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:3000
echo.
echo 管理员用户信息:
echo 用户名: admin
echo 密码: admin123
echo.
echo 注意: 这是唯一的启动脚本，其他启动脚本已被清理。
echo 按任意键继续...

pause