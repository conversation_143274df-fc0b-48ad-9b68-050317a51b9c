const Setting = require('../models/setting');
const UserPreference = require('../models/userPreference');
const { logger } = require('../utils/logger');
const config = require('../config');

class SettingsController {
  /**
   * 获取所有系统设置
   */
  static async getAllSettings(req, res, next) {
    try {
      const settings = await Setting.getAll();
      
      // 过滤敏感信息，只返回安全的设置项
      const safeSettings = settings.filter(setting => !setting.is_sensitive);
      
      // 如果用户是管理员，则返回所有设置项
      const allSettings = req.user.role === 'admin' ? settings : safeSettings;
      
      res.json({
        success: true,
        data: allSettings
      });
    } catch (error) {
      logger.error(`获取所有设置失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 根据键获取特定设置
   */
  static async getSettingByKey(req, res, next) {
    try {
      const { key } = req.params;
      const setting = await Setting.getByKey(key);
      
      if (!setting) {
        return res.status(404).json({
          success: false,
          message: '设置项不存在'
        });
      }
      
      // 检查敏感设置
      if (setting.is_sensitive && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '您没有权限访问此设置项'
        });
      }
      
      res.json({
        success: true,
        data: setting
      });
    } catch (error) {
      logger.error(`获取设置(Key: ${req.params.key})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：更新设置
   */
  static async updateSetting(req, res, next) {
    try {
      const { key } = req.params;
      const { value, description } = req.body;
      
      // 验证请求
      if (value === undefined) {
        return res.status(400).json({
          success: false,
          message: '必须提供设置值'
        });
      }
      
      // 检查设置是否存在
      const setting = await Setting.getByKey(key);
      if (!setting) {
        return res.status(404).json({
          success: false,
          message: '设置项不存在'
        });
      }
      
      // 准备更新数据
      const settingData = {
        value,
        updated_at: new Date(),
        updated_by: req.user.id
      };
      
      if (description !== undefined) {
        settingData.description = description;
      }
      
      await Setting.update(key, settingData);
      logger.info(`管理员 ${req.user.username} (ID: ${req.user.id}) 更新了设置 ${key}`);
      
      // 获取更新后的设置
      const updatedSetting = await Setting.getByKey(key);
      
      res.json({
        success: true,
        message: '设置更新成功',
        data: updatedSetting
      });
    } catch (error) {
      logger.error(`更新设置(Key: ${req.params.key})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：批量更新设置
   */
  static async batchUpdateSettings(req, res, next) {
    try {
      const { settings } = req.body;
      
      // 验证请求格式
      if (!Array.isArray(settings) || settings.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的设置数组'
        });
      }
      
      const updatedSettings = [];
      
      // 逐个更新设置
      for (const setting of settings) {
        if (!setting.key || setting.value === undefined) {
          return res.status(400).json({
            success: false,
            message: '每个设置项必须包含key和value字段'
          });
        }
        
        // 检查设置是否存在
        const existingSetting = await Setting.getByKey(setting.key);
        if (!existingSetting) {
          return res.status(404).json({
            success: false,
            message: `设置项 ${setting.key} 不存在`
          });
        }
        
        const settingData = {
          value: setting.value,
          updated_at: new Date(),
          updated_by: req.user.id
        };
        
        if (setting.description !== undefined) {
          settingData.description = setting.description;
        }
        
        await Setting.update(setting.key, settingData);
        
        // 获取更新后的设置
        const updatedSetting = await Setting.getByKey(setting.key);
        updatedSettings.push(updatedSetting);
      }
      
      logger.info(`管理员 ${req.user.username} (ID: ${req.user.id}) 批量更新了 ${updatedSettings.length} 个设置`);
      
      res.json({
        success: true,
        message: `成功更新 ${updatedSettings.length} 个设置`,
        data: {
          settings: updatedSettings
        }
      });
    } catch (error) {
      logger.error(`批量更新设置失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取当前系统配置
   */
  static async getSystemConfig(req, res, next) {
    try {
      // 过滤掉敏感配置项
      const safeConfig = {
        app: config.app,
        server: {
          port: config.server.port,
          host: config.server.host,
          apiVersion: config.server.apiVersion
        },
        database: {
          host: config.database.host,
          port: config.database.port,
          name: config.database.name
        },
        env: config.env,
        cors: config.cors,
        scheduling: config.scheduling
      };
      
      res.json({
        success: true,
        data: safeConfig
      });
    } catch (error) {
      logger.error(`获取系统配置失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：更新系统配置
   */
  static async updateSystemConfig(req, res, next) {
    try {
      // 由于系统配置可能需要重启服务器才能生效，这里只模拟更新操作
      logger.info(`管理员 ${req.user.username} (ID: ${req.user.id}) 尝试更新系统配置`);
      
      res.json({
        success: true,
        message: '系统配置更新需要重启服务器才能生效，请联系系统管理员手动更新配置文件'
      });
    } catch (error) {
      logger.error(`更新系统配置失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取用户个人设置
   */
  static async getUserPreferences(req, res, next) {
    try {
      const userId = req.user.id;
      const preferences = await UserPreference.getByUserId(userId);
      
      res.json({
        success: true,
        data: preferences
      });
    } catch (error) {
      logger.error(`获取用户个人设置失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 更新用户个人设置
   */
  static async updateUserPreferences(req, res, next) {
    try {
      const userId = req.user.id;
      const { preferences } = req.body;
      
      // 验证请求格式
      if (!preferences || typeof preferences !== 'object') {
        return res.status(400).json({
          success: false,
          message: '请提供有效的偏好设置对象'
        });
      }
      
      await UserPreference.update(userId, preferences);
      logger.info(`用户 ${req.user.username} (ID: ${userId}) 更新了个人设置`);
      
      // 获取更新后的偏好设置
      const updatedPreferences = await UserPreference.getByUserId(userId);
      
      res.json({
        success: true,
        message: '个人设置更新成功',
        data: updatedPreferences
      });
    } catch (error) {
      logger.error(`更新用户个人设置失败: ${error.message}`);
      next(error);
    }
  }
}

module.exports = SettingsController; 