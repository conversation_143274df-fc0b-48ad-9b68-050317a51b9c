const { workerData, parentPort } = require('worker_threads');
const mysql = require('mysql2/promise');

// 从workerData获取配置
const {
  tokenChunk,
  backupId,
  workerId,
  dbConfig,
  batchSize = 500
} = workerData;

// 创建数据库连接
async function createConnection() {
  try {
    // 使用传入的数据库配置
    const connection = await mysql.createConnection({
      host: dbConfig.host || 'localhost',
      user: dbConfig.user || 'root',
      password: dbConfig.password || 'qq@666666',
      database: dbConfig.database || 'token_management',
      // 增加连接超时时间，避免大量数据处理时连接断开
      connectTimeout: 60000
      // 注意：mysql2不支持在连接配置中使用timeout选项
      // 查询超时需要在执行查询时设置
    });

    // 设置连接参数
    await connection.query('SET SESSION group_concat_max_len = 1000000');
    await connection.query('SET SESSION wait_timeout = 600');
    await connection.query('SET SESSION interactive_timeout = 600');

    return connection;
  } catch (error) {
    parentPort.postMessage({
      type: 'error',
      error: `Worker ${workerId} 创建数据库连接失败: ${error.message}`
    });
    throw error;
  }
}

// 带有重试机制的查询函数
async function insertWithRetry(connection, query, params, maxRetries = 5) {
  let retries = 0;
  let lastError = null;

  while (retries < maxRetries) {
    try {
      return await connection.query({ sql: query, timeout: 60000 }, params);
    } catch (error) {
      lastError = error;

      // 检查是否是死锁错误或锁等待超时错误
      if ((error.code === 'ER_LOCK_DEADLOCK' ||
           error.code === 'ER_LOCK_WAIT_TIMEOUT' ||
           error.sqlState === '40001' ||
           error.message.includes('Deadlock found') ||
           error.message.includes('Lock wait timeout')) &&
          retries < maxRetries - 1) {

        retries++;
        console.log(`Worker ${workerId}: 检测到死锁或锁等待超时，第 ${retries} 次重试...`);

        // 添加随机延迟，避免再次死锁
        const delay = 1000 + Math.random() * 2000 + (retries * 1000);
        await new Promise(resolve => setTimeout(resolve, delay));

        // 向主线程报告重试状态
        parentPort.postMessage({
          type: 'retry',
          workerId,
          retryCount: retries,
          error: error.message
        });
      } else {
        // 其他错误或重试次数用尽，抛出错误
        throw error;
      }
    }
  }

  // 如果所有重试都失败，抛出最后一个错误
  throw lastError;
}

// 批量处理token
async function processTokenChunk() {
  // 创建数据库连接
  const connection = await createConnection();

  try {
    // 向主线程发送开始消息
    parentPort.postMessage({
      type: 'start',
      workerId,
      tokenCount: tokenChunk.length
    });

    // 添加较小的随机延迟，错开不同Worker的处理时间
    // 减小延迟时间，提高性能
    const startDelay = 200 * workerId + Math.random() * 300;
    console.log(`Worker ${workerId}: 添加 ${Math.round(startDelay)}ms 启动延迟`);
    await new Promise(resolve => setTimeout(resolve, startDelay));

    // 使用更大的批处理大小，减少数据库操作次数
    const LARGE_BATCH_SIZE = Math.min(batchSize, 500); // 减小批处理大小，降低单次操作的锁竞争

    // 分批处理token
    for (let i = 0; i < tokenChunk.length; i += LARGE_BATCH_SIZE) {
      const batch = tokenChunk.slice(i, i + LARGE_BATCH_SIZE);
      const currentBatchSize = batch.length;

      // 准备批量插入的值
      const completeValues = [];
      const completeParams = [];

      // 为每个token准备插入数据
      for (const tokenData of batch) {
        const tokenId = tokenData.id;
        const tokenInfo = tokenData.info || {};

        // 准备插入完整备份表的数据
        const uid = tokenInfo.uid || tokenId;
        const token = tokenInfo.token || tokenInfo.token_value || tokenId;
        const username = tokenInfo.username || '';
        const nickname = tokenInfo.nickname || '';
        const avatar = tokenInfo.avatar || '';
        const status = tokenInfo.status || 'active';
        const platform = tokenInfo.platform || '';
        const purchase_time = tokenInfo.purchase_time || null;
        const expiry_time = tokenInfo.expiry_date || null;
        const order_id = tokenInfo.order_id || '';

        // 添加到参数数组
        completeParams.push(
          backupId, uid, token, username, nickname, avatar,
          status, platform, purchase_time, expiry_time, order_id
        );

        // 添加到值占位符
        completeValues.push('(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
      }

      // 批量插入到backup_tokens_complete表
      if (completeValues.length > 0) {
        // 使用优化的SQL语句
        const completeQuery = `
          INSERT INTO backup_tokens_complete (
            backup_id, uid, token, username, nickname, avatar,
            status, platform, purchase_time, expiry_time, order_id
          ) VALUES ${completeValues.join(', ')}
        `;

        try {
          // 使用带重试机制的插入函数
          await insertWithRetry(connection, completeQuery, completeParams);

          // 向主线程报告进度
          parentPort.postMessage({
            type: 'progress',
            workerId,
            processed: i + batch.length,
            total: tokenChunk.length
          });

          // 每批处理完成后添加短暂延迟，减少并发冲突
          if (i + LARGE_BATCH_SIZE < tokenChunk.length) {
            await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
          }
        } catch (batchError) {
          // 向主线程报告错误
          parentPort.postMessage({
            type: 'error',
            workerId,
            error: `批量插入Token失败: ${batchError.message}`
          });
          throw batchError;
        }
      }
    }

    // 向主线程报告完成
    parentPort.postMessage({
      type: 'complete',
      workerId,
      processed: tokenChunk.length
    });

    // 关闭连接
    await connection.end();

  } catch (error) {
    // 关闭连接
    try {
      await connection.end();
    } catch (e) {
      // 忽略关闭连接时的错误
    }

    // 向主线程报告错误
    parentPort.postMessage({
      type: 'error',
      workerId,
      error: error.message
    });

    throw error;
  }
}

// 执行处理
processTokenChunk()
  .then(() => {
    // 成功完成
    parentPort.postMessage({ type: 'success', workerId });
    // 正常退出进程
    process.exit(0);
  })
  .catch(error => {
    // 处理失败
    console.error(`Worker ${workerId} 错误:`, error);
    parentPort.postMessage({ type: 'error', workerId, error: error.message });
    // 异常退出进程
    process.exit(1);
  });
