import axios from 'axios'

// 创建axios实例
const request = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 15000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从本地存储获取token并设置到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      return Promise.reject(new Error(res.message || '请求失败'))
    }
    return res
  },
  error => {
    return Promise.reject(error)
  }
)

// Token相关API
export const tokenApi = {
  /**
   * 获取Token列表
   * @param {Object} params 查询参数
   * @returns {Promise} 请求结果
   */
  getTokenList(params) {
    return request({
      url: '/tokens',
      method: 'get',
      params
    })
  },

  /**
   * 获取Token详情
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  getTokenDetail(uid) {
    return request({
      url: `/tokens/${uid}`,
      method: 'get'
    })
  },

  /**
   * 创建Token
   * @param {Object} data Token数据
   * @returns {Promise} 请求结果
   */
  createToken(data) {
    return request({
      url: '/tokens',
      method: 'post',
      data
    })
  },

  /**
   * 删除Token
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  deleteToken(uid) {
    return request({
      url: `/tokens/${uid}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除Token
   * @param {Array} uids Token的UID数组
   * @returns {Promise} 请求结果
   */
  batchDeleteTokens(uids) {
    return request({
      url: '/tokens/batch-delete',
      method: 'post',
      data: { uids }
    })
  },

  /**
   * 更新Token
   * @param {string} uid Token的UID
   * @param {Object} data 更新数据
   * @returns {Promise} 请求结果
   */
  updateToken(uid, data) {
    return request({
      url: `/tokens/${uid}`,
      method: 'put',
      data
    })
  },

  /**
   * 重置Token
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  resetToken(uid) {
    return request({
      url: `/tokens/${uid}/reset`,
      method: 'post'
    })
  },

  /**
   * 批量重置Token
   * @param {Array} uids Token的UID数组
   * @returns {Promise} 请求结果
   */
  batchResetTokens(uids) {
    return request({
      url: '/tokens/batch-reset',
      method: 'post',
      data: { uids }
    })
  },

  /**
   * 激活Token
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  activateToken(uid) {
    return request({
      url: `/tokens/${uid}/activate`,
      method: 'post'
    })
  },

  /**
   * 批量激活Token
   * @param {Array} uids Token的UID数组
   * @returns {Promise} 请求结果
   */
  batchActivateTokens(uids) {
    return request({
      url: '/tokens/batch-activate',
      method: 'post',
      data: { uids }
    })
  },

  /**
   * 禁用Token
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  disableToken(uid) {
    return request({
      url: `/tokens/${uid}/disable`,
      method: 'post'
    })
  },

  /**
   * 批量禁用Token
   * @param {Array} uids Token的UID数组
   * @returns {Promise} 请求结果
   */
  batchDisableTokens(uids) {
    return request({
      url: '/tokens/batch-disable',
      method: 'post',
      data: { uids }
    })
  },

  /**
   * 续期Token
   * @param {string} uid Token的UID
   * @param {Object} data 续期数据 {days: 30}
   * @returns {Promise} 请求结果
   */
  renewToken(uid, data) {
    return request({
      url: `/tokens/${uid}/renew`,
      method: 'post',
      data
    })
  },

  /**
   * 批量续期Token
   * @param {Array} uids Token的UID数组
   * @param {Object} data 续期数据 {days: 30}
   * @returns {Promise} 请求结果
   */
  batchRenewTokens(uids, data) {
    return request({
      url: '/tokens/batch-renew',
      method: 'post',
      data: { uids, ...data }
    })
  },

  /**
   * 设置Token标签
   * @param {string} uid Token的UID
   * @param {Object} data 标签数据 {tags: ['tag1', 'tag2']}
   * @returns {Promise} 请求结果
   */
  setTags(uid, data) {
    return request({
      url: `/tokens/${uid}/tags`,
      method: 'post',
      data
    })
  },

  /**
   * 批量设置Token标签
   * @param {Array} uids Token的UID数组
   * @param {Object} data 标签数据 {tags: ['tag1', 'tag2']}
   * @returns {Promise} 请求结果
   */
  batchSetTags(uids, data) {
    return request({
      url: '/tokens/batch-set-tags',
      method: 'post',
      data: { uids, ...data }
    })
  },

  /**
   * 移除Token标签
   * @param {string} uid Token的UID
   * @param {Object} data 标签数据 {tags: ['tag1', 'tag2']}
   * @returns {Promise} 请求结果
   */
  removeTags(uid, data) {
    return request({
      url: `/tokens/${uid}/tags`,
      method: 'delete',
      data
    })
  },

  /**
   * 批量移除Token标签
   * @param {Array} uids Token的UID数组
   * @param {Object} data 标签数据 {tags: ['tag1', 'tag2']}
   * @returns {Promise} 请求结果
   */
  batchRemoveTags(uids, data) {
    return request({
      url: '/tokens/batch-remove-tags',
      method: 'post',
      data: { uids, ...data }
    })
  },

  /**
   * 检查Token状态
   * @param {string} uid Token的UID
   * @returns {Promise} 请求结果
   */
  checkTokenStatus(uid) {
    return request({
      url: `/tokens/${uid}/status`,
      method: 'get'
    })
  },

  /**
   * 批量检查Token状态
   * @param {Array} uids Token的UID数组
   * @returns {Promise} 请求结果
   */
  batchCheckTokenStatus(uids) {
    return request({
      url: '/tokens/batch-check-status',
      method: 'post',
      data: { uids }
    })
  }
}

export default tokenApi 