const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const DATA_DIR = path.join(__dirname, '../data');
const BACKUPS_FILE = path.join(DATA_DIR, 'backups.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// 确保备份文件存在
if (!fs.existsSync(BACKUPS_FILE)) {
  fs.writeFileSync(BACKUPS_FILE, JSON.stringify([]));
}

// 读取所有备份
const getBackups = () => {
  try {
    const data = fs.readFileSync(BACKUPS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('读取备份文件失败:', error);
    return [];
  }
};

// 保存备份
const saveBackups = (backups) => {
  try {
    // 确保数据目录存在
    if (!fs.existsSync(DATA_DIR)) {
      fs.mkdirSync(DATA_DIR, { recursive: true });
      console.log('创建数据目录:', DATA_DIR);
    }

    // 写入前先备份原文件
    if (fs.existsSync(BACKUPS_FILE)) {
      const backupFilePath = `${BACKUPS_FILE}.bak`;
      fs.copyFileSync(BACKUPS_FILE, backupFilePath);
      console.log('备份原文件到:', backupFilePath);
    }

    // 将数据写入文件
    const dataToWrite = JSON.stringify(backups, null, 2);
    console.log('写入数据长度:', dataToWrite.length);

    fs.writeFileSync(BACKUPS_FILE, dataToWrite, { encoding: 'utf8', flag: 'w' });
    console.log('成功写入文件:', BACKUPS_FILE);

    // 验证文件写入是否成功
    const fileExists = fs.existsSync(BACKUPS_FILE);
    const fileSize = fileExists ? fs.statSync(BACKUPS_FILE).size : 0;
    console.log('文件写入后大小:', fileSize);

    return fileExists && fileSize > 0;
  } catch (error) {
    console.error('保存备份文件失败:', error);
    return false;
  }
};

// 备份控制器
const backupController = {
  // 创建备份
  createBackup: (req, res) => {
    try {
      const { name, description, tokens } = req.body;

      // 验证必填字段
      if (!tokens || !Array.isArray(tokens)) {
        return res.status(400).json({ success: false, message: '无效的备份数据' });
      }

      // 获取现有备份
      const backups = getBackups();

      // 创建新备份
      const newBackup = {
        id: `backup_${Date.now()}_${uuidv4().substring(0, 8)}`,
        name: name || `备份 ${new Date().toLocaleString()}`,
        description: description || `包含${tokens.length}个Token的备份`,
        token_count: tokens.length,
        type: 'manual',
        backup_type: '数据库备份',
        created_at: new Date().toISOString(),
        tokens: tokens
      };

      // 添加到备份列表
      backups.unshift(newBackup);

      // 保存备份
      if (saveBackups(backups)) {
        return res.status(201).json({
          success: true,
          message: '备份创建成功',
          data: {
            backup_id: newBackup.id,
            token_count: newBackup.token_count
          }
        });
      } else {
        return res.status(500).json({ success: false, message: '保存备份失败' });
      }
    } catch (error) {
      console.error('创建备份失败:', error);
      return res.status(500).json({ success: false, message: '创建备份失败' });
    }
  },

  // 获取所有备份
  getAllBackups: (req, res) => {
    try {
      const backups = getBackups();

      // 返回备份列表（但不包含tokens数据，减少数据传输量）
      const backupsWithoutTokens = backups.map(backup => {
        const { tokens, ...backupData } = backup;
        return backupData;
      });

      return res.json({
        success: true,
        data: {
          backups: backupsWithoutTokens,
          total: backupsWithoutTokens.length
        }
      });
    } catch (error) {
      console.error('获取备份列表失败:', error);
      return res.status(500).json({ success: false, message: '获取备份列表失败' });
    }
  },

  // 获取单个备份的详细信息（包括tokens）
  getBackupById: (req, res) => {
    try {
      const { id } = req.params;
      const backups = getBackups();

      // 查找备份
      const backup = backups.find(b => b.id === id);

      if (!backup) {
        return res.status(404).json({ success: false, message: '备份不存在' });
      }

      return res.json({
        success: true,
        data: {
          backup
        }
      });
    } catch (error) {
      console.error('获取备份详情失败:', error);
      return res.status(500).json({ success: false, message: '获取备份详情失败' });
    }
  },

  // 获取单个备份的tokens
  getBackupTokens: (req, res) => {
    try {
      const { id } = req.params;
      const backups = getBackups();

      // 查找备份
      const backup = backups.find(b => b.id === id);

      if (!backup) {
        return res.status(404).json({ success: false, message: '备份不存在' });
      }

      // 提取并格式化tokens
      const tokens = backup.tokens.map(token => ({
        accountId: token.accountId || token.account_id || '-',
        tokenValue: token.token || token.tokenValue || '-',
        loadDate: token.loadDate || token.purchase_time || '-',
        status: token.status || '正常',
        platform: token.platform || '-',
        username: token.user || token.username || '-',
        createTime: token.createTime || token.created_at || new Date().toLocaleString(),
        lastUsedTime: token.lastUsedTime || token.last_used_at || '-'
      }));

      return res.json({
        success: true,
        data: {
          backup_id: backup.id,
          backup_name: backup.name,
          token_count: tokens.length,
          tokens
        }
      });
    } catch (error) {
      console.error('获取备份tokens失败:', error);
      return res.status(500).json({ success: false, message: '获取备份tokens失败' });
    }
  },

  // 删除备份
  deleteBackup: (req, res) => {
    try {
      const { id } = req.params;
      console.log('收到删除备份请求:', id);

      // 读取当前备份数据
      const backups = getBackups();
      console.log('当前备份数量:', backups.length);

      // 尝试多种匹配方式
      let backupIndex = backups.findIndex(b => b.id === id);

      // 如果没找到，尝试其他属性
      if (backupIndex === -1) {
        backupIndex = backups.findIndex(b => b.backup_id === id || b.backupId === id);
      }

      console.log('找到的备份索引:', backupIndex);

      if (backupIndex === -1) {
        console.log('备份不存在:', id);
        return res.status(404).json({ success: false, message: '备份不存在' });
      }

      // 记录要删除的备份信息
      const backupToDelete = backups[backupIndex];
      console.log('要删除的备份:', backupToDelete);

      // 从数组中移除备份
      backups.splice(backupIndex, 1);
      console.log('删除后的备份数量:', backups.length);

      // 保存更新后的备份列表
      const saveResult = saveBackups(backups);
      console.log('保存结果:', saveResult);

      if (saveResult) {
        console.log('备份删除成功:', id);
        return res.json({
          success: true,
          message: '备份删除成功',
          data: { deletedBackup: backupToDelete }
        });
      } else {
        console.error('保存更新后的备份列表失败');
        return res.status(500).json({ success: false, message: '删除备份失败' });
      }
    } catch (error) {
      console.error('删除备份失败:', error);
      return res.status(500).json({ success: false, message: `删除备份失败: ${error.message}` });
    }
  },

  // 清空所有备份
  clearAllBackups: (req, res) => {
    try {
      // 保存空数组，清空所有备份
      if (saveBackups([])) {
        return res.json({
          success: true,
          message: '所有备份已清空'
        });
      } else {
        return res.status(500).json({ success: false, message: '清空备份失败' });
      }
    } catch (error) {
      console.error('清空备份失败:', error);
      return res.status(500).json({ success: false, message: '清空备份失败' });
    }
  }
};

module.exports = backupController;