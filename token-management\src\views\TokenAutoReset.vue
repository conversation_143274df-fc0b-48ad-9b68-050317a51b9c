<template>
  <div class="token-auto-reset-container">
    <!-- 自动重置设置卡片 -->
    <div class="page-header">
      <h1 class="page-title">Token自动重置管理</h1>
      <div class="page-desc">设置智能管理您的Token，实现自动化重置、备份和订单管理</div>
    </div>
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <i class="el-icon-timer header-icon"></i>
            <span>自动Token重置</span>
          </div>
          <el-switch
            v-model="autoResetEnabled"
            active-text="启用自动重置"
            inactive-text="禁用自动重置"
            @change="handleAutoResetChange"
          />
        </div>
      </template>
      
      <el-form label-position="top" :model="autoResetSettings" class="settings-form">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="8">
            <el-form-item label="自动加载设置">
              <div class="input-with-label">
                <el-input-number
                  v-model="autoResetSettings.loadDaysBefore"
                  :min="1"
                  :max="180"
                  :step="1"
                  :disabled="!autoResetEnabled"
                />
                <span class="form-item-hint">天前的Token</span>
              </div>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="自动执行时间">
              <el-time-picker
                v-model="autoResetSettings.time"
                format="HH:mm"
                placeholder="选择时间"
                class="full-width"
                :disabled="!autoResetEnabled"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="8">
            <el-form-item label="简单预览">
              <div class="preview-text" :class="{ 'preview-disabled': !autoResetEnabled }">
                系统将在每天 {{ formatTime(autoResetSettings.time) }} 自动加载并重置 {{ autoResetSettings.loadDaysBefore }} 天前的所有Token
                <div class="date-info">{{ autoResetSettings.loadDaysBefore }}天前日期为: {{ formatDate(autoResetSettings.loadDaysBefore) }}</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider>自动重置选项</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="options-group">
              <el-checkbox 
                v-model="autoResetSettings.autoBackupBeforeReset"
                :disabled="!autoResetEnabled"
              >
                重置前自动备份
              </el-checkbox>
              
              <el-checkbox 
                v-model="autoResetSettings.autoBackupAfterReset"
                :disabled="!autoResetEnabled"
              >
                重置后自动备份
              </el-checkbox>
              
              <el-checkbox 
                v-model="autoResetSettings.notifyOnCompletion"
                :disabled="!autoResetEnabled"
              >
                完成后通知我
              </el-checkbox>
              
              <el-checkbox 
                v-model="autoResetSettings.autoAddToOrder"
                :disabled="!autoResetEnabled"
              >
                重置完自动置入账号内订单
              </el-checkbox>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item>
          <div class="action-buttons">
            <el-button 
              type="primary" 
              size="large" 
              @click="saveSettings" 
              :disabled="!autoResetEnabled"
              class="action-btn"
            >
              <i class="el-icon-check"></i> 保存设置
            </el-button>
            <el-button 
              size="large" 
              @click="resetSettings" 
              :disabled="!autoResetEnabled"
              class="action-btn"
            >
              <i class="el-icon-refresh-left"></i> 重置设置
            </el-button>
            <el-button 
              type="success" 
              size="large" 
              @click="executeNow" 
              class="execute-now-btn action-btn"
            >
              <i class="el-icon-video-play"></i> 立即执行一次
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 手动重置区域 -->
    <el-card class="manual-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <i class="el-icon-refresh header-icon"></i>
            <span>一键手动重置</span>
          </div>
        </div>
      </template>
      
      <el-form label-position="top" :model="manualSettings" class="settings-form">
        <div class="manual-reset-content">
          <div class="manual-setting-box">
            <div class="setting-title">加载天数设置</div>
            <div class="load-days-setting">
              <el-input-number
                v-model="manualSettings.loadDaysBefore"
                :min="1"
                :max="180"
                :step="1"
                controls-position="right"
                size="large"
              />
              <div class="load-days-info">
                <span class="form-item-label">加载日期: <b>{{ formatDate(manualSettings.loadDaysBefore) }}</b></span>
                <span class="form-item-hint">选择要重置的Token加载天数</span>
              </div>
            </div>
          </div>
          
          <div class="manual-setting-box">
            <div class="setting-title">操作选项</div>
            <div class="options-group manual-options">
              <el-checkbox v-model="manualSettings.backupBeforeReset">
                <div class="option-content">
                  <i class="el-icon-upload2 option-icon"></i>
                  <span>重置前自动备份</span>
                </div>
              </el-checkbox>
              <el-checkbox v-model="manualSettings.createBackup">
                <div class="option-content">
                  <i class="el-icon-download option-icon"></i>
                  <span>重置完成后创建备份</span>
                </div>
              </el-checkbox>
              <el-checkbox v-model="manualSettings.notifyOnCompletion">
                <div class="option-content">
                  <i class="el-icon-bell option-icon"></i>
                  <span>完成后通知我</span>
                </div>
              </el-checkbox>
              <el-checkbox v-model="manualSettings.addToOrder">
                <div class="option-content">
                  <i class="el-icon-shopping-cart-full option-icon"></i>
                  <span>重置完自动置入账号内订单</span>
                </div>
              </el-checkbox>
            </div>
          </div>
        </div>
        
        <el-form-item>
          <el-button 
            type="primary" 
            size="large"
            class="manual-reset-btn" 
            @click="manualReset" 
            :loading="isLoading"
          >
            <i class="el-icon-magic-stick" style="font-size: 20px; margin-right: 6px;"></i>
            一键加载并重置 {{ manualSettings.loadDaysBefore }} 天前的Token
            <div class="date-tag">{{ formatDate(manualSettings.loadDaysBefore) }}</div>
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 操作结果表格 -->
    <el-card class="result-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <i class="el-icon-document-checked header-icon"></i>
            <span>重置结果</span>
            <span class="result-count" v-if="resultList.length > 0">{{ resultList.length }}条记录</span>
          </div>
          <div class="header-actions">
            <el-button-group>
              <el-button type="primary" @click="refreshResults">
                <el-icon><el-icon-refresh /></el-icon>刷新
              </el-button>
              <el-button type="success" @click="exportResults">
                <el-icon><el-icon-download /></el-icon>导出
              </el-button>
              <el-button type="danger" @click="clearResults">
                <el-icon><el-icon-delete /></el-icon>清空
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      
      <div v-if="resultList.length === 0" class="empty-result">
        <el-empty description="暂无重置记录" />
      </div>
      
      <el-table
        v-else
        ref="resultTable"
        :data="resultList"
        style="width: 95%; margin: 0 auto;"
        border
        stripe
        highlight-current-row
        size="medium"
        :header-cell-style="{background: '#f5f7fa', color: '#303133', fontSize: '15px', height: '56px'}"
        :cell-style="{fontSize: '14px', padding: '12px 0'}"
      >
        <el-table-column prop="loadDate" label="已卖日期" width="110" sortable />
        <el-table-column prop="resetDate" label="重置日期" width="110" sortable />
        <el-table-column prop="tokenCount" label="Token数量" width="100" align="center" />
        <el-table-column prop="successCount" label="重置成功数量" width="120" align="center">
          <template #default="scope">
            <span :class="scope.row.successCount > 0 ? 'success-text' : ''">{{ scope.row.successCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="failCount" label="重置失败数量" width="120" align="center">
          <template #default="scope">
            <span :class="scope.row.failCount > 0 ? 'fail-text' : ''">{{ scope.row.failCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" effect="plain">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="备份关联ID" width="240">
          <template #default="scope">
            <el-tooltip
              effect="dark"
              :content="scope.row.taskId || '无关联ID'"
              placement="top"
            >
              <div class="backup-link-cell">
                <i class="el-icon-link" v-if="scope.row.taskId"></i>
                <span>{{ getBackupLinkInfo(scope.row) }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="backups.beforeResetId" label="重置前备份" width="180">
          <template #default="scope">
            <el-tag v-if="scope.row.backups?.beforeReset" type="success" size="small" effect="dark">
              <el-tooltip effect="dark" :content="scope.row.backups?.beforeResetId || ''" placement="top">
                <span>{{ getBakcupIDDisplay(scope.row, 'before') }}</span>
              </el-tooltip>
            </el-tag>
            <span v-else class="no-backup">未创建</span>
          </template>
        </el-table-column>
        <el-table-column prop="backups.afterResetId" label="重置后备份" width="180">
          <template #default="scope">
            <el-tag v-if="scope.row.backups?.afterReset" type="warning" size="small" effect="dark">
              <el-tooltip effect="dark" :content="scope.row.backups?.afterResetId || ''" placement="top">
                <span>{{ getBakcupIDDisplay(scope.row, 'after') }}</span>
              </el-tooltip>
            </el-tag>
            <span v-else class="no-backup">未创建</span>
          </template>
        </el-table-column>
        <el-table-column prop="置入账号内订单" label="置入账号内订单" width="140">
          <template #default="scope">
            <el-tag :type="scope.row.addedToOrder ? 'success' : 'info'" effect="plain">
              {{ scope.row.addedToOrder ? '已置入' : '未置入' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderDeleted" label="删除订单" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.orderDeleted ? 'danger' : 'info'" size="medium" effect="plain">
              {{ scope.row.orderDeleted ? '已删除' : '未删除' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executeTime" label="执行时间" width="160" sortable />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.type === '自动' ? 'warning' : 'primary'" size="medium">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right" align="center">
          <template #default="scope">
            <div class="table-actions">
              <el-button 
                type="primary" 
                size="small"
                class="detail-btn action-inline-btn"
                @click="viewDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button 
                type="success" 
                size="small"
                class="backup-btn before-backup action-inline-btn"
                @click="viewBackup(scope.row, 'before')"
                :disabled="!scope.row.backups.beforeReset"
              >
                重置前备份
              </el-button>
              <el-button 
                type="warning" 
                size="small"
                class="backup-btn after-backup action-inline-btn"
                @click="viewBackup(scope.row, 'after')"
                :disabled="!scope.row.backups.afterReset"
              >
                重置后备份
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetail"
      title="重置任务详情"
      width="720px"
      class="reset-detail-dialog"
      append-to-body
    >
      <div class="detail-header">
        <div class="detail-type">
          <el-tag :type="selectedResult?.type === '自动' ? 'warning' : 'primary'" size="large" effect="dark">
            {{ selectedResult?.type }}
          </el-tag>
        </div>
        <div class="detail-time">
          <i class="el-icon-time"></i> {{ selectedResult?.executeTime }}
        </div>
      </div>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="已卖日期">
          <div class="date-value">{{ selectedResult?.loadDate }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="重置日期">
          <div class="date-value">{{ selectedResult?.resetDate }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(selectedResult?.status)" effect="plain">
            {{ selectedResult?.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="Token数量">
          <div class="token-count">{{ selectedResult?.tokenCount }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="成功数量">
          <div class="success-count">
            <i class="el-icon-check success-icon"></i> {{ selectedResult?.successCount }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="失败数量">
          <div class="fail-count">
            <i class="el-icon-close fail-icon"></i> {{ selectedResult?.failCount }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="置入账号内订单">
          <el-tag :type="selectedResult?.addedToOrder ? 'success' : 'info'" effect="plain">
            {{ selectedResult?.addedToOrder ? '已置入' : '未置入' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="删除订单">
          <el-tag :type="selectedResult?.orderDeleted ? 'danger' : 'info'" effect="plain">
            {{ selectedResult?.orderDeleted ? '已删除' : '未删除' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 备份状态信息 -->
      <div class="backup-status-container" v-if="selectedResult?.taskId">
        <h3 class="backup-section-title">
          <i class="el-icon-document-copy"></i> 备份信息
          <span class="task-id">关联ID: {{ selectedResult?.taskId }}</span>
        </h3>
        
        <div class="backup-status">
          <div 
            class="backup-status-item" 
            :class="{'active': selectedResult?.backups?.beforeReset, 'not-available': !selectedResult?.backups?.beforeReset}"
          >
            <el-tag type="success" effect="dark" size="small" v-if="selectedResult?.backups?.beforeReset">重置前备份</el-tag>
            <el-tag type="info" effect="plain" size="small" v-else>重置前备份</el-tag>
            
            <div class="backup-id" v-if="selectedResult?.backups?.beforeResetId">
              {{ selectedResult?.backups?.beforeResetId }}
            </div>
            <div class="backup-unavailable" v-else>未创建备份</div>
            
            <el-button 
              size="small" 
              type="success" 
              class="backup-btn before-backup" 
              @click="viewBackup(selectedResult, 'before')" 
              :disabled="!selectedResult?.backups?.beforeReset"
            >
              查看备份
            </el-button>
          </div>
          
          <div 
            class="backup-status-item" 
            :class="{'active': selectedResult?.backups?.afterReset, 'not-available': !selectedResult?.backups?.afterReset}"
          >
            <el-tag type="warning" effect="dark" size="small" v-if="selectedResult?.backups?.afterReset">重置后备份</el-tag>
            <el-tag type="info" effect="plain" size="small" v-else>重置后备份</el-tag>
            
            <div class="backup-id" v-if="selectedResult?.backups?.afterResetId">
              {{ selectedResult?.backups?.afterResetId }}
            </div>
            <div class="backup-unavailable" v-else>未创建备份</div>
            
            <el-button 
              size="small" 
              type="warning" 
              class="backup-btn after-backup" 
              @click="viewBackup(selectedResult, 'after')" 
              :disabled="!selectedResult?.backups?.afterReset"
            >
              查看备份
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="detail-section">
        <h3 class="detail-section-title">
          <i class="el-icon-info"></i> 操作日志
        </h3>
        
        <div class="detail-logs">
          <div v-for="(log, index) in selectedResult?.logs" :key="index" class="log-item" :class="{'log-highlight': highlightLogType(log.message)}">
            <div class="log-time">{{ log.time }}</div>
            <div class="log-message">{{ log.message }}</div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetail = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="downloadLogs" 
            :disabled="!selectedResult?.logs?.length"
          >
            <i class="el-icon-download"></i> 下载日志
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useTokenStore } from '@/store/token'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as tokenService from '@/services/tokenService'

const tokenStore = useTokenStore()
const route = useRoute()
const router = useRouter()

// 自动重置设置
const autoResetEnabled = ref(false)
const autoResetSettings = ref({
  loadDaysBefore: 12, // 默认为12天，根据用户习惯设置
  time: new Date(2024, 0, 1, 3, 0), // 默认设置为凌晨3点
  autoBackupBeforeReset: true,
  autoBackupAfterReset: true,
  notifyOnCompletion: true,
  autoAddToOrder: true
})

// 手动重置设置
const manualSettings = ref({
  loadDaysBefore: 12, // 默认为12天，根据用户习惯设置
  backupBeforeReset: true,
  createBackup: true,
  addToOrder: true,
  notifyOnCompletion: true
})

// 操作状态
const isLoading = ref(false)

// 当自动重置状态变化时
const handleAutoResetChange = (value) => {
  // 可以在这里添加逻辑
}

// 保存设置
const saveSettings = () => {
  ElMessage.success('自动重置设置已保存')
}

// 重置设置
const resetSettings = () => {
  autoResetSettings.value = {
    loadDaysBefore: 12,
    time: new Date(2024, 0, 1, 3, 0),
    autoBackupBeforeReset: true,
    autoBackupAfterReset: true,
    notifyOnCompletion: true,
    autoAddToOrder: true
  }
}

// 立即执行一次自动重置
const executeNow = () => {
  isLoading.value = true
  
  setTimeout(() => {
    isLoading.value = false
    ElMessage.success(`已成功重置 ${autoResetSettings.value.loadDaysBefore} 天前的Token`)
    
    // 计算日期
    const today = new Date()
    const resetDate = today.toISOString().split('T')[0]
    
    const loadDate = new Date()
    loadDate.setDate(today.getDate() - autoResetSettings.value.loadDaysBefore)
    const loadDateStr = loadDate.toISOString().split('T')[0]
    
    // 生成唯一关联ID - 将作为同一次重置操作的关联标识
    const timestamp = Date.now()
    const randomId = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
    const uniqueId = `${timestamp}-${randomId}`
    
    // 任务ID - 用于关联"重置前"和"重置后"备份
    const taskId = uniqueId.replace(/-/g, '')
    
    // 生成备份ID，使用相同的taskId
    const beforeResetId = autoResetSettings.value.autoBackupBeforeReset ? 
      `QBF${resetDate.replace(/-/g, '')}_${taskId}` : null;
    const afterResetId = autoResetSettings.value.autoBackupAfterReset ? 
      `HBF${resetDate.replace(/-/g, '')}_${taskId}` : null;
    
    // 添加一条新的结果记录
    const newResult = {
      id: resultList.value.length + 1,
      type: '自动',
      executeTime: formatDateTime(new Date()),
      loadDate: loadDateStr,
      resetDate: resetDate,
      tokenCount: 45,
      successCount: 42,
      failCount: 3,
      status: '已完成',
      taskId: taskId,
      addedToOrder: autoResetSettings.value.autoAddToOrder,
      orderDeleted: false,
      backups: {
        beforeReset: autoResetSettings.value.autoBackupBeforeReset,
        afterReset: autoResetSettings.value.autoBackupAfterReset,
        beforeResetId: beforeResetId,
        afterResetId: afterResetId
      },
      logs: [
        { time: formatDateTime(new Date(Date.now() - 600000)), message: '开始执行自动重置任务' },
        { time: formatDateTime(new Date(Date.now() - 550000)), message: `加载了${autoResetSettings.value.loadDaysBefore}天前的45个Token` },
        { time: formatDateTime(new Date(Date.now() - 500000)), message: autoResetSettings.value.autoBackupBeforeReset ? '创建重置前的Token备份' : '跳过重置前备份（未启用）' },
        { time: formatDateTime(new Date(Date.now() - 450000)), message: '开始重置Token' },
        { time: formatDateTime(new Date(Date.now() - 250000)), message: '重置了42个Token成功，3个失败' },
        { time: formatDateTime(new Date(Date.now() - 100000)), message: autoResetSettings.value.autoBackupAfterReset ? '创建重置后的Token备份' : '跳过重置后备份（未启用）' },
        { time: formatDateTime(new Date(Date.now() - 50000)), message: autoResetSettings.value.autoAddToOrder ? '自动置入账号内订单' : '跳过置入账号内订单（未启用）' },
        { time: formatDateTime(new Date()), message: '任务完成' }
      ]
    }
    
    // 如果启用了完成后通知，则显示通知
    if (autoResetSettings.value.notifyOnCompletion) {
      ElMessage({
        message: `已完成${autoResetSettings.value.loadDaysBefore}天前的Token重置，成功42个，失败3个`,
        type: 'success',
        duration: 5000,
        showClose: true
      })
    }
    
    resultList.value.unshift(newResult)
  }, 2000)
}

// 手动重置
const manualReset = () => {
  isLoading.value = true
  
  setTimeout(() => {
    isLoading.value = false
    ElMessage.success(`已成功重置 ${manualSettings.value.loadDaysBefore} 天前的Token`)
    
    // 计算日期
    const today = new Date()
    const resetDate = today.toISOString().split('T')[0]
    
    const loadDate = new Date()
    loadDate.setDate(today.getDate() - manualSettings.value.loadDaysBefore)
    const loadDateStr = loadDate.toISOString().split('T')[0]
    
    // 生成关联ID 
    const timestamp = Date.now()
    const randomId = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
    const uniqueId = `${timestamp}-${randomId}`
    
    // 任务ID - 用于关联"重置前"和"重置后"备份
    const taskId = uniqueId.replace(/-/g, '')
    
    // 生成备份ID，使用相同的taskId
    const beforeResetId = manualSettings.value.backupBeforeReset ? 
      `QBF${resetDate.replace(/-/g, '')}_${taskId}` : null;
    const afterResetId = manualSettings.value.createBackup ? 
      `HBF${resetDate.replace(/-/g, '')}_${taskId}` : null;
    
    // 添加一条新的结果记录
    const newResult = {
      id: resultList.value.length + 1,
      type: '手动',
      executeTime: formatDateTime(new Date()),
      loadDate: loadDateStr,
      resetDate: resetDate,
      tokenCount: 36,
      successCount: 35,
      failCount: 1, 
      status: '已完成',
      taskId: taskId,
      addedToOrder: manualSettings.value.addToOrder,
      orderDeleted: false,
      backups: {
        beforeReset: manualSettings.value.backupBeforeReset,
        afterReset: manualSettings.value.createBackup,
        beforeResetId: beforeResetId,
        afterResetId: afterResetId
      },
      logs: [
        { time: formatDateTime(new Date(Date.now() - 600000)), message: '开始执行手动重置任务' },
        { time: formatDateTime(new Date(Date.now() - 550000)), message: `加载了${manualSettings.value.loadDaysBefore}天前的36个Token` },
        manualSettings.value.backupBeforeReset ? { time: formatDateTime(new Date(Date.now() - 500000)), message: '创建重置前的Token备份' } : null,
        { time: formatDateTime(new Date(Date.now() - 450000)), message: '开始重置Token' },
        { time: formatDateTime(new Date(Date.now() - 250000)), message: '重置了35个Token成功，1个失败' },
        manualSettings.value.createBackup ? { time: formatDateTime(new Date(Date.now() - 100000)), message: '创建重置后的Token备份' } : null,
        manualSettings.value.addToOrder ? { time: formatDateTime(new Date()), message: '自动置入账号内订单' } : null,
        manualSettings.value.notifyOnCompletion ? { time: formatDateTime(new Date()), message: '发送完成通知' } : null,
        { time: formatDateTime(new Date()), message: '任务完成' }
      ].filter(log => log !== null)
    }
    
    // 如果启用了完成后通知，则显示通知
    if (manualSettings.value.notifyOnCompletion) {
      ElMessage({
        message: `已完成${manualSettings.value.loadDaysBefore}天前的Token重置，成功35个，失败1个`,
        type: 'success',
        duration: 5000,
        showClose: true
      })
    }
    
    resultList.value.unshift(newResult)
  }, 2000)
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '--:--'
  const hours = time.getHours().toString().padStart(2, '0')
  const minutes = time.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 计算并格式化指定天数前的日期
const formatDate = (days) => {
  const date = new Date()
  date.setDate(date.getDate() - days)
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '--:--:--'
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 结果列表数据
const resultList = ref([
  {
    id: 1,
    executeTime: '2024-03-29 03:15:36',
    type: '自动',
    loadDate: '2024-03-17',
    resetDate: '2024-03-29',
    tokenCount: 120,
    successCount: 115,
    failCount: 5,
    status: '已完成',
    addedToOrder: true,
    orderDeleted: false,
    backups: {
      beforeReset: true,
      afterReset: true,
      beforeResetId: 'QBF20240329167112345670001',
      afterResetId: 'HBF20240329167112347890001'
    },
    logs: [
      { time: '2024-03-29 03:00:05', message: '开始执行自动重置任务' },
      { time: '2024-03-29 03:00:10', message: '加载了12天前的120个Token' },
      { time: '2024-03-29 03:00:15', message: '创建重置前的Token备份' },
      { time: '2024-03-29 03:00:30', message: '开始重置Token' },
      { time: '2024-03-29 03:10:45', message: '重置了115个Token成功，5个失败' },
      { time: '2024-03-29 03:15:32', message: '创建重置后的Token备份' },
      { time: '2024-03-29 03:15:34', message: '自动置入账号内订单' },
      { time: '2024-03-29 03:15:36', message: '任务完成' }
    ]
  },
  {
    id: 2,
    executeTime: '2024-03-28 15:22:18',
    type: '手动',
    loadDate: '2024-03-16',
    resetDate: '2024-03-28',
    tokenCount: 85,
    successCount: 85,
    failCount: 0,
    status: '已完成',
    addedToOrder: true,
    orderDeleted: true,
    backups: {
      beforeReset: true,
      afterReset: false,
      beforeResetId: 'QBF20240328167109876540001',
      afterResetId: null
    },
    logs: [
      { time: '2024-03-28 15:20:03', message: '开始执行手动重置任务' },
      { time: '2024-03-28 15:20:10', message: '加载了12天前的85个Token' },
      { time: '2024-03-28 15:20:15', message: '创建重置前的Token备份' },
      { time: '2024-03-28 15:21:30', message: '开始重置Token' },
      { time: '2024-03-28 15:22:05', message: '重置了85个Token成功，0个失败' },
      { time: '2024-03-28 15:22:16', message: '自动置入账号内订单' },
      { time: '2024-03-28 15:22:17', message: '发送完成通知' },
      { time: '2024-03-28 15:22:18', message: '任务完成' }
    ]
  }
])

// 详情相关
const showDetail = ref(false)
const selectedResult = ref(null)

// 状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '进行中':
      return 'primary'
    case '等待中':
      return 'info'
    case '失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 结果相关操作
const refreshResults = () => {
  ElMessage.success('结果列表已刷新')
}

const exportResults = () => {
  ElMessage.success('已将结果导出到文件')
}

const clearResults = () => {
  ElMessage({
    message: '确定要清空结果数据吗？',
    type: 'warning',
    showClose: true,
    duration: 0,
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    onConfirm: () => {
      resultList.value = []
      ElMessage.success('结果数据已清空')
    }
  })
}

// 查看详情
const viewDetail = (result) => {
  selectedResult.value = result
  showDetail.value = true
}

// 下载日志
const downloadLogs = () => {
  ElMessage.success(`已下载ID为 ${selectedResult.value.id} 的操作日志`)
}

// 日志高亮判断
const highlightLogType = (message) => {
  if (!message) return false;
  
  if (message.includes('失败')) {
    return 'error';
  } else if (message.includes('成功') || message.includes('完成')) {
    return 'success';
  } else if (message.includes('开始')) {
    return 'start';
  } else if (message.includes('备份')) {
    return 'backup';
  } else if (message.includes('加载')) {
    return 'load';
  }
  
  return false;
}

// 查看备份
const viewBackup = (result, type) => {
  const backupType = type === 'before' ? '重置前' : '重置后';
  const backupId = type === 'before' ? result.backups.beforeResetId : result.backups.afterResetId;
  
  if (type === 'before' && result.backups.beforeReset) {
    ElMessage.success(`正在跳转到${backupType}备份，ID: ${backupId}`);
    // 跳转到备份页面并传递备份ID
    router.push(`/token-backup?id=${backupId}`);
  } else if (type === 'after' && result.backups.afterReset) {
    ElMessage.success(`正在跳转到${backupType}备份，ID: ${backupId}`);
    // 跳转到备份页面并传递备份ID
    router.push(`/token-backup?id=${backupId}`);
  } else {
    ElMessage.warning(`该记录没有可用的${backupType}备份`);
  }
}

// 全局函数共享，供其他页面调用
const exportTokenFunctions = () => {
  // 检查是否需要初始化函数并返回
  const initFunctions = route.query.initFunctions === 'true';
  
  // 创建要共享的函数
  window.tokenResetFunctions = {
    // 单个token操作
    checkAvatarNickname: (token) => {
      ElMessage.info(`查询"${token.username}"的头像昵称`)
      // 实际的头像昵称查询逻辑
      console.log('查询头像昵称:', token)
    },
    
    checkOnline: (token) => {
      ElMessage.info(`查询"${token.username}"的在线状态`)
      // 实际的在线状态查询逻辑
      console.log('查询在线状态:', token)
    },
    
    modifyAvatar: (token) => {
      ElMessage.info(`修改"${token.username}"的头像`)
      // 实际的头像修改逻辑
      console.log('修改头像:', token)
    },
    
    modifyNickname: (token) => {
      ElMessage.info(`修改"${token.username}"的昵称`)
      // 实际的昵称修改逻辑
      console.log('修改昵称:', token)
    },
    
    addToOrder: (token) => {
      ElMessage.info(`将Token置入订单: ${token.tokenValue.substring(0, 10)}...`)
      // 实际的置入订单逻辑
      console.log('置入订单:', token)
    },
    
    assignToAccount: (token) => {
      ElMessage.info(`将Token指定给账号: ${token.tokenValue.substring(0, 10)}...`)
      // 实际的指定账号逻辑
      console.log('指定账号:', token)
    },
    
    deleteOrder: (token) => {
      ElMessage.info(`删除"${token.username}"的订单`)
      // 实际的删除订单逻辑
      console.log('删除订单:', token)
    },
    
    // 批量操作
    batchCheckAvatarNickname: (tokens) => {
      ElMessage.info(`批量查询${tokens.length}个Token的头像昵称`)
      // 实际的批量头像昵称查询逻辑
      console.log('批量查询头像昵称:', tokens)
      
      // 准备token数据
      const tokenData = tokens.map(token => ({
        uid: token.uid || token.id,
        token: token.token || token.tokenValue
      }));
      
      // 显示加载提示
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在查询头像和昵称...',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 调用查询API
      tokenService.queryTokenUserInfo(tokenData).then(result => {
        if (result.success) {
          ElMessage.success(`查询完成: ${result.results.filter(r => r.success).length}个成功，${result.results.filter(r => !r.success).length}个失败`);
          
          // 统计信息
          const statsHtml = `
            <div style="text-align: center; margin-top: 10px;">
              <div style="font-size: 16px; margin-bottom: 10px;">查询完成</div>
              <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                  <span style="margin-right: 5px;">成功查询</span> 
                  <span style="font-size: 18px;">${result.results.filter(r => r.success).length}</span>
                </div>
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                  <span style="margin-right: 5px;">查询失败</span> 
                  <span style="font-size: 18px;">${result.results.filter(r => !r.success).length}</span>
                </div>
              </div>
              <div style="font-size: 14px; color: #409EFF;">查询结果已处理完成</div>
            </div>
          `;
          
          // 显示统计信息
          ElMessageBox.alert(statsHtml, '查询结果', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定'
          });
        } else {
          ElMessage.error(`查询失败: ${result.message || '未知错误'}`);
        }
      }).catch(error => {
        console.error('批量查询头像昵称异常:', error);
        ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
      }).finally(() => {
        // 关闭加载提示
        loadingInstance.close();
      });
    },
    
    batchCheckOnline: (tokens) => {
      ElMessage.info(`批量查询${tokens.length}个Token的在线状态`)
      // 实际的批量在线状态查询逻辑
      console.log('批量查询在线状态:', tokens)
    },
    
    batchModifyAvatar: (tokens) => {
      ElMessage.info(`批量修改${tokens.length}个Token的头像`)
      // 实际的批量头像修改逻辑
      console.log('批量修改头像:', tokens)
    },
    
    batchModifyNickname: (tokens) => {
      ElMessage.info(`批量修改${tokens.length}个Token的昵称`)
      // 实际的批量昵称修改逻辑
      console.log('批量修改昵称:', tokens)
    },
    
    batchAddToOrder: (tokens) => {
      ElMessage.info(`批量将${tokens.length}个Token置入订单`)
      // 实际的批量置入订单逻辑
      console.log('批量置入订单:', tokens)
    },
    
    batchAssignToAccount: (tokens) => {
      ElMessage.info(`批量将${tokens.length}个Token指定给账号`)
      // 实际的批量指定账号逻辑
      console.log('批量指定账号:', tokens)
    },
    
    batchDeleteOrder: (tokens) => {
      ElMessage.info(`批量删除${tokens.length}个Token的订单`)
      // 实际的批量删除订单逻辑
      console.log('批量删除订单:', tokens)
    }
  };
  
  // 如果是初始化请求，完成后跳回备份页面
  if (initFunctions) {
    const returnData = localStorage.getItem('backupPageReturn');
    if (returnData) {
      try {
        const { path, query, params } = JSON.parse(returnData);
        ElMessage.success('Token操作功能已初始化，正在返回备份页面...');
        // 返回到备份页面
        router.push({ path, query, params });
      } catch (e) {
        console.error('解析返回路径失败:', e);
        // 返回到备份页面的默认路径
        router.push('/token-backup');
      }
    } else {
      // 没有返回路径就留在当前页面
      ElMessage.info('Token操作功能已初始化');
    }
  }
};

// 生命周期钩子
onMounted(() => {
  // 默认设置为用户习惯
  manualSettings.value.loadDaysBefore = 12
  autoResetSettings.value.loadDaysBefore = 12
  
  // 导出函数共享
  exportTokenFunctions()
})

// 监听路由变化，如果有初始化请求，就重新导出函数
watch(() => route.query, (newQuery) => {
  if (newQuery.initFunctions === 'true') {
    exportTokenFunctions()
  }
}, { immediate: true })

// 在表格中展示备份关联信息
const getBackupLinkInfo = (row) => {
  if (!row.taskId) return '无关联信息'
  
  const hasBeforeBackup = row.backups?.beforeReset
  const hasAfterBackup = row.backups?.afterReset
  
  if (hasBeforeBackup && hasAfterBackup) {
    return `关联备份ID: ${row.taskId}`
  } else if (hasBeforeBackup) {
    return `仅重置前备份: ${row.taskId}`
  } else if (hasAfterBackup) {
    return `仅重置后备份: ${row.taskId}`
  } else {
    return '无备份'
  }
}

// 在表格中自定义展示备份ID
const getBakcupIDDisplay = (row, type) => {
  if (type === 'before' && row.backups?.beforeResetId) {
    return row.backups.beforeResetId.split('_')[0] + '_...'
  } else if (type === 'after' && row.backups?.afterResetId) {
    return row.backups.afterResetId.split('_')[0] + '_...'
  }
  return '无备份'
}
</script>

<style scoped>
.token-auto-reset-container {
  padding: 20px;
  background-color: #f6f8fa;
  min-height: calc(100vh - 60px);
}

/* 页面标题部分 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-desc {
  font-size: 14px;
  color: #606266;
}

/* 卡片样式 */
.settings-card,
.manual-card,
.result-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: none;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  font-size: 18px;
  color: #409EFF;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 设置表单样式 */
.settings-form {
  padding: 0 20px 20px;
}

.input-with-label {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item-hint {
  color: #909399;
  font-size: 14px;
}

.full-width {
  width: 100%;
}

.preview-text {
  background-color: #f5f7fa;
  padding: 12px 15px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.preview-disabled {
  opacity: 0.6;
}

.date-info {
  font-size: 12px;
  margin-top: 6px;
  color: #409EFF;
}

/* 选项组样式 */
.options-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 10px 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.action-btn {
  min-width: 120px;
}

.execute-now-btn {
  margin-left: auto;
  min-width: 200px;
}

/* 手动重置样式 */
.manual-reset-content {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.manual-setting-box {
  flex: 1;
}

.setting-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
}

.load-days-setting {
  display: flex;
  align-items: center;
  gap: 15px;
}

.load-days-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-item-label {
  font-size: 14px;
  color: #303133;
}

.manual-options {
  flex-direction: column;
  gap: 15px;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-icon {
  font-size: 16px;
  color: #409EFF;
}

.manual-reset-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.date-tag {
  position: absolute;
  right: 15px;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 结果表格样式 */
.result-count {
  font-size: 12px;
  background-color: #ecf5ff;
  color: #409EFF;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 10px;
}

.empty-result {
  padding: 60px 0;
}

.success-text {
  color: #67C23A;
  font-weight: 600;
}

.fail-text {
  color: #F56C6C;
  font-weight: 600;
}

/* 表格中的操作按钮 */
.table-actions {
  display: flex;
  justify-content: center;
  gap: 5px;
  flex-wrap: nowrap;
}

.action-inline-btn {
  padding: 4px 8px;
  min-width: 60px;
}

/* 详情对话框样式 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-type {
  display: flex;
  align-items: center;
}

.detail-time {
  color: #909399;
  font-size: 14px;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

.date-value, .token-count {
  font-weight: 500;
}

.success-count {
  color: #67C23A;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.fail-count {
  color: #F56C6C;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.success-icon {
  font-size: 16px;
}

.fail-icon {
  font-size: 16px;
}

.detail-section {
  margin-top: 20px;
  border-top: 1px dashed #EBEEF5;
  padding-top: 20px;
}

.detail-logs {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.log-item {
  display: flex;
  border-bottom: 1px solid #EBEEF5;
  padding: 8px 12px;
  font-size: 13px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  width: 180px;
  color: #909399;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
}

.log-item.log-highlight.success {
  background-color: #f0f9eb;
}

.log-item.log-highlight.error {
  background-color: #fef0f0;
}

.log-item.log-highlight.start {
  background-color: #ecf5ff;
}

.log-item.log-highlight.backup {
  background-color: #fdf6ec;
}

.log-item.log-highlight.load {
  background-color: #f5f7fa;
}

/* 备份关联样式 */
.backup-link-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  font-family: monospace;
  font-size: 12px;
  background-color: #f0f9eb;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px dashed #67c23a;
  cursor: pointer;
  transition: all 0.2s;
  max-width: 220px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.backup-link-cell:hover {
  background-color: #e1f3d8;
  border-color: #85ce61;
}

.backup-link-cell i {
  color: #67c23a;
  font-size: 16px;
}

.no-backup {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

/* 备份状态容器 */
.backup-status-container {
  margin-top: 20px;
  padding: 15px;
  background: #f9fafc;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.backup-section-title, 
.detail-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id {
  margin-left: auto;
  font-size: 13px;
  font-weight: 500;
  font-family: monospace;
  color: #67c23a;
  background-color: #f0f9eb;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px dashed #c2e7b0;
}

.backup-status {
  display: flex;
  gap: 15px;
  margin: 10px 0;
}

.backup-status-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.backup-status-item.active {
  background-color: rgba(103, 194, 58, 0.05);
  border-color: #c2e7b0;
}

.backup-status-item.not-available {
  background-color: #f5f7fa;
  opacity: 0.8;
}

.backup-id {
  font-family: Consolas, monospace;
  color: #606266;
  font-size: 13px;
  word-break: break-all;
}

.backup-unavailable {
  color: #909399;
  font-style: italic;
}
</style> 