const mysql = require('mysql2/promise');

async function checkAdmin() {
  const pool = await mysql.createPool({
    host: 'localhost',
    user: 'root',
    password: 'qq@666666',
    database: 'token_management'
  });

  try {
    const [rows] = await pool.query('SELECT * FROM users WHERE username = ?', ['admin']);
    console.log('Admin用户信息:', rows);
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await pool.end();
  }
}

checkAdmin(); 