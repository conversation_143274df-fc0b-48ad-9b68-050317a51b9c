const db = require('./database/db');
const { logger } = require('./utils/logger');

async function testConnection() {
  try {
    // 查询现有用户
    const [rows] = await db.query('SELECT * FROM users');
    console.log('数据库连接成功!');
    console.log('当前用户数据:', rows);
    
    // 查询用户表结构
    const [tableInfo] = await db.query('DESCRIBE users');
    console.log('用户表结构:', tableInfo);
    
    // 尝试添加一个测试用户
    const testUser = {
      username: 'testuser' + Date.now(),
      email: 'test' + Date.now() + '@example.com',
      password: 'password123',
      role: 'user',
      is_active: 1  // 使用is_active而不是status
    };
    
    const [result] = await db.query(
      `INSERT INTO users (username, password, email, role, is_active, created_at) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [testUser.username, testUser.password, testUser.email, testUser.role, testUser.is_active, new Date()]
    );
    
    console.log('测试用户添加成功:', result.insertId);
    
    // 验证添加是否成功
    const [newUsers] = await db.query('SELECT * FROM users WHERE id = ?', [result.insertId]);
    console.log('新添加的用户:', newUsers[0]);
    
    // 测试更新现有用户
    await db.query(
      `UPDATE users SET email = ? WHERE username = ?`,
      ['<EMAIL>', testUser.username]
    );
    
    // 验证更新是否成功
    const [updatedUsers] = await db.query('SELECT * FROM users WHERE username = ?', [testUser.username]);
    console.log('更新后的用户:', updatedUsers[0]);
    
  } catch (err) {
    console.error('数据库操作失败:', err);
  }
}

testConnection().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(err => {
  console.error('测试过程出错:', err);
  process.exit(1);
}); 