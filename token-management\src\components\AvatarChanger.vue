<template>
  <div class="avatar-changer">
    <!-- 修改头像按钮 -->
    <el-button
      type="primary"
      :icon="Picture"
      :loading="loading"
      @click="openAvatarDialog"
    >
      修改头像
    </el-button>

    <!-- 修改头像对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="修改头像"
      width="500px"
    >
      <div class="avatar-dialog-content">
        <!-- 当前头像预览，只在选择自定义头像时显示 -->
        <div class="avatar-preview-section" v-if="showPreview">
          <h4>当前头像预览</h4>
          <div class="avatar-preview">
            <el-avatar
              :size="100"
              :src="previewImage || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
            />
          </div>
        </div>

        <!-- 头像选择选项 -->
        <div class="avatar-options">
          <el-button type="primary" @click="randomModifyAvatar" :loading="randomLoading">
            <el-icon><el-icon-refresh /></el-icon>随机修改
          </el-button>
          <el-button type="success" @click="openFileSelector">
            <el-icon><el-icon-upload /></el-icon>自定义头像
          </el-button>
        </div>

        <!-- 隐藏的文件输入 -->
        <input
          type="file"
          ref="fileInputRef"
          style="display: none;"
          @change="handleFileSelected"
          accept="image/*"
        />

        <!-- 提示信息 -->
        <div class="info-section" v-if="selectedTokens.length > 0">
          <el-alert
            title="操作提示"
            type="info"
            :closable="false"
          >
            <p>已选择 {{ selectedTokens.length }} 个Token，点击“随机修改”将为每个Token分配随机头像，点击“自定义头像”可上传自定义头像。</p>
          </el-alert>
        </div>
        <div class="no-tokens-selected" v-else>
          <el-alert
            title="未选择Token"
            type="warning"
            :closable="false"
          >
            请先在Token列表中选择要修改头像的Token
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="changeAvatar"
            :disabled="!canChangeAvatar || (showPreview && !previewImage)"
            :loading="changing"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import axios from 'axios'

// 定义props
const props = defineProps({
  selectedTokens: {
    type: Array,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['update:tokens', 'success', 'error'])

// 状态变量
const dialogVisible = ref(false)
const loading = ref(false)
const randomLoading = ref(false)
const changing = ref(false)
const previewImage = ref('')
const fileInputRef = ref(null)
const selectedFile = ref(null)
const imageBase64 = ref('')
const showPreview = ref(false) // 是否显示头像预览

// 计算属性
const canChangeAvatar = computed(() => {
  return props.selectedTokens.length > 0
})

// 打开头像对话框
const openAvatarDialog = () => {
  dialogVisible.value = true
  // 重置状态
  previewImage.value = ''
  selectedFile.value = null
  imageBase64.value = ''
  showPreview.value = false // 初始不显示头像预览
}

// 打开文件选择器
const openFileSelector = () => {
  showPreview.value = true // 显示头像预览
  fileInputRef.value.click()
}

// 处理文件选择
const handleFileSelected = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  selectedFile.value = file

  // 读取文件并预览
  const reader = new FileReader()
  reader.onload = (e) => {
    previewImage.value = e.target.result
    // 提取base64数据
    imageBase64.value = e.target.result.split(',')[1]
  }
  reader.readAsDataURL(file)

  // 重置文件输入，以便下次选择同一文件时也能触发事件
  event.target.value = ''
}

// 随机修改头像
const randomModifyAvatar = async () => {
  if (!canChangeAvatar.value) {
    ElMessage.warning('请先选择Token')
    return
  }

  try {
    randomLoading.value = true
    // 直接调用修改头像函数，不显示预览
    showPreview.value = false
    await changeAvatar(true) // 传入true表示随机修改
  } catch (error) {
    console.error('随机修改头像失败:', error)
    ElMessage.error(`随机修改头像失败: ${error.message}`)
  } finally {
    randomLoading.value = false
  }
}

// 获取随机头像
const getRandomAvatar = async () => {
  try {
    // 调用后端API获取随机头像
    const response = await axios.get('/api/random-avatar')

    if (response.data && response.data.success && response.data.imageBase64) {
      return {
        success: true,
        imageBase64: response.data.imageBase64,
        fileName: response.data.fileName || ''
      }
    } else {
      throw new Error('获取随机头像失败: 响应数据不完整')
    }
  } catch (error) {
    console.error('获取随机头像失败:', error)
    // 如果后端服务不可用，使用模拟数据
    return simulateRandomAvatar()
  }
}

// 模拟随机头像（仅用于演示）
const simulateRandomAvatar = () => {
  // 这里应该是从服务器获取随机头像
  // 由于无法直接访问本地文件系统，我们使用一个模拟的随机头像
  const randomAvatars = [
    'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    'https://pic1.zhimg.com/v2-3b4fc7e3a1195a081d0259246c38debc_720w.jpg',
    'https://pic1.zhimg.com/v2-5a3c9cb0b2a7c8f7b1e2bc14b8c1ddc9_720w.jpg',
    'https://pic1.zhimg.com/v2-6f35d4c696c839a7be3c2e2992b4c2a7_720w.jpg'
  ]

  const randomIndex = Math.floor(Math.random() * randomAvatars.length)
  const randomUrl = randomAvatars[randomIndex]

  // 在实际环境中，我们需要将图片转换为base64
  // 这里我们模拟一个base64字符串
  const dummyBase64 = 'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'

  console.log('使用模拟随机头像:', randomUrl)

  // 返回模拟的成功结果
  return {
    success: true,
    imageBase64: dummyBase64,
    fileName: '模拟头像'
  }
}

// 修改头像
const changeAvatar = async (isRandom = false) => {
  // 如果是自定义头像模式，需要检查是否选择了头像
  if (!isRandom && showPreview.value && !previewImage.value) {
    ElMessage.warning('请先选择头像')
    return
  }

  // 检查是否选择了Token
  if (!canChangeAvatar.value) {
    ElMessage.warning('请先选择Token')
    return
  }

  try {
    changing.value = true

    // 显示加载提示
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在修改头像...'
    })

    // 获取选中的Token
    const tokens = props.selectedTokens
    if (tokens.length === 0) {
      throw new Error('未选择Token')
    }

    // 初始化结果对象
    const results = {
      success: 0,
      failed: 0,
      tokens: [],
      total: tokens.length,
      processed: 0
    }

    // 并发控制参数
    const BATCH_SIZE = 10 // 每批处理的Token数量
    const MAX_CONCURRENT = 5 // 最大并发数
    const MAX_RETRIES = 3 // 最大重试次数

    // 将Token分批
    const batches = []
    for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
      batches.push(tokens.slice(i, i + BATCH_SIZE))
    }

    // 并发控制函数
    const concurrentControl = async (tasks, maxConcurrent) => {
      const results = []
      const running = new Set()

      for (const task of tasks) {
        if (running.size >= maxConcurrent) {
          // 等待任意一个任务完成
          await Promise.race([...running])
        }

        // 创建新任务
        const promise = (async () => {
          try {
            const result = await task()
            results.push(result)
          } catch (error) {
            results.push({ error })
          } finally {
            running.delete(promise)
          }
        })()

        running.add(promise)
      }

      // 等待所有任务完成
      await Promise.all([...running])

      return results
    }

    // 处理单个Token的函数（带重试）
    const processToken = async (token, retries = 0) => {
      try {
        // 检查Token是否有效
        if (!token.token) {
          throw new Error('Token无效')
        }

        let currentImageBase64 = imageBase64.value
        let currentPreviewImage = previewImage.value

        // 如果是随机修改模式，为每个Token生成不同的随机头像
        if (isRandom) {
          // 获取随机头像
          const randomResult = await getRandomAvatar()
          currentImageBase64 = randomResult.imageBase64
          currentPreviewImage = `data:image/jpeg;base64,${randomResult.imageBase64}`
        }

        // 修改头像
        await changeAvatarForToken(token.token, currentImageBase64)

        // 更新Token信息
        const updatedToken = { ...token, avatar: currentPreviewImage }

        // 更新进度
        results.processed++
        loadingInstance.setText(`正在修改头像... (${results.processed}/${results.total})`)

        return { success: true, token: updatedToken }
      } catch (error) {
        console.error(`修改Token ${token.uid} 的头像失败:`, error)

        // 重试机制
        if (retries < MAX_RETRIES) {
          console.log(`重试修改Token ${token.uid} 的头像，第 ${retries + 1} 次重试`)
          return processToken(token, retries + 1)
        }

        // 更新进度
        results.processed++
        loadingInstance.setText(`正在修改头像... (${results.processed}/${results.total})`)

        return { success: false, error, token }
      }
    }

    // 处理所有批次
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      console.log(`开始处理第 ${i + 1}/${batches.length} 批，共 ${batch.length} 个Token`)

      // 创建任务列表
      const tasks = batch.map(token => () => processToken(token))

      // 并发执行任务
      const batchResults = await concurrentControl(tasks, MAX_CONCURRENT)

      // 处理结果
      for (const result of batchResults) {
        if (result.success) {
          results.tokens.push(result.token)
          results.success++
        } else {
          results.failed++
        }
      }

      console.log(`完成第 ${i + 1}/${batches.length} 批处理，成功: ${results.success}, 失败: ${results.failed}`)
    }

    // 更新Token列表
    if (results.tokens.length > 0) {
      emit('update:tokens', results.tokens)
    }

    // 显示结果
    if (results.success > 0) {
      ElMessage.success(`成功修改 ${results.success} 个Token的头像`)
      emit('success', results)
    }

    if (results.failed > 0) {
      ElMessage.warning(`${results.failed} 个Token的头像修改失败`)
    }

    // 关闭对话框
    dialogVisible.value = false
  } catch (error) {
    console.error('修改头像失败:', error)
    ElMessage.error(`修改头像失败: ${error.message}`)
    emit('error', error)
  } finally {
    changing.value = false
    ElLoading.service().close()
  }
}

// 为单个Token修改头像
const changeAvatarForToken = async (token, imageBase64) => {
  try {
    console.log('开始修改头像，通过后端API请求');

    // 使用后端API一次完成头像修改
    const response = await axios.post('/api/avatar/change', {
      token,
      imageBase64
    });

    console.log('修改头像响应:', response.data);

    if (response.data && response.data.success) {
      return true;
    } else {
      throw new Error(response.data?.message || '修改头像失败');
    }
  } catch (error) {
    console.error('修改头像API请求失败:', error);
    throw error;
  }
}

// 在组件挂载时初始化
onMounted(() => {
  // 可以在这里进行初始化操作
})
</script>

<style scoped>
.avatar-changer {
  display: inline-block;
}

.avatar-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.avatar-preview-section {
  text-align: center;
  margin-bottom: 10px;
}

.avatar-preview {
  margin: 10px 0;
  display: flex;
  justify-content: center;
}

.avatar-options {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.info-section,
.no-tokens-selected {
  width: 100%;
  margin-top: 20px;
}

.token-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>
