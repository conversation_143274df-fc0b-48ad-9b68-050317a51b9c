﻿<template>
  <div class="token-reset-container">
    <el-card class="token-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>Token列表</span>
          <div class="token-count-indicator" v-if="totalTokenCount > 0">
            <el-tag type="success" effect="plain">
              <el-icon><el-icon-document /></el-icon>
              共 {{ totalTokenCount }} 个Token
            </el-tag>
          </div>
          <div class="header-actions">
            <!-- 筛选功能区域 -->
            <div class="filter-container">
              <el-select
                v-model="statusFilter"
                placeholder="状态筛选"
                clearable
                class="filter-select"
                @change="handleFilterChange"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-tag :type="getStatusType(item.value)" effect="light" size="small">
                    {{ item.label }}
                  </el-tag>
                </el-option>
              </el-select>

              <el-select
                v-model="userFilter"
                placeholder="用户筛选"
                clearable
                class="filter-select"
                filterable
                @change="handleFilterChange"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user"
                  :label="user"
                  :value="user"
                />
              </el-select>
            </div>

            <!-- 搜索区域 -->
            <div class="search-container">
            <el-input
              v-model="searchKeyword"
                placeholder="搜索UID、TOKEN/用户"
              class="search-input"
              clearable
              @clear="handleSearch"
              @input="handleSearch"
                @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><el-icon-search /></el-icon>
              </template>
            </el-input>

              <!-- 增加搜索和重置按钮 -->
              <div class="search-actions">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><el-icon-search /></el-icon>搜索
                </el-button>
                <el-button @click="resetFilters" class="reset-button">
                  <el-icon><el-icon-refresh /></el-icon>重置筛选
                </el-button>
              </div>
            </div>

            <!-- 数据统计信息 -->
            <div class="data-stats">
              <div class="stats-card info-card">
                <span class="stats-value">{{ totalTokenCount }}</span>
                <span class="stats-label">总数据</span>
              </div>
              <div class="stats-card primary-card">
                <span class="stats-value">{{ filteredCount }}</span>
                <span class="stats-label">筛选结果</span>
              </div>
              <div class="stats-card warning-card" v-if="selectedTokens.length > 0">
                <span class="stats-value">{{ selectedTokens.length }}</span>
                <span class="stats-label">已选择</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 功能按钮区域 -->
      <div class="function-buttons">
        <el-button-group>
          <el-button type="primary" @click="handleImportToken">
            <el-icon><el-icon-upload /></el-icon>导入Token
          </el-button>
          <el-button type="danger" @click="handleClearTable">
            <el-icon><el-icon-delete /></el-icon>清空表格
          </el-button>
          <UnsoldTokenLoader :current-tokens="tokens" @update:tokens="handleUnsoldTokensUpdate" />
          <SoldTokenLoader :current-tokens="tokens" @update:tokens="handleSoldTokensUpdate" />
          <OfflineTokenCleaner :tokens="tokens" @update:tokens="handleOfflineTokensUpdate" />
        </el-button-group>

        <el-button-group>
          <el-button type="info" @click="handleQueryAvatarNickname">
            <el-icon><el-icon-user /></el-icon>查询头像昵称
          </el-button>
          <el-button type="success" @click="handleQueryOnline">
            <el-icon><el-icon-connection /></el-icon>查询在线
          </el-button>
          <el-button type="warning" @click="handleQueryOrder">
            <el-icon><el-icon-goods /></el-icon>查询订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <AvatarChanger :selected-tokens="selectedTokens" @update:tokens="handleAvatarUpdate" />
          <NicknameChanger :selected-tokens="selectedTokens" @update:tokens="handleNicknameUpdate" />
        </el-button-group>

        <el-button-group>
          <el-button type="success" @click="handleCreateBackup">
            <el-icon><el-icon-document-copy /></el-icon>创建备份
          </el-button>
          <el-button type="warning" @click="handleResetSelected">
            <el-icon><el-icon-refresh-right /></el-icon>重置选中
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="primary" @click="handleUploadSelected">
            <el-icon><el-icon-upload /></el-icon>选中上传后台
          </el-button>
          <el-button type="warning" @click="handleAddToOrder">
            <el-icon><el-icon-shopping-cart /></el-icon>新Token置入账号内订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="danger" @click="handleDeleteOrder">
            <el-icon><el-icon-delete /></el-icon>删除订单
          </el-button>
          <el-button type="success" @click="handleAssignToAccount">
            <el-icon><el-icon-user-filled /></el-icon>选中给指定账号
          </el-button>
          <el-button type="danger" @click="handleDeleteUnsold">
            <el-icon><el-icon-delete /></el-icon>删除未卖
          </el-button>
        </el-button-group>

        <!-- 将TokenExporter放在最右侧 -->
        <TokenExporter :tokens="tokens" :selected-tokens="selectedTokens" />
      </div>

      <!-- Token表格 -->
      <div class="table-actions">
        <el-button
          type="primary"
          size="small"
          @click="selectAllTokens"
          :disabled="tokens.length === 0"
        >
          <el-icon><el-icon-select /></el-icon>全选所有数据
        </el-button>
        <el-button
          type="info"
          size="small"
          @click="clearAllSelection"
          :disabled="selectedTokens.length === 0"
        >
          <el-icon><el-icon-close /></el-icon>清除选择
        </el-button>
        <div v-if="hasSelectionAcrossPages" class="selection-info">
          <el-tag type="warning" effect="plain">
            已选择 <strong>{{ selectedTokens.length }}</strong> 条数据（包含跨页选择）
          </el-tag>
        </div>
      </div>
      <el-table
        ref="tokenTableRef"
        :data="tableData"
        style="width: 100%"
        border
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        v-loading="loading"
        row-key="uid"
        stripe
        highlight-current-row
        class="token-table"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        <el-table-column prop="uid" label="UID" width="150" sortable />
        <el-table-column prop="token" label="Token" min-width="230" show-overflow-tooltip />
        <el-table-column prop="user" label="用户" width="120" sortable>
          <template #default="scope">
            <el-button
              type="text"
              @click="filterByUser(scope.row.user)"
              :style="{ color: userFilter === scope.row.user ? '#409EFF' : '' }"
              class="user-filter-btn"
            >
              {{ scope.row.user }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" class="token-avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        <el-table-column prop="purchaseTime" label="购买时间" width="180" sortable />
        <el-table-column prop="expiryDate" label="到期时间" width="180" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              effect="light"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderInfo" label="订单" width="280">
          <template #default="scope">
            <div v-if="scope.row.orderInfo">
              <!-- 掉线状态 -->
              <div v-if="scope.row.orderInfo.status === '掉线'" class="order-info">
                <div class="status-badge status-offline">
                  <i class="el-icon-warning-outline"></i>
                  <span>掉线</span>
                </div>
              </div>
              <!-- 在线但无订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount === 0" class="order-info">
                <div class="status-badge status-no-order">
                  <i class="el-icon-shopping-bag-1"></i>
                  <span>无订单</span>
                </div>
              </div>
              <!-- 在线且有订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount > 0" class="order-info">
                <!-- 显示删除状态和进度 -->
                <div v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.totalOrders > 0" class="order-status-container">
                  <!-- 删除状态信息 -->
                  <div class="order-deletion-status">
                    <template v-if="scope.row.orderDeletionStatus.inProgress">
                      <!-- 正在删除订单时显示加载状态 -->
                      <div class="order-count-badge" @click="handleViewOrders(scope.row)">
                        <i class="el-icon-loading"></i>
                        <span class="order-text">正在删除订单...</span>
                      </div>
                    </template>
                    <template v-else>
                      <!-- 删除完成后显示结果 -->
                      <div class="order-count-badge" @click="handleViewOrders(scope.row)">
                        <i class="el-icon-goods"></i>
                        <span class="order-number" v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.successCount > 0">
                          剩余 {{ scope.row.orderInfo.orderCount }} 单, 已删 {{ scope.row.orderDeletionStatus.successCount }} 单
                        </span>
                        <span class="order-number" v-else>
                          {{ scope.row.orderInfo.orderCount }} 单
                        </span>
                      </div>

                      <!-- 只在需要时显示额外的删除结果标签 -->
                      <div v-if="scope.row.orderDeletionStatus.failCount > 0" class="order-status-text">
                        <el-tag type="danger" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-close"></i>
                          <span>失败 {{ scope.row.orderDeletionStatus.failCount }} 个</span>
                        </el-tag>
                      </div>
                    </template>
                  </div>
                </div>
                <!-- 默认订单显示 -->
                <div
                  v-else
                  class="order-count-badge"
                  @click="handleViewOrders(scope.row)"
                >
                  <i class="el-icon-goods"></i>
                  <span class="order-number" v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.successCount > 0">
                    剩余 {{ scope.row.orderInfo.orderCount }} 单, 已删 {{ scope.row.orderDeletionStatus.successCount }} 单
                  </span>
                  <span class="order-number" v-else>
                    {{ scope.row.orderInfo.orderCount }} 单
                  </span>
                </div>
              </div>
              <!-- 查询失败或未知状态 -->
              <div v-else class="order-info">
                <div class="status-badge status-error">
                  <i class="el-icon-circle-close"></i>
                  <span>{{ scope.row.orderInfo.status || '查询失败' }}</span>
                </div>
              </div>
            </div>
            <div v-else class="order-info">
              <div class="status-badge status-not-queried">
                <i class="el-icon-question"></i>
                <span>未查询</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <div class="table-action-buttons">
              <el-button type="primary" size="small" text @click="handleReset(scope.row)" class="action-btn">重置</el-button>
              <el-button type="success" size="small" text @click="handleBackup(scope.row)" class="action-btn">备份</el-button>
              <el-button type="info" size="small" text @click="handleEdit(scope.row)" class="action-btn">编辑</el-button>
              <el-button type="danger" size="small" text @click="handleDelete(scope.row)" class="action-btn">删除</el-button>
              <!-- 替换原来的QuickOrderDelete组件为直接删除订单按钮 -->
              <el-button
                type="warning"
                size="small"
                text
                @click="handleQuickDeleteOrder(scope.row)"
                class="action-btn"
                :loading="scope.row.orderDeleteLoading"
              >
                删除订单
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="filteredCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 隐藏的文件输入框 -->
    <input
      type="file"
      ref="fileInputRef"
      style="display: none;"
      @change="handleFileInputChange"
      accept=".xlsx,.xls,.txt"
    />

    <!-- 其他功能对话框 -->
    <el-dialog
      v-model="operationDialogVisible"
      :title="operationTitle"
      width="40%"
    >
      <div class="operation-dialog-content">
        {{ operationMessage }}
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="operationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmOperation">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看订单详情对话框 -->
    <el-dialog
      v-model="ordersDialogVisible"
      title="订单详情"
      width="70%"
    >
      <div class="order-details">
        <!-- 添加订单统计信息和批量操作按钮 -->
        <div class="orders-summary-bar">
          <div class="orders-stats">
            <el-tag type="info" effect="plain">
              总计 <strong>{{ currentOrders.length }}</strong> 个订单
            </el-tag>
            <el-tag type="success" effect="plain" class="ml-10">
              可删除 <strong>{{ deletableOrdersCount }}</strong> 个订单
            </el-tag>
          </div>
          <div class="batch-actions">
            <el-button
              type="primary"
              size="small"
              @click="selectAllDeletableOrders"
              :disabled="deletableOrdersCount === 0"
            >
              <el-icon><el-icon-select /></el-icon>全选可删除订单
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="batchDeleteSelectedOrders"
              :disabled="selectedOrdersInList.length === 0"
            >
              <el-icon><el-icon-delete /></el-icon>批量删除 ({{ selectedOrdersInList.length }})
            </el-button>
          </div>
        </div>

        <el-table
          :data="currentOrders"
          style="width: 100%"
          border
          v-loading="loading"
          row-key="orderId"
          stripe
          highlight-current-row
          class="order-table"
          @selection-change="handleOrderSelectionChange"
          ref="orderTableRef"
        >
          <el-table-column type="selection" width="55" :selectable="row => isDeletableOrder(row.status)" />
          <el-table-column prop="orderId" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getOrderStatusTag(scope.row.status)" effect="light">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="80">
            <template #default="scope">
              ¥{{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="goodsInfo" label="商品信息" min-width="200" show-overflow-tooltip />
          <el-table-column prop="orderTime" label="订单时间" width="180" sortable />
          <el-table-column prop="mallName" label="商家名称" width="120" show-overflow-tooltip />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <div class="order-action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewOrderDetail(scope.row)"
                text
              >
                查看详情
              </el-button>
                <el-button
                  v-if="isDeletableOrder(scope.row.status)"
                  type="danger"
                  size="small"
                  @click="handleDeleteSingleOrder(scope.row)"
                  text
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ordersDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量处理进度对话框 -->
    <el-dialog
      v-model="showBatchProgress"
      title="批量处理进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false">
      <div class="progress-container">
        <el-progress
          :percentage="batchProgress.progress"
          :format="() => `${batchProgress.current}/${batchProgress.total}`"
          status="success">
        </el-progress>
        <div class="progress-message">{{ batchProgress.message }}</div>
      </div>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="orderDetailDialogVisible"
      title="订单详细信息"
      width="50%"
    >
      <div v-if="currentOrderDetail" class="order-detail-content">
        <!-- 添加显示删除按钮的条件判断 -->
        <div class="order-detail-actions" v-if="isDeletableOrder(currentOrderDetail.status)">
          <el-button
            type="danger"
            size="small"
            @click="handleDeleteSingleOrder(currentOrderDetail)"
          >
            删除订单
          </el-button>
          <el-tooltip content="已评价、交易成功、交易已取消、未发货或退款成功的订单可删除" placement="top">
            <el-tag type="success" size="small" style="margin-left: 10px;">可删除</el-tag>
          </el-tooltip>
        </div>
        <div class="order-detail-actions" v-else>
          <el-tooltip content="该订单状态不允许删除" placement="top">
            <el-tag type="danger" size="small">不可删除</el-tag>
          </el-tooltip>
          <span class="deletion-notice">（只有已评价、交易成功、交易已取消、未发货或退款成功的订单可删除）</span>
        </div>

        <div class="order-detail-item">
          <div class="order-detail-label">订单号:</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderId }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">状态:</div>
          <div class="order-detail-value">
            <el-tag :type="getOrderStatusTag(currentOrderDetail.status)">
              {{ currentOrderDetail.status }}
            </el-tag>
          </div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">金额:</div>
          <div class="order-detail-value">¥{{ currentOrderDetail.amount }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">下单时间:</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderTime }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商品:</div>
          <div class="order-detail-value">{{ currentOrderDetail.goodsInfo }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">物流单号:</div>
          <div class="order-detail-value">{{ currentOrderDetail.trackingNumber || '暂无' }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商家:</div>
          <div class="order-detail-value">{{ currentOrderDetail.mallName }}</div>
        </div>
        <el-divider />
        <div class="order-extra-info">
          <el-collapse>
            <el-collapse-item title="更多订单信息" name="1">
              <pre>{{ JSON.stringify(currentOrderDetail.extraInfo, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加TokenOrderDialog组件 -->
    <token-order-dialog
      v-model:visible="orderDialogVisible"
      :selected-tokens="selectedTokens"
      @success="handleOrderSuccess"
      @error="handleOrderError"
    />

    <!-- 增加备份对话框 -->
    <el-dialog
      v-model="backupDialogVisible"
      title="创建备份"
      width="40%"
    >
      <div class="operation-dialog-content">
        <p>请选择备份类型:</p>
        <el-select v-model="backupSelectedType" style="width: 100%; margin-bottom: 15px;">
          <el-option label="重置前" value="重置前" />
          <el-option label="重置后" value="重置后" />
          <el-option label="自定义类型" value="自定义" />
        </el-select>
        <div v-if="backupSelectedType === '自定义'" style="margin-top: 15px;">
          <p>请输入自定义备份类型名称:</p>
          <el-input v-model="backupCustomType" placeholder="请输入类型名称，如：测试前、紧急备份等" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="backupDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="backupLoading" @click="confirmBackupCreation">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick, defineComponent, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import UnsoldTokenLoader from '../components/UnsoldTokenLoader.vue'
import SoldTokenLoader from '../components/SoldTokenLoader.vue'
import OfflineTokenCleaner from '../components/OfflineTokenCleaner.vue'
import AvatarChanger from '../components/AvatarChanger.vue'
import NicknameChanger from '../components/NicknameChanger.vue'
import TokenExporter from '../components/TokenExporter.vue'
import { ElMessage, ElLoading, ElMessageBox, ElNotification, ElSelect, ElOption, ElInput } from 'element-plus'
import * as XLSX from 'xlsx'
import * as tokenService from '@/services/tokenService'
import { batchCheckOnlineStatus, applyOnlineStatusToTokens } from '@/services/onlineCheckService'
import { parseImportedFile, processTxtData } from '@/utils/tokenParser'
import { formatDateTime, validateTokenData } from '@/utils/helpers'
// 导入组件
import * as orderService from '../services/orderService'
import * as avatarService from '@/services/avatarService'
import TokenOrderDialog from '../components/TokenOrderDialog.vue';
import axios from 'axios'
// 导入新的云端 Token 服务
import { truncateCloudUnsoldTokens } from '@/services/cloudTokenService';
// 导入新的上传服务
import { uploadSelectedTokens } from '@/services/uploadService';

// 初始化路由器
const router = useRouter()
const route = useRoute()

// 对话框控制
const operationDialogVisible = ref(false)
const operationTitle = ref('')
const operationMessage = ref('')
const currentOperation = ref('')

// 文件上传相关
const fileInputRef = ref(null)
const selectedFile = ref(null)

// 直接打开文件选择框
const handleImportToken = () => {
  fileInputRef.value.click()
}

// 文件选择变化处理
const handleFileInputChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  selectedFile.value = file
  processSelectedFile(file)

  // 重置文件输入，以便下次选择同一文件时也能触发事件
  event.target.value = ''
}

// 处理选中的文件
const processSelectedFile = async (file) => {
  const fileName = file.name
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.txt')) {
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在导入数据...'
    })

    try {
      const parsedData = await parseImportedFile(file)

      if (parsedData.length === 0) {
        ElMessage.warning('文件中没有有效数据')
        loadingInstance.close()
        return
      }

      const { newTokens, duplicateCount } = processAndDeduplicateTokens(parsedData)

      // 修改：保存当前页码
      const currentPageBeforeUpdate = currentPage.value

      // 设置不重置页码的标志
      shouldResetPage.value = false

      tokens.value = newTokens
      totalTokenCount.value = newTokens.length

      let successMsg = `成功导入 ${newTokens.length} 个Token`
      if (duplicateCount > 0) {
        successMsg += `，已自动去除 ${duplicateCount} 个重复Token`
      }

      ElMessage.success(successMsg)
      loadingInstance.close()

      // 恢复之前的页码，如果页码超出范围，filterTokens会自动调整
      nextTick(() => {
        currentPage.value = currentPageBeforeUpdate
      })
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败: ' + (error.message || '无效的数据格式'))
      loadingInstance.close()
    }
  } else {
    ElMessage.error('不支持的文件格式，仅支持Excel(.xlsx, .xls)和文本文件(.txt)')
  }
}

// 公共操作对话框
const showOperationDialog = (title, message, operation) => {
  operationTitle.value = title
  operationMessage.value = message
  currentOperation.value = operation
  operationDialogVisible.value = true
}

// Token列表数据
const tokens = ref([]);
const selectedTokens = ref([]);
const loading = ref(false);
const isLocalData = ref(true); // 始终标记为本地数据
const tableHeight = ref('500px'); // 表格高度，可根据窗口大小调整

// 批量处理进度显示相关
const showBatchProgress = ref(false);
const batchProgress = ref({
  current: 0,
  total: 0,
  progress: 0,
  message: ''
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(100); // 默认每页显示20条，减轻渲染压力
const totalTokenCount = ref(0);

// 优化表格渲染和筛选性能
// 缓存已筛选的数据，避免重复计算
const filteredTokensCache = ref([]);
const isFiltering = ref(false);

// 更改为方法而不是计算属性，实现延迟计算
const filterTokens = () => {
  // 如果正在筛选中，不重复执行
  if (isFiltering.value) return;

  isFiltering.value = true;
  loading.value = true;

  // 使用setTimeout将筛选操作移到下一个事件循环，避免阻塞UI
  setTimeout(() => {
    try {
      // 保存原始token的deletableOrderCount属性
      const deletableOrderCountMap = {};
      tokens.value.forEach(token => {
        if (token.uid && token.deletableOrderCount !== undefined) {
          deletableOrderCountMap[token.uid] = token.deletableOrderCount;
        }
      });

      console.log('保存的deletableOrderCount映射:', deletableOrderCountMap);

      // 先筛选数据
      let result = [...tokens.value]; // 创建拷贝，避免直接修改原数组

      // 按状态筛选
      if (statusFilter.value) {
        result = result.filter(token => token.status === statusFilter.value);
      }

      // 按用户筛选
      if (userFilter.value) {
        result = result.filter(token => token.user === userFilter.value);
      }

      // 按关键字搜索
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(token =>
          (token.uid && token.uid.toLowerCase().includes(keyword)) ||
          (token.user && token.user.toLowerCase().includes(keyword)) ||
          (token.nickname && token.nickname && token.nickname.toLowerCase().includes(keyword)) ||
          (token.token && token.token.toLowerCase().includes(keyword)) ||
          (token.status && token.status.toLowerCase().includes(keyword))
        );
      }

      // 恢复deletableOrderCount属性
      result = result.map(token => {
        if (token.uid && deletableOrderCountMap[token.uid] !== undefined) {
          return {
            ...token,
            deletableOrderCount: deletableOrderCountMap[token.uid]
          };
        }
        return token;
      });

      console.log('恢复后的result示例:', result.slice(0, 2));

      // 更新缓存的筛选结果
      filteredTokensCache.value = result;

      // 更新总计数
      totalTokenCount.value = tokens.value.length;

      // 处理筛选完成后的操作
      nextTick(() => {
        // 重置到第一页如果当前页超出范围
        const totalPages = Math.ceil(filteredTokensCache.value.length / pageSize.value) || 1;
        if (currentPage.value > totalPages) {
          currentPage.value = 1;
        }
        // 移除以下代码块，不再基于shouldResetPage自动重置页码
        // else if (shouldResetPage.value) {
        //   // 仅当需要重置页码时才重置
        //   currentPage.value = 1;
        // }

        // 重新应用选择状态，确保选中状态与数据一致
        applyTableSelectionStatus();

        // 不再在这里重置标志，以保持当前设置
        // shouldResetPage.value = true;
      });
  } catch (error) {
      console.error('数据筛选错误:', error);
  } finally {
      isFiltering.value = false;
      loading.value = false;
    }
  }, 0);
};

// 优化表格数据计算属性
const tableData = computed(() => {
  // 计算当前页数据
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = Math.min(startIndex + pageSize.value, filteredTokensCache.value.length);

  // 返回当前页的数据
  return filteredTokensCache.value.slice(startIndex, endIndex);
});

// 筛选后的数据总量计算属性
const filteredCount = computed(() => filteredTokensCache.value.length);

// 搜索
const searchKeyword = ref('');

// 筛选功能相关
const statusFilter = ref('');
const userFilter = ref('');

// 状态选项
const statusOptions = [
  { value: '未知', label: '未知' },
  { value: '在线', label: '在线' },
  { value: '掉线', label: '掉线' },

//  { value: '正常', label: '正常' },
//  { value: '过期', label: '过期' },
//  { value: '待激活', label: '待激活' },
//  { value: '已重置', label: '已重置' }
];

// 用户选项（从Token数据中提取）
const userOptions = computed(() => {
  // 提取所有不同的用户名
  const userSet = new Set();
  tokens.value.forEach(token => {
    if (token.user) {
      userSet.add(token.user);
    }
  });
  return Array.from(userSet).sort();
});

// 表格引用
const tokenTableRef = ref(null);

// 应用表格选择状态
const applyTableSelection = () => {
  try {
    // 清除所有选择
    tokenTableRef.value?.clearSelection();

    // 获取当前页面上应该被选中的行
    nextTick(() => {
      // 对于当前页的每个选中项，手动设置选中
      tableData.value.forEach(token => {
        if (selectedTokens.value.some(selected => selected.uid === token.uid)) {
          tokenTableRef.value?.toggleRowSelection(token, true);
        }
      });
    });
  } catch (error) {
    console.error('应用选择状态错误:', error);
  }
};

// 处理筛选变化 - 优化版
const handleFilterChange = () => {
  // 筛选变化时重置到第一页
  // 只有用户明确进行筛选操作时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 搜索处理 - 优化版
const handleSearch = () => {
  // 搜索时重置到第一页
  // 只有用户明确进行搜索操作时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 通过用户名筛选 - 优化版
const filterByUser = (username) => {
  if (userFilter.value === username) {
    // 如果已经是筛选这个用户，点击则取消筛选
    userFilter.value = '';
  } else {
    // 否则设置为筛选这个用户
    userFilter.value = username;
  }

  // 用户明确筛选时才重置到第一页
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 增加搜索和重置按钮
const resetFilters = () => {
  statusFilter.value = '';
  userFilter.value = '';
  searchKeyword.value = '';
  // 用户明确重置筛选时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 刷新Token列表 - 优化版
const refreshTokens = () => {
  // 如果正在刷新中，避免重复执行
  if (loading.value) return;

  // 显示加载状态
  loading.value = true;

  // 刷新时不重置页码，保持当前页
  // shouldResetPage.value = true;

  // 重新筛选数据
  filterTokens();

  // 确保在刷新完成后重新应用选择状态
  setTimeout(() => {
    applyTableSelectionStatus();
    loading.value = false;
  }, 50);
};

// 监听导入数据状态，优化大数据集处理
watch(tokens, (newTokens) => {
  if (newTokens.length > 1000) {
    // 数据量大时自动调整页大小
    if (pageSize.value < 50) {
      pageSize.value = 50;
      ElMessage.info('数据量较大，已自动调整为每页显示50条');
    }
  }

  // 不再强制重置到第一页
  // shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
}, { deep: false });

// 更新选中的Token信息
const updateTokensInfo = (updatedTokens) => {
  if (!updatedTokens || updatedTokens.length === 0) return

  // 更新本地数据
  tokens.value = tokens.value.map(token => {
    const updated = updatedTokens.find(t => t.uid === token.uid)
    return updated ? { ...token, ...updated } : token
  })

  // 设置不重置页码标志
  shouldResetPage.value = false;

  // 应用筛选但不改变页码
  filterTokens();
}

// 处理头像更新
const handleAvatarUpdate = (updatedTokens) => {
  if (!updatedTokens || updatedTokens.length === 0) return

  // 更新本地数据
  tokens.value = tokens.value.map(token => {
    const updated = updatedTokens.find(t => t.uid === token.uid)
    return updated ? { ...token, ...updated } : token
  })

  // 设置不重置页码标志
  shouldResetPage.value = false;

  // 应用筛选但不改变页码
  filterTokens();
}

// 处理昵称更新
const handleNicknameUpdate = (updatedTokens) => {
  if (!updatedTokens || updatedTokens.length === 0) return

  // 更新本地数据
  tokens.value = tokens.value.map(token => {
    const updated = updatedTokens.find(t => t.uid === token.uid)
    return updated ? { ...token, ...updated } : token
  })

  // 设置不重置页码标志
  shouldResetPage.value = false;

  // 应用筛选但不改变页码
  filterTokens();

  ElMessage.success(`成功更新 ${updatedTokens.length} 个Token的昵称`);
}

// 处理单个Token操作
const handleReset = async (token) => {
  ElMessageBox.confirm(
    '确认要重置此Token吗？',
    '操作确认',
    {
      confirmButtonText: '重置',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟重置效果
    const index = tokens.value.findIndex(t => t.uid === token.uid);
    if (index !== -1) {
      const newToken = { ...token };
      newToken.token = 'NEW_' + Math.random().toString(36).substring(2, 10);
      newToken.status = '已重置';
      newToken.resetTime = formatDateTime(new Date());
      tokens.value[index] = newToken;
      ElMessage.success('Token已重置');
    }
  }).catch(() => {});
};

const handleBackup = async (token) => {
  ElMessage.info('本地模式：备份功能仅在服务器模式下可用');
};

const handleEdit = (token) => {
  ElMessage.info('编辑功能尚未实现');
};

const handleDelete = async (token) => {
  ElMessageBox.confirm(
    '确认要删除此Token吗？',
    '操作确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 从数组中移除
    tokens.value = tokens.value.filter(t => t.uid !== token.uid);
    totalTokenCount.value = tokens.value.length;
    ElMessage.success('Token已删除');
  }).catch(() => {});
};

// 表格操作
const handleSelectionChange = (selection) => {
  // 直接替换整个已选择列表
  // 注意：这可能会导致跨页选择丢失，因此我们需要特殊处理

  try {
    // 获取当前页面的所有UID
    const currentPageUids = new Set(tableData.value.map(item => item.uid));

    // 构建新的选择列表：
    // 1. 保留不在当前页面的之前选择的项
    // 2. 添加当前页面新选择的项
    const newSelection = [
      // 保留不在当前页面的选择项
      ...selectedTokens.value.filter(item => !currentPageUids.has(item.uid)),
      // 添加当前页面的选择项
      ...selection
    ];

    // 更新选择列表
    selectedTokens.value = newSelection;
  } catch (error) {
    console.error('选择变化处理错误:', error);
  }
};

// 页面切换处理
const handleSizeChange = (size) => {
  // 记住之前的选择
  const previousSelection = [...selectedTokens.value];

  // 更新页大小
  pageSize.value = size;

  // 延时处理，避免DOM更新冲突
  setTimeout(() => {
    try {
      // 清除表格UI上的选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 如果当前页码超出范围则重置
      const totalPages = Math.ceil(filteredTokensCache.value.length / size) || 1;
      if (currentPage.value > totalPages) {
        currentPage.value = 1;
      }

      // 重新设置选中状态
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 为当前页的每行检查是否在选中列表中
          tableData.value.forEach(row => {
            // 查找当前行是否在选中列表中
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 手动设置选中状态
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
    }
  } catch (error) {
      console.error('页面大小变化错误:', error);
    }
  }, 50);
};

const handleCurrentChange = (page) => {
  // 记住之前的选择
  const previousSelection = [...selectedTokens.value];

  // 更新页码
  currentPage.value = page;

  // 延时处理，避免DOM更新冲突
  setTimeout(() => {
    try {
      // 清除表格UI上的选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 重新设置选中状态
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 为当前页的每行检查是否在选中列表中
          tableData.value.forEach(row => {
            // 查找当前行是否在选中列表中
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 手动设置选中状态
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('页面切换错误:', error);
    }
  }, 50);
};

// 专门用于应用表格选择状态的函数
const applyTableSelectionStatus = () => {
  // 防止未初始化时调用
  if (!tokenTableRef.value) return;

  try {
    // 先清除表格的所有选中状态，避免UI状态混乱
    tokenTableRef.value.clearSelection();

    // 延迟到下一个tick确保表格已更新
    nextTick(() => {
      // 重新为当前页的每一行检查是否应该选中
      tableData.value.forEach(row => {
        const shouldBeSelected = selectedTokens.value.some(selected => selected.uid === row.uid);
        if (shouldBeSelected) {
          tokenTableRef.value?.toggleRowSelection(row, true);
        }
      });
    });
  } catch (error) {
    console.error('应用选择状态错误:', error);
  }
};

// 通过搜索关键字过滤的Token列表
const filteredTokens = computed(() => tokens.value);

// 处理去重和设置默认状态的函数
const processAndDeduplicateTokens = (parsedData) => {
  // 确保每个Token都有默认状态为"未知"
  const processedData = parsedData.map(token => ({
    ...token,
    status: token.status || '未知'
  }));

  // 如果现有数据为空，直接返回处理后的数据
  if (tokens.value.length === 0) {
    return {
      newTokens: processedData,
      duplicateCount: 0
    };
  }

  // 创建已存在的UID和Token值的集合，用于去重
  const existingUids = new Set(tokens.value.map(token => token.uid));
  const existingTokenValues = new Set(tokens.value.map(token => token.token));

  // 过滤重复的Token（基于UID或Token值）
  const uniqueTokens = [];
  let duplicateCount = 0;

  for (const token of processedData) {
    if (!existingUids.has(token.uid) && !existingTokenValues.has(token.token)) {
      uniqueTokens.push(token);
      existingUids.add(token.uid);
      existingTokenValues.add(token.token);
    } else {
      duplicateCount++;
    }
  }

  // 合并现有Token和新导入的唯一Token
  return {
    newTokens: [...tokens.value, ...uniqueTokens],
    duplicateCount
  };
};

// 获取状态对应的类型
const getStatusType = (status) => {
  const statusMap = {
    '在线': 'success',
    '掉线': 'danger',
    '未知': 'info',
    '正常': 'success',
    '过期': 'danger',
    '待激活': 'warning',
    '已重置': 'info'
  };
  return statusMap[status] || 'info';
};

// 页面加载时只调整表格高度
onMounted(() => {
  adjustTableHeight();

  // 监听窗口大小变化，调整表格高度
  window.addEventListener('resize', adjustTableHeight);

  // 初始化筛选数据
  filterTokens();
});

// 全选所有数据的功能
const selectAllTokens = () => {
  try {
    // 先显示加载中
    loading.value = true;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 直接将所有筛选结果作为选中项
    selectedTokens.value = JSON.parse(JSON.stringify(filteredTokensCache.value));

    // 设置定时器，确保后续操作在DOM更新后执行
    setTimeout(() => {
      // 强制为当前页的每一行都设置选中状态
      if (tokenTableRef.value) {
        tableData.value.forEach(row => {
          tokenTableRef.value.toggleRowSelection(row, true);
        });
      }

      // 显示成功消息
      ElMessage.success(`已选中全部 ${selectedTokens.value.length} 条数据`);
      loading.value = false;
    }, 100);
  } catch (error) {
    console.error('全选操作错误:', error);
    loading.value = false;
    ElMessage.error('全选操作失败，请重试');
  }
};

// 清除所有选择
const clearAllSelection = () => {
  try {
    // 记录当前页码，确保清除选择后不会改变页码
    const currentPageBeforeClearing = currentPage.value;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 清除选择数据
    selectedTokens.value = [];

    // 不再重置这些值，只清除选择
    // totalTokenCount.value = 0;
    // filteredTokensCache.value = [];

    // 不再重置页码
    // currentPage.value = 1;

    // 确保表格选择状态被清除
    if (tokenTableRef.value) {
      tokenTableRef.value.clearSelection();
    }

    // 设置不重置页码的标志
    shouldResetPage.value = false;

    // 轻量级刷新，只刷新表格选择状态，不重置页码或过滤条件
    nextTick(() => {
      // 恢复原页码
      currentPage.value = currentPageBeforeClearing;
      // 应用表格选择状态
      applyTableSelectionStatus();
    });

    ElMessage.success('已清除所有选择');
  } catch (error) {
    console.error('清除选择错误:', error);
    ElMessage.error('清除选择失败，请重试');
  }
};

// 检查是否有跨页选择
const hasSelectionAcrossPages = computed(() => {
  return selectedTokens.value.length > tableData.value.length;
});

// 调整表格高度
const adjustTableHeight = () => {
  const windowHeight = window.innerHeight;
  tableHeight.value = `${windowHeight - 280}px`; // 减去其他UI元素的高度
};

// 新增的行点击处理函数
const handleRowClick = (row, column) => {
  // 忽略点击选择框和操作列的情况，因为这些列有自己的点击行为
  if (column.type === 'selection' || column.label === '操作') {
    return;
  }

  // 切换行的选择状态
  if (tokenTableRef.value) {
    // 检查当前行是否已被选中
    const isSelected = selectedTokens.value.some(item => item.uid === row.uid);

    // 切换选中状态
    tokenTableRef.value.toggleRowSelection(row, !isSelected);

    // 由于toggleRowSelection会触发selection-change事件，所以不需要在这里手动更新selectedTokens
  }
};

// 查询在线状态
const handleQueryOnline = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确定要查询选中的 ${selectedTokens.value.length} 个Token的在线状态吗？此操作将通过后端并发处理，速度更快。`,
      '操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询在线状态...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存当前页码
    const currentPageBeforeQuery = currentPage.value;
    // 设置不重置页码标志
    shouldResetPage.value = false;

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用后端优化API进行查询
      const result = await tokenService.queryOnlineStatusOptimized(selectedTokenData);

      if (result.success) {
        // 更新token状态
        const updatedTokens = applyOnlineStatusToTokens(tokens.value, result.results);

        // 设置不重置页码
        shouldResetPage.value = false;
        tokens.value = updatedTokens;

        // 统计结果
        const onlineCount = result.results.filter(r => r.status === '在线').length;
        const offlineCount = result.results.filter(r => r.status === '掉线').length;

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span>
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span>
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">状态已更新到表格中</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格时不重置页码
            shouldResetPage.value = false;
            refreshTokens();

            // 确保在刷新后保持在原页码
            nextTick(() => {
              currentPage.value = currentPageBeforeQuery;
            });
          }
        });

        ElMessage.success('查询完成，状态已更新');
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询在线状态异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();

      // 确保保持在原页码
      nextTick(() => {
        currentPage.value = currentPageBeforeQuery;
      });
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 查询头像昵称
const handleQueryAvatarNickname = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确定要查询选中的 ${selectedTokens.value.length} 个Token的头像和昵称吗？此操作将通过后端并发处理，速度更快。`,
      '操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询头像和昵称...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用后端API进行查询
      const result = await tokenService.queryTokenUserInfo(selectedTokenData);

      if (result.success) {
        // 更新token的头像和昵称
        const updatedTokens = tokens.value.map(token => {
          const userInfo = result.results.find(r => r.uid === token.uid);
          if (userInfo && userInfo.success && userInfo.data) {
            return {
              ...token,
              avatar: userInfo.data.avatar || token.avatar,
              nickname: userInfo.data.nickname || token.nickname,
              // 如果token在线，也更新状态
              status: userInfo.data.isOnline ? '在线' : token.status
            };
          }
          return token;
        });

        tokens.value = updatedTokens;

        // 统计结果
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">成功</span>
                <span style="font-size: 18px;">${successCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">失败</span>
                <span style="font-size: 18px;">${failCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">头像和昵称信息已更新到表格中</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格
            refreshTokens();
          }
        });

        ElMessage.success('查询完成，头像和昵称已更新');
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询头像昵称异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 查询订单信息
const handleQueryOrder = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  try {
    // 提示用户确认操作
    await ElMessageBox.confirm(
      `确定要查询 ${selectedTokens.value.length} 个选中Token的订单信息吗？`,
      '查询订单信息',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询订单信息...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用API进行查询
      const result = await tokenService.queryTokenOrderInfo(selectedTokenData);

      if (result.success) {
        // 更新token的订单信息
        const updatedTokens = tokens.value.map(token => {
          const orderResult = result.results.find(r => r.uid === token.uid);
          if (orderResult && orderResult.success) {
            return {
              ...token,
              // 同时更新token的主status属性，使在线状态在主表格中显示
              status: orderResult.status || token.status,
              orderInfo: {
                isOnline: orderResult.isOnline,
                status: orderResult.status || '未知',
                orderCount: orderResult.data?.orderCount || 0,
                orders: orderResult.data?.orders || [],
                message: orderResult.message
              }
            };
          }
          return {
            ...token,
            // 如果在查询结果中但查询失败，则更新状态，否则保留原状态
            status: orderResult ? (orderResult.status || token.status) : token.status,
            // 修复：如果不在查询结果中，保留原有orderInfo，不要设为null
            orderInfo: orderResult ? {
              isOnline: orderResult.isOnline || false,
              status: orderResult.status || '未知',
              orderCount: 0,
              orders: [],
              message: orderResult.message || '查询失败'
            } : token.orderInfo // 保留原有的orderInfo而不是设为null
          };
        });

        // 设置不重置页码的标志
        shouldResetPage.value = false;

        // 更新tokens并保持当前页码
        tokens.value = updatedTokens;

        // 统计结果
        const onlineCount = result.results.filter(r => r.status === '在线').length;
        const offlineCount = result.results.filter(r => r.status === '掉线').length;
        const hasOrderCount = result.results.filter(r => r.success && r.status === '在线' && r.data && r.data.orderCount > 0).length;

        ElNotification({
          title: '查询成功',
          message: `共处理 ${result.stats.total} 个Token，${onlineCount} 个在线，${hasOrderCount} 个有订单`,
          type: 'success',
          duration: 5000
        });

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.stats.total} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span>
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span>
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #e6f2ff; color: #409EFF; font-weight: bold;">
                <span style="margin-right: 5px;">有订单</span>
                <span style="font-size: 18px;">${hasOrderCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">订单信息已更新到表格中，点击"订单数量"可查看详情</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格
            refreshTokens();
          }
        });
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询订单信息异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 获取订单状态标签类型
const getOrderStatusTag = (status) => {
  if (!status) return 'info';
  if (status.includes('待收货') || status.includes('待发货')) return 'warning';
  if (status.includes('已收货') || status.includes('已完成')) return 'success';
  if (status.includes('已取消') || status.includes('已关闭')) return 'danger';
  return 'info';
};

// 格式化订单ID
const formatOrderId = (id) => {
  if (id.length > 10) {
    return id.slice(0, 5) + '...' + id.slice(-5);
  }
  return id;
};

// 获取订单工具提示
const getOrderTooltip = (orderInfo) => {
  if (orderInfo.detail) {
    return orderInfo.detail;
  }
  return orderInfo.id ? orderInfo.id : '无订单信息';
};

// 查看订单详情
const ordersDialogVisible = ref(false);
const currentOrders = ref([]);
const currentTokenUid = ref('');
const orderDetailDialogVisible = ref(false);
const currentOrderDetail = ref(null);

// 处理查看订单
const handleViewOrders = (row) => {
  if (row.orderInfo && row.orderInfo.orders && row.orderInfo.orders.length > 0) {
    // 为每个订单添加所属token的信息，以便支持订单详情页中的删除功能
    currentOrders.value = row.orderInfo.orders.map(order => ({
      ...order,
      uid: row.uid,
      tokenValue: row.token
    }));
    currentTokenUid.value = row.uid;
    ordersDialogVisible.value = true;
  } else {
    ElMessage.warning('没有找到订单信息');
  }
};

// 查看单个订单详情
const viewOrderDetail = (order) => {
  // 传递订单所属token的uid和token值，以便能够删除订单
  if (order) {
    const tokenInfo = order.tokenInfo || {};
    currentOrderDetail.value = {
      ...order,
      uid: order.uid || tokenInfo.uid,
      tokenValue: order.tokenValue || tokenInfo.token
    };
  orderDetailDialogVisible.value = true;
  }
};

// 格式化订单时间
const formatOrderTime = (time) => {
  if (!time) return '未知';
  return time;
};

// 删除订单信息
const handleDeleteOrder = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要删除订单的Token');
    return;
  }

  // 询问用户是否确认删除
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${selectedTokens.value.length}个Token的订单吗？系统将自动分批查询并删除所有可删除的订单。`,
      '批量删除订单确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
  } catch (error) {
    return; // 用户取消操作
  }

  // 显示加载中状态
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '正在删除订单..',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  // 重置并显示进度
  batchProgress.value = {
    current: 0,
    total: 0,
    progress: 0,
    message: '准备处理...'
  };
  showBatchProgress.value = true;

  // 保存当前页码
  const currentPageBeforeDelete = currentPage.value;
  // 设置不重置页码标志
  shouldResetPage.value = false;

  try {
    // 使用并发查询和删除，添加进度回调，使用优化的参数
    const result = await orderService.concurrentQueryAndDeleteOrders(
      selectedTokens.value,
      {
        batchSize: 50, // 从20增加到50
        concurrency: 10, // 从5增加到10
        tokenBatchSize: 90, // 每批90个Token
        parallelBatches: 3, // 并行处理3批token
        onProgress: (progress) => {
          batchProgress.value = progress;
          // 更新loading文本
          if (loadingInstance) {
            loadingInstance.setText(`正在删除订单..${progress.message}`);
          }
        }
      }
    );

    // 处理结果
    if (result.success) {
      // 检查是否有成功删除的订单
      const successCount = result.results.filter(r => r.success).length;
      const failCount = result.results.length - successCount;

      // 更新本地数据，从订单列表中移除已删除的订单
      updateLocalOrdersAfterDeletion(result.results);

      // 处理查询结果，更新UI显示
      if (result.queriedTokens && result.queriedTokens.length > 0) {
        // 更新已查询但没有可删除订单的Token状态
        updateTokensAfterQuery(result.queriedTokens);
      }

      // 使用通知显示详细结果
      ElNotification({
        title: '删除订单完成',
        message: `共处理了${result.processedBatchCount}/${result.batchCount}批Token，成功删除${successCount}个订单${failCount > 0 ? `，${failCount}个删除失败` : ''}`,
        type: 'success',
        duration: 100000000
      });

      // 保证不重置页码的情况下刷新表格数据
      shouldResetPage.value = false;
      refreshTokens();

      // 确保在刷新后保持页码
      nextTick(() => {
        currentPage.value = currentPageBeforeDelete;
      });
    } else {
      // API调用失败
      ElMessage.error(result.message || '查询或删除订单失败');

      // 显示详细错误信息
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <p><strong>错误信息:</strong> ${result.message || '未知错误'}</p>
          <p style="margin-top: 10px; font-size: 12px; color: #909399;">
            请确认：
            <ul>
              <li>Token是否在线</li>
              <li>Token权限是否有效</li>
              <li>订单状态是否为"已评价"</li>
            </ul>
          </p>
        </div>`,
        '删除订单失败',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          type: 'error'
        }
      );
    }
  } catch (error) {
    console.error('删除订单错误:', error);

    // 更详细的错误提示
    ElMessage.error(`操作异常: ${error.message || '未知错误'}`);

    // 显示技术细节
    ElMessageBox.alert(
      `<div style="text-align: left;">
        <p><strong>错误信息:</strong> ${error.message || '未知错误'}</p>
        <p style="margin-top: 10px; font-size: 12px; color: #909399;">
          可能的原因：
          <ul>
            <li>网络连接问题</li>
            <li>服务器响应超时</li>
            <li>API限流或权限问题</li>
          </ul>
        </p>
      </div>`,
      '系统错误',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了',
        type: 'error'
      }
    );

    // 更新token的订单状态为失败
    selectedTokens.value.forEach(token => {
      if (token.orderDeletionStatus) {
        token.orderDeletionStatus.inProgress = false;
      }
    });
  } finally {
    // 关闭加载提示
    loadingInstance.close();

    // 隐藏进度显示
    setTimeout(() => {
      showBatchProgress.value = false;
    }, 3000);

    // 确保保持在原页码
    nextTick(() => {
      currentPage.value = currentPageBeforeDelete;
    });
  }
};

// 新增：更新已查询但没有可删除订单的Token状态
const updateTokensAfterQuery = (queriedTokens) => {
  if (!queriedTokens || queriedTokens.length === 0) return;

  console.log('更新已查询Token状态:', queriedTokens);

  // 设置不重置页码的标志
  shouldResetPage.value = false;

  // 更新查询到的token信息
  tokens.value = tokens.value.map(token => {
    // 查找当前token是否在查询结果中
    const queriedToken = queriedTokens.find(qt => qt.uid === token.uid);

    if (queriedToken) {
      // 计算可删除的订单数量
      const deletableOrders = (queriedToken.orders || []).filter(order => isDeletableOrder(order.status));

      // 保留原有的orderDeletionStatus，如果存在的话
      const existingDeletionStatus = token.orderDeletionStatus || { successCount: 0, failCount: 0, inProgress: false };

      // 为token添加查询结果
      return {
        ...token,
        orderInfo: {
          isOnline: queriedToken.isOnline !== undefined ? queriedToken.isOnline : true,
          status: queriedToken.status || '在线',
          orderCount: queriedToken.orders?.length || 0,
          orders: queriedToken.orders || [],
          message: '查询成功'
        },
        // 添加可删除订单相关属性
        deletableOrders: deletableOrders,
        deletableOrderCount: deletableOrders.length,
        // 确保保留orderDeletionStatus，这样"已删XX单"的信息才能正确显示
        // 只有在token没有orderDeletionStatus或者successCount为0时才设置新的orderDeletionStatus
        // 这样可以避免覆盖updateLocalOrdersAfterDeletion设置的orderDeletionStatus
        orderDeletionStatus: token.orderDeletionStatus && token.orderDeletionStatus.successCount > 0
          ? token.orderDeletionStatus  // 保留原有的orderDeletionStatus
          : {
              ...existingDeletionStatus,
              totalOrders: (queriedToken.orders?.length || 0) + existingDeletionStatus.successCount
            }
      };
    }

    return token;
  });
};

// 更新本地订单数据（删除后）
const updateLocalOrdersAfterDeletion = (results) => {
  // 设置不重置页码的标志，防止删除订单后跳转到第一页
  shouldResetPage.value = false;

  // 保存当前页码
  const currentPageBeforeUpdate = currentPage.value;

  // 分离成功和失败的结果
  const successfullyDeletedOrders = results
    .filter(r => r.success)
    .map(r => r.orderSn);

  const failedDeletes = results.filter(r => !r.success);

  // 增加日志：记录删除结果概况
  console.log('===== 订单删除结果 =====');
  console.log(`成功删除订单数: ${successfullyDeletedOrders.length}`);
  console.log(`删除失败订单数: ${failedDeletes.length}`);
  console.log('成功删除的订单ID:', successfullyDeletedOrders);

  // 输出错误信息
  if (failedDeletes.length > 0) {
    console.warn('删除失败的订单:', failedDeletes);

    // 提取错误消息并统计
    const errorMessages = {};
    failedDeletes.forEach(result => {
      const msg = result.message || '未知错误';
      errorMessages[msg] = (errorMessages[msg] || 0) + 1;
    });

    // 构建错误信息HTML
    let errorHtml = `
      <div style="margin-top: 15px;">
        <div style="font-weight: bold; color: #f56c6c; margin-bottom: 10px;">删除失败原因:</div>
        <ul style="text-align: left; margin: 0; padding-left: 20px;">
    `;

    for (const [message, count] of Object.entries(errorMessages)) {
      errorHtml += `<li>${message} (${count}个订单)</li>`;
    }

    errorHtml += `
        </ul>
        <div style="margin-top: 10px; font-size: 12px; color: #909399;">
          常见原因：订单未评价、Token已失效或者无权限操作
        </div>
      </div>
    `;

    // 显示错误信息
    if (failedDeletes.length > 0) {
      ElMessageBox.alert(
        errorHtml,
        '订单删除失败详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          type: 'warning'
        }
      );
    }
  }

  if (successfullyDeletedOrders.length === 0) {
    // 如果没有成功删除任何订单，则显示提示并返回
    if (results.length > 0) {
      ElMessage.warning('没有成功删除任何订单，请查看失败详情');
    }
    console.log('没有成功删除任何订单，函数提前返回');
    return;
  }

  console.log('开始更新本地订单数据，成功删除的订单数量:', successfullyDeletedOrders.length);

  // 增加日志：记录更新前的所有token订单数量
  console.log('===== 更新前的Token订单数量 =====');
  tokens.value.forEach(token => {
    if (token.orderInfo && token.orderInfo.orders) {
      console.log(`Token ${token.uid}: 订单数量=${token.orderInfo.orderCount}, 实际orders长度=${token.orderInfo.orders.length}, 可删除订单数量=${getDeletableCount(token)}`);
    }
  });

  // 更新token的订单列表
  tokens.value = tokens.value.map(token => {
    if (token.orderInfo && token.orderInfo.orders) {
      // 计算实际被删除的订单数量（通过比较原始订单和剩余订单）
      const originalOrders = token.orderInfo.orders;
      const updatedOrders = originalOrders.filter(
        order => !successfullyDeletedOrders.includes(order.orderId)
      );

      // 计算本次实际删除的订单数量
      const deletedCount = originalOrders.length - updatedOrders.length;
      console.log(`Token ${token.uid}: 原始订单数量=${originalOrders.length}, 更新后订单数量=${updatedOrders.length}, 删除数量=${deletedCount}`);

      // 只在有订单被删除时更新
      if (deletedCount > 0) {
        // 确保保留原始orderInfo中的其他字段
        const updatedOrderInfo = {
          ...token.orderInfo,
          orders: updatedOrders,
          orderCount: updatedOrders.length,  // 确保orderCount为实际剩余订单数量
          // 保留以下字段确保UI显示正常
          isOnline: token.orderInfo.isOnline,
          status: token.orderInfo.status
        };

        // 增加日志：记录更新后的orderInfo
        console.log(`Token ${token.uid} 更新后的orderInfo:`, {
          orderCount: updatedOrderInfo.orderCount,
          ordersLength: updatedOrderInfo.orders.length,
          isOnline: updatedOrderInfo.isOnline,
          status: updatedOrderInfo.status
        });

        // 更新删除状态 - 累加successCount
        const updatedDeletionStatus = {
          totalOrders: originalOrders.length,
          // 如果已有orderDeletionStatus，则累加successCount，否则使用当前删除数量
          successCount: (token.orderDeletionStatus ? token.orderDeletionStatus.successCount : 0) + deletedCount,
          failCount: 0,
          inProgress: false
        };

        // 记录删除状态更新
        console.log(`Token ${token.uid} 删除状态更新:`, {
          原始successCount: token.orderDeletionStatus ? token.orderDeletionStatus.successCount : 0,
          当前删除数量: deletedCount,
          更新后successCount: updatedDeletionStatus.successCount
        });

        // 计算可删除的订单数量
        const updatedDeletableOrders = updatedOrders.filter(order => isDeletableOrder(order.status));

        // 记录可删除订单数量的更新
        console.log(`Token ${token.uid} 可删除订单数量更新: 原始=${token.deletableOrderCount || getDeletableCount(token)}, 更新后=${updatedDeletableOrders.length}`);

        // 返回更新后的token对象
        return {
          ...token,
          orderInfo: updatedOrderInfo,
          orderDeletionStatus: updatedDeletionStatus,
          // 更新currentDisplayOrders和deletableOrders
          currentDisplayOrders: updatedOrders,
          deletableOrders: updatedDeletableOrders,
          // 添加专门的属性记录可删除的订单数量
          deletableOrderCount: updatedDeletableOrders.length
        };
      }
    }
    return token;
  });

  // 增加日志：记录更新后的所有token订单数量
  console.log('===== 更新后的Token订单数量 =====');
  tokens.value.forEach(token => {
    if (token.orderInfo && token.orderInfo.orders) {
      console.log(`Token ${token.uid}: 订单数量=${token.orderInfo.orderCount}, 实际orders长度=${token.orderInfo.orders.length}, 可删除订单数量=${getDeletableCount(token)}`);
    }
  });

  // 更新当前显示的订单列表
  if (currentOrders.value.length > 0) {
    const beforeLength = currentOrders.value.length;
    currentOrders.value = currentOrders.value.filter(
      order => !successfullyDeletedOrders.includes(order.orderId)
    );
    console.log(`当前显示的订单列表: 更新前=${beforeLength}, 更新后=${currentOrders.value.length}`);
  }

  // 更新可删除订单列表
  if (deletableOrders.value.length > 0) {
    const beforeLength = deletableOrders.value.length;
    deletableOrders.value = deletableOrders.value.filter(
      order => !successfullyDeletedOrders.includes(order.orderId)
    );
    console.log(`可删除订单列表: 更新前=${beforeLength}, 更新后=${deletableOrders.value.length}`);
  }

  // 强制刷新表格显示
  nextTick(() => {
    // 保证不重置页码
    currentPage.value = currentPageBeforeUpdate;

    // 通知表格数据已经改变
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
      console.log('已强制刷新表格显示');
    }
    // 应用表格选择状态
    applyTableSelectionStatus();
    console.log('已应用表格选择状态');

    // 增加日志：记录nextTick后的token订单数量
    console.log('===== nextTick后的Token订单数量 =====');
    tokens.value.forEach(token => {
      if (token.orderInfo && token.orderInfo.orders) {
        console.log(`Token ${token.uid}: 订单数量=${token.orderInfo.orderCount}, 实际orders长度=${token.orderInfo.orders.length}, 可删除订单数量=${getDeletableCount(token)}`);
      }
    });

    // 添加额外的刷新机制，确保UI被正确更新
    // 使用setTimeout延迟执行，确保在Vue更新周期之后
    setTimeout(() => {
      console.log('执行额外的刷新机制');
      // 创建tokens的浅拷贝，触发Vue的响应式更新
      const tokensCopy = [...tokens.value];
      tokens.value = tokensCopy;

      // 再次强制刷新表格
      if (tokenTableRef.value) {
        tokenTableRef.value.$forceUpdate();
      }

      // 记录最终的token订单数量
      console.log('===== 最终的Token订单数量 =====');
      tokens.value.forEach(token => {
        if (token.orderInfo && token.orderInfo.orders) {
          console.log(`Token ${token.uid}: 订单数量=${token.orderInfo.orderCount}, 实际orders长度=${token.orderInfo.orders.length}, 可删除订单数量=${getDeletableCount(token)}`);
        }
      });
    }, 100);
  });
};

// 显示删除结果详情 - 修改为仅显示简单消息
const showDeletionResultDetails = (results) => {
  const successResults = results.filter(r => r.success);
  const failResults = results.filter(r => !r.success);

  // 显示简单的成功消息，不再弹出详细结果对话框
  if (successResults.length > 0) {
    ElMessage.success(`成功删除 ${successResults.length} 个订单${failResults.length > 0 ? `，${failResults.length} 个删除失败` : ''}`);
  } else if (failResults.length > 0) {
    ElMessage.warning(`全部 ${failResults.length} 个订单删除失败`);
  }
};

// 清空表格
const handleClearTable = () => {
  ElMessageBox.confirm(
    '确定要清空当前表格中的所有数据吗？此操作不可恢复！',
    '清空表格确认',
    {
      confirmButtonText: '确定清空',
      cancelButtonText: '取消',
      type: 'warning',
      distinguishCancelAndClose: true,
      customClass: 'warning-confirm-dialog'
    }
  )
    .then(() => {
      // 清空数据数组
      tokens.value = [];
      // 重置相关状态
      selectedTokens.value = [];
      totalTokenCount.value = 0;
      filteredTokensCache.value = [];

      // 清空表格是用户明确的操作，所以在此情况下重置页码是合理的
      currentPage.value = 1;

      // 清除表格选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 刷新筛选状态
      filterTokens();

      ElMessage.success('表格数据已全部清空');
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    });
};

// 处理单个订单删除
const handleDeleteSingleOrder = async (orderDetail) => {
  if (!orderDetail || !orderDetail.uid || !orderDetail.tokenValue || !orderDetail.orderId) {
    ElMessage.warning('订单信息不完整，无法删除');
    return;
  }

  // 确认是否可删除
  if (!isDeletableOrder(orderDetail.status)) {
    ElMessage.warning(`只能删除已评价的订单，当前订单状态为"${orderDetail.status}"`);
    return;
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除订单 ${orderDetail.orderId} 吗？此操作不可恢复！`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 调用API删除订单
      const result = await tokenService.deleteOrder(
        orderDetail.uid,
        orderDetail.tokenValue,
        orderDetail.orderId
      );

      if (result.success) {
        // 删除成功，关闭订单详情对话框
        orderDetailDialogVisible.value = false;

        // 更新本地数据
        updateLocalOrdersAfterDeletion([{
          success: true,
          orderSn: orderDetail.orderId
        }]);

        ElMessage.success('订单删除成功');

        // 如果主订单列表对话框也打开，更新它
        if (ordersDialogVisible.value && currentOrders.value.length > 0) {
          // 从当前列表中过滤掉已删除的订单
          currentOrders.value = currentOrders.value.filter(
            order => order.orderId !== orderDetail.orderId
          );
        }
      } else {
        // 增强错误信息显示
        const errorMsg = result.message || '删除失败';
        ElMessage.error(errorMsg);

        // 构建更详细的错误提示
        let detailsHtml = `
          <div style="text-align: left; margin: 15px 0;">
            <h3 style="margin-bottom: 10px;">删除失败详情</h3>
            <div style="color: #f56c6c;">
              <p>${errorMsg}${result.errorCode ? ` (错误码: ${result.errorCode})` : ''}</p>
            </div>

            <div style="margin-top: 15px; font-size: 14px;">
              <p>可能的原因：</p>
              <ul style="padding-left: 20px; margin-top: 5px;">
                <li>订单状态不是"已评价"(拼多多要求必须是严格的"已评价")</li>
                <li>Token已失效或没有足够权限</li>
                <li>网络问题或API限流</li>
              </ul>
            </div>
        `;

        if (result.responseData) {
          detailsHtml += `
            <div style="margin-top: 15px;">
              <details>
                <summary>API响应详情</summary>
                <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(result.responseData, null, 2)}</pre>
              </details>
            </div>
          `;
        }

        detailsHtml += `</div>`;

        // 显示详细错误
        ElMessageBox.alert(detailsHtml, '删除失败', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        });
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 订单列表表格选择相关
const orderTableRef = ref(null);
const selectedOrdersInList = ref([]);
const deletableOrdersCount = computed(() => {
  return currentOrders.value.filter(order => isDeletableOrder(order.status)).length;
});

// 处理订单列表表格的选择变化
const handleOrderSelectionChange = (selection) => {
  selectedOrdersInList.value = selection;
};

// 全选可删除的订单
const selectAllDeletableOrders = () => {
  // 先清除所有选择
  orderTableRef.value?.clearSelection();

  // 然后选中所有可删除的订单
  currentOrders.value.forEach(order => {
    if (isDeletableOrder(order.status)) {
      orderTableRef.value?.toggleRowSelection(order, true);
    }
  });
};

// 批量删除选中的订单
const batchDeleteSelectedOrders = async () => {
  if (selectedOrdersInList.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单');
    return;
  }

  try {
    // 确认是否删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedOrdersInList.value.length} 个订单吗？`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 构建删除请求数据
      const orderItems = selectedOrdersInList.value.map(order => ({
        uid: order.uid,
        token: order.tokenValue,
        orderSn: order.orderId
      }));

      // 调用批量删除API
      const result = await tokenService.batchDeleteOrders(orderItems);

      if (result.success) {
        // 统计结果
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 更新本地数据，从订单列表中移除已删除的订单
        updateLocalOrdersAfterDeletion(result.results);

        // 清除表格选择
        selectedOrdersInList.value = [];
        orderTableRef.value?.clearSelection();

        // 使用通知显示详细结果
        ElNotification({
          title: '删除订单完成',
          message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
          type: 'success',
          duration: 5000
        });

        // 强制刷新表格数据
        refreshTokens();
      } else {
        ElMessage.error(`批量删除失败: ${result.message || '未知错误'}`);
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 判断订单是否可删除（允许已评价、交易成功、交易已取消、未发货和退款成功的订单）
const isDeletableOrder = (status) => {
  if (!status) return false;

  // 严格匹配"已评价"状态
  if (status === '已评价') {
    return true;
  }

  // 严格匹配"交易成功"状态
  if (status === '交易成功') {
    return true;
  }

  // 严格匹配"交易已取消"状态
  if (status === '交易已取消') {
    return true;
  }

  // 严格匹配"未发货"状态
  if (status === '未发货') {
    return true;
  }

  // 严格匹配"退款成功"状态
  if (status === '退款成功') {
    return true;
  }

  // 包含"已评价"的状态也可能可以删除
  if (status.includes('已评价')) {
    return true;
  }

  // 包含"交易成功"的状态也可能可以删除
  if (status.includes('交易成功')) {
    return true;
  }

  // 包含"交易已取消"的状态也可能可以删除
  if (status.includes('交易已取消')) {
    return true;
  }

  // 包含"未发货"的状态也可能可以删除
  if (status.includes('未发货')) {
    return true;
  }

  // 包含"退款成功"的状态也可能可以删除
  if (status.includes('退款成功')) {
    return true;
  }

  // 其他状态不可删除
  return false;
};

// 获取可删除的订单数量
const getDeletableCount = (row) => {
  // 优先使用deletableOrderCount属性（如果存在）
  if (row.deletableOrderCount !== undefined) {
    return row.deletableOrderCount;
  }

  // 否则计算可删除订单数量
  if (!row.orderInfo || !row.orderInfo.orders) return 0;
  return row.orderInfo.orders.filter(order => isDeletableOrder(order.status)).length;
};

// 添加deletableOrders变量
const deletableOrders = ref([]);

// 处理订单被删除的回调
const handleOrderDeleted = (data) => {
  // 使用现有的updateLocalOrdersAfterDeletion方法更新本地数据
  if (data && data.results) {
    updateLocalOrdersAfterDeletion(data.results);
  }
};

// 一键删除订单处理函数
const handleQuickDeleteOrder = async (token) => {
  // 防止重复点击
  if (token.orderDeleteLoading) return;

  // 设置loading状态
  token.orderDeleteLoading = true;

  try {
    // 询问用户是否确认删除
    await ElMessageBox.confirm(
      `确定要删除此Token的订单吗？将会自动查询并使用并发模式删除所有可删除的订单。`,
      '删除订单确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示加载中状态
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在处理订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 重置并显示进度
    batchProgress.value = {
      current: 0,
      total: 0,
      progress: 0,
      message: '准备处理...'
    };
    showBatchProgress.value = true;

    try {
      // 使用并发查询和删除，添加进度回调，使用优化的参数
      const result = await orderService.concurrentQueryAndDeleteOrders(
        [token],
        {
          batchSize: 50, // 从20增加到50
          concurrency: 10, // 从5增加到10
          tokenBatchSize: 90, // 每批90个Token
          parallelBatches: 3, // 并行处理3批token
          onProgress: (progress) => {
            batchProgress.value = progress;
            // 更新loading文本
            if (loadingInstance) {
              loadingInstance.setText(`正在删除订单..${progress.message}`);
            }
          }
        }
      );

      if (result.success) {
        // 检查是否有成功删除的订单
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 先更新本地数据
        updateLocalOrdersAfterDeletion(result.results);

        // 处理查询结果，更新UI显示
        if (result.queriedTokens && result.queriedTokens.length > 0) {
          // 更新已查询但没有可删除订单的Token状态
          // 注意：这里不会覆盖updateLocalOrdersAfterDeletion设置的orderDeletionStatus
          updateTokensAfterQuery(result.queriedTokens);
        }

        // 确保orderDeletionStatus正确设置
        if (successCount > 0) {
          // 再次检查token的orderDeletionStatus是否正确设置
          tokens.value = tokens.value.map(t => {
            if (t.uid === token.uid && (!t.orderDeletionStatus || t.orderDeletionStatus.successCount < successCount)) {
              console.log(`修正Token ${t.uid}的orderDeletionStatus: 设置successCount=${successCount}`);
              return {
                ...t,
                orderDeletionStatus: {
                  ...t.orderDeletionStatus || {},
                  successCount: successCount,
                  totalOrders: (t.orderInfo?.orderCount || 0) + successCount,
                  inProgress: false
                }
              };
            }
            return t;
          });
        }

        // 使用通知显示结果
        if (successCount > 0) {
          ElNotification({
            title: '删除订单完成',
            message: `共处理了${result.processedBatchCount || 1}/${result.batchCount || 1}批Token，成功删除${successCount}个订单${failCount > 0 ? `，${failCount}个删除失败` : ''}`,
            type: 'success',
            duration: 5000000000000
          });
        } else if (result.results.length > 0) {
          // 有订单但全部删除失败
          ElNotification({
            title: '删除订单未成功',
            message: `未能成功删除任何订单，${failCount} 个删除失败`,
            type: 'warning',
            duration: 5000
          });
        } else {
          // 无可删除订单
          ElNotification({
            title: '无可删除订单',
            message: '查询完成，但没有找到可删除的订单（只有已评价、交易成功、交易已取消、未发货或退款成功的订单可以删除）',
            type: 'info',
            duration: 5000
          });
        }

        // 强制刷新表格数据
        refreshTokens();
      } else {
        // API调用失败
        ElMessage.error(result.message || '操作失败');
      }
    } finally {
      loadingInstance.close();
      token.orderDeleteLoading = false;

      // 隐藏进度显示
      setTimeout(() => {
        showBatchProgress.value = false;
      }, 3000);
    }
  } catch (error) {
    token.orderDeleteLoading = false;
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 添加一个标志来控制是否需要重置页码
const shouldResetPage = ref(true);

// 在script setup中添加组件引用和状态
const orderDialogVisible = ref(false);

// 处理新Token置入账号内订单
const handleAddToOrder = () => {
  // 验证是否有选中的Token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要置入订单的Token');
    return;
  }

  // 打开对话框
  orderDialogVisible.value = true;
};

// 处理订单操作成功
const handleOrderSuccess = (result) => {
  ElMessage.success(result.message || '操作成功');

  // 刷新数据
  refreshTokens();
};

// 处理订单操作失败
const handleOrderError = (error) => {
  ElMessage.error(error.message || '操作失败');
};

// 获取选中的Token
const getSelectedTokens = () => {
  const table = document.querySelector('.token-table');
  if (!table) return [];

  const selection = table.__vue__?.store?.states?.selection?.value || [];
  return selection;
};

// 增加备份对话框相关变量
const backupDialogVisible = ref(false);
const backupSelectedType = ref('重置前');
const backupCustomType = ref('');
const showCustomTypeInput = ref(false);
const backupLoading = ref(false);

// 观察备份类型变化
watch(backupSelectedType, (newValue) => {
  showCustomTypeInput.value = newValue === '自定义';
  if (showCustomTypeInput.value) {
    backupCustomType.value = '';
  }
});

// 创建备份的方法
const handleCreateBackup = async () => {
  // 增强前端验证
  if (!selectedTokens.value || selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要备份的Token');
    return;
  }

  // 检查选中的Token是否有效
  const validTokenCount = selectedTokens.value.filter(token => token && (token.id || token.uid)).length;
  if (validTokenCount === 0) {
    ElMessage.warning('选中的Token数据无效，请重新选择');
    return;
  }

  // 显示备份数量提示
  ElMessage.info(`将备份 ${validTokenCount} 个Token`);
  
  // 重置对话框状态
  backupSelectedType.value = '重置前';
  backupCustomType.value = '';
  showCustomTypeInput.value = false;
  
  // 显示备份对话框
  backupDialogVisible.value = true;
};

// 确认创建备份
const confirmBackupCreation = async () => {
  // 验证自定义类型是否已填写
  if (backupSelectedType.value === '自定义' && !backupCustomType.value) {
    ElMessage.warning('请输入自定义备份类型名称');
    return;
  }
  
  // 获取实际备份类型
  let backupType = backupSelectedType.value;
  if (backupType === '自定义') {
    backupType = backupCustomType.value;
  }
  
  // 设置加载状态
  backupLoading.value = true;
  
  try {
    // 获取有效的Token数量
    const validTokenCount = selectedTokens.value.filter(token => token && (token.id || token.uid)).length;
    
    // 提取Token ID列表，优先使用id字段，如果没有则使用uid字段
    const tokenIds = selectedTokens.value.map(token => {
      // 首先尝试使用id字段
      if (token.id) {
        const id = parseInt(token.id);
        if (!isNaN(id)) return id;
      }

      // 如果没有id字段或id无效，则尝试使用uid字段
      if (token.uid) {
        // 记录日志，帮助调试
        console.log('使用uid代替id:', token.uid);
        return token.uid;
      }

      // 如果都没有，返回null
      return null;
    }).filter(id => id !== null); // 过滤掉无效的ID

    console.log('准备备份的Token IDs:', tokenIds);

    if (tokenIds.length === 0) {
      ElMessage.warning('没有有效的Token ID可以备份');
      backupLoading.value = false;
      return;
    }

    // 准备要备份的数据
    const timestamp = new Date().toLocaleString();
    const sourceText = 'Token重置页面';

    // 收集所有Token的详细信息
    const tokenInfo = {};
    selectedTokens.value.forEach(token => {
      const tokenId = token.id || token.uid;
      if (tokenId) {
        // 保存Token的所有信息
        // 打印原始数据以便于调试
        console.log('原始Token数据:', token);

        tokenInfo[tokenId] = {
          uid: token.uid || tokenId,
          token: token.token || token.value || tokenId, // 使用token字段而不是token_value
          status: token.status || '未知',
          platform: token.platform || '',
          username: token.username || token.user || '', // 添加token.user作为备选
          nickname: token.nickname || '',
          avatar: token.avatar || '',
          created_at: token.created_at || timestamp,
          expiry_date: token.expiry_date || null,
          source: token.source || '直接备份',
          order_id: token.order_id || '',
          remark: token.remark || ''
        };

        // 打印处理后的数据
        console.log('处理后的Token数据:', tokenInfo[tokenId]);
      }
    });

    console.log('收集到的Token详细信息:', tokenInfo);

    const backupData = {
      name: `${backupType} ${timestamp}`,
      description: `从${sourceText}创建的${backupType}备份，包含${validTokenCount}个Token`,
      backup_type: backupType,
      token_ids: tokenIds,
      token_info: tokenInfo, // 添加Token详细信息
      type: 'manual',
      skip_validation: true, // 跳过Token验证，使用前端提供的ID直接创建备份
      use_uid_as_id: true, // 标记使用UID作为ID，让后端知道如何处理
      direct_backup: true, // 标记为直接备份模式，不验证Token是否存在
      created_at: timestamp // 添加创建时间信息
    };

    console.log('正在发送备份请求，数据包含', selectedTokens.value.length, '个Token');

    // 保存到后端数据库
    const apiUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'}/backups/test/create`;
    console.log('备份API地址:', apiUrl);

    // 更新提示文本
    const loadingText = `正在备份数据... (共${selectedTokens.value.length}个Token)`;
    console.log(loadingText);

    // 发送备份请求
    const response = await axios.post(apiUrl, backupData);
    console.log('备份API响应:', response.data);

    if (response.data.success) {
      // 关闭对话框
      backupDialogVisible.value = false;
      
      // 成功提示使用通知组件，可以包含更多信息
      const successMessage = `成功创建了 ${backupType} 备份，包含 ${validTokenCount} 个Token`;

      ElNotification({
        title: '备份成功',
        message: successMessage,
        type: 'success',
        duration: 5000
      });

      // 清空选中的Token
      selectedTokens.value = [];

      // 显示跳转到备份页面的确认框
      setTimeout(() => {
        ElMessageBox.confirm(
          '备份已成功创建，是否前往备份页面查看？',
          '备份成功',
          {
            confirmButtonText: '前往查看',
            cancelButtonText: '留在当前页面',
            type: 'success'
          }
        ).then(() => {
          router.push('/token-backup');
        }).catch(() => {
          // 用户选择留在当前页面，不做任何操作
        });
      }, 500);
    } else {
      throw new Error(response.data.message || '备份创建失败');
    }
  } catch (apiError) {
    console.error('备份API错误:', apiError);

    // 错误消息和类型
    let errorTitle = '备份失败';
    let errorMessage = '未知错误，请重试';
    let errorType = 'error';

    // 根据不同错误类型设置不同的错误信息
    if (apiError.code === 'ERR_NETWORK') {
      errorMessage = '无法连接到服务器，请检查服务器是否运行';
      errorType = 'warning';
    } else if (apiError.response) {
      // 根据响应状态码处理
      switch (apiError.response.status) {
        case 401:
          errorMessage = '未授权访问，请先登录';
          break;
        case 400:
          errorMessage = `请求参数错误: ${apiError.response.data?.message || '请检查提交的数据'}`;
          // 如果是没有有效Token ID的错误，显示更友好的提示
          if (apiError.response.data?.message?.includes('没有有效的Token ID')) {
            errorMessage = '所选Token不存在或已被删除，请重新选择';
          }
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误，请稍后再试';
          break;
        default:
          errorMessage = `服务器响应错误 (${apiError.response.status}): ${apiError.response.data?.message || apiError.message || '未知错误'}`;
      }
    } else {
      errorMessage = `备份API错误: ${apiError.message || '未知错误'}`;
    }

    // 使用通知组件显示错误
    ElNotification({
      title: errorTitle,
      message: errorMessage,
      type: errorType,
      duration: 8000 // 8秒后自动关闭
    });
  } finally {
    // 关闭加载状态
    backupLoading.value = false;

    // 刷新表格数据
    refreshTokens();
  }
};

// 初始化生命周期
onMounted(() => {
  // 调整表格高度
  adjustTableHeight();
  window.addEventListener('resize', adjustTableHeight);

  // 初始化数据
  // 在实际应用中，这里可能会从后端加载数据
  // 模拟数据加载
  // loadMockData();

  // 检查是否有从备份页面恢复的数据
  checkForRecoveredTokens();
});

// 处理从未卖加载器组件更新的Token数据
const handleUnsoldTokensUpdate = (updatedTokens) => {
  // 更新Token列表
  tokens.value = updatedTokens;
  totalTokenCount.value = updatedTokens.length;

  // 重置选中状态
  selectedTokens.value = [];

  // 强制刷新表格
  nextTick(() => {
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
    }
  });
};

// 处理从已卖加载器组件更新的Token数据
const handleSoldTokensUpdate = (updatedTokens) => {
  // 更新Token列表
  tokens.value = updatedTokens;
  totalTokenCount.value = updatedTokens.length;

  // 重置选中状态
  selectedTokens.value = [];

  // 强制刷新表格
  nextTick(() => {
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
    }
  });
};

// 处理从清除掉线组件更新的Token数据
const handleOfflineTokensUpdate = (updatedTokens) => {
  // 更新Token列表
  tokens.value = updatedTokens;
  totalTokenCount.value = updatedTokens.length;

  // 重置选中状态
  selectedTokens.value = [];

  // 强制刷新表格
  nextTick(() => {
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
    }
  });
};

// 检查是否有从备份页面恢复的数据
const checkForRecoveredTokens = () => {
  // 检查URL参数
  const isRecovered = route.query.recovered === 'true';
  const backupId = route.query.backup_id;
  const backupName = route.query.backup_name || '备份数据';

  if (isRecovered && backupId) {
    // 从 localStorage 中获取恢复的数据
    const recoveredTokensStr = localStorage.getItem('recovered_tokens');

    if (recoveredTokensStr) {
      try {
        const recoveredTokens = JSON.parse(recoveredTokensStr);

        if (recoveredTokens && recoveredTokens.length > 0) {
          // 显示加载状态
          const loadingInstance = ElLoading.service({
            fullscreen: true,
            text: '正在导入备份数据...'
          });

          // 处理数据并导入
          const { newTokens, duplicateCount } = processAndDeduplicateTokens(recoveredTokens);

          // 设置不重置页码的标志
          shouldResetPage.value = false;

          tokens.value = newTokens;
          totalTokenCount.value = newTokens.length;

          // 清除localStorage中的数据，避免重复导入
          localStorage.removeItem('recovered_tokens');

          // 清除URL参数，避免刷新页面时重复导入
          router.replace({ path: '/token-reset' });

          let successMsg = `成功从备份"${backupName}"导入 ${newTokens.length} 个Token`;
          if (duplicateCount > 0) {
            successMsg += `，已自动去除 ${duplicateCount} 个重复Token`;
          }

          loadingInstance.close();
          ElMessage.success(successMsg);
        }
      } catch (error) {
        console.error('处理恢复数据失败:', error);
        ElMessage.error(`处理恢复数据失败: ${error.message}`);
      }
    }
  }
};

// 修改：删除未卖 Token 的处理函数 - 调用云端接口
const handleDeleteUnsold = async () => {
  try {
    // 步骤 1: 确认对话框
    await ElMessageBox.confirm(
      `确定要执行【删除未卖】操作吗？此操作将清空服务器上所有未售出的Token，且不可恢复！请谨慎操作！`,
      `删除云端未卖Token确认`,
      {
        confirmButtonText: `确认删除云端数据`,
        cancelButtonText: `取消`,
        type: `warning`
      }
    );

    // 步骤 2: 显示加载提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: `正在请求云端接口以删除未卖Token...`,
      background: `rgba(0, 0, 0, 0.7)`,
    });

    try {
      // 步骤 3: 调用后端 API
      const result = await truncateCloudUnsoldTokens();

      // 步骤 4: 处理后端响应
      if (result.success) {
        ElMessage.success(result.message);
        // --- 本地数据操作代码已移除 ---
      } else {
        ElMessage.error(`操作失败: ${result.message}`);
      }
    } catch (apiError) {
      // 处理 API 调用本身的错误 (理论上服务层已处理，但以防万一)
      console.error(`调用云端删除接口时发生意外错误:`, apiError);
      ElMessage.error(`调用API时出错: ${apiError.message || `未知错误`}`);
    } finally {
      // 步骤 5: 无论成功或失败，关闭加载提示
      loadingInstance.close();
    }

  } catch (error) {
    // 步骤 6: 处理用户取消操作或 MessageBox 出错
    if (error !== `cancel`) {
      // 如果不是用户点击了"取消"
      console.error(`删除未卖Token操作过程中出现错误:`, error);
      // 可以选择性地显示错误消息，或者只记录日志
      // ElMessage.error('操作过程中出现错误');
    } else {
      // 用户点击了"取消"
      ElMessage.info(`操作已取消`);
    }
  }
};

// 组件卸载时清除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', adjustTableHeight);
});

// --- 新增 handleUploadSelected 方法 ---
const handleUploadSelected = async () => {
  // 1. 检查是否有选中的 Token
  if (!selectedTokens.value || selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要上传到后台的 Token。');
    return;
  }

  const tokensToUpload = selectedTokens.value;
  const count = tokensToUpload.length;

  try {
    // 2. 确认对话框
    await ElMessageBox.confirm(
      `确定要将选中的 ${count} 个 Token 上传到后台吗？`,
      `上传 Token 确认`,
      {
        confirmButtonText: `确认上传 (${count})`,
        cancelButtonText: `取消`,
        type: `info`
      }
    );

    // 3. 显示加载提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: `正在上传 ${count} 个 Token 到后台...`,
      background: `rgba(0, 0, 0, 0.7)`,
    });

    try {
      // 4. 调用上传服务
      const result = await uploadSelectedTokens(tokensToUpload);

      // 5. 处理上传结果
      if (result.success) {
        ElMessage.success(result.message || `成功上传 ${result.uploadedCount || count} 个 Token！`);
        // 上传成功后清除选择 (可选)
        // clearAllSelection();
      } else {
        ElMessage.error(`上传失败: ${result.message}`);
        // 显示更详细的错误，如果后端提供了
        if (result.details) {
           console.error('Upload failed details:', result.details);
           // 可以考虑使用 ElMessageBox 显示更详细的错误
        }
      }
    } catch (uploadError) {
      // 处理 uploadSelectedTokens 函数本身可能抛出的错误（理论上内部已处理）
      console.error(`调用上传服务时发生意外错误:`, uploadError);
      ElMessage.error(`上传服务调用出错: ${uploadError.message || '未知错误'}`);
    } finally {
      // 6. 关闭加载提示
      loadingInstance.close();
    }

  } catch (cancelError) {
    // 7. 处理用户取消操作或 MessageBox 出错
    if (cancelError !== `cancel`) {
      console.error(`上传 Token 操作过程中出现错误:`, cancelError);
    } else {
      ElMessage.info(`上传操作已取消`);
    }
  }
};
// --- 结束 handleUploadSelected 方法 ---
</script>

<style scoped>
.token-reset-container {
  padding: 10px;
}

.token-list-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;

}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.token-count-indicator {
  display: flex;
  align-items: center;
  margin-right: auto;
  margin-left: 15px;
}

.function-buttons {
  margin: 0 0 20px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.function-buttons .el-button-group {
  margin-right: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 10px;
}

.filter-select {
  width: 130px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 280px;
}

.data-stats {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.stats-card {
  min-width: 70px;
  height: 43px;
  border-radius: 4px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  text-align: center;
  cursor: default;
}

.info-card {
  background-color: #909399;
}

.primary-card {
  background-color: #409EFF;
}

.warning-card {
  background-color: #E6A23C;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.2;
  display: block;
}

.stats-label {
  font-size: 12px;
  opacity: 0.9;
  display: block;
  margin-top: 2px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.selection-info {
  margin-left: auto;
}

/* 表格样式优化 */
.token-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.token-table :deep(.el-table__header) {
  font-weight: 600;
  color: #303133;
}

.token-table :deep(.el-table__row) {
  height: 60px;
  cursor: pointer; /* 添加手型指针，提示可点击 */
}

.token-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
  transition: background-color 0.2s ease; /* 添加过渡效果 */
}

.token-table :deep(.el-table__row.current-row) {
  --el-table-current-row-bg-color: #ecf5ff;
}

.token-table :deep(.el-table-fixed-column--right) {
  background-color: #fff;
}

.token-avatar {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #fff;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 导入对话框格式说明样式 */
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th,
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 文本导入样式 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 用户头像样式 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 进度显示样式 */
.progress-container {
  padding: 10px;
}
.progress-message {
  margin-top: 10px;
  text-align: center;
  color: #606266;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.order-count-badge {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  background: linear-gradient(135deg, #e8f4ff, #c5e3ff);
  color: #1a4b8c;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 110, 255, 0.12);
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 179, 255, 0.2);
  font-size: 13px;
  min-width: 60px;
  justify-content: center;
  height: auto; /* 改为自适应高度 */
  min-height: 32px;
  font-weight: 500;
  flex-wrap: nowrap; /* 防止内容换行 */
  white-space: nowrap; /* 防止文本换行 */
}

.order-count-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 110, 255, 0.15);
  background: linear-gradient(135deg, #d1e9ff, #b3d9ff);
}

.order-number {
  font-weight: 600;
  font-size: 14px; /* 稍微减小字体大小 */
  margin-right: 3px;
  color: #0960d0;
  white-space: nowrap; /* 防止文本换行 */
}

.order-text {
  font-size: 13px;
  color: #5a8dc8;
}

.deleted-text {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 500;
}

.order-deletable {
  font-size: 12px;
  color: #67c23a;
  margin-left: 5px;
  font-weight: normal;
}

.deletable-tag {
  margin-left: 8px;
  font-size: 11px;
  height: 22px;
  line-height: 20px;
  padding: 0 6px;
}

.deletable-count {
  font-size: 11px;
  color: #67c23a;
  margin-left: 4px;
}

.deleted-count {
  font-size: 11px;
  color: #f56c6c;
  margin-left: 2px;
}

/* 状态标签通用样式 */
.status-badge {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.3s ease;
  gap: 4px;
  height: 24px;
}

.status-badge i {
  font-size: 14px;
}

/* 掉线状态样式 */
.status-offline {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.1);
}

/* 无订单状态样式 */
.status-no-order {
  background-color: #f5f7fa;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 查询失败状态样式 */
.status-error {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid rgba(230, 162, 60, 0.1);
}

/* 未查询状态样式 */
.status-not-queried {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 订单详情对话框样式 */
.order-details {
  margin-top: 10px;
}

.order-table {
  margin-bottom: 15px;
}

.order-table .el-table__row:hover {
  background-color: #f0f7ff;
}

.order-detail-item {
  display: flex;
  margin-bottom: 8px;
}

.order-detail-label {
  font-weight: bold;
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

.order-detail-value {
  flex: 1;
}

.order-status-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.order-deletion-status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.order-status-text {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-tag i {
  font-size: 12px;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 导入对话框格式说明样式 */
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th,
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 文本导入样式 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 用户头像样式 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

</style>