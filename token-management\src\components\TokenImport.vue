<template>
  <div class="token-import">
    <!-- 导入按钮 -->
    <el-button type="primary" @click="handleImportToken">
      <el-icon><el-icon-upload /></el-icon>导入Token
    </el-button>

    <!-- 隐藏的文件输入框 -->
    <input
      type="file"
      ref="fileInputRef"
      style="display: none;"
      @change="handleFileInputChange"
      accept=".xlsx,.xls,.txt"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { nextTick } from 'vue'
import useTokenImport from '@/composables/useTokenImport'
import { parseImportedFile } from '@/utils/tokenParser'

const props = defineProps({
  tokens: {
    type: Array,
    required: true
  },
  shouldResetPage: {
    type: Object,
    required: true
  },
  totalTokenCount: {
    type: Object,
    required: true
  },
  currentPage: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:tokens', 'import-success', 'import-error'])

// 使用组合式函数
const {
  fileInputRef,
  handleImportToken
} = useTokenImport({
  tokens: ref(props.tokens),
  shouldResetPage: props.shouldResetPage,
  processAndDeduplicateTokens: processAndDeduplicateTokens,
  refreshTokens: (newTokens) => {
    // 更新父组件的tokens
    emit('update:tokens', newTokens)

    // 更新总数量
    if (props.totalTokenCount && typeof props.totalTokenCount === 'object' && props.totalTokenCount !== null) {
      props.totalTokenCount.value = newTokens.length
    }

    // 触发导入成功事件
    emit('import-success', {
      count: newTokens.length
    })
  }
})

// 处理去重和设置默认状态的函数
function processAndDeduplicateTokens(parsedData) {
  // 确保每个Token都有默认状态为"未知"
  const processedData = parsedData.map(token => ({
    ...token,
    status: token.status || '未知'
  }))

  // 如果现有数据为空，直接返回处理后的数组
  if (props.tokens.length === 0) {
    return {
      newTokens: processedData,
      duplicateCount: 0
    }
  }

  // 创建已存在的UID和Token值的集合，用于去重
  const existingUids = new Set(props.tokens.map(token => token.uid))
  const existingTokenValues = new Set(props.tokens.map(token => token.token))

  // 过滤重复的Token（基于UID或Token值）
  const uniqueTokens = []
  let duplicateCount = 0

  for (const token of processedData) {
    if (!existingUids.has(token.uid) && !existingTokenValues.has(token.token)) {
      uniqueTokens.push(token)
      existingUids.add(token.uid)
      existingTokenValues.add(token.token)
    } else {
      duplicateCount++
    }
  }

  // 合并现有Token和新导入的唯一Token
  return {
    newTokens: [...props.tokens, ...uniqueTokens],
    duplicateCount
  }
}

// 直接实现处理选中的文件函数
const processSelectedFile = async (file) => {
  const fileName = file.name
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.txt')) {
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在导入数据...'
    })

    try {
      const parsedData = await parseImportedFile(file)

      if (parsedData.length === 0) {
        ElMessage.warning('文件中没有有效数据')
        loadingInstance.close()
        return
      }

      const { newTokens, duplicateCount } = processAndDeduplicateTokens(parsedData)

      // 修改：保存当前页码
      const currentPageBeforeUpdate = props.currentPage.value

      // 设置不重置页码的标志
      if (props.shouldResetPage && typeof props.shouldResetPage === 'object' && props.shouldResetPage !== null) {
        props.shouldResetPage.value = false
      }

      // 更新父组件的tokens
      emit('update:tokens', newTokens)
      if (props.totalTokenCount && typeof props.totalTokenCount === 'object' && props.totalTokenCount !== null) {
        props.totalTokenCount.value = newTokens.length
      }

      let successMsg = `成功导入 ${newTokens.length} 个Token`
      if (duplicateCount > 0) {
        successMsg += `，已自动去除 ${duplicateCount} 个重复Token`
      }

      ElMessage.success(successMsg)
      loadingInstance.close()

      // 恢复之前的页码
      nextTick(() => {
        if (props.currentPage && typeof props.currentPage === 'object' && props.currentPage !== null) {
          props.currentPage.value = currentPageBeforeUpdate
        }
      })

      // 触发导入成功事件
      emit('import-success', {
        count: newTokens.length
      })
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败: ' + (error.message || '无效的数据格式'))
      loadingInstance.close()

      // 触发导入失败事件
      emit('import-error', error)
    }
  } else {
    ElMessage.error('不支持的文件格式，仅支持Excel(.xlsx, .xls)和文本文件(.txt)')
    emit('import-error', { message: '不支持的文件格式' })
  }
}

// 处理文件选择变化
const handleFileInputChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  processSelectedFile(file)

  // 重置文件输入，以便下次选择同一文件时也能触发事件
  event.target.value = ''
}
</script>

<style scoped>
.token-import {
  display: inline-block;
}
</style>
