<template>
  <div class="token-exporter">
    <el-dropdown @command="handleExport" trigger="click">
      <el-button type="primary">
        <el-icon><Download /></el-icon>导出数据
        <el-icon><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="all">导出所有数据</el-dropdown-item>
          <el-dropdown-item command="selected" :disabled="!hasSelection">导出选中</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, ArrowDown } from '@element-plus/icons-vue'
import exportService from '../services/exportService'

// 定义props
const props = defineProps({
  tokens: {
    type: Array,
    required: true
  },
  selectedTokens: {
    type: Array,
    required: true
  }
})

// 导出状态
const exporting = ref(false)

// 计算是否有选中的Token
const hasSelection = computed(() => props.selectedTokens && props.selectedTokens.length > 0)

// 处理导出命令
const handleExport = async (command) => {
  if (exporting.value) return
  
  try {
    exporting.value = true
    
    if (command === 'all') {
      // 导出所有数据
      if (props.tokens.length === 0) {
        ElMessage.warning('没有可导出的数据')
        return
      }
      
      await exportService.exportAllTokens(props.tokens)
      ElMessage.success(`成功导出 ${props.tokens.length} 条数据`)
    } else if (command === 'selected') {
      // 导出选中数据
      if (!hasSelection.value) {
        ElMessage.warning('请先选择要导出的数据')
        return
      }
      
      await exportService.exportSelectedTokens(props.selectedTokens)
      ElMessage.success(`成功导出 ${props.selectedTokens.length} 条选中数据`)
    }
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error(`导出失败: ${error.message}`)
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.token-exporter {
  display: inline-block;
  margin-right: 10px;
}
</style> 