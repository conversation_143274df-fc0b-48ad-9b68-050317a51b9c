const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { logger } = require('../utils/logger');

/**
 * 获取随机昵称
 * 从网名.txt文件中随机选择一个昵称并返回
 */
router.get('/random', (req, res) => {
  try {
    // 网名文件路径
    const nicknamePath = path.resolve(process.cwd(), '..', '网名.txt');
    logger.info('网名文件路径:', nicknamePath);
    
    // 读取文件内容
    fs.readFile(nicknamePath, 'utf8', (err, data) => {
      if (err) {
        logger.error('读取网名文件失败:', err);
        return res.status(500).json({
          success: false,
          message: '读取网名文件失败'
        });
      }
      
      // 按行分割
      const nicknames = data.split('\n').filter(line => line.trim() !== '');
      
      if (nicknames.length === 0) {
        return res.status(404).json({
          success: false,
          message: '网名文件中没有有效的昵称'
        });
      }
      
      // 随机选择一个昵称
      const randomIndex = Math.floor(Math.random() * nicknames.length);
      const randomNickname = nicknames[randomIndex].trim();
      
      // 返回随机昵称
      res.json({
        success: true,
        nickname: randomNickname
      });
    });
  } catch (error) {
    logger.error('获取随机昵称失败:', error);
    res.status(500).json({
      success: false,
      message: `获取随机昵称失败: ${error.message}`
    });
  }
});

/**
 * 修改昵称
 * 通过外部API修改用户昵称
 */
router.post('/change', async (req, res) => {
  try {
    const { token, nickname } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: '缺少token参数'
      });
    }
    
    if (!nickname) {
      return res.status(400).json({
        success: false,
        message: '缺少nickname参数'
      });
    }
    
    // 调用外部API修改昵称
    const response = await axios.post('https://apiv3.yangkeduo.com/user/profile/update/nickname',
      {
        "nickname": nickname
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://apiv3.yangkeduo.com/user/profile/update/nickname?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回修改结果
    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    logger.error('修改昵称失败:', error);
    
    // 返回错误信息
    res.status(500).json({
      success: false,
      message: `修改昵称失败: ${error.message}`,
      error: error.response ? error.response.data : null
    });
  }
});

module.exports = router;
