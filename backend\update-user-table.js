const mysql = require('mysql2/promise');

async function updateUserTable() {
  const pool = await mysql.createPool({
    host: 'localhost',
    user: 'root',
    password: 'qq@666666',
    database: 'token_management'
  });

  try {
    // 修改email字段为可空
    await pool.query(
      'ALTER TABLE users MODIFY email VARCHAR(100) NULL'
    );
    console.log('用户表email字段修改成功');
  } catch (error) {
    console.error('修改失败:', error);
  } finally {
    await pool.end();
  }
}

updateUserTable(); 