const express = require('express');
const axios = require('axios');
// const { v4: uuidv4 } = require('uuid'); // 不再需要，因为值是固定的
// const { URLSearchParams } = require('url'); // 不再需要
// const logger = require('../utils/logger'); // 假设的日志工具 - 已替换为 console
const apiConfig = require('../config/externalApiConfig');

const router = express.Router();

// 使用集中配置替代硬编码的URL和凭证
const EXTERNAL_API_URL = apiConfig.endpoints.TOKEN.IMPORT;
const API_USERNAME_ENV = process.env.EXTERNAL_UPLOAD_USERNAME || apiConfig.credentials.USERNAME; // 保留环境变量定义但使用配置中的默认值
const API_TIMEOUT = apiConfig.timeout;
// const API_PASSWORD = process.env.EXTERNAL_UPLOAD_PASSWORD || 'lijinrong11'; // 未使用

// Helper to create Basic Auth header - Not needed
/*
const createBasicAuthHeader = (username, password) => {
  const credentials = `${username}:${password}`;
  const buffer = Buffer.from(credentials);
  return `Basic ${buffer.toString('base64')}`;
};
*/

// POST /api/upload/selected
router.post('/selected', async (req, res) => {
  const { tokens: selectedTokensFrontend } = req.body;

  if (!Array.isArray(selectedTokensFrontend) || selectedTokensFrontend.length === 0) {
    return res.status(400).json({ success: false, message: '未提供用于上传的 Token。' }); // 中文提示
  }

  // 映射前端 Token 结构到 API 期望的结构
  const tokensForApi = selectedTokensFrontend.map(token => ({
    uid: token.uid,
    token: token.token,
    username: token.user || '' // 这个 username (例如 bfyy123321) 似乎是 Token 特有的，而不是主用户的
  }));

  // --- 完全按照要求硬编码值 --- 
  const requestBody = {
    random_value: "your-random-value", // 使用固定的字符串
    username: apiConfig.credentials.USERNAME, // 使用配置中的用户名
    tokens: tokensForApi
  };

  // const authHeader = createBasicAuthHeader(API_USERNAME, API_PASSWORD); // 移除

  console.log(`尝试通过 JSON (固定值) 上传 ${tokensForApi.length} 个 Token。`); // 中文日志
  console.debug('上传请求体 (JSON):', JSON.stringify(requestBody, null, 2));

  try {
    const apiResponse = await axios.post(EXTERNAL_API_URL, requestBody, { // 发送 JSON 请求体
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*'
        // 移除特定的 User-Agent 和 Auth 头
        // 'User-Agent': 'python-requests/2.32.3' 
        // 'Authorization': authHeader 
      },
      timeout: API_TIMEOUT // 使用配置的超时时间
    });

    console.log('外部 API 响应状态:', apiResponse.status);
    console.debug('外部 API 响应数据:', apiResponse.data);

    // --- 响应处理 --- 
    let successMessage = '上传已处理，但响应格式未知。'; // 中文提示
    let uploadSuccess = false;

    if (apiResponse.status === 200 && apiResponse.data) {
        let responseText = '';
        if (typeof apiResponse.data === 'string') {
            try { responseText = JSON.parse(apiResponse.data); } catch(e) { responseText = apiResponse.data; } // 尝试解析 JSON 字符串或直接使用字符串
        } else if (typeof apiResponse.data.message === 'string') {
             responseText = apiResponse.data.message;
        } else {
            responseText = JSON.stringify(apiResponse.data); // 其他情况转为字符串
        }

        // 检查响应是否包含成功标识
        if (responseText.includes('成功') || responseText.includes('success')) {
             uploadSuccess = true;
             successMessage = responseText; // 使用实际的成功消息
             console.log(`外部 API 报告成功: ${successMessage}`);
        } else {
            uploadSuccess = false;
            successMessage = `外部 API 返回状态 200 但响应可能表示失败: ${responseText}`;
            console.warn(successMessage);
        }
    } else {
        successMessage = `外部 API 返回状态 ${apiResponse.status}。`;
        console.error(successMessage);
        uploadSuccess = false;
    }

    // 根据处理结果返回成功或失败
    if (uploadSuccess) {
        res.json({ success: true, message: successMessage, uploadedCount: tokensForApi.length });
    } else {
        res.status(apiResponse.status !== 200 ? apiResponse.status : 500).json({ success: false, message: successMessage, details: apiResponse.data }); // 返回合适的错误状态码
    }

  } catch (error) {
    console.error('调用外部上传 API 出错 (固定值测试):', error.message); // 中文日志
    if (error.response) {
      // 请求已发出，服务器返回了非 2xx 状态码
      console.error('外部 API 错误响应状态:', error.response.status);
      console.error('外部 API 错误响应数据:', error.response.data);
      res.status(error.response.status).json({ success: false, message: `外部 API 错误: ${error.response.data?.message || error.message}`, details: error.response.data });
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('未收到外部 API 响应:', error.request);
      res.status(504).json({ success: false, message: '未收到外部上传服务响应 (超时)。' }); // 中文提示
    } else {
      // 设置请求时触发了错误
      console.error('Axios 请求设置错误:', error.message);
      res.status(500).json({ success: false, message: `内部请求设置错误: ${error.message}` }); // 中文提示
    }
  }
});

module.exports = router; 