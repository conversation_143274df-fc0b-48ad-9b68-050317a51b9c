const pool = require('../database/db');
const { logger } = require('../utils/logger');

class User {
  /**
   * 根据ID获取用户
   * @param {number} id - 用户ID
   * @returns {Promise<Object|null>} 用户对象或null
   */
  static async getById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE id = ?',
        [id]
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      logger.error(`获取用户失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据用户名获取用户
   * @param {string} username - 用户名
   * @returns {Promise<Object|null>} 用户对象或null
   */
  static async getByUsername(username) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE username = ?',
        [username]
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      logger.error(`根据用户名获取用户失败 (username: ${username}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据邮箱获取用户
   * @param {string} email - 邮箱地址
   * @returns {Promise<Object|null>} 用户对象或null
   */
  static async getByEmail(email) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE email = ?',
        [email]
      );
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      logger.error(`根据邮箱获取用户失败 (email: ${email}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建新用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<number>} 新创建用户的ID
   */
  static async create(userData) {
    try {
      const { username, password, email, full_name, role, status } = userData;
      
      const [result] = await pool.query(
        `INSERT INTO users (username, password, email, full_name, role, status, created_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [username, password, email, full_name, role || 'user', status || 'active', new Date()]
      );
      
      return result.insertId;
    } catch (error) {
      logger.error(`创建用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户的最后登录时间
   * @param {number} id - 用户ID
   * @returns {Promise<boolean>} 是否成功
   */
  static async updateLastLogin(id) {
    try {
      await pool.query(
        'UPDATE users SET last_login = ? WHERE id = ?',
        [new Date(), id]
      );
      return true;
    } catch (error) {
      logger.error(`更新用户最后登录时间失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户密码
   * @param {number} id - 用户ID
   * @param {string} hashedPassword - 加密后的密码
   * @returns {Promise<boolean>} 是否成功
   */
  static async updatePassword(id, hashedPassword) {
    try {
      await pool.query(
        'UPDATE users SET password = ?, updated_at = ? WHERE id = ?',
        [hashedPassword, new Date(), id]
      );
      return true;
    } catch (error) {
      logger.error(`更新用户密码失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户角色
   * @param {number} id - 用户ID
   * @param {string} role - 新角色
   * @returns {Promise<boolean>} 是否成功
   */
  static async updateRole(id, role) {
    try {
      await pool.query(
        'UPDATE users SET role = ?, updated_at = ? WHERE id = ?',
        [role, new Date(), id]
      );
      return true;
    } catch (error) {
      logger.error(`更新用户角色失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户状态
   * @param {number} id - 用户ID
   * @param {string} status - 新状态
   * @returns {Promise<boolean>} 是否成功
   */
  static async updateStatus(id, status) {
    try {
      await pool.query(
        'UPDATE users SET status = ?, updated_at = ? WHERE id = ?',
        [status, new Date(), id]
      );
      return true;
    } catch (error) {
      logger.error(`更新用户状态失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取所有用户
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {Object} filters - 过滤条件
   * @returns {Promise<Object>} 用户列表和总数
   */
  static async getAll(page, limit, filters = {}) {
    try {
      let query = `
        SELECT id, username, email, full_name, role, status, created_at, last_login 
        FROM users 
        WHERE 1=1
      `;
      
      const params = [];
      
      if (filters.status) {
        query += ' AND status = ?';
        params.push(filters.status === 'active');
      }
      
      if (filters.role) {
        query += ' AND role = ?';
        params.push(filters.role);
      }
      
      if (filters.search) {
        query += ' AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }
      
      // 获取总数
      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total FROM (${query}) as t`,
        params
      );
      const total = countResult[0].total;
      
      // 添加分页
      query += ' ORDER BY id DESC LIMIT ? OFFSET ?';
      params.push(limit, (page - 1) * limit);
      
      const [rows] = await pool.query(query, params);
      
      return { users: rows, total };
    } catch (error) {
      logger.error(`获取所有用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 保存重置密码令牌
   * @param {number} id - 用户ID
   * @param {string} token - 重置令牌
   * @returns {Promise<boolean>} 是否成功
   */
  static async saveResetToken(id, token) {
    try {
      // 这里实际应该有一个password_resets表，为简化演示，我们暂时不实现
      // 在生产环境中，应该在专门的表中保存重置令牌
      logger.info(`保存重置令牌 (ID: ${id})`);
      return true;
    } catch (error) {
      logger.error(`保存重置令牌失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证重置令牌
   * @param {number} id - 用户ID
   * @param {string} token - 重置令牌
   * @returns {Promise<boolean>} 是否有效
   */
  static async validateResetToken(id, token) {
    try {
      // 这里实际应该验证保存在password_resets表中的令牌
      // 为简化演示，直接返回true
      logger.info(`验证重置令牌 (ID: ${id})`);
      return true;
    } catch (error) {
      logger.error(`验证重置令牌失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 清除重置令牌
   * @param {number} id - 用户ID
   * @returns {Promise<boolean>} 是否成功
   */
  static async clearResetToken(id) {
    try {
      // 这里实际应该删除password_resets表中的记录
      // 为简化演示，直接返回true
      logger.info(`清除重置令牌 (ID: ${id})`);
      return true;
    } catch (error) {
      logger.error(`清除重置令牌失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户个人资料
   * @param {number} id - 用户ID
   * @param {object} userData - 用户数据
   * @returns {Promise<boolean>} 是否成功
   */
  static async updateProfile(id, userData) {
    try {
      const { username, email, full_name } = userData;
      
      // 构建更新SQL
      const updates = [];
      const params = [];
      
      if (username !== undefined) {
        updates.push('username = ?');
        params.push(username);
      }
      
      if (email !== undefined) {
        updates.push('email = ?');
        params.push(email);
      }
      
      if (full_name !== undefined) {
        updates.push('full_name = ?');
        params.push(full_name);
      }
      
      // 添加更新时间
      updates.push('updated_at = ?');
      params.push(new Date());
      
      // 添加WHERE条件
      params.push(id);
      
      // 执行更新
      await pool.query(
        `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
        params
      );
      
      return true;
    } catch (error) {
      logger.error(`更新用户个人资料失败 (ID: ${id}): ${error.message}`);
      throw error;
    }
  }
}

module.exports = User; 