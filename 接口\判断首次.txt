GET https://api.pinduoduo.com/api/engels/review/my/review/info?pdduid= HTTP/1.1
Accept: */*
Accept-Language: zh-cn
Accesstoken: JGB6EA11ANWZ2UP3XTU75GRZQV24J6UZXNMPJW46Q6ULQLFGQODQ1216a75
Connection: Keep-Alive
Host: api.pinduoduo.com
Referer: https://api.pinduoduo.com/api/engels/review/my/review/info?pdduid=
User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.116 UBrowser/5.6.12150.8 Safari/537.36

=====================返回

HTTP/1.1 424 Failed Dependency
Connection: keep-alive
Content-Security-Policy-Report-Only: default-src 'none';script-src 'report-sample';report-uri https://tc.pinduoduo.com/sec.gif
Content-Type: application/json
Date: Tue, 01 Apr 2025 14:06:23 GMT
Server: nginx
Set-Cookie: api_uid=CkzllGfr8t9OfwCvlLGCAg==; expires=Thu, 31-Dec-37 23:55:55 GMT; domain=.pinduoduo.com; path=/
cip: **************
gw-ext: glbver=2
x-yak-request-id: 1743516383932-1897f146659333e6cd5451d73e35761f
yak-timeinfo: 1743516383932|2

{"error_msg":"","error_code":40001}
