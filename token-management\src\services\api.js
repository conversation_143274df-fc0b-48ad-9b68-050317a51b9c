import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api', // 直接指定完整路径
  timeout: 5000, // 减少超时时间
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    console.log('发送请求:', config.url, config.baseURL + config.url);
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status);
    return response.data;
  },
  error => {
    console.error('请求错误:', error);
    const { response } = error;
    if (response) {
      // 处理401错误：未授权，只清除token，不做页面跳转
      if (response.status === 401) {
        localStorage.removeItem('token');
        // 删除直接跳转的逻辑，改为统一在App.vue组件中处理
        // window.location.href = '/login';
      }
      return Promise.reject(response.data || 'Error');
    }
    return Promise.reject(error);
  }
);

export default api; 