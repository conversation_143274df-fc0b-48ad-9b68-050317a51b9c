POST http://aaaaa.pxxcp.com/wp-json/custom-token-plugin/v1/import HTTP/1.1
Accept: */*
Accept-Encoding: gzip, deflate
Connection: keep-alive
Content-Length: 322
Content-Type: application/json
Host: aaaaa.pxxcp.com
User-Agent: python-requests/2.32.3

{"random_value": "your-random-value", "username": "a15604402", "tokens": [{"uid": "5747403088486", "token": "RBBDCX6NO4OFWYZOCAUTBN45R33GG3SB4UACK5IXLLOY2YECQNOA1226629", "username": "bfyy123321"}, {"uid": "5748391135232", "token": "HBLCAUXSFSEEJ3LUZMAQFTCYUTKS3VEPYSMBFNKW2W6772Q32K7A120002e", "username": "bfyy123321"}]}

=====返回=================

HTTP/1.1 200 OK
Access-Control-Allow-Headers: Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type
Access-Control-Expose-Headers: X-WP-Total, X-WP-TotalPages, Link
Allow: POST
Cache-Control: no-store, no-cache, must-revalidate
Connection: keep-alive
Content-Type: application/json; charset=UTF-8
Date: Sat, 19 Apr 2025 17:20:00 GMT
Expires: Thu, 19 Nov 1981 08:52:00 GMT
Link: <http://aaaaa.pxxcp.com/index.php/wp-json/>; rel="https://api.w.org/"
Pragma: no-cache
Server: nginx
Set-Cookie: PHPSESSID=lnm7pj4u8eiv150jr06pr84gd8; path=/
X-Content-Type-Options: nosniff
X-Robots-Tag: noindex



"\u5bfc\u5165\u6210\u529f: 2"