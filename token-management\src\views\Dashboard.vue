<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1>欢迎使用 Token 管理系统</h1>
        <p>今天是 {{ currentDate }}，{{ welcomeMessage }}</p>
      </div>
      <div class="welcome-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><el-icon-refresh /></el-icon>刷新数据
        </el-button>
      </div>
    </div>
    
    <!-- 数据总览卡片 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card-container">
          <div class="data-card token-card">
            <div class="data-card-icon">
              <el-icon><el-icon-key /></el-icon>
            </div>
            <div class="data-card-content">
              <div class="data-card-value">{{ tokenStore.getTokenCount }}</div>
              <div class="data-card-title">Token总数</div>
            </div>
            <div class="data-card-trend" v-if="tokenTrend > 0">
              <el-icon class="trend-up"><el-icon-top /></el-icon>
              <span>{{ tokenTrend }}%</span>
            </div>
            <div class="data-card-trend" v-else>
              <el-icon class="trend-down"><el-icon-bottom /></el-icon>
              <span>{{ Math.abs(tokenTrend) }}%</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card-container">
          <div class="data-card backup-card">
            <div class="data-card-icon">
              <el-icon><el-icon-document-copy /></el-icon>
            </div>
            <div class="data-card-content">
              <div class="data-card-value">{{ tokenStore.getBackupCount }}</div>
              <div class="data-card-title">备份数量</div>
            </div>
            <div class="data-card-trend" v-if="backupTrend > 0">
              <el-icon class="trend-up"><el-icon-top /></el-icon>
              <span>{{ backupTrend }}%</span>
            </div>
            <div class="data-card-trend" v-else>
              <el-icon class="trend-down"><el-icon-bottom /></el-icon>
              <span>{{ Math.abs(backupTrend) }}%</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card-container">
          <div class="data-card expiry-card">
            <div class="data-card-icon">
              <el-icon><el-icon-timer /></el-icon>
            </div>
            <div class="data-card-content">
              <div class="data-card-value">{{ formatDate(tokenStore.getLastExpiryDate) }}</div>
              <div class="data-card-title">最近到期时间</div>
            </div>
            <div class="data-card-countdown">
              还有 {{ expiryDays }} 天
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="data-card-container">
          <div class="data-card status-card">
            <div class="data-card-icon">
              <el-icon><el-icon-data-line /></el-icon>
            </div>
            <div class="data-card-content">
              <div class="data-card-value">{{ tokenStatusTypes.length }}种</div>
              <div class="data-card-title">Token状态</div>
            </div>
            <div class="data-card-badges">
              <el-badge v-for="(count, index) in statusCounts" :key="index" :value="count" :type="getBadgeType(index)" class="status-badge" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Token状态分布</span>
              <div class="header-actions">
                <el-radio-group v-model="statusChartType" size="small">
                  <el-radio-button label="pie">饼图</el-radio-button>
                  <el-radio-button label="bar">柱状图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div v-if="statusChartType === 'pie'" class="chart-container">
            <div class="chart-placeholder pie-chart">
              <div class="pie-container">
                <div class="pie-legend">
                  <div v-for="(type, index) in tokenStatusTypes" :key="index" class="legend-item">
                    <span class="legend-color" :style="{backgroundColor: getPieColor(index)}"></span>
                    <span class="legend-label">{{ type }}: {{ statusCounts[index] }}</span>
                  </div>
                </div>
                <div class="pie">
                  <div v-for="(count, index) in statusCounts" :key="index" 
                       class="pie-slice" 
                       :style="getPieSliceStyle(index)">
                  </div>
                  <div class="pie-center">{{ statusCounts.reduce((sum, count) => sum + count, 0) }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="chart-container">
            <div class="chart-placeholder bar-chart">
              <div class="bar-container">
                <div v-for="(count, index) in statusCounts" :key="index" 
                    class="bar-item">
                  <div class="bar-column" 
                       :style="{ height: getBarHeight(count), backgroundColor: getPieColor(index) }">
                    <div class="bar-value">{{ count }}</div>
                  </div>
                  <div class="bar-label">{{ tokenStatusTypes[index] }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>Token使用趋势</span>
              <div class="header-actions">
                <el-radio-group v-model="trendTimeRange" size="small">
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="year">全年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder line-chart">
              <div class="line-container">
                <div class="line-header">
                  <div class="line-title">{{ trendTimeRangeTitle }}</div>
                  <div class="line-subtitle">Token使用量变化趋势</div>
                </div>
                <div class="line-body">
                  <div class="line-chart-container">
                    <div class="line-points">
                      <div v-for="(value, index) in getTrendData()" :key="index" 
                           class="point" 
                           :style="{ left: `${index / (getTrendData().length - 1) * 100}%`, bottom: `${value / 50 * 100}%` }">
                        <div class="point-tooltip">{{ value }}</div>
                      </div>
                    </div>
                    <svg class="line-svg" viewBox="0 0 100 50">
                      <polyline class="line-path" :points="getTrendPoints()" />
                    </svg>
                    <div class="line-area"></div>
                  </div>
                </div>
                <div class="line-footer">
                  <div v-for="(label, index) in getTrendLabels()" :key="index" 
                       class="line-label" 
                       :style="{ left: `${index / (getTrendLabels().length - 1) * 100}%` }">
                    {{ label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快捷操作区域 -->
    <el-card shadow="hover" class="quick-action-card">
      <template #header>
        <div class="card-header">
          <span><el-icon><el-icon-magic-stick /></el-icon> 快捷操作</span>
        </div>
      </template>
      <div class="quick-actions">
        <div class="quick-action-item" @click="navigateTo('/token-reset')">
          <div class="quick-action-icon token-reset-icon">
            <el-icon><el-icon-refresh-right /></el-icon>
          </div>
          <span>Token重置</span>
          <div class="quick-action-desc">批量重置和更新Token</div>
        </div>
        <div class="quick-action-item" @click="navigateTo('/token-auto-reset')">
          <div class="quick-action-icon auto-reset-icon">
            <el-icon><el-icon-alarm-clock /></el-icon>
          </div>
          <span>自动重置</span>
          <div class="quick-action-desc">设置定时自动重置规则</div>
        </div>
        <div class="quick-action-item" @click="navigateTo('/token-backup')">
          <div class="quick-action-icon backup-icon">
            <el-icon><el-icon-document-copy /></el-icon>
          </div>
          <span>Token备份</span>
          <div class="quick-action-desc">备份和恢复Token数据</div>
        </div>
        <div class="quick-action-item" @click="navigateTo('/settings')">
          <div class="quick-action-icon settings-icon">
            <el-icon><el-icon-set-up /></el-icon>
          </div>
          <span>系统设置</span>
          <div class="quick-action-desc">配置系统和用户参数</div>
        </div>
      </div>
    </el-card>
    
    <!-- 最近操作记录 -->
    <el-row>
      <el-col :span="24">
        <el-card shadow="hover" class="table-card">
          <template #header>
            <div class="card-header">
              <span>最近操作记录</span>
              <div class="header-actions">
                <el-button type="success" text @click="exportActivities">
                  <el-icon><el-icon-download /></el-icon>导出
                </el-button>
                <el-button type="primary" text @click="navigateTo('/activities')">
                  <el-icon><el-icon-more /></el-icon>查看更多
                </el-button>
              </div>
            </div>
          </template>
          <el-table :data="recentActivities" style="width: 100%" stripe>
            <el-table-column prop="time" label="时间" width="180" sortable />
            <el-table-column prop="type" label="操作类型" width="120">
              <template #default="scope">
                <el-tag :type="getActivityTypeTag(scope.row.type)">{{ scope.row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="操作内容" />
            <el-table-column prop="operator" label="操作人" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">{{ scope.row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button type="primary" text size="small" @click="viewActivityDetail(scope.row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 活动详情对话框 -->
    <el-dialog v-model="activityDetailVisible" title="操作详情" width="50%">
      <el-descriptions border :column="2">
        <el-descriptions-item label="操作时间">{{ selectedActivity?.time }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ selectedActivity?.type }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ selectedActivity?.operator }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ selectedActivity?.status }}</el-descriptions-item>
        <el-descriptions-item label="操作内容" :span="2">{{ selectedActivity?.content }}</el-descriptions-item>
        <el-descriptions-item label="详细信息" :span="2">
          <div class="activity-detail-content">
            <p>这里是操作的详细信息内容，可以展示操作的详细步骤和结果。</p>
            <p>包含Token ID、影响范围、操作结果等详细记录。</p>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTokenStore } from '@/store/token'
import { ElMessage } from 'element-plus'

// 如果安装了echarts和vueuse/core，可以取消注释下面的代码
// import * as echarts from 'echarts'
// import { useElementSize, useWindowSize } from '@vueuse/core'

const tokenStore = useTokenStore()
const router = useRouter()

// 欢迎信息
const welcomeMessages = [
  '今天Token管理状态良好',
  '您有5个Token即将过期',
  '系统运行正常',
  '有新的系统更新可用'
]
const welcomeMessage = ref(welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)])
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' })
})

// Token相关数据
const tokenTrend = ref(12.5) // 模拟数据，实际应从API获取
const backupTrend = ref(-4.2) // 模拟数据，实际应从API获取
const expiryDays = computed(() => {
  const today = new Date()
  const expiryDate = new Date(tokenStore.getLastExpiryDate)
  const diffTime = expiryDate - today
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

// Token状态数据
const tokenStatusTypes = ref(['正常', '过期', '待激活'])
const statusCounts = ref([65, 12, 23]) // 模拟数据

// 图表相关数据
const statusChartRef = ref(null)
const trendChartRef = ref(null)
const statusChartType = ref('pie')
const trendTimeRange = ref('month')
let statusChart = null
let trendChart = null

// 监听图表类型变化
watch(statusChartType, (newVal) => {
  if (statusChart) {
    renderStatusChart()
  }
})

watch(trendTimeRange, (newVal) => {
  if (trendChart) {
    renderTrendChart()
  }
})

// 格式化日期函数
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取状态徽章类型
const getBadgeType = (index) => {
  const types = ['success', 'danger', 'warning']
  return types[index] || 'info'
}

// 获取操作类型标签样式
const getActivityTypeTag = (type) => {
  const typeMap = {
    'Token重置': 'warning',
    'Token备份': 'success',
    'Token导入': 'primary',
    'Token删除': 'danger'
  }
  return typeMap[type] || 'info'
}

// 活动记录详情
const activityDetailVisible = ref(false)
const selectedActivity = ref(null)

const viewActivityDetail = (activity) => {
  selectedActivity.value = activity
  activityDetailVisible.value = true
}

// 渲染状态分布图表
const renderStatusChart = () => {
  if (!statusChartRef.value) return

  /* 以下代码在未安装依赖时注释掉，安装后可取消注释
  if (!statusChart) {
    statusChart = echarts.init(statusChartRef.value)
  }

  let option
  
  if (statusChartType.value === 'pie') {
    option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: tokenStatusTypes.value
      },
      series: [
        {
          name: 'Token状态',
          type: 'pie',
          radius: '60%',
          center: ['50%', '50%'],
          data: tokenStatusTypes.value.map((type, index) => ({
            value: statusCounts.value[index],
            name: type
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  } else {
    option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: tokenStatusTypes.value
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'Token数量',
          type: 'bar',
          data: statusCounts.value
        }
      ]
    }
  }

  option && statusChart.setOption(option)
  */
}

// 渲染趋势图表
const renderTrendChart = () => {
  if (!trendChartRef.value) return
  
  /* 以下代码在未安装依赖时注释掉，安装后可取消注释
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }

  // 根据时间范围生成不同的数据
  let xAxisData = []
  let seriesData = []

  if (trendTimeRange.value === 'week') {
    xAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    seriesData = [12, 15, 10, 23, 19, 8, 11]
  } else if (trendTimeRange.value === 'month') {
    xAxisData = Array.from({length: 30}, (_, i) => `${i+1}日`)
    seriesData = Array.from({length: 30}, () => Math.floor(Math.random() * 30 + 10))
  } else {
    xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    seriesData = [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149]
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'Token使用量',
        type: 'line',
        data: seriesData,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.8)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ]
          }
        }
      }
    ]
  }

  option && trendChart.setOption(option)
  */
}

// 刷新数据
const refreshData = () => {
  // 这里应该是实际刷新数据的逻辑
  ElMessage.success('数据已刷新')
}

// 导出活动记录
const exportActivities = () => {
  // 这里应该是实际导出功能的逻辑
  ElMessage.success('活动记录已导出')
}

// 页面跳转
const navigateTo = (path) => {
  router.push(path)
}

// 模拟最近操作记录数据
const recentActivities = ref([
  {
    time: '2024-03-29 14:30:45',
    type: 'Token重置',
    content: '重置了50个Token',
    operator: '管理员',
    status: '成功'
  },
  {
    time: '2024-03-28 16:12:33',
    type: 'Token备份',
    content: '备份了所有Token数据',
    operator: '管理员',
    status: '成功'
  },
  {
    time: '2024-03-27 09:45:21',
    type: 'Token导入',
    content: '导入了100个新Token',
    operator: '管理员',
    status: '成功'
  },
  {
    time: '2024-03-26 11:28:39',
    type: 'Token删除',
    content: '删除了5个过期Token',
    operator: '管理员',
    status: '成功'
  },
  {
    time: '2024-03-25 08:15:22',
    type: 'Token备份',
    content: '手动创建了系统备份',
    operator: '管理员',
    status: '成功'
  }
])

// 在组件挂载后初始化图表
onMounted(() => {
  // 初始化图表
  setTimeout(() => {
    renderStatusChart()
    renderTrendChart()
  }, 100)

  // 窗口大小改变时重新渲染图表
  /* 以下代码在未安装依赖时注释掉，安装后可取消注释
  window.addEventListener('resize', () => {
    statusChart && statusChart.resize()
    trendChart && trendChart.resize()
  })
  */
})

// 获取饼图颜色
const getPieColor = (index) => {
  const colors = ['#1890ff', '#f5222d', '#faad14', '#52c41a', '#722ed1']
  return colors[index % colors.length]
}

// 获取饼图切片样式
const getPieSliceStyle = (index) => {
  const total = statusCounts.value.reduce((sum, count) => sum + count, 0)
  let startAngle = 0
  
  // 计算前面所有切片的总和角度
  for (let i = 0; i < index; i++) {
    startAngle += (statusCounts.value[i] / total) * 360
  }
  
  const angle = (statusCounts.value[index] / total) * 360
  const endAngle = startAngle + angle
  
  // 将角度转换为CSS渐变角度
  return {
    background: `conic-gradient(${getPieColor(index)} ${startAngle}deg ${endAngle}deg, transparent ${endAngle}deg 360deg)`,
    zIndex: 3 - index // 确保较小的切片显示在上层
  }
}

// 获取柱状图高度
const getBarHeight = (value) => {
  const max = Math.max(...statusCounts.value)
  return `${(value / max) * 200}px`
}

// 获取趋势图数据
const getTrendData = () => {
  if (trendTimeRange.value === 'week') {
    return [12, 15, 10, 23, 19, 8, 11]
  } else if (trendTimeRange.value === 'month') {
    return [5, 10, 15, 20, 25, 30, 25, 20, 15, 35, 40, 30]
  } else {
    return [20, 18, 19, 23, 29, 33, 31, 12, 44, 32, 9, 15]
  }
}

// 获取趋势图点的SVG坐标
const getTrendPoints = () => {
  const data = getTrendData()
  const max = Math.max(...data)
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = 50 - (value / max) * 50
    return `${x},${y}`
  })
  return points.join(' ')
}

// 获取趋势图标签
const getTrendLabels = () => {
  if (trendTimeRange.value === 'week') {
    return ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  } else if (trendTimeRange.value === 'month') {
    return ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
  } else {
    return ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  }
}

// 获取趋势时间范围标题
const trendTimeRangeTitle = computed(() => {
  const map = {
    'week': '本周',
    'month': '本月',
    'year': '全年'
  }
  return map[trendTimeRange.value]
})
</script>

<style scoped>
.dashboard-container {
  padding: 16px;
}

/* 欢迎区域样式 */
.welcome-section {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.welcome-content h1 {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 500;
}

.welcome-content p {
  font-size: 14px;
  opacity: 0.9;
}

/* 数据卡片样式 */
.data-card-container {
  margin-bottom: 24px;
  height: 100%;
}

.data-card {
  padding: 24px;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.token-card {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
}

.backup-card {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
}

.expiry-card {
  background: linear-gradient(135deg, #faad14 0%, #d4b106 100%);
  color: white;
}

.status-card {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  color: white;
}

.data-card-icon {
  font-size: 28px;
  margin-bottom: 16px;
}

.data-card-content {
  margin-bottom: 16px;
}

.data-card-value {
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

.data-card-title {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 8px;
}

.data-card-trend {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.trend-up {
  color: #52c41a;
  margin-right: 4px;
}

.trend-down {
  color: #f5222d;
  margin-right: 4px;
}

.data-card-countdown {
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  align-self: flex-start;
}

.data-card-badges {
  display: flex;
  gap: 8px;
}

/* 图表样式 */
.chart-row {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 24px;
  height: 100%;
}

.chart-container {
  height: 350px;
  width: 100%;
  position: relative;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 饼图样式 */
.pie-chart {
  padding: 20px;
}

.pie-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.pie-legend {
  width: 30%;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.legend-color {
  display: block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  margin-right: 8px;
}

.legend-label {
  font-size: 14px;
}

.pie {
  position: relative;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: rotate 2s ease-out;
}

@keyframes rotate {
  from { transform: rotate(-90deg); }
  to { transform: rotate(0); }
}

.pie-slice {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: transform 0.3s;
}

.pie-slice:hover {
  transform: scale(1.05);
}

.pie-center {
  position: absolute;
  width: 100px;
  height: 100px;
  background: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* 柱状图样式 */
.bar-chart {
  padding: 20px;
}

.bar-container {
  width: 100%;
  height: 250px;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  padding-bottom: 40px;
  position: relative;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 60px;
}

.bar-column {
  width: 40px;
  border-radius: 6px 6px 0 0;
  position: relative;
  transition: all 0.5s;
  animation: grow 1.5s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes grow {
  from { height: 0; }
}

.bar-column:hover {
  transform: scaleY(1.05);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.bar-value {
  position: absolute;
  top: -25px;
  color: #606266;
  font-weight: bold;
}

.bar-label {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

/* 折线图样式 */
.line-chart {
  padding: 20px 30px;
}

.line-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.line-header {
  margin-bottom: 15px;
}

.line-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.line-subtitle {
  font-size: 14px;
  color: #909399;
}

.line-body {
  flex: 1;
  position: relative;
}

.line-chart-container {
  width: 100%;
  height: 200px;
  position: relative;
  margin-bottom: 30px;
  overflow: hidden;
}

.line-points {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 3;
}

.point {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: absolute;
  transform: translate(-50%, 50%);
  transition: all 0.3s;
  z-index: 5;
}

.point:hover {
  transform: translate(-50%, 50%) scale(1.5);
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
}

.point-tooltip {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
}

.point:hover .point-tooltip {
  opacity: 1;
}

.line-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.line-path {
  fill: none;
  stroke: #1890ff;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: dash 2s ease-out forwards;
}

@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}

.line-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(64, 158, 255, 0.2), transparent);
  z-index: 1;
  clip-path: polygon(0% 100%, 100% 100%, 100% 30%, 0% 70%);
  animation: fill 2s ease-out forwards;
}

@keyframes fill {
  from {
    clip-path: polygon(0% 100%, 0% 100%, 0% 100%, 0% 100%);
  }
}

.line-footer {
  height: 30px;
  position: relative;
}

.line-label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 12px;
  color: #606266;
}

/* 快捷操作区域 */
.quick-action-card {
  margin-bottom: 24px;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 15px;
  background-color: #f5f7fa;
  border-radius: 12px;
  width: calc(25% - 15px);
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.quick-action-item:hover {
  background-color: #fff;
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #e6f7ff;
}

.quick-action-item:hover .quick-action-icon {
  transform: scale(1.1);
}

.quick-action-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.quick-action-icon .el-icon {
  font-size: 30px;
  color: white;
}

.token-reset-icon {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.auto-reset-icon {
  background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
}

.backup-icon {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.settings-icon {
  background: linear-gradient(135deg, #faad14 0%, #d4b106 100%);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
}

.quick-action-item span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 8px 0;
}

.quick-action-item .quick-action-desc {
  font-size: 12px;
  color: #909399;
  text-align: center;
  margin-top: 4px;
}

.quick-action-item::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.5s;
}

.quick-action-item:hover::after {
  opacity: 1;
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 24px;
}

/* 活动详情样式 */
.activity-detail-content {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .welcome-actions {
    margin-top: 16px;
  }
  
  .quick-action-item {
    width: calc(50% - 8px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 