const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const TokenController = require('../controllers/token');
const TokenBackupController = require('../controllers/tokenBackup');

// 获取所有token（带分页和筛选）
router.get('/', authenticate, TokenController.getAllTokens);

// 获取单个token
router.get('/:id', authenticate, TokenController.getTokenById);

// 创建新token
router.post('/', authenticate, TokenController.createToken);

// 批量创建token
router.post('/batch', authenticate, TokenController.batchCreateTokens);

// 更新token
router.put('/:id', authenticate, TokenController.updateToken);

// 删除token
router.delete('/:id', authenticate, TokenController.deleteToken);

// 批量删除token
router.post('/batch-delete', authenticate, TokenController.batchDeleteTokens);

// 重置token的值
router.post('/:id/reset', authenticate, TokenController.resetToken);

// 批量重置token
router.post('/batch-reset', authenticate, TokenController.batchResetTokens);

// 验证token的合法性
router.post('/validate', authenticate, TokenController.validateToken);

// 导出token（CSV/Excel）
router.get('/export/:format', authenticate, TokenController.exportTokens);

// 导入token
router.post('/import', authenticate, TokenController.importTokens);

// 获取token统计信息
router.get('/stats/overview', authenticate, TokenController.getTokenStats);

// 优化的查询在线状态API
router.post('/query/online-optimized', authenticate, TokenController.queryOnlineStatusOptimized);

// 查询Token用户信息API（头像、昵称）
router.post('/query/userinfo', authenticate, TokenController.queryTokenUserInfo);

// 查询Token订单信息API
router.post('/query/order', authenticate, TokenController.queryTokenOrderInfo);

// 测试用，暂时跳过身份验证的版本
router.post('/test/query/online-optimized', TokenController.queryOnlineStatusOptimized);

// 测试用，查询用户信息（无需身份验证）
router.post('/test/query/userinfo', TokenController.queryTokenUserInfo);

// 测试用，查询订单信息（无需身份验证）
router.post('/test/query/order', TokenController.queryTokenOrderInfo);

// 删除订单API
router.post('/delete-order', authenticate, TokenController.deleteOrder);

// 批量删除订单API
router.post('/batch-delete-orders', authenticate, TokenController.batchDeleteOrders);

// 新Token置入账号内订单API
router.post('/place-order', authenticate, TokenController.placeOrder);

// 测试用，新Token置入订单（无需身份验证）
router.post('/test/place-order', TokenController.placeOrder);

// 测试用，删除订单（无需身份验证）
router.post('/test/delete-order', TokenController.deleteOrder);

// 测试用，批量删除订单（无需身份验证）
router.post('/test/batch-delete-orders', TokenController.batchDeleteOrders);

// 通过UID创建备份（专为前端设计）
router.post('/create-backup-by-uid', authenticate, TokenBackupController.createBackupByUid);

module.exports = router; 