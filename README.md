# Token 管理系统

一个现代化的 Token 管理解决方案，基于 Vue 3 + Node.js + Express + MySQL 构建。

## 项目概览

Token 管理系统是一个全栈应用，用于创建、管理、备份和重置各类 Token。系统提供完整的用户认证、权限控制、数据统计和系统设置功能，适用于需要大量管理 API 密钥、访问令牌或其他凭证的团队或个人。

## 主要功能

- **Token 管理**：创建、编辑、删除、查询和批量操作 Token
- **订单管理**：查询、显示和删除与Token关联的订单
- **在线状态查询**：实时检查Token的在线状态和有效性
- **备份管理**：备份关键 Token，支持按需恢复
- **用户认证**：完整的用户注册、登录和权限管理
- **数据统计**：提供详细的数据分析和可视化图表
- **系统设置**：灵活配置系统参数和用户偏好
- **云端同步**：支持Token数据的云端备份和同步
- **头像与昵称管理**：查询和修改Token关联的头像和昵称
- **批量导入导出**：支持Excel和TXT格式的Token数据批量导入导出

## 技术栈

### 前端
- Vue 3 (Composition API)
- Vue Router 4
- Pinia (状态管理)
- Element Plus (UI 组件库)
- Axios (HTTP 客户端)
- ECharts (数据可视化)
- Vite (构建工具)
- TypeScript
- XLSX (Excel文件处理)
- Crypto-JS (数据加密)

### 后端
- Node.js
- Express.js
- MySQL (数据库)
- JWT (用户认证)
- Winston (日志记录)
- Bcrypt (密码加密)
- Multer (文件上传)
- Archiver (文件压缩)
- Cors (跨域资源共享)
- Helmet (安全增强)
- Dotenv (环境变量管理)

## 项目结构

```
/
├── backend/            # 后端项目
│   ├── config/         # 配置文件
│   │   ├── database.js # 数据库配置
│   │   └── index.js    # 主配置文件
│   ├── controllers/    # 控制器
│   │   ├── auth.js     # 认证控制器
│   │   ├── backup.js   # 备份控制器
│   │   ├── reset.js    # 重置控制器
│   │   ├── settings.js # 设置控制器
│   │   ├── stats.js    # 统计控制器
│   │   ├── token.js    # Token控制器
│   │   └── tokenBackup.js # Token备份控制器
│   ├── database/       # 数据库相关
│   │   ├── db.js       # 数据库连接
│   │   └── schema.sql  # 数据库结构
│   ├── logs/           # 日志文件
│   ├── middleware/     # 中间件
│   │   ├── auth.js     # 认证中间件
│   │   └── errorHandler.js # 错误处理中间件
│   ├── models/         # 数据模型
│   │   ├── activityLog.js # 活动日志模型
│   │   ├── backup.js   # 备份模型
│   │   ├── reset.js    # 重置模型
│   │   ├── setting.js  # 设置模型
│   │   ├── token.js    # Token模型
│   │   ├── user.js     # 用户模型
│   │   └── userPreference.js # 用户偏好模型
│   ├── routes/         # 路由定义
│   │   ├── auth.js     # 认证路由
│   │   ├── avatar.js   # 头像路由
│   │   ├── avatarRoutes.js # 头像API路由
│   │   ├── backup.js   # 备份路由
│   │   ├── cloud.js    # 云服务路由
│   │   ├── index.js    # 路由入口
│   │   ├── nickname.js # 昵称路由
│   │   ├── reset.js    # 重置路由
│   │   ├── settings.js # 设置路由
│   │   ├── stats.js    # 统计路由
│   │   ├── token.js    # Token路由
│   │   └── upload.js   # 上传路由
│   ├── check-admin.js  # 管理员检查
│   └── server.js       # 服务器入口
├── token-management/   # 前端项目
│   ├── dist/           # 构建输出目录
│   ├── public/         # 静态资源
│   ├── server/         # 服务器相关
│   │   └── routes/     # 服务器路由
│   ├── src/            # 源代码
│   │   ├── api/        # API接口
│   │   ├── assets/     # 项目资源文件
│   │   ├── components/ # 公共组件
│   │   ├── composables/ # 组合式API
│   │   ├── router/     # 路由配置
│   │   ├── services/   # API服务
│   │   ├── store/      # 状态管理
│   │   ├── utils/      # 工具函数
│   │   ├── views/      # 页面组件
│   │   ├── App.vue     # 根组件
│   │   └── main.js     # 入口文件
│   ├── package.json    # 项目依赖
│   └── vite.config.js  # Vite配置
├── 头像库/              # 头像资源目录
├── 接口/                # API接口文档
└── README.md           # 项目总体说明
```

## 功能模块详情

### 首页 (Dashboard)

首页展示系统核心指标：
- Token 总数量
- 备份数量
- 最近到期时间
- Token 状态分布图表
- 最近操作记录

### Token 重置与订单管理

此模块提供以下功能：

**Token管理功能：**
- 导入 Token 数据（支持 Excel 和 TXT 格式）
- 显示 UID 和 Token 信息
- 支持批量操作：重置、删除、导出
- 搜索筛选功能
- 查询Token在线状态（通过API验证Token有效性）
- 查询Token的用户头像和昵称

**订单管理功能：**
- 查询关联Token的订单信息
- 显示订单详情（订单号、商品、金额、状态等）
- 删除已评价的订单（符合平台要求的可删除订单）
- 批量删除订单功能
- 订单状态筛选和分类展示
- 订单删除进度和结果实时显示
- 商家信息显示（商家名称、订单关联信息）

### Token 备份

Token 备份页面提供以下功能：
- 创建新备份
- 恢复备份
- 查看备份详情
- 自动备份设置
- 下载备份
- 关联ID显示（支持"重置前"和"重置后"备份关联显示）
- 双备份标识（同一关联ID同时有"重置前"和"重置后"备份时显示）
- 状态过滤功能

### 系统设置

设置页面提供以下功能：
- 基本设置（系统名称、主题、语言等）
- 用户设置（密码修改等）
- 通知设置（邮件通知、到期提醒等）
- 数据管理（导入导出、清理）

## 快速开始

### 前置条件

- Node.js (v16 或更高版本)
- MySQL (v8 或更高版本)
- npm 或 yarn

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/token-management-system.git
cd token-management-system
```

2. 安装后端依赖并启动
```bash
cd backend
npm install
# 配置环境变量
cp .env.example .env
# 编辑 .env 文件设置数据库连接信息
```

3. 创建数据库和表
```bash
# 创建数据库
mysql -u your_db_user -p -e "CREATE DATABASE IF NOT EXISTS token_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
# 创建数据库表
mysql -u your_db_user -p token_management < backend/database/schema.sql
```

4. 启动后端服务
```bash
cd backend
npm run dev
```

5. 安装前端依赖并启动
```bash
cd token-management
npm install
# 配置环境变量
cp .env.example .env.development
# 启动服务
npm run dev
```

前端应用将在 http://localhost:5174 运行，后端 API 将在 http://localhost:3000 提供服务。

### 开发模式

在开发过程中，你可以使用以下命令：

```bash
# 后端开发模式（支持热重载）
cd backend
npm run dev

# 前端开发模式（支持热重载）
cd token-management
npm run dev
```

## 环境配置

### 后端 (.env)
```
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=token_management

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_refresh_secret
JWT_RESET_SECRET=your_reset_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_USE_COOKIES=false

# 跨域配置
CORS_ORIGIN=http://localhost:5174

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/server.log
```

### 前端 (.env.development)
```
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=Token管理系统
VITE_ENABLE_MOCK=false
VITE_UPLOAD_URL=http://localhost:3000/api/upload
```

## 部署指南

### 后端部署

1. 准备服务器环境 (Node.js, MySQL)
2. 配置环境变量
3. 构建生产版本
```bash
cd backend
npm install
NODE_ENV=production npm start
```

### 前端部署

1. 构建生产版本
```bash
cd token-management
npm install
npm run build
```
2. 将 `dist` 目录部署到 Nginx 或其他静态文件服务器

## 系统截图

![Dashboard](docs/screenshots/dashboard.png)
*仪表盘界面*

![Token Management](docs/screenshots/token-management.png)
*Token 管理界面*

![Backup Management](docs/screenshots/backup-management.png)
*备份管理界面*

![Statistics](docs/screenshots/statistics.png)
*数据统计界面*

## 更新记录

### [v1.4.0] - 2025-04-02
- 完善订单管理功能
  - 添加订单批量删除功能
  - 优化订单删除API集成
  - 修复订单删除API处理逻辑
  - 增强错误处理和用户提示
- 改进用户界面
  - 添加商家名称显示在订单列表中
  - 优化删除进度和结果显示
  - 添加操作确认对话框
- 代码优化
  - 解决重复变量声明问题
  - 重构Token处理逻辑
  - 优化API请求处理
  - 改进错误处理机制

### [v1.3.0] - 2023-12-20
- 添加 Token 状态管理功能
- 实现完整的认证和权限系统
- 完善后端 API 架构
- 添加重置记录管理功能
- 增强数据统计和可视化功能
- 添加云端同步功能
- 优化头像和昵称管理

### [v1.2.0] - 2023-10-12
- 添加 Token 状态筛选功能
- 优化备份管理界面
- 添加 Token 批量操作功能
- 添加在线状态检查功能
- 改进数据导入导出功能
- 修复已知 BUG

### [v1.1.0] - 2023-08-01
- 添加备份管理功能
- 添加统计图表功能
- 优化用户界面和后端 API
- 添加用户偏好设置
- 改进数据安全性

### [v1.0.0] - 2023-06-15
- 初始版本发布
- 基础 Token 管理功能
- 用户认证系统
- 简单的数据统计

## 贡献指南

我们欢迎并感谢任何形式的贡献。如果您想为这个项目做出贡献，请遵循以下步骤：

1. Fork 该仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启一个 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 联系方式

项目负责人 - [@yourusername](https://github.com/yourusername)

项目链接: [https://github.com/yourusername/token-management-system](https://github.com/yourusername/token-management-system)

如有问题或建议，请通过以下方式联系我们：
- 提交 [Issue](https://github.com/yourusername/token-management-system/issues)
- 发送邮件至 [<EMAIL>](mailto:<EMAIL>)
- 加入我们的讨论组 [Discussions](https://github.com/yourusername/token-management-system/discussions)