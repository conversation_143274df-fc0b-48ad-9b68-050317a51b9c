@echo off
echo ===================================================================
echo Token����ϵͳ - ���ݿ����ú�ǰ����������
echo ===================================================================
echo.

:MENU
echo ��ѡ��Ҫִ�еĲ���:
echo [1] ��װMySQL (�����δ��װ)
echo [2] ����MySQL���ݿ�
echo [3] ����ǰ��ģ������
echo [4] ȫ��ִ��
echo [5] �˳�
echo.

set /p choice=������ѡ����:

if "%choice%"=="1" goto INSTALL_MYSQL
if "%choice%"=="2" goto SETUP_DATABASE
if "%choice%"=="3" goto CLEANUP_FRONTEND
if "%choice%"=="4" goto EXECUTE_ALL
if "%choice%"=="5" goto EXIT

echo ��Чѡ�������ѡ��
goto MENU

:INSTALL_MYSQL
echo.
echo ===================================================================
echo ��װMySQL
echo ===================================================================
echo.
echo �밴�����²��谲װMySQL:
echo 1. ���� https://dev.mysql.com/downloads/installer/
echo 2. ����MySQL��װ����
echo 3. ���а�װ����ѡ��"Developer Default"��"Server only"ѡ��
echo 4. ���������õ�root���룬�Ժ���Ҫ�õ�
echo 5. ��ɰ�װ���뷵�ش˹��߼�����������
echo.
echo ��װ��ɺ󣬰����������...
pause > nul
goto MENU

:SETUP_DATABASE
echo.
echo ===================================================================
echo ����MySQL���ݿ�
echo ===================================================================
echo.

echo ������MySQL��װ·�� (Ĭ��: C:\Program Files\MySQL\MySQL Server 8.0):
set MYSQL_PATH=C:\Program Files\MySQL\MySQL Server 8.0
set /p MYSQL_PATH="MySQL��װ·�� [%MYSQL_PATH%]: "

echo.
echo ������MySQL root�û�����:
set /p MYSQL_PWD=MySQL root����: 

echo.
echo �������ݿ����ر�...
"%MYSQL_PATH%\bin\mysql" --user=root --password="%MYSQL_PWD%" < backend\database\schema.sql

if %ERRORLEVEL% NEQ 0 (
    echo ����: MySQL���ݿⴴ��ʧ�ܡ�
    echo ����MySQL�Ƿ���ȷ��װ���Լ������Ƿ���ȷ��
    pause
    goto MENU
) else (
    echo ���ݿⴴ���ɹ���
)

echo.
echo ����.env�ļ�...
echo # ���������� > backend\.env
echo PORT=3000 >> backend\.env
echo NODE_ENV=development >> backend\.env
echo. >> backend\.env
echo # ���ݿ����� >> backend\.env
echo DB_HOST=localhost >> backend\.env
echo DB_PORT=3306 >> backend\.env
echo DB_USER=root >> backend\.env
echo DB_PASSWORD=%MYSQL_PWD% >> backend\.env
echo DB_NAME=token_management >> backend\.env
echo. >> backend\.env
echo # JWT���� >> backend\.env
echo JWT_SECRET=token_management_jwt_secret_key >> backend\.env
echo JWT_EXPIRES_IN=24h >> backend\.env
echo. >> backend\.env
echo # �������� >> backend\.env
echo CORS_ORIGIN=http://localhost:5173 >> backend\.env

echo ���������ļ�(.env)�����ɹ���
echo.
echo �����������...
pause > nul
goto MENU

:CLEANUP_FRONTEND
echo.
echo ===================================================================
echo ����ǰ��ģ������
echo ===================================================================
echo.

echo ����ǰ�˻��������ļ�...
echo VITE_API_BASE_URL=http://localhost:3000/api > token-management\.env.development
echo VITE_APP_TITLE=Token����ϵͳ >> token-management\.env.development

echo ǰ�˻��������ļ������ɹ���
echo.
echo ģ����������֮ǰ�Ĺ�����������ɡ�
echo Ŀǰ������:
echo - TokenBackups.vue �е�ģ�ⱸ������
echo - TokenReset.vue �е�ģ��Token����
echo.

echo ����������Ŀ:
echo 1. �������: cd backend && npm run dev
echo 2. ����ǰ��: cd token-management && npm run dev
echo.
echo �����������...
pause > nul
goto MENU

:EXECUTE_ALL
echo.
echo ===================================================================
echo ִ�����в���
echo ===================================================================
echo.
echo �����ֶ���װMySQL��Ȼ�����������...
pause > nul
goto SETUP_DATABASE
goto CLEANUP_FRONTEND

:EXIT
echo.
echo ��лʹ��Token����ϵͳ���ù��ߣ�
echo.
exit /b 0 