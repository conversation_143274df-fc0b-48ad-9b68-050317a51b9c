const Backup = require('../models/backup');
const Token = require('../models/token');
const { logger } = require('../utils/logger');

class TokenBackupController {
  /**
   * 通过Token UID创建备份
   * 这是为前端特殊需求设计的API端点
   */
  static async createBackupByUid(req, res, next) {
    try {
      const { 
        name, 
        description, 
        backup_type,
        type = 'manual', 
        token_uids, // 使用token_uids代替token_ids
        task_id = null,
        retention_days = 30 
      } = req.body;
      
      // 验证必填字段
      if (!name || !backup_type || !token_uids) {
        return res.status(400).json({
          success: false,
          message: '名称、备份类型和token UID列表为必填项'
        });
      }
      
      // 验证token_uids格式
      if (!Array.isArray(token_uids) || token_uids.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'token_uids必须是非空数组'
        });
      }
      
      // 从UID获取Token ID
      const tokenIds = [];
      
      for (const uid of token_uids) {
        // 通过UID查找Token
        const token = await Token.getByUid(uid);
        
        if (!token) {
          return res.status(404).json({
            success: false,
            message: `UID为${uid}的Token不存在`
          });
        }
        
        tokenIds.push(token.id);
      }
      
      // 构建备份数据
      const backupData = {
        name,
        description,
        backup_type,
        task_id,
        type,
        status: 'active',
        retention_days,
        created_at: new Date()
      };
      
      // 创建备份记录
      const backupId = await Backup.create(backupData, tokenIds, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 通过UID创建了新备份 (ID: ${backupId})`);
      
      // 获取创建的备份
      const createdBackup = await Backup.getById(backupId);
      
      res.status(201).json({
        success: true,
        message: '备份创建成功',
        data: {
          backup: createdBackup
        }
      });
    } catch (error) {
      logger.error(`通过UID创建备份失败: ${error.message}`);
      next(error);
    }
  }
}

module.exports = TokenBackupController; 