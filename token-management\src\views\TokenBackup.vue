<template>
  <div class="token-backup-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="backup-list-card">
          <template #header>
            <div class="card-header">
              <span class="header-title">Token备份管理</span>
              <div class="header-actions">
                <el-radio-group v-model="backupTypeFilter" size="small" class="backup-type-filter">
                  <el-radio-button label="all">全部</el-radio-button>
                  <el-radio-button label="重置前">重置前</el-radio-button>
                  <el-radio-button label="重置后">重置后</el-radio-button>
                  <el-radio-button label="双备份">双备份</el-radio-button>
                </el-radio-group>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索备份"
                  class="search-input"
                  clearable
                >
                  <template #prefix>
                    <el-icon><el-icon-search /></el-icon>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  size="small"
                  @click="showRelationFilter = !showRelationFilter"
                  class="relation-filter-btn"
                  :class="{ 'is-active': activeRelationFilter }"
                >
                  <i class="el-icon-connection" style="margin-right: 5px;"></i>
                  关联筛选
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="confirmBatchDeleteBackups"
                  :disabled="selectedBackups.length === 0"
                >
                  批量删除 ({{ selectedBackups.length }})
                </el-button>
              </div>
            </div>
          </template>

          <!-- 关联ID筛选弹出框 -->
          <div class="relation-filter-panel" v-if="showRelationFilter">
            <div class="filter-title">按关联ID筛选</div>
            <div class="filter-content">
              <el-input
                v-model="relationFilterValue"
                placeholder="输入关联ID"
                clearable
                @clear="clearRelationFilter"
              >
                <template #append>
                  <el-button @click="applyRelationFilter">
                    <i class="el-icon-search"></i>
                  </el-button>
                </template>
              </el-input>

              <div class="relation-tags" v-if="availableRelations.length > 0">
                <div class="tags-title">可选关联ID:</div>
                <div class="tags-content">
                  <el-tag
                    v-for="relation in availableRelations"
                    :key="relation.id"
                    effect="plain"
                    class="relation-tag"
                    @click="selectRelation(relation.id)"
                    :class="{ 'is-active': relation.id === relationFilterValue }"
                  >
                    {{ relation.id.substring(0, 8) }}...
                    <span class="tag-count">({{ relation.count }})</span>
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="filter-footer">
              <el-button size="small" @click="clearRelationFilter">清除筛选</el-button>
              <el-button size="small" type="primary" @click="applyRelationFilter">应用筛选</el-button>
            </div>
          </div>

          <el-table
            :data="filteredBackups"
            style="width: 100%"
            border
            @row-click="handleSelectBackup"
            highlight-current-row
            :header-cell-style="{background: '#f5f7fa', color: '#303133', fontSize: '15px', height: '56px'}"
            :cell-style="{fontSize: '14px', padding: '12px 0'}"
            @selection-change="handleBackupSelectionChange"
            ref="backupTableRef"
          >
            <el-table-column
              type="selection"
              width="55"
              :selectable="(row) => !row.deleteStatus"
            />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="备份名称" width="180">
              <template #default="scope">
                <span>{{ getFormattedBackupName(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="backupTime" label="备份时间" width="180" sortable>
              <template #default="scope">
                <span>{{ scope.row.backupTime || scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已备份时间" width="120" sortable>
              <template #default="scope">
                <span>{{ getTimeAgo(scope.row.backupTime || scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="tokenCount" label="Token数量" width="120" sortable />
            <el-table-column prop="createTime" label="创建时间" width="180" sortable />
            <el-table-column label="状态" width="120">
              <template #default="scope">
                <el-tooltip
                  effect="dark"
                  :content="getBackupStatusTooltip(scope.row)"
                  placement="top"
                >
                  <el-tag
                    :type="getBackupStatusType(scope.row)"
                    effect="plain"
                    class="backup-status-tag"
                  >
                    {{ getBackupStatus(scope.row) }}
                  </el-tag>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="backupId" label="备份ID" width="220">
              <template #default="scope">
                <div class="backup-id-cell">{{ scope.row.backupId || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="备份关联ID" width="240">
              <template #default="scope">
                <el-tooltip
                  effect="dark"
                  :content="getBackupTaskId(scope.row.backupId) ? `关联ID: ${getBackupTaskId(scope.row.backupId)}` : '无关联ID'"
                  placement="top"
                >
                  <div
                    class="backup-link-cell"
                    :class="{'has-relation': getBackupTaskId(scope.row.backupId)}"
                  >
                    <i class="el-icon-link" v-if="getBackupTaskId(scope.row.backupId)"></i>
                    <span>{{ getBackupRelationInfo(scope.row) }}</span>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="备份类型" width="120">
              <template #default="scope">
                <el-tag
                  :type="getBackupTypeTagType(scope.row)"
                  effect="plain"
                >
                  {{ getBackupTypeDisplay(scope.row) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="文件大小" width="100" />
            <el-table-column label="操作" width="420" fixed="right">
              <template #default="scope">
                <div class="operation-buttons">
                  <el-button type="primary" size="small" class="restore-btn" @click.stop="handleRestoreBackup(scope.row)">查看备份数据</el-button>
                  <el-button type="success" size="small" class="export-btn" @click.stop="exportBackupData(scope.row)">导出数据</el-button>
                  <el-button type="warning" size="small" class="recover-btn" @click.stop="recoverDataToReset(scope.row)">恢复数据</el-button>
                  <el-button type="danger" size="small" class="delete-btn" @click.stop="confirmDeleteBackup(scope.row)">删除备份</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="table-footer">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalBackupCount"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 备份数据展示卡片 -->
    <el-row :gutter="20" class="backup-data-row" v-if="restoredTokens.length > 0">
      <el-col :span="24">
        <el-card class="backup-data-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <i class="el-icon-document-checked header-icon"></i>
                <span>备份数据</span>
                <div class="backup-info">
                  <div class="backup-type-tag"
                       :class="{'before': activeBackupType === 'before', 'after': activeBackupType === 'after'}"
                  >
                    {{ activeBackupType === 'before' ? '重置前备份' : '重置后备份' }}
                  </div>
                  <div class="backup-id-display">
                    关联ID: <span class="backup-id-text">{{ selectedBackup?.taskId || '-' }}</span>
                  </div>
                </div>
              </div>
              <div class="header-actions">
                <div class="backup-type-selector" v-if="selectedBackup && selectedBackup.combinedBackup">
                  <el-radio-group v-model="activeBackupType" size="small">
                    <el-radio-button label="before" :disabled="!selectedBackup.hasBeforeBackup">重置前</el-radio-button>
                    <el-radio-button label="after" :disabled="!selectedBackup.hasAfterBackup">重置后</el-radio-button>
                  </el-radio-group>
                </div>
                <el-button @click="clearRestoredData" type="danger" size="small">
                  清除数据
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            :data="restoredTokens"
            style="width: 100%"
            border
            stripe
            highlight-current-row
            size="medium"
            :header-cell-style="{background: '#f5f7fa', color: '#303133', fontSize: '15px', height: '56px'}"
            :cell-style="{fontSize: '14px', padding: '12px 0'}"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="uid" label="UID" width="150" sortable>
              <template #default="scope">
                <el-tooltip
                  effect="dark"
                  :content="scope.row.uid || '-'"
                  placement="top"
                >
                  <div class="token-relation-id">{{ scope.row.uid || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="tokenValue" label="TOKEN">
              <template #default="scope">
                <div class="token-value">{{ scope.row.tokenValue }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="用户" width="120" />
            <el-table-column label="头像" width="70" align="center">
              <template #default="scope">
                <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
              </template>
            </el-table-column>
            <el-table-column prop="nickname" label="昵称" width="120">
              <template #default="scope">
                <span>{{ scope.row.nickname || scope.row.username || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="loadDate" label="购买时间" width="180" sortable>
              <template #default="scope">
                <span>{{ scope.row.loadDate }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="expireDate" label="到期时间" width="180" sortable>
              <template #default="scope">
                <span>{{ scope.row.expireDate || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getTokenStatusType(scope.row.status)" size="medium">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" sortable />
            <el-table-column label="操作" width="360" fixed="right">
              <template #default="scope">
                <div class="token-actions">
                  <el-button type="primary" size="small" @click="checkAvatarNickname(scope.row)" class="action-btn">查询头像昵称</el-button>
                  <el-button type="success" size="small" @click="checkOnline(scope.row)" class="action-btn">查询在线</el-button>
                  <el-button type="info" size="small" @click="modifyAvatar(scope.row)" class="action-btn">修改头像</el-button>
                  <el-button type="warning" size="small" @click="modifyNickname(scope.row)" class="action-btn">修改昵称</el-button>
                  <el-button type="success" size="small" @click="addToOrder(scope.row)" class="action-btn">置入订单</el-button>
                  <el-button type="primary" size="small" @click="assignToAccount(scope.row)" class="action-btn">指定账号</el-button>
                  <el-button type="danger" size="small" @click="deleteOrder(scope.row)" class="action-btn">删除订单</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页控制器 -->
          <div class="pagination-container" v-if="tokenTotal > 0">
            <el-pagination
              @size-change="handleTokenSizeChange"
              @current-change="handleTokenCurrentChange"
              :current-page="tokenCurrentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="tokenPageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tokenTotal"
              background
            >
            </el-pagination>
          </div>

          <!-- 添加与Token列表页面相同的功能按钮 -->
          <div class="action-toolbar" v-if="restoredTokens.length > 0">
            <el-button type="primary" @click="batchCheckAvatarNickname">查询头像昵称</el-button>
            <el-button type="success" @click="batchCheckOnline">查询在线</el-button>
            <el-button type="info" @click="batchModifyAvatar">修改头像</el-button>
            <el-button type="warning" @click="batchModifyNickname">修改昵称</el-button>
            <el-button type="success" @click="batchAddToOrder">新Token置入账号内订单</el-button>
            <el-button type="primary" @click="batchAssignToAccount">选中给指定账号</el-button>
            <el-button type="danger" @click="batchDeleteOrder">删除订单</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeMount, watch, onBeforeUnmount } from 'vue'
import { useTokenStore } from '@/store/token'
import { ElMessage, ElAvatar, ElMessageBox, ElLoading } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import * as XLSX from 'xlsx'

const tokenStore = useTokenStore()
const route = useRoute()
const router = useRouter()

// 选中的备份
const selectedBackup = ref(null)

// 当前恢复的备份数据
const restoredTokens = ref([])
const activeBackupId = ref('')

// Token数据分页相关状态
const tokenCurrentPage = ref(1)
const tokenPageSize = ref(10)
const tokenTotal = ref(0)

// 备份筛选相关状态
const backupTypeFilter = ref('all') // 默认显示全部
const searchKeyword = ref('')
const showRelationFilter = ref(false)
const relationFilterValue = ref('')
const activeRelationFilter = computed(() => relationFilterValue.value !== '')
const activeBackupType = ref('before') // 默认显示重置前备份

// 批量删除相关状态
const selectedBackups = ref([])
const backupTableRef = ref(null)



const backups = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalBackupCount = computed(() => filteredBackups.value.length)

// 过滤后的备份列表（合并相同关联ID的备份）
const filteredBackups = computed(() => {
  // 先按原始条件筛选
  const filtered = backups.value.filter(backup => {
    // 根据备份类型筛选
    if (backupTypeFilter.value !== 'all') {
      // 对于"双备份"类型的特殊处理
      if (backupTypeFilter.value === '双备份') {
        const taskId = getBackupTaskId(backup.backupId);
        if (!taskId) return false;

        // 检查是否同时存在重置前和重置后备份
        const hasBeforeBackup = backups.value.some(b =>
          getBackupTaskId(b.backupId) === taskId && b.backupType === '重置前'
        );
        const hasAfterBackup = backups.value.some(b =>
          getBackupTaskId(b.backupId) === taskId && b.backupType === '重置后'
        );

        return hasBeforeBackup && hasAfterBackup;
      } else if (backup.backupType !== backupTypeFilter.value) {
        return false;
      }
    }

    // 根据关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      if (
        !(backup.name?.toLowerCase().includes(keyword) ||
          backup.backupId?.toLowerCase().includes(keyword) ||
          backup.backupType?.toLowerCase().includes(keyword) ||
          backup.type?.toLowerCase().includes(keyword))
      ) {
        return false;
      }
    }

    // 根据关联ID筛选
    if (relationFilterValue.value) {
      const backupTaskId = getBackupTaskId(backup.backupId);
      if (backupTaskId !== relationFilterValue.value) {
        return false;
      }
    }

    return true;
  });

  // 将相同关联ID的备份合并显示
  const relationMap = new Map();

  filtered.forEach(backup => {
    const taskId = getBackupTaskId(backup.backupId);

    // 如果没有关联ID则直接显示
    if (!taskId) {
      if (!relationMap.has(`single_${backup.id}`)) {
        relationMap.set(`single_${backup.id}`, { ...backup, relatedBackups: [] });
      }
      return;
    }

    // 如果已存在相同关联ID的备份，合并信息
    if (relationMap.has(taskId)) {
      const existingData = relationMap.get(taskId);

      // 将当前备份添加到相关备份列表
      existingData.relatedBackups.push(backup);

      // 更新显示信息
      if (backup.backupType === '重置前') {
        existingData.hasBeforeBackup = true;
        existingData.beforeBackupId = backup.backupId;
        existingData.beforeBackupData = backup.tokenData;
      } else if (backup.backupType === '重置后') {
        existingData.hasAfterBackup = true;
        existingData.afterBackupId = backup.backupId;
        existingData.afterBackupData = backup.tokenData;
      }
    } else {
      // 不存在相同关联ID的备份，创建新条目
      const newData = {
        ...backup,
        taskId,
        relatedBackups: [],
        hasBeforeBackup: backup.backupType === '重置前',
        hasAfterBackup: backup.backupType === '重置后',
        beforeBackupId: backup.backupType === '重置前' ? backup.backupId : null,
        afterBackupId: backup.backupType === '重置后' ? backup.backupId : null,
        beforeBackupData: backup.backupType === '重置前' ? backup.tokenData : null,
        afterBackupData: backup.backupType === '重置后' ? backup.tokenData : null,
        combinedBackup: true
      };

      relationMap.set(taskId, newData);
    }
  });

  // 转换为数组并排序
  return Array.from(relationMap.values())
    .sort((a, b) => {
      // 按创建时间降序排列
      const timeA = new Date(a.createTime || 0);
      const timeB = new Date(b.createTime || 0);
      return timeB - timeA;
    });
});

// 计算所有可用的关联ID
const availableRelations = computed(() => {
  const relationMap = new Map();

  backups.value.forEach(backup => {
    const taskId = getBackupTaskId(backup.backupId);
    if (taskId) {
      if (relationMap.has(taskId)) {
        relationMap.set(taskId, relationMap.get(taskId) + 1);
      } else {
        relationMap.set(taskId, 1);
      }
    }
  });

  // 转换为数组并排序，仅保留出现次数大于1的关联ID（有关联的备份）
  return Array.from(relationMap.entries())
    .filter(([_, count]) => count > 1)
    .map(([id, count]) => ({ id, count }))
    .sort((a, b) => b.count - a.count);
});

// 关联筛选相关方法
const applyRelationFilter = () => {
  showRelationFilter.value = false;
  // 关联筛选已经通过计算属性实现，这里只需关闭面板
}

const clearRelationFilter = () => {
  relationFilterValue.value = '';
}

const selectRelation = (relationId) => {
  relationFilterValue.value = relationId;
}

// 备份列表分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// Token数据分页处理
const handleTokenSizeChange = async (val) => {
  tokenPageSize.value = val
  await loadTokenData()
}

const handleTokenCurrentChange = async (val) => {
  tokenCurrentPage.value = val
  await loadTokenData()
}

// 加载当前备份的Token数据
const loadTokenData = async () => {
  if (!selectedBackup.value || !activeBackupId.value) {
    return
  }

  const backupId = activeBackupId.value
  const result = await fetchBackupTokens(backupId, tokenCurrentPage.value, tokenPageSize.value)

  if (result.success && result.tokens.length > 0) {
    restoredTokens.value = result.tokens
    tokenTotal.value = result.count

    ElMessage({
      message: `已加载第${tokenCurrentPage.value}页数据，共${result.count}个Token，当前显示${result.tokens.length}条`,
      type: 'success',
      duration: 2000
    })
  } else {
    ElMessage.warning('没有找到数据')
  }
}

// 选择备份
const handleSelectBackup = (row, column, event) => {
  // 如果点击的是复选框所在的列，不进行备份选择
  if (column && column.type === 'selection') {
    return;
  }

  // 如果点击的是操作列的按钮，不进行备份选择
  if (column && column.label === '操作') {
    return;
  }

  selectedBackup.value = row;
  handleRestoreBackup(row);
}

// 从后端获取备份的Token数据
const fetchBackupTokens = async (backupId, page = 1, pageSize = 10) => {
  try {
    ElMessage.info(`正在获取备份数据: ${backupId}, 页码: ${page}, 每页数量: ${pageSize}`)

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载备份数据...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用后端 API，增加分页参数
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}/tokens`, {
      params: {
        page: page,
        limit: pageSize
      }
    })
    console.log('备份数据响应:', response.data)

    loading.close()

    if (response.data.success) {
      // 处理返回的Token数据
      console.log('原始数据:', response.data.data.tokens);
      const tokens = (response.data.data.tokens || []).map(token => {
        // 输出每个token的详细信息以便于调试
        console.log('处理token数据:', token);
        return {
          uid: token.uid || token.id || '',
          tokenValue: token.token || '',  // 直接使用token字段，不再使用token_value
          username: token.username || '',
          nickname: token.nickname || '',
          avatar: token.avatar || '',
          status: token.status || 'active',
          platform: token.platform || '',
          purchaseTime: token.purchase_time ? new Date(token.purchase_time).toLocaleString() : '',
          expiryDate: token.expiry_time ? new Date(token.expiry_time).toLocaleString() : '',
          createTime: token.created_time ? new Date(token.created_time).toLocaleString() :
                    (token.created_at ? new Date(token.created_at).toLocaleString() : ''),
          orderId: token.order_id || ''
        };
      })

      // 获取分页信息
      const pagination = response.data.data.pagination || {
        total: tokens.length,
        page: page,
        limit: pageSize,
        pages: Math.ceil(tokens.length / pageSize)
      };

      return {
        success: true,
        tokens: tokens,
        count: pagination.total,
        pagination: pagination
      }
    } else {
      ElMessage.error(response.data.message || '获取备份数据失败')
      return {
        success: false,
        tokens: [],
        count: 0
      }
    }
  } catch (error) {
    console.error('获取备份数据失败:', error)
    ElMessage.error(`获取备份数据失败: ${error.message}`)
    return {
      success: false,
      tokens: [],
      count: 0
    }
  }
}

// 恢复备份数据（显示备份内容）
const handleRestoreBackup = async (backup) => {
  if (!backup) {
    ElMessage.warning('请先选择备份')
    return
  }

  // 重置分页状态
  tokenCurrentPage.value = 1
  tokenPageSize.value = 10
  tokenTotal.value = 0

  selectedBackup.value = backup

  // 如果是合并显示的备份，根据当前选择的类型(重置前/重置后)加载对应数据
  if (backup.combinedBackup) {
    // 默认选择有效的备份类型
    if (backup.hasBeforeBackup) {
      activeBackupType.value = 'before'
    } else if (backup.hasAfterBackup) {
      activeBackupType.value = 'after'
    }

    loadBackupDataByType()
  } else {
    // 普通备份 - 从后端获取数据
    // 设置当前活动备份ID
    // 优先使用backup_id，如果没有再使用数字id
    activeBackupId.value = backup.backup_id || backup.backupId || backup.id

    // 使用通用函数加载数据
    await loadTokenData()
  }
}

// 根据选择的类型加载备份数据
const loadBackupDataByType = async () => {
  if (!selectedBackup.value) return

  const backup = selectedBackup.value

  if (activeBackupType.value === 'before' && backup.hasBeforeBackup) {
    // 加载重置前备份 - 从后端获取数据
    const backupId = backup.beforeBackupId
    if (!backupId) {
      ElMessage.warning('重置前备份数据不可用')
      return
    }

    ElMessage.info(`正在加载重置前备份数据`)

    // 设置当前活动备份ID
    // 使用完整的备份ID
    activeBackupId.value = backupId

    // 使用通用函数加载数据
    await loadTokenData()
  }
  else if (activeBackupType.value === 'after' && backup.hasAfterBackup) {
    // 加载重置后备份 - 从后端获取数据
    const backupId = backup.afterBackupId
    if (!backupId) {
      ElMessage.warning('重置后备份数据不可用')
      return
    }

    ElMessage.info(`正在加载重置后备份数据`)

    // 设置当前活动备份ID
    // 使用完整的备份ID
    activeBackupId.value = backupId

    // 使用通用函数加载数据
    await loadTokenData()
  } else {
    ElMessage.warning('所选备份类型不可用')
  }
}

// 监听备份类型变化
watch(activeBackupType, () => {
  if (selectedBackup.value && selectedBackup.value.combinedBackup) {
    loadBackupDataByType()
  }
})

// 清除已恢复的数据
const clearRestoredData = () => {
  restoredTokens.value = []
  activeBackupId.value = ''
  ElMessage({
    message: '已清除备份数据显示',
    type: 'info',
    duration: 2000
  })
}

// 获取Token状态类型
const getTokenStatusType = (status) => {
  switch (status) {
    case '正常':
      return 'success'
    case '已重置':
      return 'info'
    case '失效':
      return 'danger'
    case '失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 添加Token操作相关的方法
const editToken = (token) => {
  ElMessage.info(`编辑Token: ${token.tokenValue.substring(0, 10)}...`)
}

const backupToken = (token) => {
  ElMessage.info(`备份Token: ${token.tokenValue.substring(0, 10)}...`)
}

// 添加工具栏操作方法
const importToken = () => {
  ElMessage.info('导入Token功能已触发')
}

const checkPassword = () => {
  ElMessage.info('检查密码功能已触发')
}

const speedUpload = () => {
  ElMessage.info('加速上传功能已触发')
}

const resetAvatarNickname = () => {
  ElMessage.info('重设头像和昵称功能已触发')
}

const checkAvatar = () => {
  ElMessage.info('检查头像功能已触发')
}

const modifyAccount = () => {
  ElMessage.info('修改账号功能已触发')
}

const createTokenBackup = () => {
  ElMessage.info('创建备份功能已触发')
}

const startReset = () => {
  ElMessage.info('重置功能已触发')
}

const exportToAccount = () => {
  ElMessage.info('导出到账号功能已触发')
}

const writeToOrderInfo = () => {
  ElMessage.info('新Token写入账号订单信息功能已触发')
}

// 初始化函数共享
const initFunctionSharing = () => {
  // 创建一个空对象，避免跳转到自动重置页面
  if (!window.tokenResetFunctions) {
    window.tokenResetFunctions = {
      batchReset: () => ElMessage.info('批量重置功能未完全初始化'),
      batchModifyAvatar: () => ElMessage.info('批量修改头像功能未完全初始化'),
      batchModifyNickname: () => ElMessage.info('批量修改昵称功能未完全初始化'),
      batchAddToOrder: () => ElMessage.info('批量置入订单功能未完全初始化'),
      batchAssignToAccount: () => ElMessage.info('批量指定给账号功能未完全初始化'),
      batchDeleteOrder: () => ElMessage.info('批量删除订单功能未完全初始化')
    }
    console.log('已创建本地Token操作函数对象')
  }
}

// 在组件挂载前尝试初始化函数共享
onBeforeMount(() => {
  initFunctionSharing()
})

// 从后端获取备份数据
const fetchBackups = async () => {
  try {
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups`);
    console.log('从后端获取的备份数据:', response.data);

    if (response.data.success) {
      // 将后端数据转换为前端需要的格式
      const apiBackups = response.data.data.backups || [];
      backups.value = apiBackups.map(backup => ({
        id: backup.id,
        name: backup.name || `备份 ${backup.id}`,
        backupId: backup.backup_id || backup.id,
        backupType: backup.backup_type || '备份',
        createTime: new Date(backup.created_at).toLocaleString(),
        backupTime: new Date(backup.backup_time || backup.created_at).toLocaleString(),
        tokenCount: backup.token_count || 0,
        size: backup.size || '0KB',
        type: backup.type || '手动',
        description: backup.description || '无描述'
      }));

      ElMessage.success(`成功获取${backups.value.length}个备份`);
    } else {
      ElMessage.warning(response.data.message || '获取备份列表失败');
    }
  } catch (error) {
    console.error('获取备份列表失败:', error);
    ElMessage.error(`获取备份列表失败: ${error.message}`);
  }
};

// 处理备份选择变化
const handleBackupSelectionChange = (selection) => {
  selectedBackups.value = selection;
  console.log('当前选中的备份:', selectedBackups.value);
}

// 确认批量删除备份
const confirmBatchDeleteBackups = () => {
  if (selectedBackups.value.length === 0) {
    ElMessage.warning('请选择要删除的备份');
    return;
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedBackups.value.length} 个备份吗？此操作不可恢复。`,
    '批量删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      distinguishCancelAndClose: true
    }
  ).then(() => {
    batchDeleteBackups();
  }).catch(() => {
    // 用户取消删除
  });
};

// 批量删除备份
const batchDeleteBackups = async () => {
  if (selectedBackups.value.length === 0) return;

  // 显示加载状态
  const loading = ElLoading.service({
    lock: true,
    text: '正在删除备份...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    const deletePromises = selectedBackups.value.map(async (backup) => {
      try {
        const backupId = backup.id || backup.backupId;
        console.log('删除备份:', backupId);

        // 调用后端 API
        const response = await axios.delete(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}`);
        console.log('删除响应:', response.data);

        if (response.data.success) {
          // 如果当前正在查看此备份，则清除
          if (activeBackupId.value === (backup.backupId || `备份ID-${backup.id}`)) {
            clearRestoredData();
          }
          return { success: true, id: backupId, name: backup.name };
        }
        return { success: false, id: backupId, name: backup.name, error: response.data.message };
      } catch (error) {
        console.error(`删除备份 ${backup.id || backup.backupId} 失败:`, error);
        return { success: false, id: backup.id || backup.backupId, name: backup.name, error: error.message };
      }
    });

    // 等待所有删除操作完成
    const results = await Promise.all(deletePromises);
    const successCount = results.filter(result => result.success).length;

    loading.close();

    if (successCount > 0) {
      ElMessage.success(`成功删除 ${successCount}/${selectedBackups.value.length} 个备份`);

      // 清除选中状态
      selectedBackups.value = [];
      if (backupTableRef.value) {
        backupTableRef.value.clearSelection();
      }

      // 重新获取备份列表
      setTimeout(() => fetchBackups(), 500);
    } else {
      ElMessage.warning('没有成功删除任何备份');
    }
  } catch (error) {
    loading.close();
    console.error('批量删除备份失败:', error);
    ElMessage.error(`批量删除备份失败: ${error.message}`);
  }
};

// 导出备份数据
const exportBackupData = async (backup) => {
  try {
    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在获取备份数据...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 获取备份ID
    const backupId = backup.id || backup.backupId;

    // 调用后端 API获取备份数据
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}/tokens`, {
      params: {
        page: 1,
        limit: 10000  // 设置一个足够大的值，获取所有数据
      }
    });

    loading.close();

    if (response.data.success && response.data.data.tokens && response.data.data.tokens.length > 0) {
      const tokens = response.data.data.tokens;

      // 准备导出数据
      const exportData = tokens.map(token => ({
        UID: token.uid || token.id || '',
        TOKEN: token.token || '',
        用户名: token.username || ''
      }));

      // 创建Excel工作表
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '备份数据');

      // 生成文件名
      const fileName = `备份数据_${backup.name || backup.backupId}_${new Date().toISOString().slice(0, 10)}.xlsx`;

      // 导出并下载Excel文件
      XLSX.writeFile(workbook, fileName);

      ElMessage.success(`成功导出 ${exportData.length} 条数据`);
    } else {
      ElMessage.warning('没有找到可导出的数据');
    }
  } catch (error) {
    console.error('导出数据失败:', error);
    ElMessage.error(`导出数据失败: ${error.message}`);
  }
};

// 恢复数据到Token重置页面
const recoverDataToReset = async (backup) => {
  if (!backup) {
    ElMessage.warning('请选择要恢复的备份')
    return
  }

  try {
    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载备份数据...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 设置当前活动备份ID
    const backupId = backup.id || backup.backupId

    // 调用后端 API获取备份数据，不使用分页，获取全部数据
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}/tokens`, {
      params: {
        page: 1,
        limit: 10000  // 设置较大的限制，确保获取全部数据
      }
    })

    loading.close()

    if (response.data.success) {
      // 处理返回的Token数据
      const tokens = (response.data.data.tokens || []).map(token => ({
        uid: token.uid || token.id || '',
        token: token.token || '',
        user: token.username || '',  // 将username映射到user字段，与Token重置页面保持一致
        username: token.username || '',
        nickname: token.nickname || '',
        avatar: token.avatar || '',
        status: token.status || 'active',
        platform: token.platform || '',
        purchaseTime: token.purchase_time ? new Date(token.purchase_time).toLocaleString() : '',
        expiryDate: token.expiry_time ? new Date(token.expiry_time).toLocaleString() : '',
        createTime: token.created_time ? new Date(token.created_time).toLocaleString() :
                  (token.created_at ? new Date(token.created_at).toLocaleString() : ''),
        orderId: token.order_id || ''
      }))

      if (tokens.length === 0) {
        ElMessage.warning('备份中没有可用的Token数据')
        return
      }

      // 将数据保存到localStorage中，传递给Token重置页面
      localStorage.setItem('recovered_tokens', JSON.stringify(tokens))

      // 显示成功消息
      ElMessage.success(`已成功加载 ${tokens.length} 个Token数据，正在跳转到Token重置页面...`)

      // 跳转到Token重置页面
      setTimeout(() => {
        router.push({
          path: '/token-reset',
          query: { recovered: 'true', backup_id: backupId, backup_name: backup.name || '备份数据' }
        })
      }, 1000)
    } else {
      ElMessage.error(response.data.message || '获取备份数据失败')
    }
  } catch (error) {
    console.error('恢复数据失败:', error)
    ElMessage.error(`恢复数据失败: ${error.message}`)
  }
};

// 在挂载后检查是否有从TokenAutoReset页面返回的标记
onMounted(() => {
  // 获取备份数据
  fetchBackups();

  // 如果URL中包含备份ID，则自动恢复该备份
  const backupId = route.params.id || route.query.id

  if (backupId) {
    // 等待备份数据加载完成
    const checkBackup = setInterval(() => {
      const backup = backups.value.find(b => b.backupId === backupId)
      if (backup) {
        clearInterval(checkBackup);
        handleRestoreBackup(backup);
      }
    }, 500);

    // 5秒后如果还没有找到备份，则提示用户
    setTimeout(() => {
      clearInterval(checkBackup);
      if (!backups.value.find(b => b.backupId === backupId)) {
        ElMessage.warning(`未找到ID为${backupId}的备份`);
      }
    }, 5000);
  }

  // 检查是否有返回标记
  const returnData = localStorage.getItem('backupPageReturn')
  if (returnData) {
    localStorage.removeItem('backupPageReturn')
    // 函数应该已经初始化完成
    if (window.tokenResetFunctions) {
      ElMessage.success('Token操作功能已成功初始化')
    } else {
      ElMessage.warning('Token操作功能初始化失败，部分功能可能无法正常使用')
    }
  }

  // 在组件挂载时启动定时器，每分钟检查一次
  const deleteCheckTimer = setInterval(() => {
    const now = new Date();

    backups.value.forEach(backup => {
      // 如果备份标记为即将删除且已经到达删除时间
      if (backup.deleteStatus && backup.scheduledDeleteTime) {
        const deleteTime = new Date(backup.scheduledDeleteTime);
        if (now >= deleteTime) {
          // 自动删除备份
          deleteBackup(backup);
          ElMessage.info(`备份"${backup.name || backup.backupId}"已自动删除`);
        }
      }
    });
  }, 60000); // 每分钟检查一次

  // 在组件卸载时清除定时器
  onBeforeUnmount(() => {
    clearInterval(deleteCheckTimer);
  });
})

// 修改单个操作方法
const checkAvatarNickname = (token) => {
  ElMessage.info(`查询"${token.username}"的头像昵称`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.checkAvatarNickname) {
    window.tokenResetFunctions.checkAvatarNickname(token)
  }
}

const checkOnline = (token) => {
  ElMessage.info(`查询"${token.username}"的在线状态`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.checkOnline) {
    window.tokenResetFunctions.checkOnline(token)
  }
}

const modifyAvatar = (token) => {
  ElMessage.info(`修改"${token.username}"的头像`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.modifyAvatar) {
    window.tokenResetFunctions.modifyAvatar(token)
  }
}

const modifyNickname = (token) => {
  ElMessage.info(`修改"${token.username}"的昵称`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.modifyNickname) {
    window.tokenResetFunctions.modifyNickname(token)
  }
}

const addToOrder = (token) => {
  ElMessage.info(`将Token置入订单: ${token.tokenValue.substring(0, 10)}...`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.addToOrder) {
    window.tokenResetFunctions.addToOrder(token)
  }
}

const assignToAccount = (token) => {
  ElMessage.info(`将Token指定给账号: ${token.tokenValue.substring(0, 10)}...`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.assignToAccount) {
    window.tokenResetFunctions.assignToAccount(token)
  }
}

const deleteOrder = (token) => {
  ElMessage.info(`删除"${token.username}"的订单`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.deleteOrder) {
    window.tokenResetFunctions.deleteOrder(token)
  }
}

// 批量操作方法
const batchCheckAvatarNickname = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量查询${selectedTokens.length}个Token的头像昵称`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchCheckAvatarNickname) {
    window.tokenResetFunctions.batchCheckAvatarNickname(selectedTokens)
  }
}

const batchCheckOnline = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量查询${selectedTokens.length}个Token的在线状态`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchCheckOnline) {
    window.tokenResetFunctions.batchCheckOnline(selectedTokens)
  }
}

const batchModifyAvatar = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量修改${selectedTokens.length}个Token的头像`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchModifyAvatar) {
    window.tokenResetFunctions.batchModifyAvatar(selectedTokens)
  }
}

const batchModifyNickname = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量修改${selectedTokens.length}个Token的昵称`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchModifyNickname) {
    window.tokenResetFunctions.batchModifyNickname(selectedTokens)
  }
}

const batchAddToOrder = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量将${selectedTokens.length}个Token置入订单`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchAddToOrder) {
    window.tokenResetFunctions.batchAddToOrder(selectedTokens)
  }
}

const batchAssignToAccount = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量将${selectedTokens.length}个Token指定给账号`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchAssignToAccount) {
    window.tokenResetFunctions.batchAssignToAccount(selectedTokens)
  }
}

const batchDeleteOrder = () => {
  const selectedTokens = getSelectedTokens()
  if (selectedTokens.length === 0) {
    ElMessage.warning('请选择要操作的Token')
    return
  }
  ElMessage.info(`批量删除${selectedTokens.length}个Token的订单`)
  // 调用token重置页面的对应方法
  if (window.tokenResetFunctions && window.tokenResetFunctions.batchDeleteOrder) {
    window.tokenResetFunctions.batchDeleteOrder(selectedTokens)
  }
}

// 获取选中的Token
const getSelectedTokens = () => {
  const table = document.querySelector('.backup-data-card .el-table')
  if (!table) return []

  const selection = table.__vue__?.store?.states?.selection?.value || []
  return selection
}

// 格式化备份名称：重置日期+数量（例如：3月29日200个token）
const getFormattedBackupName = (backup) => {
  if (!backup) return '-'

  // 从创建时间中提取日期
  const createDate = backup.createTime ? new Date(backup.createTime) : new Date()
  const month = createDate.getMonth() + 1
  const day = createDate.getDate()

  // 获取token数量
  const tokenCount = backup.tokenCount || 0

  // 组合成新的名称格式
  return `${month}月${day}日${tokenCount}个token`
}

// 计算时间间隔（从备份时间到现在）
const getTimeAgo = (timeString) => {
  if (!timeString) return '-'

  const backupTime = new Date(timeString)
  const now = new Date()
  const diffMilliseconds = now - backupTime

  // 计算时间差（天、小时、分钟）
  const days = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diffMilliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diffMilliseconds % (1000 * 60 * 60)) / (1000 * 60))

  // 格式化输出
  if (days > 0) {
    return `${days}天${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 确认删除备份
const confirmDeleteBackup = (backup) => {
  const taskId = getBackupTaskId(backup.backupId);
  const hasRelation = taskId && availableRelations.value.some(relation => relation.id === taskId);

  // 添加删除选项
  const options = [
    {
      label: '立即删除',
      value: 'delete_now'
    },
    {
      label: '标记为即将删除',
      value: 'mark_for_deletion'
    }
  ];

  if (backup.deleteStatus) {
    options.push({
      label: '取消删除标记',
      value: 'cancel_deletion'
    });
  }

  if (hasRelation) {
    // 弹出选择确认框
    ElMessageBox.confirm(
      `此备份与其他备份有关联，请选择操作方式`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        showCancelButton: true,
        distinguishCancelAndClose: true,
        showClose: true,
        closeOnClickModal: false
      }
    )
    .then(() => {
      // 弹出选择操作方式对话框
      ElMessageBox.confirm(
        `请选择删除操作方式`,
        '选择操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          showInput: true,
          inputType: 'select',
          inputValue: 'delete_now',
          inputPlaceholder: '请选择操作方式',
          inputOptions: options
        }
      ).then(({ value }) => {
        switch (value) {
          case 'delete_now':
            deleteAllRelatedBackups(taskId);
            break;
          case 'mark_for_deletion':
            // 标记所有关联备份为即将删除
            const relatedBackups = backups.value.filter(b =>
              getBackupTaskId(b.backupId) === taskId
            );
            relatedBackups.forEach(b => markForDeletion(b));
            break;
          case 'cancel_deletion':
            // 取消所有关联备份的删除标记
            const markedBackups = backups.value.filter(b =>
              getBackupTaskId(b.backupId) === taskId
            );
            markedBackups.forEach(b => cancelDeletion(b));
            break;
        }
      }).catch(() => {});
    })
    .catch(() => {});
  } else {
    // 无关联备份，弹出选择操作方式对话框
    ElMessageBox.confirm(
      `请选择删除操作方式`,
      '选择操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        showInput: true,
        inputType: 'select',
        inputValue: 'delete_now',
        inputPlaceholder: '请选择操作方式',
        inputOptions: options
      }
    ).then(({ value }) => {
      switch (value) {
        case 'delete_now':
          deleteBackup(backup);
          break;
        case 'mark_for_deletion':
          markForDeletion(backup);
          break;
        case 'cancel_deletion':
          cancelDeletion(backup);
          break;
      }
    }).catch(() => {});
  }
};

// 删除所有关联备份
const deleteAllRelatedBackups = async (taskId) => {
  const relatedBackups = backups.value.filter(b =>
    getBackupTaskId(b.backupId) === taskId
  );

  // 实际应用中调用API批量删除
  const deletePromises = relatedBackups.map(async (backup) => {
    try {
      const backupId = backup.id || backup.backupId;
      console.log('删除关联备份:', backupId);

      // 调用后端 API
      const response = await axios.delete(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}`);
      console.log('删除响应:', response.data);

      if (response.data.success) {
        // 如果当前正在查看此备份，则清除
        if (activeBackupId.value === (backup.backupId || `备份ID-${backup.id}`)) {
          clearRestoredData();
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error(`删除备份 ${backup.id || backup.backupId} 失败:`, error);
      return false;
    }
  });

  // 等待所有删除操作完成
  const results = await Promise.all(deletePromises);
  const successCount = results.filter(result => result).length;

  if (successCount > 0) {
    ElMessage.success(`已删除 ${successCount} 个关联备份`);

    // 清除关联筛选
    if (relationFilterValue.value === taskId) {
      clearRelationFilter();
    }

    // 重新获取备份列表
    setTimeout(() => fetchBackups(), 500);
  } else {
    ElMessage.warning('没有成功删除任何备份');
  }
};

// 删除单个备份
const deleteBackup = async (backup) => {
  try {
    // 调用后端 API 删除备份
    const backupId = backup.id || backup.backupId;
    console.log('删除备份:', backupId);

    // 调用后端 API
    const response = await axios.delete(`${import.meta.env.VITE_API_BASE_URL || '/api'}/backups/${backupId}`);
    console.log('删除响应:', response.data);

    if (response.data.success) {
      // 从前端数组中移除
      const index = backups.value.findIndex(b => b.id === backup.id);
      if (index !== -1) {
        backups.value.splice(index, 1);

        // 如果当前正在查看此备份，则清除
        if (activeBackupId.value === (backup.backupId || `备份ID-${backup.id}`)) {
          clearRestoredData();
        }

        ElMessage.success(`已删除备份"${backup.name || backup.backupId}"`);

        // 重新获取备份列表
        setTimeout(() => fetchBackups(), 500);
      }
    } else {
      ElMessage.error(response.data.message || '删除备份失败');
    }
  } catch (error) {
    console.error('删除备份失败:', error);
    ElMessage.error(`删除备份失败: ${error.message}`);
  }
};

// 提取备份ID中的关联ID
const getBackupTaskId = (backupId) => {
  if (!backupId) return null;

  // 格式为QBF20240329_1671123456789或HBF20240329_1671123456789
  const parts = backupId.split('_');
  if (parts.length === 2) {
    return parts[1]; // 返回下划线后面的关联ID部分
  }

  return null;
}

// 获取备份关联信息显示文本
const getBackupRelationInfo = (backup) => {
  const taskId = getBackupTaskId(backup.backupId);
  if (!taskId) return '无关联ID';

  // 判断备份类型前缀
  const isBeforeReset = backup.backupId?.startsWith('QBF');
  const isAfterReset = backup.backupId?.startsWith('HBF');

  // 在备份列表中查找其它与此备份具有相同关联ID的备份
  const relatedBackups = backups.value.filter(b =>
    b.id !== backup.id && getBackupTaskId(b.backupId) === taskId
  );

  if (relatedBackups.length > 0) {
    const relatedType = relatedBackups[0].backupId?.startsWith('QBF') ? '重置前备份' :
                         relatedBackups[0].backupId?.startsWith('HBF') ? '重置后备份' : '其他备份';
    return `关联ID: ${taskId.substring(0, 8)}... (关联${relatedType})`;
  }

  return `关联ID: ${taskId.substring(0, 8)}...`;
}

// 获取备份类型显示文本
const getBackupTypeDisplay = (backup) => {
  if (backup.combinedBackup && backup.hasBeforeBackup && backup.hasAfterBackup) {
    return '双备份';
  } else if (backup.backupType) {
    return backup.backupType;
  } else {
    return backup.type || '未知';
  }
}

// 获取备份类型标签样式
const getBackupTypeTagType = (backup) => {
  if (backup.combinedBackup && backup.hasBeforeBackup && backup.hasAfterBackup) {
    return 'primary'; // 双备份使用蓝色标签
  } else if (backup.backupType === '重置前') {
    return 'success'; // 重置前使用绿色标签
  } else if (backup.backupType === '重置后') {
    return 'warning'; // 重置后使用黄色标签
  } else if (backup.type === '手动') {
    return 'primary'; // 手动备份使用蓝色标签
  } else {
    return 'success'; // 其他类型默认使用绿色标签
  }
}

// 获取备份状态
const getBackupStatus = (backup) => {
  // 如果标记为即将删除状态
  if (backup.deleteStatus) {
    return '即将删除';
  }

  // 根据备份时间计算状态
  const backupTime = new Date(backup.backupTime || backup.createTime);
  const now = new Date();
  const diffInDays = (now - backupTime) / (1000 * 60 * 60 * 24);

  if (diffInDays <= 1) {
    return '冷却中';
  } else {
    return '备份中';
  }
}

// 获取备份状态标签类型
const getBackupStatusType = (backup) => {
  const status = getBackupStatus(backup);

  switch (status) {
    case '冷却中':
      return 'warning'; // 黄色
    case '备份中':
      return 'success'; // 绿色
    case '即将删除':
      return 'danger'; // 红色
    default:
      return 'info';
  }
}

// 获取备份状态的提示文字
const getBackupStatusTooltip = (backup) => {
  const status = getBackupStatus(backup);

  if (status === '冷却中') {
    const backupTime = new Date(backup.backupTime || backup.createTime);
    const now = new Date();
    const diffInHours = Math.round((now - backupTime) / (1000 * 60 * 60));
    const remainingHours = 24 - diffInHours;
    return `备份冷却中，${remainingHours}小时后进入备份中状态`;
  } else if (status === '即将删除') {
    // 如果有预定的删除时间，则显示倒计时
    if (backup.scheduledDeleteTime) {
      const deleteTime = new Date(backup.scheduledDeleteTime);
      const now = new Date();
      const diffInHours = Math.round((deleteTime - now) / (1000 * 60 * 60));
      return `将在${diffInHours}小时后删除 (${backup.scheduledDeleteTime})`;
    }
    return `将在2天后自动删除`;
  } else {
    return `备份已保存${getTimeAgo(backup.backupTime || backup.createTime)}`;
  }
}

// 标记备份为即将删除状态
const markForDeletion = (backup) => {
  // 设置删除状态和计划删除时间（2天后）
  const now = new Date();
  const deleteDate = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000);
  backup.deleteStatus = true;
  backup.scheduledDeleteTime = deleteDate.toLocaleString();

  ElMessage.success(`备份已标记为即将删除，将在2天后自动删除`);

  // 在实际应用中，这里应该调用API保存状态变更
}

// 取消备份的删除标记
const cancelDeletion = (backup) => {
  backup.deleteStatus = false;
  backup.scheduledDeleteTime = null;

  ElMessage.success(`已取消删除标记`);

  // 在实际应用中，这里应该调用API保存状态变更
}
</script>

<style scoped>
.token-backup-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px);
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.8), rgba(240, 242, 245, 0.5));
}

.backup-list-card,
.backup-data-card {
  margin-bottom: 20px;
  border-radius: 6px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.backup-list-card::before,
.backup-data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.backup-data-card::before {
  background: linear-gradient(90deg, #409eff, #e6a23c);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  font-size: 22px;
  color: #409eff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.backup-source {
  font-size: 14px;
  font-weight: normal;
  font-family: Consolas, monospace;
  padding: 4px 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
  border-radius: 12px;
  margin-left: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap; /* 允许按钮换行 */
}

.search-input {
  width: 300px;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.backup-data-row {
  margin-top: 20px;
}

.token-value {
  font-family: Consolas, monospace;
  color: #606266;
  font-size: 14px;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.backup-id-cell {
  font-family: Consolas, monospace;
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

/* 表格样式 */
:deep(.el-table) {
  font-size: 14px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table th) {
  background: linear-gradient(to right, #f5f7fa, #e4e7ed);
  font-weight: 600;
  color: #303133;
  height: 56px;
  padding: 8px 0;
  font-size: 15px;
  border-bottom: 2px solid #e4e7ed;
}

:deep(.el-table--medium .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__row:hover td) {
  background-color: #f0f7ff !important;
}

:deep(.el-tag) {
  font-weight: 500;
  font-size: 13px;
  padding: 0 10px;
  height: 26px;
  line-height: 26px;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.restore-btn {
  background: linear-gradient(to right, #409eff, #1989fa);
  border: none;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  color: #fff;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.25s ease;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-button--primary) {
  background: linear-gradient(to right, #409eff, #1989fa);
  border: none;
}

:deep(.el-button--success) {
  background: linear-gradient(to right, #67c23a, #4caf50);
  border: none;
}

:deep(.el-button--danger) {
  background: linear-gradient(to right, #f56c6c, #e53935);
  border: none;
}

/* 添加Token操作相关样式 */
.token-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
  white-space: nowrap;
  min-width: 0;
}

/* 备份操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 2px 6px;
  font-size: 12px;
  margin: 0 2px 2px 0;
  min-width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-toolbar {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.action-toolbar .el-button {
  margin: 0;
}

.backup-type-filter {
  margin-right: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backup-type-filter :deep(.el-radio-button__inner) {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
}

.backup-type-filter :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: linear-gradient(to right, #409eff, #1989fa);
  border-color: #1989fa;
  box-shadow: 0 0 0 1px #1989fa;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.delete-btn {
  background: linear-gradient(to right, #f56c6c, #e53935);
  border: none;
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
  color: #fff;
  font-weight: 500;
}

/* 备份关联样式 */
.backup-link-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  font-family: monospace;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px dashed #c0c4cc;
  cursor: pointer;
  transition: all 0.2s;
  max-width: 220px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f5f7fa;
}

.backup-link-cell.has-relation {
  background-color: #f0f9eb;
  border-color: #67c23a;
}

.backup-link-cell.has-relation i {
  color: #67c23a;
  font-size: 16px;
}

.backup-link-cell:hover {
  background-color: #e1f3d8;
  border-color: #85ce61;
}

.backup-id-cell {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.relation-filter-btn {
  margin-left: 10px;
  background: linear-gradient(to right, #409eff, #1989fa);
  border: none;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  color: #fff;
  font-weight: 500;
}

.relation-filter-btn.is-active {
  background: linear-gradient(to right, #1989fa, #409eff);
}

.relation-filter-panel {
  margin-top: 10px;
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}

.filter-content {
  margin-bottom: 15px;
}

.relation-tags {
  margin-top: 10px;
}

.tags-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.relation-tag {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d9ecff;
  cursor: pointer;
  transition: all 0.2s;
}

.relation-tag.is-active {
  background: linear-gradient(to right, #409eff, #1989fa);
  color: #fff;
}

.tag-count {
  font-size: 12px;
  font-weight: normal;
  color: #909399;
  margin-left: 5px;
}

.filter-footer {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.backup-type-selector {
  margin-right: 15px;
}

.token-relation-id {
  font-family: monospace;
  font-size: 13px;
  color: #67c23a;
  background-color: #f0f9eb;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px dashed #c2e7b0;
  display: inline-block;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.token-relation-id:hover {
  background-color: #e1f3d8;
  border-color: #85ce61;
}

.backup-type-selector {
  margin-right: 15px;
}

.backup-type-selector :deep(.el-radio-button__inner) {
  padding: 8px 16px;
  font-weight: 500;
}

.backup-type-selector :deep(.el-radio-button--small .el-radio-button__inner) {
  padding: 5px 15px;
}

.backup-type-selector :deep(.el-radio-button:first-child .el-radio-button__inner) {
  background-color: #f0f9eb;
  border-color: #c2e7b0;
  color: #67c23a;
}

.backup-type-selector :deep(.el-radio-button:last-child .el-radio-button__inner) {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.backup-info {
  display: flex;
  align-items: center;
  margin-left: 15px;
  gap: 10px;
}

.backup-type-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.backup-type-tag.before {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

.backup-type-tag.after {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.backup-id-display {
  font-size: 13px;
  color: #606266;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #ebeef5;
}

.backup-id-text {
  font-family: monospace;
  color: #409eff;
  font-weight: 500;
}

/* 双备份标签样式 */
:deep(.el-tag--primary.el-tag--plain) {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #b3d8ff;
}

:deep(.el-tag--primary.el-tag--plain:hover) {
  background-color: #409eff;
  color: #fff;
}

.backup-status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  text-align: center;
}

:deep(.el-tag--danger.el-tag--plain) {
  background-color: #fef0f0;
  color: #f56c6c;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}
</style>