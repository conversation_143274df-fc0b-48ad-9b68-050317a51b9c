const express = require('express');
const router = express.Router();
const backupController = require('../controllers/backupController');
const disableAuth = require('../middleware/disableAuth');

// 为所有路由添加禁用身份验证的中间件
router.use(disableAuth);

// 获取所有备份
router.get('/', backupController.getAllBackups);

// 获取单个备份的详细信息
router.get('/:id', backupController.getBackupById);

// 获取单个备份的tokens
router.get('/:id/tokens', backupController.getBackupTokens);

// 创建备份
router.post('/', backupController.createBackup);

// 删除备份
router.delete('/:id', backupController.deleteBackup);

// 清空所有备份
router.delete('/', backupController.clearAllBackups);

module.exports = router; 