import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import apiConfig from './src/config/apiConfig'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('el-icon-')
        }
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 5174,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送请求到:', proxyReq.path);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('收到响应:', proxyRes.statusCode);
          });
        }
      },
      '/pdd-api': {
        target: 'https://mobile.yangkeduo.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/pdd-api/, ''),
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('拼多多API代理错误', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送请求到拼多多API:', proxyReq.path);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('收到拼多多API响应:', proxyRes.statusCode);
          });
        }
      },
      '/wp-json': {
        target: apiConfig.baseUrl,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('未卖Token API代理错误', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送请求到未卖Token API:', proxyReq.path);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('收到未卖Token API响应:', proxyRes.statusCode);
          });
        }
      }
    }
  }
})