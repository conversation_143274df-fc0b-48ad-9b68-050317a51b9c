const { workerData, parentPort } = require('worker_threads');
const axios = require('axios');

// 获取工作线程数据
const { tokenChunk, workerId, concurrency, timeout, retryCount } = workerData;

// 向主线程发送开始消息
parentPort.postMessage({
  type: 'start',
  workerId,
  tokenCount: tokenChunk.length
});

/**
 * 查询Token的订单信息
 */
async function queryTokenOrderInfo(tokenData, timeout = 8000, retryCount = 1) {
  try {
    // 设置请求头
    const headers = {
      'AccessToken': tokenData.token,
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Referer': 'https://mobile.yangkeduo.com/orders.html',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
    };

    // 首先检查用户状态是否在线
    try {
      const onlineCheckResponse = await axios.get('https://mobile.yangkeduo.com/proxy/api/api/apollo/v3/user/active', {
        headers,
        timeout: 5000
      });

      // 如果用户不在线，直接返回掉线状态
      if (onlineCheckResponse.data && onlineCheckResponse.data.error_code) {
        return {
          uid: tokenData.uid,
          success: true,
          isOnline: false,
          status: '掉线',
          message: '用户处于掉线状态'
        };
      }
    } catch (error) {
      // 如果检查在线状态出错，也视为掉线
      return {
        uid: tokenData.uid,
        success: true,
        isOnline: false,
        status: '掉线',
        message: '用户处于掉线状态'
      };
    }

    // 用户在线，查询订单信息
    const allOrders = [];
    let page = 1;
    let hasMorePages = true;
    const MAX_PAGES = 100; // 设置一个足够大的页数上限以防无限循环

    // 分页查询，获取所有订单 - 使用并发查询多个页面
    // 初始化第一页
    let initialPage = 1;
    let initialOffset = '';
    let offsetMap = {}; // 存储每个页面的offset

    // 首先查询第一页，获取初始offset
    try {
      console.log(`查询第${initialPage}页订单, UID: ${tokenData.uid}`);
      const queryParams = {
        type: "all",
        page: initialPage,
        origin_host_name: "mobile.yangkeduo.com",
        page_from: 0,
        size: 50
      };

      const response = await axios.post('https://mobile.yangkeduo.com/proxy/api/api/aristotle/order_list_v3?is_back=1',
        queryParams,
        {
          headers,
          timeout: timeout
        }
      );

      // 处理响应数据
      if (response.data && !response.data.error_code) {
        const pageOrders = response.data.orders || [];
        console.log(`第${initialPage}页查询到${pageOrders.length}个订单, UID: ${tokenData.uid}`);

        // 添加到总订单列表
        allOrders.push(...pageOrders);

        // 检查是否有更多页
        if (pageOrders.length > 0) {
          // 优先使用API返回的offset
          if (response.data.offset) {
            console.log(`使用API返回的offset进行分页: ${response.data.offset}`);
            initialOffset = response.data.offset;
            offsetMap[initialPage] = initialOffset;
            page = initialPage + 1;
          }
          // 如果API没有返回offset，则使用最后一个订单的sort_id或order_sn
          else {
            const lastOrder = pageOrders[pageOrders.length - 1];
            if (lastOrder && (lastOrder.sort_id || lastOrder.order_sn)) {
              initialOffset = lastOrder.sort_id || lastOrder.order_sn;
              offsetMap[initialPage] = initialOffset;
              console.log(`使用最后一个订单ID作为偏移量: ${initialOffset}`);
              page = initialPage + 1;
            } else {
              console.log(`没有获取到有效的偏移量，停止分页查询, UID: ${tokenData.uid}`);
              hasMorePages = false;
            }
          }
        } else {
          console.log(`第${initialPage}页没有查询到订单，停止分页查询, UID: ${tokenData.uid}`);
          hasMorePages = false;
        }
      } else {
        // 如果API返回错误代码，停止分页查询
        console.log(`订单查询API返回错误, UID: ${tokenData.uid}, 错误代码: ${response.data?.error_code}`);
        hasMorePages = false;
      }
    } catch (error) {
      // 捕获分页查询中的异常
      console.error(`查询第${initialPage}页订单失败, UID: ${tokenData.uid}, Error: ${error.message}`);
      hasMorePages = false;
    }

    // 如果有更多页，使用并发查询
    if (hasMorePages) {
      // 设置并发查询的页数
      const CONCURRENT_PAGES = 10; // 同时查询10个页面

      while (hasMorePages && page <= MAX_PAGES) {
        try {
          // 准备并发查询的页面
          const pagesToQuery = [];
          const startPage = page;
          const endPage = Math.min(page + CONCURRENT_PAGES - 1, MAX_PAGES);

          console.log(`准备并发查询第${startPage}到第${endPage}页订单, UID: ${tokenData.uid}`);

          // 创建并发查询任务
          for (let currentPage = startPage; currentPage <= endPage; currentPage++) {
            // 获取上一页的offset
            const prevPage = currentPage - 1;
            const currentOffset = offsetMap[prevPage];

            if (!currentOffset) {
              console.log(`没有找到第${prevPage}页的offset，无法查询第${currentPage}页, UID: ${tokenData.uid}`);
              continue;
            }

            // 创建查询任务
            pagesToQuery.push({
              page: currentPage,
              offset: currentOffset
            });
          }

          if (pagesToQuery.length === 0) {
            console.log(`没有可查询的页面，停止分页查询, UID: ${tokenData.uid}`);
            hasMorePages = false;
            break;
          }

          // 执行并发查询
          const queryPromises = pagesToQuery.map(pageInfo => {
            return (async () => {
              try {
                console.log(`并发查询第${pageInfo.page}页订单, UID: ${tokenData.uid}, offset: ${pageInfo.offset}`);
                const queryParams = {
                  type: "all",
                  page: pageInfo.page,
                  origin_host_name: "mobile.yangkeduo.com",
                  page_from: 0,
                  size: 50,
                  offset: pageInfo.offset
                };

                const response = await axios.post('https://mobile.yangkeduo.com/proxy/api/api/aristotle/order_list_v3?is_back=1',
                  queryParams,
                  {
                    headers,
                    timeout: timeout
                  }
                );

                // 处理响应数据
                if (response.data && !response.data.error_code) {
                  const pageOrders = response.data.orders || [];
                  console.log(`第${pageInfo.page}页查询到${pageOrders.length}个订单, UID: ${tokenData.uid}`);

                  // 获取下一页的offset
                  let nextOffset = '';
                  if (pageOrders.length > 0) {
                    // 优先使用API返回的offset
                    if (response.data.offset) {
                      nextOffset = response.data.offset;
                    }
                    // 如果API没有返回offset，则使用最后一个订单的sort_id或order_sn
                    else {
                      const lastOrder = pageOrders[pageOrders.length - 1];
                      if (lastOrder && (lastOrder.sort_id || lastOrder.order_sn)) {
                        nextOffset = lastOrder.sort_id || lastOrder.order_sn;
                      }
                    }
                  }

                  return {
                    page: pageInfo.page,
                    orders: pageOrders,
                    hasOrders: pageOrders.length > 0,
                    nextOffset: nextOffset
                  };
                } else {
                  console.log(`第${pageInfo.page}页订单查询API返回错误, UID: ${tokenData.uid}, 错误代码: ${response.data?.error_code}`);
                  return {
                    page: pageInfo.page,
                    orders: [],
                    hasOrders: false,
                    error: response.data?.error_code
                  };
                }
              } catch (error) {
                console.error(`查询第${pageInfo.page}页订单失败, UID: ${tokenData.uid}, Error: ${error.message}`);
                return {
                  page: pageInfo.page,
                  orders: [],
                  hasOrders: false,
                  error: error.message
                };
              }
            })();
          });

          // 等待所有查询完成
          const results = await Promise.all(queryPromises);

          // 处理查询结果
          let hasAnyOrders = false;
          let maxPageWithOrders = 0;

          // 按页码排序结果，确保订单按正确顺序添加
          results.sort((a, b) => a.page - b.page);

          results.forEach(result => {
            if (result.hasOrders) {
              // 添加到总订单列表
              allOrders.push(...result.orders);
              hasAnyOrders = true;

              // 更新offset映射
              if (result.nextOffset) {
                offsetMap[result.page] = result.nextOffset;
                maxPageWithOrders = Math.max(maxPageWithOrders, result.page);
              }
            }
          });

          // 更新下一批查询的起始页
          if (hasAnyOrders && maxPageWithOrders > 0) {
            page = maxPageWithOrders + 1;
          } else {
            console.log(`没有查询到更多订单，停止分页查询, UID: ${tokenData.uid}`);
            hasMorePages = false;
          }

        } catch (error) {
          console.error(`并发查询订单失败, UID: ${tokenData.uid}, Error: ${error.message}`);
          hasMorePages = false;
        }
      }
    }

    if (page > MAX_PAGES) {
      console.log(`达到最大查询页数限制(${MAX_PAGES}页), UID: ${tokenData.uid}`);
    }

    // 处理响应数据
    if (allOrders.length > 0) {
      // 返回所有订单信息
      const orderList = allOrders.map(order => ({
        orderId: order.order_sn || '',
        status: order.order_status_prompt || '',
        amount: order.order_amount / 100 || 0, // 转换为元
        orderTime: order.order_time ? new Date(order.order_time * 1000).toLocaleString() : '',
        goodsInfo: order.order_goods && order.order_goods.length > 0
          ? `${order.order_goods[0].goods_name}` : '未知商品',
        trackingNumber: order.tracking_number || '',
        mallName: order.mall?.mall_name || '',
        orderType: order.type || 0,
        extraInfo: order
      }));

      return {
        uid: tokenData.uid,
        success: true,
        isOnline: true,
        status: '在线',
        data: {
          orderCount: orderList.length,
          orders: orderList
        }
      };
    } else {
      return {
        uid: tokenData.uid,
        success: true,
        isOnline: true,
        status: '在线',
        data: {
          orderCount: 0,
          orders: []
        },
        message: '没有找到订单信息'
      };
    }
  } catch (error) {
    // 重试机制
    if (retryCount > 0) {
      return queryTokenOrderInfo(tokenData, timeout, retryCount - 1);
    }

    return {
      uid: tokenData.uid,
      success: false,
      status: '未知',
      message: `查询订单信息失败: ${error.message}`
    };
  }
}

// 处理并发请求
async function processBatch(tokens, concurrency) {
  const results = [];
  let processed = 0;

  // 分批处理
  for (let i = 0; i < tokens.length; i += concurrency) {
    const batch = tokens.slice(i, i + concurrency);
    const batchPromises = batch.map(tokenData =>
      queryTokenOrderInfo(tokenData, timeout, retryCount)
    );

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    processed += batch.length;

    // 向主线程发送进度消息
    parentPort.postMessage({
      type: 'progress',
      workerId,
      processed,
      total: tokens.length
    });

    // 添加短暂延迟以避免API限制 - 减少延迟时间
    if (i + concurrency < tokens.length) {
      await new Promise(resolve => setTimeout(resolve, 200)); // 从300ms减少到200ms
    }
  }

  return results;
}

// 主处理逻辑
async function main() {
  try {
    const results = await processBatch(tokenChunk, concurrency);

    // 发送结果到主线程
    parentPort.postMessage(results);
  } catch (error) {
    // 发送错误到主线程
    parentPort.postMessage({
      type: 'error',
      workerId,
      error: error.message
    });
  }
}

// 执行主函数
main();