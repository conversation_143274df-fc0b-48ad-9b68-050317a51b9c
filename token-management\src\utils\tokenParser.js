import * as XLSX from 'xlsx';

// 解析导入的文件
export async function parseImportedFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      // 处理Excel文件
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          // 检查表头并处理数据
          if (jsonData.length > 0) {
            const processedData = processExcelData(jsonData);
            resolve(processedData);
          } else {
            reject(new Error('Excel文件中没有数据'));
          }
        } catch (error) {
          console.error('解析Excel文件失败:', error);
          reject(new Error('解析Excel文件失败'));
        }
      };
      reader.readAsArrayBuffer(file);
    } else if (fileName.endsWith('.txt')) {
      // 处理TXT文件
      reader.onload = (e) => {
        try {
          const text = e.target.result;
          const processedData = processTxtData(text);
          resolve(processedData);
        } catch (error) {
          console.error('解析TXT文件失败:', error);
          reject(new Error('解析TXT文件失败'));
        }
      };
      reader.readAsText(file);
    } else {
      reject(new Error('不支持的文件格式'));
    }
  });
}

// 处理Excel数据，智能识别表头
export function processExcelData(jsonData) {
  const result = [];
  
  // 检查表头，寻找UID、token和用户相关列
  const firstRow = jsonData[0];
  const headers = Object.keys(firstRow);
  
  // 查找匹配的列名
  let uidColumn = headers.find(h => 
    h.toLowerCase().includes('uid') || 
    h.toLowerCase() === 'id' || 
    h.toLowerCase().includes('用户id')
  );
  
  let tokenColumn = headers.find(h => 
    h.toLowerCase().includes('token') || 
    h.toLowerCase().includes('令牌')
  );
  
  let userColumn = headers.find(h => 
    h.toLowerCase().includes('user') || 
    h.toLowerCase().includes('username') || 
    h.toLowerCase().includes('用户') || 
    h.toLowerCase().includes('用户名')
  );
  
  // 如果没有找到匹配的列，使用前三列
  if (!uidColumn || !tokenColumn || !userColumn) {
    if (headers.length >= 3) {
      uidColumn = headers[0];
      tokenColumn = headers[1];
      userColumn = headers[2];
    } else if (headers.length === 2) {
      uidColumn = headers[0];
      tokenColumn = headers[1];
      userColumn = null;
    } else if (headers.length === 1) {
      // 尝试解析单列数据
      return processSingleColumnData(jsonData, headers[0]);
    } else {
      throw new Error('Excel文件格式不正确，无法识别列');
    }
  }
  
  // 处理每一行数据
  jsonData.forEach(row => {
    if (row[uidColumn] && row[tokenColumn]) {
      result.push({
        uid: String(row[uidColumn]),
        token: String(row[tokenColumn]),
        user: userColumn && row[userColumn] ? String(row[userColumn]) : '未知用户'
      });
    }
  });
  
  return result;
}

// 处理单列数据，尝试从中解析出多个字段
function processSingleColumnData(jsonData, column) {
  const result = [];
  
  jsonData.forEach(row => {
    const value = String(row[column]);
    // 尝试用常见分隔符分割
    const parts = value.split(/[,;:|\/\\\s]+/).filter(Boolean);
    
    if (parts.length >= 2) {
      result.push({
        uid: parts[0],
        token: parts[1],
        user: parts[2] || '未知用户'
      });
    }
  });
  
  return result;
}

// 处理TXT数据，支持多种分隔符
export function processTxtData(text) {
  const result = [];
  const lines = text.split(/\r?\n/).filter(line => line.trim());
  
  lines.forEach(line => {
    // 1. 先尝试使用"----"作为分隔符
    if (line.includes('----')) {
      const parts = line.split('----');
      if (parts.length >= 2) {
        result.push({
          uid: parts[0].trim(),
          token: parts[1].trim(),
          user: parts.length >= 3 ? parts[2].trim() : '未知用户'
        });
        return;
      }
    }
    
    // 2. 再尝试使用常见分隔符（如制表符、逗号、分号等）
    const commonSeparators = ['\t', ',', ';', '|', ' '];
    
    for (const separator of commonSeparators) {
      if (line.includes(separator)) {
        const parts = line.split(separator).filter(Boolean);
        if (parts.length >= 2) {
          result.push({
            uid: parts[0].trim(),
            token: parts[1].trim(),
            user: parts.length >= 3 ? parts[2].trim() : '未知用户'
          });
          return;
        }
      }
    }
    
    // 3. 如果上述分隔符都不匹配，尝试按固定长度分割（假设UID和Token长度相对固定）
    if (line.length > 20) {
      const uid = line.substring(0, 10).trim();
      const token = line.substring(10).trim();
      if (uid && token) {
        result.push({
          uid,
          token,
          user: '未知用户'
        });
      }
    }
  });
  
  return result;
} 