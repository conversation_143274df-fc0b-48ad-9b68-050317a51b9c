const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');

/**
 * 获取随机头像
 * 从头像库目录中随机选择一个头像并返回其base64编码
 */
router.get('/random-avatar', (req, res) => {
  try {
    // 头像库目录路径
    const avatarDir = path.join(__dirname, '../../..', '头像库');
    
    // 读取目录中的所有文件
    fs.readdir(avatarDir, (err, files) => {
      if (err) {
        console.error('读取头像库目录失败:', err);
        return res.status(500).json({
          success: false,
          message: '读取头像库目录失败'
        });
      }
      
      // 过滤出图片文件
      const imageFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
      });
      
      if (imageFiles.length === 0) {
        return res.status(404).json({
          success: false,
          message: '头像库中没有图片文件'
        });
      }
      
      // 随机选择一个图片文件
      const randomIndex = Math.floor(Math.random() * imageFiles.length);
      const randomImage = imageFiles[randomIndex];
      const imagePath = path.join(avatarDir, randomImage);
      
      // 读取图片文件并转换为base64
      fs.readFile(imagePath, (err, data) => {
        if (err) {
          console.error('读取图片文件失败:', err);
          return res.status(500).json({
            success: false,
            message: '读取图片文件失败'
          });
        }
        
        // 转换为base64
        const imageBase64 = data.toString('base64');
        
        // 返回结果
        res.json({
          success: true,
          message: '获取随机头像成功',
          imageBase64,
          fileName: randomImage
        });
      });
    });
  } catch (error) {
    console.error('获取随机头像失败:', error);
    res.status(500).json({
      success: false,
      message: `获取随机头像失败: ${error.message}`
    });
  }
});

module.exports = router;
