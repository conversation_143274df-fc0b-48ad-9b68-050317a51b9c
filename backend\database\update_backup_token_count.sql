-- 更新备份表token_count字段的SQL脚本
-- 创建日期：2024年06月
-- 修改日期：2024年06月
-- 功能：
-- 1. 创建存储过程更新所有备份的token_count字段
-- 2. 创建触发器在插入/删除备份Token关联记录时自动更新token_count字段
-- 修改说明：
-- 1. 修改计算逻辑，只使用backup_tokens_complete表计算token_count
--    原因：之前的计算方式会导致token数量显示为双倍，因为同一个token会同时存储在
--    backup_tokens_complete表和backup_tokens/backup_tokens_string表中

USE token_management;

-- 删除已存在的存储过程（如果有）
DROP PROCEDURE IF EXISTS update_all_backup_token_counts;

-- 创建存储过程，用于更新所有备份的token_count字段
DELIMITER //
CREATE PROCEDURE update_all_backup_token_counts()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE backup_id_var INT;
    DECLARE cur CURSOR FOR SELECT id FROM backups;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO backup_id_var;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 更新当前备份的token_count字段
        UPDATE backups b SET token_count = (
            -- 只计算backup_tokens_complete表中的Token数量
            SELECT IFNULL(
                (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = b.id), 0
            )
        ) WHERE b.id = backup_id_var;

    END LOOP;

    CLOSE cur;

    SELECT CONCAT('已更新 ', (SELECT COUNT(*) FROM backups), ' 个备份的token_count字段') AS result;
END //
DELIMITER ;

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS after_backup_tokens_insert;
DROP TRIGGER IF EXISTS after_backup_tokens_delete;
DROP TRIGGER IF EXISTS after_backup_tokens_string_insert;
DROP TRIGGER IF EXISTS after_backup_tokens_string_delete;
DROP TRIGGER IF EXISTS after_backup_tokens_complete_insert;
DROP TRIGGER IF EXISTS after_backup_tokens_complete_delete;

-- 创建触发器：在backup_tokens表插入记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_insert
AFTER INSERT ON backup_tokens
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = NEW.backup_id), 0
        )
    ) WHERE id = NEW.backup_id;
END //
DELIMITER ;

-- 创建触发器：在backup_tokens表删除记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_delete
AFTER DELETE ON backup_tokens
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = OLD.backup_id), 0
        )
    ) WHERE id = OLD.backup_id;
END //
DELIMITER ;

-- 创建触发器：在backup_tokens_string表插入记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_string_insert
AFTER INSERT ON backup_tokens_string
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = NEW.backup_id), 0
        )
    ) WHERE id = NEW.backup_id;
END //
DELIMITER ;

-- 创建触发器：在backup_tokens_string表删除记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_string_delete
AFTER DELETE ON backup_tokens_string
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = OLD.backup_id), 0
        )
    ) WHERE id = OLD.backup_id;
END //
DELIMITER ;

-- 创建触发器：在backup_tokens_complete表插入记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_complete_insert
AFTER INSERT ON backup_tokens_complete
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = NEW.backup_id), 0
        )
    ) WHERE id = NEW.backup_id;
END //
DELIMITER ;

-- 创建触发器：在backup_tokens_complete表删除记录后更新token_count
DELIMITER //
CREATE TRIGGER after_backup_tokens_complete_delete
AFTER DELETE ON backup_tokens_complete
FOR EACH ROW
BEGIN
    UPDATE backups SET token_count = (
        -- 只计算backup_tokens_complete表中的Token数量
        SELECT IFNULL(
            (SELECT COUNT(*) FROM backup_tokens_complete WHERE backup_id = OLD.backup_id), 0
        )
    ) WHERE id = OLD.backup_id;
END //
DELIMITER ;

-- 执行存储过程，立即更新所有现有备份的token_count字段
CALL update_all_backup_token_counts();

-- 输出确认信息
SELECT 'token_count字段更新脚本执行完成' AS result;
