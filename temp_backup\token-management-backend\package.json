{"name": "token-management-backend", "version": "1.0.0", "description": "Token管理系统后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.5.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}