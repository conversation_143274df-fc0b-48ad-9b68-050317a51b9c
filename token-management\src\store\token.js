import { defineStore } from 'pinia'

export const useTokenStore = defineStore('token', {
  state: () => ({
    tokens: [],
    backups: [],
    lastExpiryDate: '2024-06-30',
    totalTokens: 0,
    totalBackups: 0,
    loading: false
  }),
  
  getters: {
    getTokenCount: (state) => state.totalTokens,
    getBackupCount: (state) => state.totalBackups,
    getLastExpiryDate: (state) => state.lastExpiryDate,
    getTokens: (state) => state.tokens,
    getBackups: (state) => state.backups
  },
  
  actions: {
    // 这里只是前端展示，不添加实际功能代码
    setTokens(tokens) {
      this.tokens = tokens
      this.totalTokens = tokens.length
    },
    
    setBackups(backups) {
      this.backups = backups
      this.totalBackups = backups.length
    },
    
    setLastExpiryDate(date) {
      this.lastExpiryDate = date
    }
  }
}) 