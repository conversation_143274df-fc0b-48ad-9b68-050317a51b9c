import{_ as xt,r as p,c as I,w as K,o as $t,a as u,b as r,d as t,e as s,t as d,f as o,g as l,u as Vt,h as _,i as P,F as C,j as D,k as qt,n as V,E as O}from"./index-CckIkgn1.js";import{u as Mt}from"./token-Bnd6eYkJ.js";const St={class:"dashboard-container"},Bt={class:"welcome-section"},zt={class:"welcome-content"},At={class:"welcome-actions"},Lt={class:"data-card-container"},Et={class:"data-card token-card"},Nt={class:"data-card-icon"},Rt={class:"data-card-content"},It={class:"data-card-value"},Pt={key:0,class:"data-card-trend"},Ut={key:1,class:"data-card-trend"},jt={class:"data-card-container"},Ft={class:"data-card backup-card"},Ht={class:"data-card-icon"},Gt={class:"data-card-content"},Jt={class:"data-card-value"},Kt={key:0,class:"data-card-trend"},Ot={key:1,class:"data-card-trend"},Qt={class:"data-card-container"},Wt={class:"data-card expiry-card"},Xt={class:"data-card-icon"},Yt={class:"data-card-content"},Zt={class:"data-card-value"},te={class:"data-card-countdown"},ee={class:"data-card-container"},se={class:"data-card status-card"},oe={class:"data-card-icon"},ne={class:"data-card-content"},ae={class:"data-card-value"},le={class:"data-card-badges"},de={class:"card-header"},ie={class:"header-actions"},ce={key:0,class:"chart-container"},re={class:"chart-placeholder pie-chart"},ue={class:"pie-container"},_e={class:"pie-legend"},pe={class:"legend-label"},ve={class:"pie"},me={class:"pie-center"},ke={key:1,class:"chart-container"},fe={class:"chart-placeholder bar-chart"},he={class:"bar-container"},ge={class:"bar-value"},ye={class:"bar-label"},be={class:"card-header"},we={class:"header-actions"},Te={class:"chart-container"},Ce={class:"chart-placeholder line-chart"},De={class:"line-container"},xe={class:"line-header"},$e={class:"line-title"},Ve={class:"line-body"},qe={class:"line-chart-container"},Me={class:"line-points"},Se={class:"point-tooltip"},Be={class:"line-svg",viewBox:"0 0 100 50"},ze=["points"],Ae={class:"line-footer"},Le={class:"card-header"},Ee={class:"quick-actions"},Ne={class:"quick-action-icon token-reset-icon"},Re={class:"quick-action-icon auto-reset-icon"},Ie={class:"quick-action-icon backup-icon"},Pe={class:"quick-action-icon settings-icon"},Ue={class:"card-header"},je={class:"header-actions"},Fe={__name:"Dashboard",setup(He){const q=Mt(),Q=Vt(),U=["今天Token管理状态良好","您有5个Token即将过期","系统运行正常","有新的系统更新可用"],W=p(U[Math.floor(Math.random()*U.length)]),X=I(()=>new Date().toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})),B=p(12.5),z=p(-4.2),Y=I(()=>{const a=new Date,h=new Date(q.getLastExpiryDate)-a;return Math.ceil(h/(1e3*60*60*24))}),A=p(["正常","过期","待激活"]),v=p([65,12,23]),Z=p(null),tt=p(null),M=p("pie"),f=p("month");K(M,a=>{}),K(f,a=>{});const et=a=>new Date(a).toLocaleDateString("zh-CN"),st=a=>["success","danger","warning"][a]||"info",ot=a=>({Token重置:"warning",Token备份:"success",Token导入:"primary",Token删除:"danger"})[a]||"info",L=p(!1),y=p(null),nt=a=>{y.value=a,L.value=!0},at=()=>{Z.value},lt=()=>{tt.value},dt=()=>{O.success("数据已刷新")},it=()=>{O.success("活动记录已导出")},x=a=>{Q.push(a)},ct=p([{time:"2024-03-29 14:30:45",type:"Token重置",content:"重置了50个Token",operator:"管理员",status:"成功"},{time:"2024-03-28 16:12:33",type:"Token备份",content:"备份了所有Token数据",operator:"管理员",status:"成功"},{time:"2024-03-27 09:45:21",type:"Token导入",content:"导入了100个新Token",operator:"管理员",status:"成功"},{time:"2024-03-26 11:28:39",type:"Token删除",content:"删除了5个过期Token",operator:"管理员",status:"成功"},{time:"2024-03-25 08:15:22",type:"Token备份",content:"手动创建了系统备份",operator:"管理员",status:"成功"}]);$t(()=>{setTimeout(()=>{at(),lt()},100)});const E=a=>{const e=["#1890ff","#f5222d","#faad14","#52c41a","#722ed1"];return e[a%e.length]},rt=a=>{const e=v.value.reduce((k,b)=>k+b,0);let h=0;for(let k=0;k<a;k++)h+=v.value[k]/e*360;const c=v.value[a]/e*360,m=h+c;return{background:`conic-gradient(${E(a)} ${h}deg ${m}deg, transparent ${m}deg 360deg)`,zIndex:3-a}},ut=a=>{const e=Math.max(...v.value);return`${a/e*200}px`},N=()=>f.value==="week"?[12,15,10,23,19,8,11]:f.value==="month"?[5,10,15,20,25,30,25,20,15,35,40,30]:[20,18,19,23,29,33,31,12,44,32,9,15],_t=()=>{const a=N(),e=Math.max(...a);return a.map((c,m)=>{const k=m/(a.length-1)*100,b=50-c/e*50;return`${k},${b}`}).join(" ")},j=()=>f.value==="week"?["周一","周二","周三","周四","周五","周六","周日"]:f.value==="month"?["1日","5日","10日","15日","20日","25日","30日"]:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],pt=I(()=>({week:"本周",month:"本月",year:"全年"})[f.value]);return(a,e)=>{const h=l("el-icon-refresh"),c=l("el-icon"),m=l("el-button"),k=l("el-icon-key"),b=l("el-icon-top"),F=l("el-icon-bottom"),g=l("el-col"),H=l("el-icon-document-copy"),vt=l("el-icon-timer"),mt=l("el-icon-data-line"),kt=l("el-badge"),R=l("el-row"),$=l("el-radio-button"),G=l("el-radio-group"),S=l("el-card"),ft=l("el-icon-magic-stick"),ht=l("el-icon-refresh-right"),gt=l("el-icon-alarm-clock"),yt=l("el-icon-set-up"),bt=l("el-icon-download"),wt=l("el-icon-more"),w=l("el-table-column"),J=l("el-tag"),Tt=l("el-table"),T=l("el-descriptions-item"),Ct=l("el-descriptions"),Dt=l("el-dialog");return r(),u("div",St,[t("div",Bt,[t("div",zt,[e[8]||(e[8]=t("h1",null,"欢迎使用 Token 管理系统",-1)),t("p",null,"今天是 "+d(X.value)+"，"+d(W.value),1)]),t("div",At,[s(m,{type:"primary",onClick:dt},{default:o(()=>[s(c,null,{default:o(()=>[s(h)]),_:1}),e[9]||(e[9]=_("刷新数据 "))]),_:1})])]),s(R,{gutter:20},{default:o(()=>[s(g,{xs:24,sm:12,md:6},{default:o(()=>[t("div",Lt,[t("div",Et,[t("div",Nt,[s(c,null,{default:o(()=>[s(k)]),_:1})]),t("div",Rt,[t("div",It,d(P(q).getTokenCount),1),e[10]||(e[10]=t("div",{class:"data-card-title"},"Token总数",-1))]),B.value>0?(r(),u("div",Pt,[s(c,{class:"trend-up"},{default:o(()=>[s(b)]),_:1}),t("span",null,d(B.value)+"%",1)])):(r(),u("div",Ut,[s(c,{class:"trend-down"},{default:o(()=>[s(F)]),_:1}),t("span",null,d(Math.abs(B.value))+"%",1)]))])])]),_:1}),s(g,{xs:24,sm:12,md:6},{default:o(()=>[t("div",jt,[t("div",Ft,[t("div",Ht,[s(c,null,{default:o(()=>[s(H)]),_:1})]),t("div",Gt,[t("div",Jt,d(P(q).getBackupCount),1),e[11]||(e[11]=t("div",{class:"data-card-title"},"备份数量",-1))]),z.value>0?(r(),u("div",Kt,[s(c,{class:"trend-up"},{default:o(()=>[s(b)]),_:1}),t("span",null,d(z.value)+"%",1)])):(r(),u("div",Ot,[s(c,{class:"trend-down"},{default:o(()=>[s(F)]),_:1}),t("span",null,d(Math.abs(z.value))+"%",1)]))])])]),_:1}),s(g,{xs:24,sm:12,md:6},{default:o(()=>[t("div",Qt,[t("div",Wt,[t("div",Xt,[s(c,null,{default:o(()=>[s(vt)]),_:1})]),t("div",Yt,[t("div",Zt,d(et(P(q).getLastExpiryDate)),1),e[12]||(e[12]=t("div",{class:"data-card-title"},"最近到期时间",-1))]),t("div",te," 还有 "+d(Y.value)+" 天 ",1)])])]),_:1}),s(g,{xs:24,sm:12,md:6},{default:o(()=>[t("div",ee,[t("div",se,[t("div",oe,[s(c,null,{default:o(()=>[s(mt)]),_:1})]),t("div",ne,[t("div",ae,d(A.value.length)+"种",1),e[13]||(e[13]=t("div",{class:"data-card-title"},"Token状态",-1))]),t("div",le,[(r(!0),u(C,null,D(v.value,(n,i)=>(r(),qt(kt,{key:i,value:n,type:st(i),class:"status-badge"},null,8,["value","type"]))),128))])])])]),_:1})]),_:1}),s(R,{gutter:20,class:"chart-row"},{default:o(()=>[s(g,{xs:24,lg:12},{default:o(()=>[s(S,{shadow:"hover",class:"chart-card"},{header:o(()=>[t("div",de,[e[16]||(e[16]=t("span",null,"Token状态分布",-1)),t("div",ie,[s(G,{modelValue:M.value,"onUpdate:modelValue":e[0]||(e[0]=n=>M.value=n),size:"small"},{default:o(()=>[s($,{label:"pie"},{default:o(()=>e[14]||(e[14]=[_("饼图")])),_:1}),s($,{label:"bar"},{default:o(()=>e[15]||(e[15]=[_("柱状图")])),_:1})]),_:1},8,["modelValue"])])])]),default:o(()=>[M.value==="pie"?(r(),u("div",ce,[t("div",re,[t("div",ue,[t("div",_e,[(r(!0),u(C,null,D(A.value,(n,i)=>(r(),u("div",{key:i,class:"legend-item"},[t("span",{class:"legend-color",style:V({backgroundColor:E(i)})},null,4),t("span",pe,d(n)+": "+d(v.value[i]),1)]))),128))]),t("div",ve,[(r(!0),u(C,null,D(v.value,(n,i)=>(r(),u("div",{key:i,class:"pie-slice",style:V(rt(i))},null,4))),128)),t("div",me,d(v.value.reduce((n,i)=>n+i,0)),1)])])])])):(r(),u("div",ke,[t("div",fe,[t("div",he,[(r(!0),u(C,null,D(v.value,(n,i)=>(r(),u("div",{key:i,class:"bar-item"},[t("div",{class:"bar-column",style:V({height:ut(n),backgroundColor:E(i)})},[t("div",ge,d(n),1)],4),t("div",ye,d(A.value[i]),1)]))),128))])])]))]),_:1})]),_:1}),s(g,{xs:24,lg:12},{default:o(()=>[s(S,{shadow:"hover",class:"chart-card"},{header:o(()=>[t("div",be,[e[20]||(e[20]=t("span",null,"Token使用趋势",-1)),t("div",we,[s(G,{modelValue:f.value,"onUpdate:modelValue":e[1]||(e[1]=n=>f.value=n),size:"small"},{default:o(()=>[s($,{label:"week"},{default:o(()=>e[17]||(e[17]=[_("本周")])),_:1}),s($,{label:"month"},{default:o(()=>e[18]||(e[18]=[_("本月")])),_:1}),s($,{label:"year"},{default:o(()=>e[19]||(e[19]=[_("全年")])),_:1})]),_:1},8,["modelValue"])])])]),default:o(()=>[t("div",Te,[t("div",Ce,[t("div",De,[t("div",xe,[t("div",$e,d(pt.value),1),e[21]||(e[21]=t("div",{class:"line-subtitle"},"Token使用量变化趋势",-1))]),t("div",Ve,[t("div",qe,[t("div",Me,[(r(!0),u(C,null,D(N(),(n,i)=>(r(),u("div",{key:i,class:"point",style:V({left:`${i/(N().length-1)*100}%`,bottom:`${n/50*100}%`})},[t("div",Se,d(n),1)],4))),128))]),(r(),u("svg",Be,[t("polyline",{class:"line-path",points:_t()},null,8,ze)])),e[22]||(e[22]=t("div",{class:"line-area"},null,-1))])]),t("div",Ae,[(r(!0),u(C,null,D(j(),(n,i)=>(r(),u("div",{key:i,class:"line-label",style:V({left:`${i/(j().length-1)*100}%`})},d(n),5))),128))])])])])]),_:1})]),_:1})]),_:1}),s(S,{shadow:"hover",class:"quick-action-card"},{header:o(()=>[t("div",Le,[t("span",null,[s(c,null,{default:o(()=>[s(ft)]),_:1}),e[23]||(e[23]=_(" 快捷操作"))])])]),default:o(()=>[t("div",Ee,[t("div",{class:"quick-action-item",onClick:e[2]||(e[2]=n=>x("/token-reset"))},[t("div",Ne,[s(c,null,{default:o(()=>[s(ht)]),_:1})]),e[24]||(e[24]=t("span",null,"Token重置",-1)),e[25]||(e[25]=t("div",{class:"quick-action-desc"},"批量重置和更新Token",-1))]),t("div",{class:"quick-action-item",onClick:e[3]||(e[3]=n=>x("/token-auto-reset"))},[t("div",Re,[s(c,null,{default:o(()=>[s(gt)]),_:1})]),e[26]||(e[26]=t("span",null,"自动重置",-1)),e[27]||(e[27]=t("div",{class:"quick-action-desc"},"设置定时自动重置规则",-1))]),t("div",{class:"quick-action-item",onClick:e[4]||(e[4]=n=>x("/token-backup"))},[t("div",Ie,[s(c,null,{default:o(()=>[s(H)]),_:1})]),e[28]||(e[28]=t("span",null,"Token备份",-1)),e[29]||(e[29]=t("div",{class:"quick-action-desc"},"备份和恢复Token数据",-1))]),t("div",{class:"quick-action-item",onClick:e[5]||(e[5]=n=>x("/settings"))},[t("div",Pe,[s(c,null,{default:o(()=>[s(yt)]),_:1})]),e[30]||(e[30]=t("span",null,"系统设置",-1)),e[31]||(e[31]=t("div",{class:"quick-action-desc"},"配置系统和用户参数",-1))])])]),_:1}),s(R,null,{default:o(()=>[s(g,{span:24},{default:o(()=>[s(S,{shadow:"hover",class:"table-card"},{header:o(()=>[t("div",Ue,[e[34]||(e[34]=t("span",null,"最近操作记录",-1)),t("div",je,[s(m,{type:"success",text:"",onClick:it},{default:o(()=>[s(c,null,{default:o(()=>[s(bt)]),_:1}),e[32]||(e[32]=_("导出 "))]),_:1}),s(m,{type:"primary",text:"",onClick:e[6]||(e[6]=n=>x("/activities"))},{default:o(()=>[s(c,null,{default:o(()=>[s(wt)]),_:1}),e[33]||(e[33]=_("查看更多 "))]),_:1})])])]),default:o(()=>[s(Tt,{data:ct.value,style:{width:"100%"},stripe:""},{default:o(()=>[s(w,{prop:"time",label:"时间",width:"180",sortable:""}),s(w,{prop:"type",label:"操作类型",width:"120"},{default:o(n=>[s(J,{type:ot(n.row.type)},{default:o(()=>[_(d(n.row.type),1)]),_:2},1032,["type"])]),_:1}),s(w,{prop:"content",label:"操作内容"}),s(w,{prop:"operator",label:"操作人",width:"100"}),s(w,{prop:"status",label:"状态",width:"100"},{default:o(n=>[s(J,{type:n.row.status==="成功"?"success":"danger"},{default:o(()=>[_(d(n.row.status),1)]),_:2},1032,["type"])]),_:1}),s(w,{label:"操作",width:"120",fixed:"right"},{default:o(n=>[s(m,{type:"primary",text:"",size:"small",onClick:i=>nt(n.row)},{default:o(()=>e[35]||(e[35]=[_(" 查看 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1}),s(Dt,{modelValue:L.value,"onUpdate:modelValue":e[7]||(e[7]=n=>L.value=n),title:"操作详情",width:"50%"},{default:o(()=>[s(Ct,{border:"",column:2},{default:o(()=>[s(T,{label:"操作时间"},{default:o(()=>{var n;return[_(d((n=y.value)==null?void 0:n.time),1)]}),_:1}),s(T,{label:"操作类型"},{default:o(()=>{var n;return[_(d((n=y.value)==null?void 0:n.type),1)]}),_:1}),s(T,{label:"操作人"},{default:o(()=>{var n;return[_(d((n=y.value)==null?void 0:n.operator),1)]}),_:1}),s(T,{label:"状态"},{default:o(()=>{var n;return[_(d((n=y.value)==null?void 0:n.status),1)]}),_:1}),s(T,{label:"操作内容",span:2},{default:o(()=>{var n;return[_(d((n=y.value)==null?void 0:n.content),1)]}),_:1}),s(T,{label:"详细信息",span:2},{default:o(()=>e[36]||(e[36]=[t("div",{class:"activity-detail-content"},[t("p",null,"这里是操作的详细信息内容，可以展示操作的详细步骤和结果。"),t("p",null,"包含Token ID、影响范围、操作结果等详细记录。")],-1)])),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},Ke=xt(Fe,[["__scopeId","data-v-43170a8f"]]);export{Ke as default};
