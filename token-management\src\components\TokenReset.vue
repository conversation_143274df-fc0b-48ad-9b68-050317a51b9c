<template>
  <div class="token-reset">
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 判断一个订单是否可以删除
// 目前只有状态为"已评价"的订单可以删除
function isDeletableOrder(order) {
  return order.status === '已评价';
}

// 显示删除结果详情
function showDeletionResultDetails(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  let content = `
    <div style="text-align: left; margin: 15px 0;">
      <h3 style="margin-bottom: 10px;">删除结果详情</h3>
      <div style="margin-bottom: 15px;">
        <h4 style="color: #67c23a; margin-bottom: 5px;">成功删除 (${successful.length})</h4>
        <h4 style="color: #f56c6c; margin-bottom: 5px;">删除失败 (${failed.length})</h4>
      </div>`;
  
  if (failed.length > 0) {
    content += `
      <div>
        <h4 style="color: #f56c6c; margin-bottom: 5px;">失败详情:</h4>
        <ul style="margin: 0; padding-left: 20px;">`;
    
    failed.forEach(item => {
      const errorCode = item.errorCode ? ` (错误码: ${item.errorCode})` : '';
      content += `
        <li>订单号: ${item.orderSn}</li>
        <li style="margin-left: 20px;">错误信息: ${item.message || '未知错误'}${errorCode}</li>`;
      
      // 添加API响应详情
      if (item.responseData) {
        content += `
          <li style="margin-left: 20px; font-size: 12px; color: #909399; margin-top: 5px;">
            <details>
              <summary>API响应详情</summary>
              <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(item.responseData, null, 2)}</pre>
            </details>
          </li>`;
      }
    });
    
    content += `</ul></div>`;
  }
  
  content += `</div>`;
  
  ElMessageBox.alert(content, '订单删除结果', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定',
    customClass: 'deletion-result-dialog'
  });
}

defineExpose({
  isDeletableOrder,
  showDeletionResultDetails
});
</script>

<style scoped>
.token-reset {
  display: block;
}

:deep(.deletion-result-dialog) {
  max-width: 600px;
  width: 90%;
}

:deep(.deletion-result-dialog .el-message-box__content) {
  max-height: 500px;
  overflow-y: auto;
}
</style> 