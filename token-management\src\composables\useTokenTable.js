import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 用于管理Token表格的组合式函数
 * 处理分页、过滤、搜索和表格选择
 */
export default function useTokenTable() {
  // 表格数据与状态
  const tokens = ref([])
  const selectedTokens = ref([])
  const loading = ref(false)
  const tableHeight = ref('400px')
  
  // 分页相关
  const currentPage = ref(1)
  const pageSize = ref(100)
  const totalTokenCount = ref(0)
  
  // 筛选相关
  const filteredTokensCache = ref([])
  const isFiltering = ref(false)
  const shouldResetPage = ref(true)
  
  // 搜索和筛选条件
  const searchKeyword = ref('')
  const statusFilter = ref('')
  const userFilter = ref('')
  
  // 表格引用
  const tokenTableRef = ref(null)
  
  // 状态选项
  const statusOptions = [
    { value: '未知', label: '未知' },
    { value: '在线', label: '在线' },
    { value: '掉线', label: '掉线' },
    { value: '正常', label: '正常' },
    { value: '过期', label: '过期' },
    { value: '待激活', label: '待激活' },
    { value: '已重置', label: '已重置' }
  ]
  
  // 用户选项（从Token数据中提取）
  const userOptions = computed(() => {
    const userSet = new Set()
    tokens.value.forEach(token => {
      if (token.user) {
        userSet.add(token.user)
      }
    })
    return Array.from(userSet).sort()
  })
  
  // 过滤后的表格数据
  const tableData = computed(() => {
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = Math.min(startIndex + pageSize.value, filteredTokensCache.value.length)
    return filteredTokensCache.value.slice(startIndex, endIndex)
  })
  
  // 筛选后的数据总量
  const filteredCount = computed(() => filteredTokensCache.value.length)
  
  // 是否有跨页选择
  const hasSelectionAcrossPages = computed(() => {
    return selectedTokens.value.length > tableData.value.length
  })
  
  // 筛选数据方法
  const filterTokens = () => {
    // 如果正在筛选中，不重复执行
    if (isFiltering.value) return
    
    isFiltering.value = true
    loading.value = true
    
    // 使用setTimeout将筛选操作移到下一个事件循环，避免阻塞UI
    setTimeout(() => {
      try {
        // 先筛选数据
        let result = [...tokens.value] // 创建拷贝，避免直接修改原数组
        
        // 按状态筛选
        if (statusFilter.value) {
          result = result.filter(token => token.status === statusFilter.value)
        }
        
        // 按用户筛选
        if (userFilter.value) {
          result = result.filter(token => token.user === userFilter.value)
        }
        
        // 按关键字搜索
        if (searchKeyword.value) {
          const keyword = searchKeyword.value.toLowerCase()
          result = result.filter(token => 
            (token.uid && token.uid.toLowerCase().includes(keyword)) ||
            (token.user && token.user.toLowerCase().includes(keyword)) ||
            (token.nickname && token.nickname && token.nickname.toLowerCase().includes(keyword)) ||
            (token.token && token.token.toLowerCase().includes(keyword)) ||
            (token.status && token.status.toLowerCase().includes(keyword))
          )
        }
        
        // 更新缓存的筛选结果
        filteredTokensCache.value = result
        
        // 更新总计
        totalTokenCount.value = tokens.value.length
        
        // 处理筛选完成后的操作
        nextTick(() => {
          // 重置到第一页如果当前页超出范围
          const totalPages = Math.ceil(filteredTokensCache.value.length / pageSize.value) || 1
          if (currentPage.value > totalPages) {
            currentPage.value = 1
          }
          
          // 重新应用选择状态，确保选中状态与数据一致
          applyTableSelectionStatus()
        })
      } catch (error) {
        console.error('数据筛选错误:', error)
      } finally {
        isFiltering.value = false
        loading.value = false
      }
    }, 0)
  }
  
  // 应用表格选择状态
  const applyTableSelectionStatus = () => {
    // 防止未初始化时调用
    if (!tokenTableRef.value) return
    
    try {
      // 先清除表格的所有选中状态，避免UI状态混乱
      tokenTableRef.value.clearSelection()
      
      // 延迟到下一个tick确保表格已更新
      nextTick(() => {
        // 重新为当前页的每一行检查是否应该选中
        tableData.value.forEach(row => {
          const shouldBeSelected = selectedTokens.value.some(selected => selected.uid === row.uid)
          if (shouldBeSelected) {
            tokenTableRef.value?.toggleRowSelection(row, true)
          }
        })
      })
    } catch (error) {
      console.error('应用选择状态错误:', error)
    }
  }
  
  // 处理筛选变化
  const handleFilterChange = () => {
    // 只有用户明确进行筛选操作时才重置页码
    shouldResetPage.value = true
    filterTokens()
  }
  
  // 搜索处理
  const handleSearch = () => {
    // 只有用户明确进行搜索操作时才重置页码
    shouldResetPage.value = true
    filterTokens()
  }
  
  // 重置筛选
  const resetFilters = () => {
    statusFilter.value = ''
    userFilter.value = ''
    searchKeyword.value = ''
    // 用户明确重置筛选时才重置页码
    shouldResetPage.value = true
    filterTokens()
  }
  
  // 通过用户名筛选
  const filterByUser = (username) => {
    if (userFilter.value === username) {
      // 如果已经是筛选这个用户，点击则取消筛选
      userFilter.value = ''
    } else {
      // 否则设置为筛选这个用户
      userFilter.value = username
    }
    
    // 用户明确筛选时才重置到第一页
    shouldResetPage.value = true
    filterTokens()
  }
  
  // 表格选择变更处理
  const handleSelectionChange = (selection) => {
    try {
      // 获取当前页面的所有UID
      const currentPageUids = new Set(tableData.value.map(item => item.uid))
      
      // 构建新的选择列表:
      // 1. 保留不在当前页面的之前选择的项
      // 2. 添加当前页面新选择的项
      const newSelection = [
        // 保留不在当前页面的选择项
        ...selectedTokens.value.filter(item => !currentPageUids.has(item.uid)),
        // 添加当前页面的选择项
        ...selection
      ]
      
      // 更新选择列表
      selectedTokens.value = newSelection
    } catch (error) {
      console.error('选择变化处理错误:', error)
    }
  }
  
  // 处理页大小变化
  const handleSizeChange = (size) => {
    // 记住之前的选择
    const previousSelection = [...selectedTokens.value]
    
    // 更新页大小
    pageSize.value = size
    
    // 延时处理，避免DOM更新冲突
    setTimeout(() => {
      try {
        // 清除表格UI上的选择状态
        if (tokenTableRef.value) {
          tokenTableRef.value.clearSelection()
        }
        
        // 如果当前页码超出范围则重置
        const totalPages = Math.ceil(filteredTokensCache.value.length / size) || 1
        if (currentPage.value > totalPages) {
          currentPage.value = 1
        }
        
        // 重新设置选中状态
        if (selectedTokens.value.length > 0) {
          setTimeout(() => {
            // 为当前页的每行检查是否在选中列表中
            tableData.value.forEach(row => {
              // 查找当前行是否在选中列表中
              const isSelected = selectedTokens.value.some(item => item.uid === row.uid)
              if (isSelected && tokenTableRef.value) {
                // 手动设置选中状态
                tokenTableRef.value.toggleRowSelection(row, true)
              }
            })
          }, 50)
        }
      } catch (error) {
        console.error('页面大小变化错误:', error)
      }
    }, 50)
  }
  
  // 处理页码变化
  const handleCurrentChange = (page) => {
    // 更新页码
    currentPage.value = page
    
    // 延时处理，避免DOM更新冲突
    setTimeout(() => {
      try {
        // 清除表格UI上的选择状态
        if (tokenTableRef.value) {
          tokenTableRef.value.clearSelection()
        }
        
        // 重新设置选中状态
        if (selectedTokens.value.length > 0) {
          setTimeout(() => {
            // 为当前页的每行检查是否在选中列表中
            tableData.value.forEach(row => {
              // 查找当前行是否在选中列表中
              const isSelected = selectedTokens.value.some(item => item.uid === row.uid)
              if (isSelected && tokenTableRef.value) {
                // 手动设置选中状态
                tokenTableRef.value.toggleRowSelection(row, true)
              }
            })
          }, 50)
        }
      } catch (error) {
        console.error('页面切换错误:', error)
      }
    }, 50)
  }
  
  // 刷新Token列表
  const refreshTokens = () => {
    // 如果正在刷新中，避免重复执行
    if (loading.value) return
    
    // 显示加载状态
    loading.value = true
    
    // 重新筛选数据
    filterTokens()
    
    // 确保在刷新完成后重新应用选择状态
    setTimeout(() => {
      applyTableSelectionStatus()
      loading.value = false
    }, 50)
  }
  
  // 全选所有数据
  const selectAllTokens = () => {
    try {
      // 先显示加载中
      loading.value = true
      
      // 清除表格UI上的选择状态
      tokenTableRef.value?.clearSelection()
      
      // 直接将所有筛选结果作为选中项
      selectedTokens.value = JSON.parse(JSON.stringify(filteredTokensCache.value))
      
      // 设置定时器，确保后续操作在DOM更新后执行
      setTimeout(() => {
        // 强制为当前页的每一行都设置选中状态
        if (tokenTableRef.value) {
          tableData.value.forEach(row => {
            tokenTableRef.value.toggleRowSelection(row, true)
          })
        }
        
        // 显示成功消息
        ElMessage.success(`已选中全部 ${selectedTokens.value.length} 条数据`)
        loading.value = false
      }, 100)
    } catch (error) {
      console.error('全选操作错误:', error)
      loading.value = false
      ElMessage.error('全选操作失败，请重试')
    }
  }
  
  // 清除所有选择
  const clearAllSelection = () => {
    try {
      // 记录当前页码，确保清除选择后不会改变页码
      const currentPageBeforeClearing = currentPage.value
      
      // 清除表格UI上的选择状态
      tokenTableRef.value?.clearSelection()
      
      // 清除选择数据
      selectedTokens.value = []
      
      // 设置不重置页码的标志
      shouldResetPage.value = false
      
      // 轻量级刷新，只刷新表格选择状态，不重置页码或过滤条件
      nextTick(() => {
        // 恢复原页码
        currentPage.value = currentPageBeforeClearing
        // 应用表格选择状态
        applyTableSelectionStatus()
      })
      
      ElMessage.success('已清除所有选择')
    } catch (error) {
      console.error('清除选择错误:', error)
      ElMessage.error('清除选择失败，请重试')
    }
  }
  
  // 处理行点击选择
  const handleRowClick = (row, column) => {
    // 忽略点击选择框和操作列的情况，因为这些列有自己的点击行为
    if (column.type === 'selection' || column.label === '操作') {
      return
    }
    
    // 切换行的选择状态
    if (tokenTableRef.value) {
      // 检查当前行是否已被选中
      const isSelected = selectedTokens.value.some(item => item.uid === row.uid)
      
      // 切换选中状态
      tokenTableRef.value.toggleRowSelection(row, !isSelected)
    }
  }
  
  // 获取状态对应的类型
  const getStatusType = (status) => {
    const statusMap = {
      '在线': 'success',
      '掉线': 'danger',
      '未知': 'info',
      '正常': 'success',
      '过期': 'danger',
      '待激活': 'warning',
      '已重置': 'info'
    }
    return statusMap[status] || 'info'
  }
  
  // 调整表格高度
  const adjustTableHeight = () => {
    const windowHeight = window.innerHeight
    // 为其他元素预留空间
    tableHeight.value = `${windowHeight - 400}px`
  }
  
  // 监听窗口大小变化
  const setupResizeListener = () => {
    window.addEventListener('resize', adjustTableHeight)
    // 初始调整
    adjustTableHeight()
    
    return () => {
      // 组件卸载时移除事件监听
      window.removeEventListener('resize', adjustTableHeight)
    }
  }
  
  // 处理Token数据变化
  const handleTokensChange = (newTokens) => {
    if (newTokens.length > 1000) {
      // 数据量大时自动调整页大小
      if (pageSize.value < 50) {
        pageSize.value = 50
        ElMessage.info('数据量较大，已自动调整为每页显示50条')
      }
    }
    
    // 执行筛选
    filterTokens()
  }
  
  // 设置观察tokens变化
  const setupTokensWatcher = () => {
    const unwatch = watch(tokens, handleTokensChange, { deep: false })
    return unwatch
  }
  
  // 处理导入后的数据去重
  const processAndDeduplicateTokens = (parsedData) => {
    // 确保每个Token都有默认状态为"未知"
    const processedData = parsedData.map(token => ({
      ...token,
      status: token.status || '未知'
    }))
    
    // 如果现有数据为空，直接返回处理后的数组
    if (tokens.value.length === 0) {
      return { 
        newTokens: processedData,
        duplicateCount: 0
      }
    }
    
    // 创建已存在的UID和Token值的集合，用于去重
    const existingUids = new Set(tokens.value.map(token => token.uid))
    const existingTokenValues = new Set(tokens.value.map(token => token.token))
    
    // 过滤重复的Token（基于UID或Token值）
    const uniqueTokens = []
    let duplicateCount = 0
    
    for (const token of processedData) {
      if (!existingUids.has(token.uid) && !existingTokenValues.has(token.token)) {
        uniqueTokens.push(token)
        existingUids.add(token.uid)
        existingTokenValues.add(token.token)
      } else {
        duplicateCount++
      }
    }
    
    // 合并现有Token和新导入的唯一Token
    return {
      newTokens: [...tokens.value, ...uniqueTokens],
      duplicateCount
    }
  }
  
  // 更新Token信息
  const updateTokensInfo = (updatedTokens) => {
    if (!updatedTokens || updatedTokens.length === 0) return
    
    // 更新本地数据
    tokens.value = tokens.value.map(token => {
      const updated = updatedTokens.find(t => t.uid === token.uid)
      return updated ? { ...token, ...updated } : token
    })
    
    // 设置不重置页码标志
    shouldResetPage.value = false
    
    // 应用筛选但不改变页码
    filterTokens()
  }
  
  // 初始化
  const initialize = () => {
    // 设置窗口大小变化监听
    const cleanupResizeListener = setupResizeListener()
    
    // 设置tokens变化监听
    const cleanupTokensWatcher = setupTokensWatcher()
    
    // 初始筛选
    filterTokens()
    
    // 返回清理函数
    return () => {
      cleanupResizeListener()
      cleanupTokensWatcher()
    }
  }
  
  return {
    // 状态
    tokens,
    selectedTokens,
    loading,
    tableHeight,
    currentPage,
    pageSize,
    totalTokenCount,
    filteredTokensCache,
    searchKeyword,
    statusFilter,
    userFilter,
    tokenTableRef,
    shouldResetPage,
    
    // 计算属性
    tableData,
    filteredCount,
    userOptions,
    hasSelectionAcrossPages,
    statusOptions,
    
    // 方法
    filterTokens,
    handleFilterChange,
    handleSearch,
    resetFilters,
    filterByUser,
    handleSelectionChange,
    handleSizeChange,
    handleCurrentChange,
    refreshTokens,
    selectAllTokens,
    clearAllSelection,
    handleRowClick,
    getStatusType,
    adjustTableHeight,
    processAndDeduplicateTokens,
    updateTokensInfo,
    applyTableSelectionStatus,
    
    // 初始化
    initialize
  }
} 