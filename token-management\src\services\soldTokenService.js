/**
 * 已卖Token服务
 * 提供加载已卖Token的功能
 */
import axios from 'axios';
import { ElMessage, ElLoading } from 'element-plus';

/**
 * 加载已卖Token
 * @param {string} date - 日期字符串，格式为YYYY-MM-DD
 * @returns {Promise<Object>} - 返回包含已卖Token数组的对象
 */
export async function loadSoldTokens(date) {
  try {
    // 显示加载中提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载已卖Token...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 创建URLSearchParams对象来发送表单数据
    const formData = new URLSearchParams();
    formData.append('username', 'a15604402');
    formData.append('password', 'lijinrong11');
    formData.append('date', date);

    // 调用API获取已卖Token
    const response = await axios.post(
      '/wp-json/myplugin/v1/get-time-tokens/',
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    // 关闭加载提示
    loading.close();

    // 处理响应数据
    if (Array.isArray(response.data)) {
      // 格式化Token数据
      const tokens = response.data.map(token => ({
        uid: token.UID || '',
        token: token.token || '',
        user: token.username || '',
        username: token.username || '',
        nickname: '',
        avatar: '',
        status: '未知',
        platform: '',
        purchaseTime: token.start_time || '',  // 使用start_time作为购买时间
        expiryDate: token.end_time || '',      // 使用end_time作为到期时间
        createTime: token.start_time || '',
        orderId: ''
      }));

      // 返回格式化后的Token数据
      return {
        success: true,
        data: tokens,
        message: `成功加载 ${tokens.length} 个已卖Token`
      };
    } else {
      return {
        success: false,
        data: [],
        message: '返回数据格式错误'
      };
    }
  } catch (error) {
    console.error('加载已卖Token失败:', error);
    return {
      success: false,
      data: [],
      message: `加载已卖Token失败: ${error.message}`
    };
  }
}

/**
 * 处理已卖Token数据并导入到现有Token列表
 * @param {Array} currentTokens - 当前Token列表
 * @param {Array} soldTokens - 已卖Token列表
 * @returns {Object} - 返回处理后的Token列表和统计信息
 */
export function processSoldTokens(currentTokens, soldTokens) {
  // 创建一个集合来存储当前Token的UID，用于去重
  const existingUids = new Set(currentTokens.map(token => token.uid));
  
  // 过滤掉已存在的Token
  const newTokens = soldTokens.filter(token => !existingUids.has(token.uid));
  
  // 计算重复的Token数量
  const duplicateCount = soldTokens.length - newTokens.length;
  
  // 合并Token列表
  const mergedTokens = [...currentTokens, ...newTokens];
  
  return {
    tokens: mergedTokens,
    stats: {
      total: soldTokens.length,
      new: newTokens.length,
      duplicate: duplicateCount
    }
  };
}

export default {
  loadSoldTokens,
  processSoldTokens
};
