const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const axios = require('axios');
const cryptoUtils = require('../utils/cryptoUtils');

// 预设的管理员凭证
const ADMIN_CREDENTIALS = {
  admin: "a15604402",
  password: "lijinrong11"
};

// 新Token置入账号内订单的路由
router.post('/tokens/place-order', async (req, res) => {
  try {
    const { tokens } = req.body;
    
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({ success: false, message: '请提供有效的Token数据' });
    }
    
    // 准备请求数据 - 使用第一个Token的用户名
    // 构建请求对象，注意：只对每个token值进行加密，其他保持明文
    const requestData = {
      username: tokens[0].username || '', // 使用第一个token的用户名
      admin: ADMIN_CREDENTIALS.admin,     // 管理员凭证明文
      password: ADMIN_CREDENTIALS.password, // 密码明文
      data: tokens.map(item => ({
        UID: item.UID,                    // UID保持明文
        token: cryptoUtils.encryptAES(item.token) // 只对token值进行加密
      }))
    };
    
    console.log('准备发送请求数据:', JSON.stringify(requestData));
    
    // 发送请求到实际的API - 直接发送requestData对象，不再对整个对象加密
    const API_URL = 'http://aaaaa.pxxcp.com/wp-json/custom-jwt-plugin/v1/bulk-update-data';
    
    console.log('正在发送请求到:', API_URL);
    
    const response = await axios.post(API_URL, requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('收到API响应:', response.data);
    
    // 返回响应
    return res.json(response.data);
  } catch (error) {
    console.error('Token置入订单失败:', error);
    return res.status(500).json({ 
      success: false, 
      message: '服务器处理请求失败',
      error: error.message 
    });
  }
});

module.exports = router; 