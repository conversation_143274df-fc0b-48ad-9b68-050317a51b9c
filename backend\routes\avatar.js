const express = require('express');
const router = express.Router();
const axios = require('axios');

/**
 * 获取头像上传签名
 */
router.post('/avatar/signature', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: token'
      });
    }
    
    // 调用外部API获取签名
    const response = await axios.post('https://apiv3.yangkeduo.com/image/signature',
      {
        "bucket_tag": "pddavatar"
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://apiv3.yangkeduo.com/image/signature?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回签名数据
    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('获取头像上传签名失败:', error);
    res.status(500).json({
      success: false,
      message: `获取头像上传签名失败: ${error.message}`
    });
  }
});

/**
 * 上传头像
 */
router.post('/avatar/upload', async (req, res) => {
  try {
    const { token, upload_sign, imageBase64 } = req.body;
    
    if (!token || !upload_sign || !imageBase64) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: token, upload_sign, imageBase64'
      });
    }
    
    // 调用外部API上传头像
    const response = await axios.post('https://apiv3.yangkeduo.com/store_avatar_image',
      {
        "upload_sign": upload_sign,
        "image": `data:image/jpeg;base64,${imageBase64}`
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://file.yangkeduo.com/store_avatar_image?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回上传结果
    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('上传头像失败:', error);
    res.status(500).json({
      success: false,
      message: `上传头像失败: ${error.message}`
    });
  }
});

/**
 * 更新头像
 */
router.post('/avatar/update', async (req, res) => {
  try {
    const { token, avatar } = req.body;
    
    if (!token || !avatar) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: token, avatar'
      });
    }
    
    // 调用外部API更新头像
    const response = await axios.post('https://apiv3.yangkeduo.com/user/profile/update/avatar',
      {
        "avatar": avatar
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://api.pinduoduo.com/user/profile/update/avatar?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回更新结果
    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('更新头像失败:', error);
    res.status(500).json({
      success: false,
      message: `更新头像失败: ${error.message}`
    });
  }
});

/**
 * 一键修改头像
 */
router.post('/avatar/change', async (req, res) => {
  try {
    const { token, imageBase64 } = req.body;
    
    if (!token || !imageBase64) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: token, imageBase64'
      });
    }
    
    // 1. 获取签名
    const signResponse = await axios.post('https://apiv3.yangkeduo.com/image/signature',
      {
        "bucket_tag": "pddavatar"
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://apiv3.yangkeduo.com/image/signature?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 2. 上传头像
    const uploadResponse = await axios.post('https://apiv3.yangkeduo.com/store_avatar_image',
      {
        "upload_sign": signResponse.data.signature,
        "image": `data:image/jpeg;base64,${imageBase64}`
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://file.yangkeduo.com/store_avatar_image?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 3. 更新头像
    const updateResponse = await axios.post('https://apiv3.yangkeduo.com/user/profile/update/avatar',
      {
        "avatar": uploadResponse.data.url
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://api.pinduoduo.com/user/profile/update/avatar?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回完整的修改结果
    res.json({
      success: true,
      data: {
        signature: signResponse.data,
        upload: uploadResponse.data,
        update: updateResponse.data
      }
    });
  } catch (error) {
    console.error('修改头像失败:', error);
    res.status(500).json({
      success: false,
      message: `修改头像失败: ${error.message}`
    });
  }
});

module.exports = router;
