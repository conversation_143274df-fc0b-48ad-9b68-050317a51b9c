const mysql = require('mysql2/promise');

async function updateAdmin() {
  const pool = await mysql.createPool({
    host: 'localhost',
    user: 'root',
    password: 'qq@666666',
    database: 'token_management'
  });

  try {
    // 直接使用明文密码
    const plainPassword = 'admin123';
    
    // 更新admin用户的密码
    await pool.query(
      'UPDATE users SET password = ? WHERE username = ?',
      [plainPassword, 'admin']
    );
    console.log('Admin密码更新成功');
    
    // 验证更新
    const [rows] = await pool.query('SELECT * FROM users WHERE username = ?', ['admin']);
    console.log('更新后的Admin用户信息:', rows);
  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await pool.end();
  }
}

updateAdmin(); 