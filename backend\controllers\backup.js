const Backup = require('../models/backup');
const Token = require('../models/token');
const { logger } = require('../utils/logger');
const { exportToCSV, exportToExcel } = require('../utils/exportUtils');

class BackupController {
  /**
   * 获取所有备份
   */
  static async getAllBackups(req, res, next) {
    try {
      const { page = 1, limit = 10, backup_type, task_id, status, type } = req.query;

      const filters = {};
      if (backup_type) filters.backup_type = backup_type;
      if (task_id) filters.task_id = task_id;
      if (status) filters.status = status;
      if (type) filters.type = type;

      const result = await Backup.getAll(parseInt(page), parseInt(limit), filters);

      res.json({
        success: true,
        data: {
          backups: result.backups,
          pagination: {
            total: result.total,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(result.total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      logger.error(`获取所有备份失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 根据ID获取单个备份
   */
  static async getBackupById(req, res, next) {
    try {
      const { id } = req.params;
      const backup = await Backup.getById(id);

      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      res.json({
        success: true,
        data: backup
      });
    } catch (error) {
      logger.error(`获取备份(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取备份中的token列表
   */
  static async getBackupTokens(req, res, next) {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;

      logger.info(`获取备份Token列表: 备份ID=${id}, 页码=${page}, 每页数量=${limit}`);

      // 检查备份是否存在
      // 先尝试通过数字ID查找
      let backup = await Backup.getById(id);

      // 如果没找到，尝试通过备份ID查找
      if (!backup && id.toString().startsWith('BKP_')) {
        logger.info(`尝试通过备份ID查找: ${id}`);
        backup = await Backup.getByBackupId(id);
      }

      if (!backup) {
        logger.warn(`备份不存在: ID=${id}`);
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      logger.info(`找到备份: ${JSON.stringify(backup)}`);

      // 获取备份的Token列表
      // 使用备份的数字ID查询Token
      const result = await Backup.getBackupTokens(backup.id, parseInt(page), parseInt(limit));

      logger.info(`获取到的Token数量: ${result.tokens ? result.tokens.length : 0}`);
      logger.info(`是否为字符串Token: ${result.is_string_tokens}`);
      logger.info(`总数: ${result.pagination ? result.pagination.total : 0}`);

      if (result.tokens && result.tokens.length > 0) {
        logger.info(`第一个Token示例: ${JSON.stringify(result.tokens[0])}`);
      } else {
        logger.warn(`没有找到Token数据`);
      }

      res.json({
        success: true,
        data: {
          backup_id: id,
          backup_name: backup.name,
          tokens: result.tokens,
          is_string_tokens: result.is_string_tokens,
          pagination: result.pagination
        }
      });
    } catch (error) {
      logger.error(`获取备份的token列表(备份ID: ${req.params.id})失败: ${error.message}`);
      logger.error(`错误详情: ${error.stack}`);
      next(error);
    }
  }

  /**
   * 创建新备份
   */
  static async createBackup(req, res, next) {
    try {
      const {
        name,
        description,
        backup_type,
        type = 'manual',
        token_ids,
        token_info, // 添加token_info参数
        task_id = null,
        retention_days = 30,
        skip_validation = false // 新增参数，允许跳过Token验证
      } = req.body;

      // 验证必填字段
      if (!name || !backup_type || !token_ids) {
        return res.status(400).json({
          success: false,
          message: '名称、备份类型和token ID列表为必填项'
        });
      }

      // 验证token_ids格式
      if (!Array.isArray(token_ids) || token_ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'token_ids必须是非空数组'
        });
      }

      // 转换后的Token ID列表
      const validTokenIds = [];

      // 检查是否使用UID作为ID
      const useUidAsId = req.body.use_uid_as_id === true;
      const directBackup = req.body.direct_backup === true; // 新增直接备份标记
      logger.info(`是否使用UID作为ID: ${useUidAsId}`);
      logger.info(`是否直接备份: ${directBackup}`);
      logger.info(`请求体: ${JSON.stringify(req.body)}`);

      // 直接备份模式 - 不验证Token是否存在
      if (directBackup) {
        logger.info(`直接备份模式，共${token_ids.length}个Token`);

        // 直接使用传入的token_ids作为有效ID
        for (const tokenId of token_ids) {
          // 将所有ID转换为字符串形式存储
          validTokenIds.push(String(tokenId));
        }

        logger.info(`直接备份模式，已添加${validTokenIds.length}个Token ID`);
      }
      // 原有的验证逻辑
      else if (skip_validation) {
        if (useUidAsId) {
          // 如果使用UID作为ID，则需要先将UID转换为ID
          logger.info(`使用UID作为ID创建备份，共${token_ids.length}个UID`);
          logger.info(`Token IDs: ${JSON.stringify(token_ids)}`);

          for (const uid of token_ids) {
            try {
              logger.info(`开始查找UID: ${uid}`);
              // 通过UID查找Token
              const token = await Token.getByUid(uid);

              if (token) {
                validTokenIds.push(token.id);
                logger.info(`成功将UID ${uid} 转换为ID ${token.id}`);
              } else {
                logger.warn(`未找到UID为 ${uid} 的Token`);
              }
            } catch (error) {
              logger.error(`通过UID ${uid} 查找Token失败:`, error);
              logger.error(`错误详情: ${error.stack}`);
            }
          }
        } else {
          // 直接使用传入的token_ids作为有效ID，但要确保它们是有效的整数
          for (const tokenId of token_ids) {
            if (tokenId && !isNaN(parseInt(tokenId))) {
              validTokenIds.push(parseInt(tokenId));
            } else {
              logger.warn(`跳过无效的Token ID: ${tokenId}`);
            }
          }
        }
      } else {
        // 检查所有token是否存在 (先尝试通过ID查找，如果失败则尝试通过UID查找)
        for (const tokenIdOrUid of token_ids) {
          let token = await Token.getById(tokenIdOrUid);

          // 如果通过ID未找到，尝试通过UID查找
          if (!token) {
            token = await Token.getByUid(tokenIdOrUid);
          }

          // 如果通过ID和UID都未找到，返回错误
          if (!token) {
            return res.status(404).json({
              success: false,
              message: `Token不存在 (ID/UID: ${tokenIdOrUid})`
            });
          }

          // 使用有效的Token ID添加到列表
          validTokenIds.push(token.id);
        }
      }

      // 获取用户信息（如果存在），否则使用默认值
      const userId = req.user ? req.user.id : 1; // 默认用户ID为1
      const username = req.user ? req.user.username : 'system'; // 默认用户名为system

      const backupData = {
        backup_id: `BKP_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        name,
        description,
        backup_type,
        task_id,
        size: `${Math.ceil(validTokenIds.length / 10) * 0.1}MB`, // 简单估算大小
        type,
        remark: req.user ? `由用户${username}通过web应用创建` : '通过无认证API创建',
        status: '备份中',
        created_at: new Date(),
        token_info: token_info, // 添加token_info
        direct_backup: directBackup // 添加direct_backup标记
      };

      logger.info(`正在创建备份，数据详情:`, {
        backup_id: backupData.backup_id,
        name: backupData.name,
        backup_type: backupData.backup_type,
        token_count: validTokenIds.length,
        user_id: userId,
        auth_mode: req.user ? 'authenticated' : 'unauthenticated'
      });

      // 检查是否有有效的Token ID
      if (validTokenIds.length === 0 && !directBackup) {
        // 根据不同情况返回更具体的错误信息
        let errorMessage = '没有有效的Token ID可以备份';

        if (token_ids.length === 0) {
          errorMessage = '请选择要备份的Token';
        } else if (useUidAsId) {
          errorMessage = '无法将UID转换为Token ID，请确认Token是否存在';
        }

        logger.warn(`创建备份失败: ${errorMessage}`);
        return res.status(400).json({
          success: false,
          message: errorMessage,
          details: {
            requestedIds: token_ids.length,
            validIds: validTokenIds.length,
            useUidAsId: useUidAsId
          }
        });
      }

      // 如果是直接备份模式但没有选中Token
      if (directBackup && token_ids.length === 0) {
        logger.warn('直接备份模式，但没有选中Token');
        return res.status(400).json({
          success: false,
          message: '请选择要备份的Token',
          details: {
            requestedIds: 0,
            directBackup: true
          }
        });
      }

      // 如果是直接备份模式，使用token_ids作为validTokenIds
      if (directBackup && validTokenIds.length === 0) {
        validTokenIds.push(...token_ids.map(id => String(id)));
        logger.info(`直接备份模式，使用原始IDs: ${validTokenIds.length}个`);
      }

      // 记录有效Token ID数量
      logger.info(`共找到 ${validTokenIds.length} 个有效Token ID进行备份`);

      const result = await Backup.create(backupData, validTokenIds, userId);
      logger.info(`用户 ${username} (ID: ${userId}) 创建了新备份 (ID: ${result.id})`);

      const createdBackup = await Backup.getById(result.id);

      res.status(201).json({
        success: true,
        message: '备份创建成功',
        data: createdBackup
      });
    } catch (error) {
      logger.error(`创建备份失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 更新备份信息
   */
  static async updateBackup(req, res, next) {
    try {
      const { id } = req.params;
      const { name, description, retention_days } = req.body;

      // 检查备份是否存在
      const backup = await Backup.getById(id);
      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      // 准备更新数据
      const backupData = {};
      if (name !== undefined) backupData.name = name;
      if (description !== undefined) backupData.description = description;
      if (retention_days !== undefined) backupData.retention_days = retention_days;

      // 仅当有数据更新时才进行更新
      if (Object.keys(backupData).length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有提供要更新的字段'
        });
      }

      await Backup.update(id, backupData);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 更新了备份 (ID: ${id})`);

      // 获取更新后的备份
      const updatedBackup = await Backup.getById(id);

      res.json({
        success: true,
        message: '备份更新成功',
        data: updatedBackup
      });
    } catch (error) {
      logger.error(`更新备份(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 删除备份
   */
  static async deleteBackup(req, res, next) {
    try {
      const { id } = req.params;

      // 检查备份是否存在
      const backup = await Backup.getById(id);
      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      await Backup.delete(id);
      logger.info(`删除了备份 (ID: ${id})`);
      // 如果有用户信息则记录
      if (req.user) {
        logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 删除了备份 (ID: ${id})`);
      }

      res.json({
        success: true,
        message: '备份删除成功'
      });
    } catch (error) {
      logger.error(`删除备份(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 标记备份为待删除（指定天数后删除）
   */
  static async markBackupForDeletion(req, res, next) {
    try {
      const { id } = req.params;
      const { days_to_delete = 7 } = req.body;

      // 检查备份是否存在
      const backup = await Backup.getById(id);
      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      await Backup.markForDeletion(id, days_to_delete);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 将备份 (ID: ${id}) 标记为 ${days_to_delete} 天后删除`);

      // 获取更新后的备份
      const updatedBackup = await Backup.getById(id);

      res.json({
        success: true,
        message: `备份已标记为 ${days_to_delete} 天后删除`,
        data: updatedBackup
      });
    } catch (error) {
      logger.error(`标记备份为待删除(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 取消备份的删除标记
   */
  static async cancelBackupDeletion(req, res, next) {
    try {
      const { id } = req.params;

      // 检查备份是否存在
      const backup = await Backup.getById(id);
      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      await Backup.cancelDeletion(id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 取消了备份 (ID: ${id}) 的删除标记`);

      // 获取更新后的备份
      const updatedBackup = await Backup.getById(id);

      res.json({
        success: true,
        message: '已取消备份的删除标记',
        data: updatedBackup
      });
    } catch (error) {
      logger.error(`取消备份删除标记(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 更新备份状态
   */
  static async updateBackupStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      // 验证状态值
      if (!status || !['active', 'inactive', 'scheduled_deletion'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: '状态必须为 active、inactive 或 scheduled_deletion'
        });
      }

      // 检查备份是否存在
      const backup = await Backup.getById(id);
      if (!backup) {
        return res.status(404).json({
          success: false,
          message: '备份不存在'
        });
      }

      await Backup.updateStatus(id, status);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 更新了备份 (ID: ${id}) 的状态为 ${status}`);

      // 获取更新后的备份
      const updatedBackup = await Backup.getById(id);

      res.json({
        success: true,
        message: '备份状态更新成功',
        data: updatedBackup
      });
    } catch (error) {
      logger.error(`更新备份状态(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取备份统计信息
   */
  static async getBackupStats(req, res, next) {
    try {
      const stats = await Backup.getStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取备份统计信息失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 导出备份记录
   */
  static async exportBackups(req, res, next) {
    try {
      const { format } = req.params;
      const { backup_type, status, type } = req.query;

      // 构建查询条件
      const filters = {};
      if (backup_type) filters.backup_type = backup_type;
      if (status) filters.status = status;
      if (type) filters.type = type;

      // 获取所有备份（不分页）
      const result = await Backup.getAll(1, 100000, filters);

      // 准备导出数据
      const backups = result.backups.map(backup => ({
        ID: backup.id,
        名称: backup.name,
        描述: backup.description || '',
        备份类型: backup.backup_type,
        类型: backup.type,
        状态: backup.status,
        保留天数: backup.retention_days,
        创建时间: new Date(backup.created_at).toLocaleString(),
        创建者: backup.created_by,
        Token数量: backup.token_count
      }));

      // 导出文件名
      const fileName = `backups_export_${new Date().toISOString().slice(0, 10)}`;

      if (format === 'csv') {
        const csv = await exportToCSV(backups);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.csv`);
        return res.send(csv);
      } else if (format === 'excel') {
        const excel = await exportToExcel(backups, 'Backups');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        return res.send(excel);
      } else {
        return res.status(400).json({
          success: false,
          message: '不支持的导出格式，请使用csv或excel'
        });
      }
    } catch (error) {
      logger.error(`导出备份记录失败: ${error.message}`);
      next(error);
    }
  }
}

module.exports = BackupController;