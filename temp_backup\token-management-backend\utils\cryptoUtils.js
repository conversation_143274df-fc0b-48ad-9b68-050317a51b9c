const crypto = require('crypto-js');

/**
 * AES加密函数
 * @param {string} text - 要加密的文本
 * @param {string} key - 加密密钥 
 * @returns {string} - 加密后的Base64字符串
 */
exports.encryptAES = function(text, key = 'jijiji1560440222') {
  // 将key转换为crypto-js可用的格式
  const keyHex = crypto.enc.Utf8.parse(key);
  
  // 配置ECB模式和PKCS7填充
  const encrypted = crypto.AES.encrypt(text, keyHex, {
    mode: crypto.mode.ECB,
    padding: crypto.pad.Pkcs7
  });
  
  // 返回Base64格式的加密结果
  return encrypted.toString();
};

/**
 * AES解密函数
 * @param {string} encryptedText - 加密后的Base64字符串
 * @param {string} key - 解密密钥
 * @returns {string} - 解密后的原文
 */
exports.decryptAES = function(encryptedText, key = 'jijiji1560440222') {
  // 将key转换为crypto-js可用的格式
  const keyHex = crypto.enc.Utf8.parse(key);
  
  // 配置ECB模式和PKCS7填充
  const decrypted = crypto.AES.decrypt(encryptedText, keyHex, {
    mode: crypto.mode.ECB,
    padding: crypto.pad.Pkcs7
  });
  
  // 返回UTF-8格式的解密结果
  return decrypted.toString(crypto.enc.Utf8);
}; 