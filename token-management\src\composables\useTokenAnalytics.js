import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { tokenApi } from '../api/token'
import dayjs from 'dayjs'
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, Line<PERSON>hart, Pie<PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的ECharts组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  LineChart,
  PieChart,
  CanvasRenderer
])

/**
 * Token统计分析相关的组合式函数
 * 提供Token数据分析和统计功能
 */
export default function useTokenAnalytics(options = {}) {
  // 从选项中获取token数据
  const { 
    tokens = ref([]),
    filterSettings = ref({})
  } = options
  
  // 统计数据
  const statsLoading = ref(false)
  const statsData = ref({
    total: 0,
    active: 0,
    inactive: 0,
    expired: 0,
    unknown: 0,
    statusDistribution: {},
    userDistribution: {},
    timeDistribution: {
      today: 0,
      week: 0,
      month: 0,
      year: 0,
      older: 0
    }
  })
  
  /**
   * 统计数据面板是否可见
   */
  const showStats = ref(false)
  
  /**
   * 计算当前筛选下的总Token数量
   */
  const filteredTokenCount = computed(() => {
    return tokens.value?.length || 0
  })
  
  /**
   * 计算不同状态的Token数量
   */
  const statusCounts = computed(() => {
    if (!tokens.value || tokens.value.length === 0) return {}
    
    // 统计每种状态的数量
    const counts = {}
    tokens.value.forEach(token => {
      const status = token.status || 'unknown'
      counts[status] = (counts[status] || 0) + 1
    })
    
    return counts
  })
  
  /**
   * 根据用户分组的Token数量
   */
  const userCounts = computed(() => {
    if (!tokens.value || tokens.value.length === 0) return {}
    
    // 统计每个用户的Token数量
    const counts = {}
    tokens.value.forEach(token => {
      const user = token.user || 'unknown'
      counts[user] = (counts[user] || 0) + 1
    })
    
    return counts
  })
  
  /**
   * 计算Token的时间分布（按创建时间）
   */
  const timeDistribution = computed(() => {
    if (!tokens.value || tokens.value.length === 0) {
      return {
        today: 0,
        week: 0,
        month: 0,
        year: 0,
        older: 0
      }
    }
    
    const now = new Date()
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()
    const weekStart = new Date(todayStart - 6 * 24 * 60 * 60 * 1000).getTime()
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1).getTime()
    const yearStart = new Date(now.getFullYear(), 0, 1).getTime()
    
    // 初始化计数
    const distribution = {
      today: 0,
      week: 0,
      month: 0,
      year: 0,
      older: 0
    }
    
    // 统计每个时间段的Token数量
    tokens.value.forEach(token => {
      if (!token.createTime) {
        distribution.unknown = (distribution.unknown || 0) + 1
        return
      }
      
      let createTime
      try {
        // 尝试将字符串转换为时间戳
        createTime = typeof token.createTime === 'string' 
          ? new Date(token.createTime).getTime() 
          : token.createTime
      } catch (e) {
        distribution.unknown = (distribution.unknown || 0) + 1
        return
      }
      
      if (createTime >= todayStart) {
        distribution.today++
      } else if (createTime >= weekStart) {
        distribution.week++
      } else if (createTime >= monthStart) {
        distribution.month++
      } else if (createTime >= yearStart) {
        distribution.year++
      } else {
        distribution.older++
      }
    })
    
    return distribution
  })
  
  /**
   * 根据Token数据更新统计信息
   */
  const updateStats = () => {
    if (!tokens.value) return
    
    statsLoading.value = true
    
    try {
      // 更新统计数据
      statsData.value = {
        total: tokens.value.length,
        active: tokens.value.filter(t => t.status === 'active').length,
        inactive: tokens.value.filter(t => t.status === 'inactive').length,
        expired: tokens.value.filter(t => t.status === 'expired').length,
        unknown: tokens.value.filter(t => !t.status || t.status === 'unknown').length,
        statusDistribution: statusCounts.value,
        userDistribution: userCounts.value,
        timeDistribution: timeDistribution.value
      }
    } catch (error) {
      console.error('更新统计数据时出错:', error)
      ElMessage.error('统计数据计算失败')
    } finally {
      statsLoading.value = false
    }
  }
  
  /**
   * 显示或隐藏统计面板
   */
  const toggleStatsPanel = () => {
    showStats.value = !showStats.value
    
    // 当显示统计面板时更新数据
    if (showStats.value) {
      updateStats()
    }
  }
  
  /**
   * 获取筛选摘要信息
   * 返回描述当前筛选条件的摘要文本
   */
  const getFilterSummary = () => {
    const summary = []
    
    // 如果没有筛选，返回"全部数据"
    if (!filterSettings.value || Object.keys(filterSettings.value).length === 0) {
      return '全部数据'
    }
    
    // 遍历筛选条件
    Object.entries(filterSettings.value).forEach(([key, value]) => {
      if (value) {
        switch (key) {
          case 'keyword':
            summary.push(`关键词: "${value}"`)
            break
          case 'status':
            summary.push(`状态: ${value}`)
            break
          case 'user':
            summary.push(`用户: ${value}`)
            break
          case 'createTime':
            if (Array.isArray(value) && value.length === 2) {
              const startDate = new Date(value[0]).toLocaleDateString()
              const endDate = new Date(value[1]).toLocaleDateString()
              summary.push(`创建时间: ${startDate} 至 ${endDate}`)
            }
            break
          case 'updateTime':
            if (Array.isArray(value) && value.length === 2) {
              const startDate = new Date(value[0]).toLocaleDateString()
              const endDate = new Date(value[1]).toLocaleDateString()
              summary.push(`更新时间: ${startDate} 至 ${endDate}`)
            }
            break
          default:
            if (typeof value === 'string' && value.trim() !== '') {
              summary.push(`${key}: ${value}`)
            }
        }
      }
    })
    
    return summary.length > 0 ? summary.join(', ') : '全部数据'
  }
  
  /**
   * 当tokens发生变化时自动更新统计数据
   */
  watch(tokens, () => {
    if (showStats.value) {
      updateStats()
    }
  }, { deep: true })
  
  return {
    // 状态
    statsLoading,
    statsData,
    showStats,
    
    // 计算属性
    filteredTokenCount,
    statusCounts,
    userCounts,
    timeDistribution,
    
    // 方法
    updateStats,
    toggleStatsPanel,
    getFilterSummary
  }
} 