const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const { logger } = require('../utils/logger');

// 加载环境变量
dotenv.config();

const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'qq@666666',
  database: process.env.DB_NAME || 'token_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

/**
 * 测试数据库连接
 */
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    logger.info('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    logger.error(`数据库连接失败: ${error.message}`);
    return false;
  }
};

/**
 * 初始化数据库函数
 * 检查并创建必要的表
 */
const initializeDatabase = async () => {
  try {
    // 获取连接
    const conn = await pool.getConnection();
    logger.info('开始初始化数据库');

    try {
      // 读取schema文件并执行，或者在这里直接执行创建表的SQL
      // 这里只是简单检查每个表是否存在，如果不存在则创建

      // 检查用户表
      await conn.query(`
        CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          role ENUM('admin', 'user') DEFAULT 'user',
          status ENUM('active', 'disabled') DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          last_login DATETIME
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // 检查令牌表
      await conn.query(`
        CREATE TABLE IF NOT EXISTS tokens (
          id INT AUTO_INCREMENT PRIMARY KEY,
          platform VARCHAR(50) NOT NULL,
          uid VARCHAR(100) NOT NULL,
          token_value VARCHAR(255) NOT NULL,
          description TEXT,
          status ENUM('active', 'disabled', 'revoked') DEFAULT 'active',
          expiry_date DATETIME,
          created_by INT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
          UNIQUE KEY platform_uid (platform, uid)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // 检查重置表
      await conn.query(`
        CREATE TABLE IF NOT EXISTS resets (
          id INT AUTO_INCREMENT PRIMARY KEY,
          token_id INT NOT NULL,
          old_value VARCHAR(255) NOT NULL,
          new_value VARCHAR(255) NOT NULL,
          reason TEXT,
          reset_by INT,
          reset_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE,
          FOREIGN KEY (reset_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // 检查备份表
      await conn.query(`
        CREATE TABLE IF NOT EXISTS backups (
          id INT AUTO_INCREMENT PRIMARY KEY,
          association_id VARCHAR(255) NOT NULL,
          platform VARCHAR(50) NOT NULL,
          uid VARCHAR(100) NOT NULL,
          token_value VARCHAR(255) NOT NULL,
          status ENUM('active', 'disabled') DEFAULT 'active',
          backup_reason ENUM('reset', 'manual', 'system') NOT NULL DEFAULT 'reset',
          description TEXT,
          created_by INT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
          INDEX (association_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // 检查设置表
      await conn.query(`
        CREATE TABLE IF NOT EXISTS settings (
          id INT AUTO_INCREMENT PRIMARY KEY,
          \`key\` VARCHAR(100) NOT NULL UNIQUE,
          value TEXT NOT NULL,
          description TEXT,
          category VARCHAR(50) DEFAULT 'general',
          is_sensitive BOOLEAN DEFAULT 0,
          created_by INT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_by INT,
          updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
          FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);

      // 创建完整的Token备份表
      try {
        await conn.query(`
          CREATE TABLE IF NOT EXISTS backup_tokens_complete (
            id INT AUTO_INCREMENT PRIMARY KEY,
            backup_id INT NOT NULL,
            uid VARCHAR(255) NOT NULL,
            token VARCHAR(1000) NOT NULL,
            username VARCHAR(255),
            nickname VARCHAR(255),
            avatar VARCHAR(255),
            status VARCHAR(50),
            platform VARCHAR(50),
            purchase_time DATETIME,
            expiry_time DATETIME,
            order_id VARCHAR(255),
            created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
        logger.info('完整的Token备份表创建成功');

        // 为完整备份表添加索引
        await conn.query('CREATE INDEX idx_backup_tokens_complete_backup_id ON backup_tokens_complete(backup_id)');
        await conn.query('CREATE INDEX idx_backup_tokens_complete_uid ON backup_tokens_complete(uid)');
        logger.info('完整备份表索引创建成功');
      } catch (error) {
        logger.error(`创建完整的Token备份表失败: ${error.message}`);
      }

      // 创建字符串类型的Token ID备份表
      try {
        await conn.query(`
          CREATE TABLE IF NOT EXISTS backup_tokens_string (
            id INT AUTO_INCREMENT PRIMARY KEY,
            backup_id INT NOT NULL,
            token_id_string VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        `);
        logger.info('字符串类型的Token ID备份表创建成功');
      } catch (error) {
        logger.error(`创建字符串类型的Token ID备份表失败: ${error.message}`);
      }

      // 添加索引以提高查询性能
      logger.info('添加数据库索引');

      try {
        // 尝试为tokens表的uid字段添加索引，兼容旧版MySQL
        await conn.query('CREATE INDEX idx_tokens_uid ON tokens(uid)');
        logger.info('成功为tokens表添加uid索引');
      } catch (error) {
        // 如果索引已存在，忽略错误
        logger.warn(`添加tokens表uid索引时出错，可能已存在: ${error.message}`);
      }

      try {
        // 尝试为backup_tokens表添加索引
        await conn.query('CREATE INDEX idx_backup_tokens_backup_id ON backup_tokens(backup_id)');
        await conn.query('CREATE INDEX idx_backup_tokens_token_id ON backup_tokens(token_id)');
        logger.info('成功为backup_tokens表添加索引');
      } catch (error) {
        logger.warn(`添加backup_tokens表索引时出错，可能已存在: ${error.message}`);
      }

      try {
        // 尝试为backups表添加索引
        await conn.query('CREATE INDEX idx_backups_backup_id ON backups(backup_id)');
        await conn.query('CREATE INDEX idx_backups_created_by ON backups(created_by)');
        await conn.query('CREATE INDEX idx_backups_status ON backups(status)');
        await conn.query('CREATE INDEX idx_backups_backup_type ON backups(backup_type)');
        logger.info('成功为backups表添加索引');
      } catch (error) {
        logger.warn(`添加backups表索引时出错，可能已存在: ${error.message}`);
      }

      logger.info('数据库索引添加完成');
      logger.info('数据库初始化完成');
    } catch (error) {
      logger.error(`数据库初始化失败: ${error.message}`);
      throw error;
    } finally {
      conn.release();
    }

    return true;
  } catch (error) {
    logger.error(`数据库初始化过程中出错: ${error.message}`);
    return false;
  }
};

// 导出池连接
module.exports = pool;

// 导出函数
module.exports.testConnection = testConnection;
module.exports.initializeDatabase = initializeDatabase;