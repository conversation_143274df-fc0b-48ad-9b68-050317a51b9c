const express = require('express');
const router = express.Router();

// 导入路由模块
const authRoutes = require('./auth');
const tokenRoutes = require('./token');
const backupRoutes = require('./backup');
const resetRoutes = require('./reset');
const settingsRoutes = require('./settings');
const statsRoutes = require('./stats');
const avatarRoutes = require('./avatarRoutes');
const avatarApiRoutes = require('./avatar');
const nicknameRoutes = require('./nickname');
// 引入新的云路由
const cloudRoutes = require('./cloud');
const uploadRoutes = require('./upload'); // Import the new upload routes

// 注册路由
router.use('/auth', authRoutes);
router.use('/tokens', tokenRoutes);
router.use('/backups', backupRoutes);
router.use('/reset', resetRoutes);
router.use('/settings', settingsRoutes);
router.use('/stats', statsRoutes);
router.use('/', avatarRoutes);
router.use('/', avatarApiRoutes);
router.use('/nickname', nicknameRoutes);
// 注册新的云路由
router.use('/cloud', cloudRoutes);
router.use('/upload', uploadRoutes); // Register the new upload routes

// API健康检查
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'API服务正常运行',
    timestamp: new Date()
  });
});

module.exports = router;