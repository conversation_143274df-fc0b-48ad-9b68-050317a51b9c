import{u as Qe}from"./token-Bnd6eYkJ.js";import{_ as je,r as O,o as Ye,w as Je,a as R,b as v,d as a,e as t,f as s,g as k,A as Pe,E as c,v as Ge,q as Ke,u as We,B as A,h as n,t as f,k as z,p as Y,F as Xe,j as Ze}from"./index-CckIkgn1.js";import{a as et}from"./tokenService-0zI_qOng.js";const tt={class:"token-auto-reset-container"},st={class:"card-header"},at={class:"input-with-label"},lt={class:"date-info"},ot={class:"options-group"},nt={class:"action-buttons"},ut={class:"manual-reset-content"},it={class:"manual-setting-box"},dt={class:"load-days-setting"},rt={class:"load-days-info"},ct={class:"form-item-label"},ft={class:"manual-setting-box"},pt={class:"options-group manual-options"},mt={class:"date-tag"},bt={class:"card-header"},kt={class:"header-title"},vt={key:0,class:"result-count"},gt={class:"header-actions"},_t={key:0,class:"empty-result"},yt={class:"backup-link-cell"},wt={key:0,class:"el-icon-link"},Dt={key:1,class:"no-backup"},Tt={key:1,class:"no-backup"},Rt={class:"table-actions"},Bt={class:"detail-header"},$t={class:"detail-type"},Ct={class:"detail-time"},It={class:"date-value"},ht={class:"date-value"},xt={class:"token-count"},Vt={class:"success-count"},St={class:"fail-count"},Ot={key:0,class:"backup-status-container"},zt={class:"backup-section-title"},At={class:"task-id"},Ft={class:"backup-status"},Ut={key:2,class:"backup-id"},Mt={key:3,class:"backup-unavailable"},Nt={key:2,class:"backup-id"},qt={key:3,class:"backup-unavailable"},Ht={class:"detail-section"},Lt={class:"detail-logs"},Et={class:"log-time"},Qt={class:"log-message"},jt={class:"dialog-footer"},Yt={__name:"TokenAutoReset",setup(Jt){Qe();const J=Pe(),M=We(),C=O(!1),d=O({loadDaysBefore:12,time:new Date(2024,0,1,3,0),autoBackupBeforeReset:!0,autoBackupAfterReset:!0,notifyOnCompletion:!0,autoAddToOrder:!0}),p=O({loadDaysBefore:12,backupBeforeReset:!0,createBackup:!0,addToOrder:!0,notifyOnCompletion:!0}),F=O(!1),Te=o=>{},Re=()=>{c.success("自动重置设置已保存")},Be=()=>{d.value={loadDaysBefore:12,time:new Date(2024,0,1,3,0),autoBackupBeforeReset:!0,autoBackupAfterReset:!0,notifyOnCompletion:!0,autoAddToOrder:!0}},$e=()=>{F.value=!0,setTimeout(()=>{F.value=!1,c.success(`已成功重置 ${d.value.loadDaysBefore} 天前的Token`);const o=new Date,e=o.toISOString().split("T")[0],m=new Date;m.setDate(o.getDate()-d.value.loadDaysBefore);const b=m.toISOString().split("T")[0],i=Date.now(),B=Math.floor(Math.random()*1e4).toString().padStart(4,"0"),I=`${i}-${B}`.replace(/-/g,""),U=d.value.autoBackupBeforeReset?`QBF${e.replace(/-/g,"")}_${I}`:null,$=d.value.autoBackupAfterReset?`HBF${e.replace(/-/g,"")}_${I}`:null,_={id:x.value.length+1,type:"自动",executeTime:g(new Date),loadDate:b,resetDate:e,tokenCount:45,successCount:42,failCount:3,status:"已完成",taskId:I,addedToOrder:d.value.autoAddToOrder,orderDeleted:!1,backups:{beforeReset:d.value.autoBackupBeforeReset,afterReset:d.value.autoBackupAfterReset,beforeResetId:U,afterResetId:$},logs:[{time:g(new Date(Date.now()-6e5)),message:"开始执行自动重置任务"},{time:g(new Date(Date.now()-55e4)),message:`加载了${d.value.loadDaysBefore}天前的45个Token`},{time:g(new Date(Date.now()-5e5)),message:d.value.autoBackupBeforeReset?"创建重置前的Token备份":"跳过重置前备份（未启用）"},{time:g(new Date(Date.now()-45e4)),message:"开始重置Token"},{time:g(new Date(Date.now()-25e4)),message:"重置了42个Token成功，3个失败"},{time:g(new Date(Date.now()-1e5)),message:d.value.autoBackupAfterReset?"创建重置后的Token备份":"跳过重置后备份（未启用）"},{time:g(new Date(Date.now()-5e4)),message:d.value.autoAddToOrder?"自动置入账号内订单":"跳过置入账号内订单（未启用）"},{time:g(new Date),message:"任务完成"}]};d.value.notifyOnCompletion&&c({message:`已完成${d.value.loadDaysBefore}天前的Token重置，成功42个，失败3个`,type:"success",duration:5e3,showClose:!0}),x.value.unshift(_)},2e3)},Ce=()=>{F.value=!0,setTimeout(()=>{F.value=!1,c.success(`已成功重置 ${p.value.loadDaysBefore} 天前的Token`);const o=new Date,e=o.toISOString().split("T")[0],m=new Date;m.setDate(o.getDate()-p.value.loadDaysBefore);const b=m.toISOString().split("T")[0],i=Date.now(),B=Math.floor(Math.random()*1e4).toString().padStart(4,"0"),I=`${i}-${B}`.replace(/-/g,""),U=p.value.backupBeforeReset?`QBF${e.replace(/-/g,"")}_${I}`:null,$=p.value.createBackup?`HBF${e.replace(/-/g,"")}_${I}`:null,_={id:x.value.length+1,type:"手动",executeTime:g(new Date),loadDate:b,resetDate:e,tokenCount:36,successCount:35,failCount:1,status:"已完成",taskId:I,addedToOrder:p.value.addToOrder,orderDeleted:!1,backups:{beforeReset:p.value.backupBeforeReset,afterReset:p.value.createBackup,beforeResetId:U,afterResetId:$},logs:[{time:g(new Date(Date.now()-6e5)),message:"开始执行手动重置任务"},{time:g(new Date(Date.now()-55e4)),message:`加载了${p.value.loadDaysBefore}天前的36个Token`},p.value.backupBeforeReset?{time:g(new Date(Date.now()-5e5)),message:"创建重置前的Token备份"}:null,{time:g(new Date(Date.now()-45e4)),message:"开始重置Token"},{time:g(new Date(Date.now()-25e4)),message:"重置了35个Token成功，1个失败"},p.value.createBackup?{time:g(new Date(Date.now()-1e5)),message:"创建重置后的Token备份"}:null,p.value.addToOrder?{time:g(new Date),message:"自动置入账号内订单"}:null,p.value.notifyOnCompletion?{time:g(new Date),message:"发送完成通知"}:null,{time:g(new Date),message:"任务完成"}].filter(H=>H!==null)};p.value.notifyOnCompletion&&c({message:`已完成${p.value.loadDaysBefore}天前的Token重置，成功35个，失败1个`,type:"success",duration:5e3,showClose:!0}),x.value.unshift(_)},2e3)},Ie=o=>{if(!o)return"--:--";const e=o.getHours().toString().padStart(2,"0"),m=o.getMinutes().toString().padStart(2,"0");return`${e}:${m}`},L=o=>{const e=new Date;e.setDate(e.getDate()-o);const m=e.getFullYear(),b=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0");return`${m}-${b}-${i}`},g=o=>{if(!o)return"--:--:--";const e=o.getFullYear(),m=(o.getMonth()+1).toString().padStart(2,"0"),b=o.getDate().toString().padStart(2,"0"),i=o.getHours().toString().padStart(2,"0"),B=o.getMinutes().toString().padStart(2,"0"),T=o.getSeconds().toString().padStart(2,"0");return`${e}-${m}-${b} ${i}:${B}:${T}`},x=O([{id:1,executeTime:"2024-03-29 03:15:36",type:"自动",loadDate:"2024-03-17",resetDate:"2024-03-29",tokenCount:120,successCount:115,failCount:5,status:"已完成",addedToOrder:!0,orderDeleted:!1,backups:{beforeReset:!0,afterReset:!0,beforeResetId:"QBF20240329167112345670001",afterResetId:"HBF20240329167112347890001"},logs:[{time:"2024-03-29 03:00:05",message:"开始执行自动重置任务"},{time:"2024-03-29 03:00:10",message:"加载了12天前的120个Token"},{time:"2024-03-29 03:00:15",message:"创建重置前的Token备份"},{time:"2024-03-29 03:00:30",message:"开始重置Token"},{time:"2024-03-29 03:10:45",message:"重置了115个Token成功，5个失败"},{time:"2024-03-29 03:15:32",message:"创建重置后的Token备份"},{time:"2024-03-29 03:15:34",message:"自动置入账号内订单"},{time:"2024-03-29 03:15:36",message:"任务完成"}]},{id:2,executeTime:"2024-03-28 15:22:18",type:"手动",loadDate:"2024-03-16",resetDate:"2024-03-28",tokenCount:85,successCount:85,failCount:0,status:"已完成",addedToOrder:!0,orderDeleted:!0,backups:{beforeReset:!0,afterReset:!1,beforeResetId:"QBF20240328167109876540001",afterResetId:null},logs:[{time:"2024-03-28 15:20:03",message:"开始执行手动重置任务"},{time:"2024-03-28 15:20:10",message:"加载了12天前的85个Token"},{time:"2024-03-28 15:20:15",message:"创建重置前的Token备份"},{time:"2024-03-28 15:21:30",message:"开始重置Token"},{time:"2024-03-28 15:22:05",message:"重置了85个Token成功，0个失败"},{time:"2024-03-28 15:22:16",message:"自动置入账号内订单"},{time:"2024-03-28 15:22:17",message:"发送完成通知"},{time:"2024-03-28 15:22:18",message:"任务完成"}]}]),N=O(!1),u=O(null),P=o=>{switch(o){case"已完成":return"success";case"进行中":return"primary";case"等待中":return"info";case"失败":return"danger";default:return"info"}},he=()=>{c.success("结果列表已刷新")},xe=()=>{c.success("已将结果导出到文件")},Ve=()=>{c({message:"确定要清空结果数据吗？",type:"warning",showClose:!0,duration:0,showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",onConfirm:()=>{x.value=[],c.success("结果数据已清空")}})},Se=o=>{u.value=o,N.value=!0},Oe=()=>{c.success(`已下载ID为 ${u.value.id} 的操作日志`)},ze=o=>o?o.includes("失败")?"error":o.includes("成功")||o.includes("完成")?"success":o.includes("开始")?"start":o.includes("备份")?"backup":o.includes("加载")?"load":!1:!1,q=(o,e)=>{const m=e==="before"?"重置前":"重置后",b=e==="before"?o.backups.beforeResetId:o.backups.afterResetId;e==="before"&&o.backups.beforeReset?(c.success(`正在跳转到${m}备份，ID: ${b}`),M.push(`/token-backup?id=${b}`)):e==="after"&&o.backups.afterReset?(c.success(`正在跳转到${m}备份，ID: ${b}`),M.push(`/token-backup?id=${b}`)):c.warning(`该记录没有可用的${m}备份`)},G=()=>{const o=J.query.initFunctions==="true";if(window.tokenResetFunctions={checkAvatarNickname:e=>{c.info(`查询"${e.username}"的头像昵称`),console.log("查询头像昵称:",e)},checkOnline:e=>{c.info(`查询"${e.username}"的在线状态`),console.log("查询在线状态:",e)},modifyAvatar:e=>{c.info(`修改"${e.username}"的头像`),console.log("修改头像:",e)},modifyNickname:e=>{c.info(`修改"${e.username}"的昵称`),console.log("修改昵称:",e)},addToOrder:e=>{c.info(`将Token置入订单: ${e.tokenValue.substring(0,10)}...`),console.log("置入订单:",e)},assignToAccount:e=>{c.info(`将Token指定给账号: ${e.tokenValue.substring(0,10)}...`),console.log("指定账号:",e)},deleteOrder:e=>{c.info(`删除"${e.username}"的订单`),console.log("删除订单:",e)},batchCheckAvatarNickname:e=>{c.info(`批量查询${e.length}个Token的头像昵称`),console.log("批量查询头像昵称:",e);const m=e.map(i=>({uid:i.uid||i.id,token:i.token||i.tokenValue})),b=Ge.service({fullscreen:!0,text:"正在查询头像和昵称...",background:"rgba(0, 0, 0, 0.7)"});et(m).then(i=>{if(i.success){c.success(`查询完成: ${i.results.filter(T=>T.success).length}个成功，${i.results.filter(T=>!T.success).length}个失败`);const B=`
            <div style="text-align: center; margin-top: 10px;">
              <div style="font-size: 16px; margin-bottom: 10px;">查询完成</div>
              <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                  <span style="margin-right: 5px;">成功查询</span> 
                  <span style="font-size: 18px;">${i.results.filter(T=>T.success).length}</span>
                </div>
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                  <span style="margin-right: 5px;">查询失败</span> 
                  <span style="font-size: 18px;">${i.results.filter(T=>!T.success).length}</span>
                </div>
              </div>
              <div style="font-size: 14px; color: #409EFF;">查询结果已处理完成</div>
            </div>
          `;Ke.alert(B,"查询结果",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})}else c.error(`查询失败: ${i.message||"未知错误"}`)}).catch(i=>{console.error("批量查询头像昵称异常:",i),c.error(`查询异常: ${i.message||"未知错误"}`)}).finally(()=>{b.close()})},batchCheckOnline:e=>{c.info(`批量查询${e.length}个Token的在线状态`),console.log("批量查询在线状态:",e)},batchModifyAvatar:e=>{c.info(`批量修改${e.length}个Token的头像`),console.log("批量修改头像:",e)},batchModifyNickname:e=>{c.info(`批量修改${e.length}个Token的昵称`),console.log("批量修改昵称:",e)},batchAddToOrder:e=>{c.info(`批量将${e.length}个Token置入订单`),console.log("批量置入订单:",e)},batchAssignToAccount:e=>{c.info(`批量将${e.length}个Token指定给账号`),console.log("批量指定账号:",e)},batchDeleteOrder:e=>{c.info(`批量删除${e.length}个Token的订单`),console.log("批量删除订单:",e)}},o){const e=localStorage.getItem("backupPageReturn");if(e)try{const{path:m,query:b,params:i}=JSON.parse(e);c.success("Token操作功能已初始化，正在返回备份页面..."),M.push({path:m,query:b,params:i})}catch(m){console.error("解析返回路径失败:",m),M.push("/token-backup")}else c.info("Token操作功能已初始化")}};Ye(()=>{p.value.loadDaysBefore=12,d.value.loadDaysBefore=12,G()}),Je(()=>J.query,o=>{o.initFunctions==="true"&&G()},{immediate:!0});const Ae=o=>{var b,i;if(!o.taskId)return"无关联信息";const e=(b=o.backups)==null?void 0:b.beforeReset,m=(i=o.backups)==null?void 0:i.afterReset;return e&&m?`关联备份ID: ${o.taskId}`:e?`仅重置前备份: ${o.taskId}`:m?`仅重置后备份: ${o.taskId}`:"无备份"},K=(o,e)=>{var m,b;return e==="before"&&((m=o.backups)!=null&&m.beforeResetId)?o.backups.beforeResetId.split("_")[0]+"_...":e==="after"&&((b=o.backups)!=null&&b.afterResetId)?o.backups.afterResetId.split("_")[0]+"_...":"无备份"};return(o,e)=>{const m=k("el-switch"),b=k("el-input-number"),i=k("el-form-item"),B=k("el-col"),T=k("el-time-picker"),I=k("el-row"),U=k("el-divider"),$=k("el-checkbox"),_=k("el-button"),H=k("el-form"),E=k("el-card"),Fe=k("el-icon-refresh"),Q=k("el-icon"),Ue=k("el-icon-download"),Me=k("el-icon-delete"),Ne=k("el-button-group"),qe=k("el-empty"),y=k("el-table-column"),w=k("el-tag"),j=k("el-tooltip"),He=k("el-table"),S=k("el-descriptions-item"),Le=k("el-descriptions"),Ee=k("el-dialog");return v(),R("div",tt,[e[58]||(e[58]=a("div",{class:"page-header"},[a("h1",{class:"page-title"},"Token自动重置管理"),a("div",{class:"page-desc"},"设置智能管理您的Token，实现自动化重置、备份和订单管理")],-1)),t(E,{class:"settings-card"},{header:s(()=>[a("div",st,[e[16]||(e[16]=a("div",{class:"header-title"},[a("i",{class:"el-icon-timer header-icon"}),a("span",null,"自动Token重置")],-1)),t(m,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=l=>C.value=l),"active-text":"启用自动重置","inactive-text":"禁用自动重置",onChange:Te},null,8,["modelValue"])])]),default:s(()=>[t(H,{"label-position":"top",model:d.value,class:"settings-form"},{default:s(()=>[t(I,{gutter:24},{default:s(()=>[t(B,{xs:24,sm:8},{default:s(()=>[t(i,{label:"自动加载设置"},{default:s(()=>[a("div",at,[t(b,{modelValue:d.value.loadDaysBefore,"onUpdate:modelValue":e[1]||(e[1]=l=>d.value.loadDaysBefore=l),min:1,max:180,step:1,disabled:!C.value},null,8,["modelValue","disabled"]),e[17]||(e[17]=a("span",{class:"form-item-hint"},"天前的Token",-1))])]),_:1})]),_:1}),t(B,{xs:24,sm:8},{default:s(()=>[t(i,{label:"自动执行时间"},{default:s(()=>[t(T,{modelValue:d.value.time,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value.time=l),format:"HH:mm",placeholder:"选择时间",class:"full-width",disabled:!C.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),t(B,{xs:24,sm:8},{default:s(()=>[t(i,{label:"简单预览"},{default:s(()=>[a("div",{class:A(["preview-text",{"preview-disabled":!C.value}])},[n(" 系统将在每天 "+f(Ie(d.value.time))+" 自动加载并重置 "+f(d.value.loadDaysBefore)+" 天前的所有Token ",1),a("div",lt,f(d.value.loadDaysBefore)+"天前日期为: "+f(L(d.value.loadDaysBefore)),1)],2)]),_:1})]),_:1})]),_:1}),t(U,null,{default:s(()=>e[18]||(e[18]=[n("自动重置选项")])),_:1}),t(I,{gutter:20},{default:s(()=>[t(B,{span:24},{default:s(()=>[a("div",ot,[t($,{modelValue:d.value.autoBackupBeforeReset,"onUpdate:modelValue":e[3]||(e[3]=l=>d.value.autoBackupBeforeReset=l),disabled:!C.value},{default:s(()=>e[19]||(e[19]=[n(" 重置前自动备份 ")])),_:1},8,["modelValue","disabled"]),t($,{modelValue:d.value.autoBackupAfterReset,"onUpdate:modelValue":e[4]||(e[4]=l=>d.value.autoBackupAfterReset=l),disabled:!C.value},{default:s(()=>e[20]||(e[20]=[n(" 重置后自动备份 ")])),_:1},8,["modelValue","disabled"]),t($,{modelValue:d.value.notifyOnCompletion,"onUpdate:modelValue":e[5]||(e[5]=l=>d.value.notifyOnCompletion=l),disabled:!C.value},{default:s(()=>e[21]||(e[21]=[n(" 完成后通知我 ")])),_:1},8,["modelValue","disabled"]),t($,{modelValue:d.value.autoAddToOrder,"onUpdate:modelValue":e[6]||(e[6]=l=>d.value.autoAddToOrder=l),disabled:!C.value},{default:s(()=>e[22]||(e[22]=[n(" 重置完自动置入账号内订单 ")])),_:1},8,["modelValue","disabled"])])]),_:1})]),_:1}),t(i,null,{default:s(()=>[a("div",nt,[t(_,{type:"primary",size:"large",onClick:Re,disabled:!C.value,class:"action-btn"},{default:s(()=>e[23]||(e[23]=[a("i",{class:"el-icon-check"},null,-1),n(" 保存设置 ")])),_:1},8,["disabled"]),t(_,{size:"large",onClick:Be,disabled:!C.value,class:"action-btn"},{default:s(()=>e[24]||(e[24]=[a("i",{class:"el-icon-refresh-left"},null,-1),n(" 重置设置 ")])),_:1},8,["disabled"]),t(_,{type:"success",size:"large",onClick:$e,class:"execute-now-btn action-btn"},{default:s(()=>e[25]||(e[25]=[a("i",{class:"el-icon-video-play"},null,-1),n(" 立即执行一次 ")])),_:1})])]),_:1})]),_:1},8,["model"])]),_:1}),t(E,{class:"manual-card"},{header:s(()=>e[26]||(e[26]=[a("div",{class:"card-header"},[a("div",{class:"header-title"},[a("i",{class:"el-icon-refresh header-icon"}),a("span",null,"一键手动重置")])],-1)])),default:s(()=>[t(H,{"label-position":"top",model:p.value,class:"settings-form"},{default:s(()=>[a("div",ut,[a("div",it,[e[29]||(e[29]=a("div",{class:"setting-title"},"加载天数设置",-1)),a("div",dt,[t(b,{modelValue:p.value.loadDaysBefore,"onUpdate:modelValue":e[7]||(e[7]=l=>p.value.loadDaysBefore=l),min:1,max:180,step:1,"controls-position":"right",size:"large"},null,8,["modelValue"]),a("div",rt,[a("span",ct,[e[27]||(e[27]=n("加载日期: ")),a("b",null,f(L(p.value.loadDaysBefore)),1)]),e[28]||(e[28]=a("span",{class:"form-item-hint"},"选择要重置的Token加载天数",-1))])])]),a("div",ft,[e[34]||(e[34]=a("div",{class:"setting-title"},"操作选项",-1)),a("div",pt,[t($,{modelValue:p.value.backupBeforeReset,"onUpdate:modelValue":e[8]||(e[8]=l=>p.value.backupBeforeReset=l)},{default:s(()=>e[30]||(e[30]=[a("div",{class:"option-content"},[a("i",{class:"el-icon-upload2 option-icon"}),a("span",null,"重置前自动备份")],-1)])),_:1},8,["modelValue"]),t($,{modelValue:p.value.createBackup,"onUpdate:modelValue":e[9]||(e[9]=l=>p.value.createBackup=l)},{default:s(()=>e[31]||(e[31]=[a("div",{class:"option-content"},[a("i",{class:"el-icon-download option-icon"}),a("span",null,"重置完成后创建备份")],-1)])),_:1},8,["modelValue"]),t($,{modelValue:p.value.notifyOnCompletion,"onUpdate:modelValue":e[10]||(e[10]=l=>p.value.notifyOnCompletion=l)},{default:s(()=>e[32]||(e[32]=[a("div",{class:"option-content"},[a("i",{class:"el-icon-bell option-icon"}),a("span",null,"完成后通知我")],-1)])),_:1},8,["modelValue"]),t($,{modelValue:p.value.addToOrder,"onUpdate:modelValue":e[11]||(e[11]=l=>p.value.addToOrder=l)},{default:s(()=>e[33]||(e[33]=[a("div",{class:"option-content"},[a("i",{class:"el-icon-shopping-cart-full option-icon"}),a("span",null,"重置完自动置入账号内订单")],-1)])),_:1},8,["modelValue"])])])]),t(i,null,{default:s(()=>[t(_,{type:"primary",size:"large",class:"manual-reset-btn",onClick:Ce,loading:F.value},{default:s(()=>[e[35]||(e[35]=a("i",{class:"el-icon-magic-stick",style:{"font-size":"20px","margin-right":"6px"}},null,-1)),n(" 一键加载并重置 "+f(p.value.loadDaysBefore)+" 天前的Token ",1),a("div",mt,f(L(p.value.loadDaysBefore)),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),t(E,{class:"result-card"},{header:s(()=>[a("div",bt,[a("div",kt,[e[36]||(e[36]=a("i",{class:"el-icon-document-checked header-icon"},null,-1)),e[37]||(e[37]=a("span",null,"重置结果",-1)),x.value.length>0?(v(),R("span",vt,f(x.value.length)+"条记录",1)):Y("",!0)]),a("div",gt,[t(Ne,null,{default:s(()=>[t(_,{type:"primary",onClick:he},{default:s(()=>[t(Q,null,{default:s(()=>[t(Fe)]),_:1}),e[38]||(e[38]=n("刷新 "))]),_:1}),t(_,{type:"success",onClick:xe},{default:s(()=>[t(Q,null,{default:s(()=>[t(Ue)]),_:1}),e[39]||(e[39]=n("导出 "))]),_:1}),t(_,{type:"danger",onClick:Ve},{default:s(()=>[t(Q,null,{default:s(()=>[t(Me)]),_:1}),e[40]||(e[40]=n("清空 "))]),_:1})]),_:1})])])]),default:s(()=>[x.value.length===0?(v(),R("div",_t,[t(qe,{description:"暂无重置记录"})])):(v(),z(He,{key:1,ref:"resultTable",data:x.value,style:{width:"95%",margin:"0 auto"},border:"",stripe:"","highlight-current-row":"",size:"medium","header-cell-style":{background:"#f5f7fa",color:"#303133",fontSize:"15px",height:"56px"},"cell-style":{fontSize:"14px",padding:"12px 0"}},{default:s(()=>[t(y,{prop:"loadDate",label:"已卖日期",width:"110",sortable:""}),t(y,{prop:"resetDate",label:"重置日期",width:"110",sortable:""}),t(y,{prop:"tokenCount",label:"Token数量",width:"100",align:"center"}),t(y,{prop:"successCount",label:"重置成功数量",width:"120",align:"center"},{default:s(l=>[a("span",{class:A(l.row.successCount>0?"success-text":"")},f(l.row.successCount),3)]),_:1}),t(y,{prop:"failCount",label:"重置失败数量",width:"120",align:"center"},{default:s(l=>[a("span",{class:A(l.row.failCount>0?"fail-text":"")},f(l.row.failCount),3)]),_:1}),t(y,{prop:"status",label:"状态",width:"100"},{default:s(l=>[t(w,{type:P(l.row.status),effect:"plain"},{default:s(()=>[n(f(l.row.status),1)]),_:2},1032,["type"])]),_:1}),t(y,{label:"备份关联ID",width:"240"},{default:s(l=>[t(j,{effect:"dark",content:l.row.taskId||"无关联ID",placement:"top"},{default:s(()=>[a("div",yt,[l.row.taskId?(v(),R("i",wt)):Y("",!0),a("span",null,f(Ae(l.row)),1)])]),_:2},1032,["content"])]),_:1}),t(y,{prop:"backups.beforeResetId",label:"重置前备份",width:"180"},{default:s(l=>{var D;return[(D=l.row.backups)!=null&&D.beforeReset?(v(),z(w,{key:0,type:"success",size:"small",effect:"dark"},{default:s(()=>{var V;return[t(j,{effect:"dark",content:((V=l.row.backups)==null?void 0:V.beforeResetId)||"",placement:"top"},{default:s(()=>[a("span",null,f(K(l.row,"before")),1)]),_:2},1032,["content"])]}),_:2},1024)):(v(),R("span",Dt,"未创建"))]}),_:1}),t(y,{prop:"backups.afterResetId",label:"重置后备份",width:"180"},{default:s(l=>{var D;return[(D=l.row.backups)!=null&&D.afterReset?(v(),z(w,{key:0,type:"warning",size:"small",effect:"dark"},{default:s(()=>{var V;return[t(j,{effect:"dark",content:((V=l.row.backups)==null?void 0:V.afterResetId)||"",placement:"top"},{default:s(()=>[a("span",null,f(K(l.row,"after")),1)]),_:2},1032,["content"])]}),_:2},1024)):(v(),R("span",Tt,"未创建"))]}),_:1}),t(y,{prop:"置入账号内订单",label:"置入账号内订单",width:"140"},{default:s(l=>[t(w,{type:l.row.addedToOrder?"success":"info",effect:"plain"},{default:s(()=>[n(f(l.row.addedToOrder?"已置入":"未置入"),1)]),_:2},1032,["type"])]),_:1}),t(y,{prop:"orderDeleted",label:"删除订单",width:"100",align:"center"},{default:s(l=>[t(w,{type:l.row.orderDeleted?"danger":"info",size:"medium",effect:"plain"},{default:s(()=>[n(f(l.row.orderDeleted?"已删除":"未删除"),1)]),_:2},1032,["type"])]),_:1}),t(y,{prop:"executeTime",label:"执行时间",width:"160",sortable:""}),t(y,{prop:"type",label:"类型",width:"80"},{default:s(l=>[t(w,{type:l.row.type==="自动"?"warning":"primary",size:"medium"},{default:s(()=>[n(f(l.row.type),1)]),_:2},1032,["type"])]),_:1}),t(y,{label:"操作",width:"320",fixed:"right",align:"center"},{default:s(l=>[a("div",Rt,[t(_,{type:"primary",size:"small",class:"detail-btn action-inline-btn",onClick:D=>Se(l.row)},{default:s(()=>e[41]||(e[41]=[n(" 详情 ")])),_:2},1032,["onClick"]),t(_,{type:"success",size:"small",class:"backup-btn before-backup action-inline-btn",onClick:D=>q(l.row,"before"),disabled:!l.row.backups.beforeReset},{default:s(()=>e[42]||(e[42]=[n(" 重置前备份 ")])),_:2},1032,["onClick","disabled"]),t(_,{type:"warning",size:"small",class:"backup-btn after-backup action-inline-btn",onClick:D=>q(l.row,"after"),disabled:!l.row.backups.afterReset},{default:s(()=>e[43]||(e[43]=[n(" 重置后备份 ")])),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"]))]),_:1}),t(Ee,{modelValue:N.value,"onUpdate:modelValue":e[15]||(e[15]=l=>N.value=l),title:"重置任务详情",width:"720px",class:"reset-detail-dialog","append-to-body":""},{footer:s(()=>{var l,D;return[a("span",jt,[t(_,{onClick:e[14]||(e[14]=V=>N.value=!1)},{default:s(()=>e[56]||(e[56]=[n("关闭")])),_:1}),t(_,{type:"primary",onClick:Oe,disabled:!((D=(l=u.value)==null?void 0:l.logs)!=null&&D.length)},{default:s(()=>e[57]||(e[57]=[a("i",{class:"el-icon-download"},null,-1),n(" 下载日志 ")])),_:1},8,["disabled"])])]}),default:s(()=>{var l,D,V,W,X,Z,ee,te,se,ae,le,oe,ne,ue,ie,de,re,ce,fe,pe,me,be,ke,ve,ge,_e,ye,we,De;return[a("div",Bt,[a("div",$t,[t(w,{type:((l=u.value)==null?void 0:l.type)==="自动"?"warning":"primary",size:"large",effect:"dark"},{default:s(()=>{var r;return[n(f((r=u.value)==null?void 0:r.type),1)]}),_:1},8,["type"])]),a("div",Ct,[e[44]||(e[44]=a("i",{class:"el-icon-time"},null,-1)),n(" "+f((D=u.value)==null?void 0:D.executeTime),1)])]),t(Le,{column:2,border:""},{default:s(()=>[t(S,{label:"已卖日期"},{default:s(()=>{var r;return[a("div",It,f((r=u.value)==null?void 0:r.loadDate),1)]}),_:1}),t(S,{label:"重置日期"},{default:s(()=>{var r;return[a("div",ht,f((r=u.value)==null?void 0:r.resetDate),1)]}),_:1}),t(S,{label:"状态"},{default:s(()=>{var r;return[t(w,{type:P((r=u.value)==null?void 0:r.status),effect:"plain"},{default:s(()=>{var h;return[n(f((h=u.value)==null?void 0:h.status),1)]}),_:1},8,["type"])]}),_:1}),t(S,{label:"Token数量"},{default:s(()=>{var r;return[a("div",xt,f((r=u.value)==null?void 0:r.tokenCount),1)]}),_:1}),t(S,{label:"成功数量"},{default:s(()=>{var r;return[a("div",Vt,[e[45]||(e[45]=a("i",{class:"el-icon-check success-icon"},null,-1)),n(" "+f((r=u.value)==null?void 0:r.successCount),1)])]}),_:1}),t(S,{label:"失败数量"},{default:s(()=>{var r;return[a("div",St,[e[46]||(e[46]=a("i",{class:"el-icon-close fail-icon"},null,-1)),n(" "+f((r=u.value)==null?void 0:r.failCount),1)])]}),_:1}),t(S,{label:"置入账号内订单"},{default:s(()=>{var r;return[t(w,{type:(r=u.value)!=null&&r.addedToOrder?"success":"info",effect:"plain"},{default:s(()=>{var h;return[n(f((h=u.value)!=null&&h.addedToOrder?"已置入":"未置入"),1)]}),_:1},8,["type"])]}),_:1}),t(S,{label:"删除订单"},{default:s(()=>{var r;return[t(w,{type:(r=u.value)!=null&&r.orderDeleted?"danger":"info",effect:"plain"},{default:s(()=>{var h;return[n(f((h=u.value)!=null&&h.orderDeleted?"已删除":"未删除"),1)]}),_:1},8,["type"])]}),_:1})]),_:1}),(V=u.value)!=null&&V.taskId?(v(),R("div",Ot,[a("h3",zt,[e[47]||(e[47]=a("i",{class:"el-icon-document-copy"},null,-1)),e[48]||(e[48]=n(" 备份信息 ")),a("span",At,"关联ID: "+f((W=u.value)==null?void 0:W.taskId),1)]),a("div",Ft,[a("div",{class:A(["backup-status-item",{active:(Z=(X=u.value)==null?void 0:X.backups)==null?void 0:Z.beforeReset,"not-available":!((te=(ee=u.value)==null?void 0:ee.backups)!=null&&te.beforeReset)}])},[(ae=(se=u.value)==null?void 0:se.backups)!=null&&ae.beforeReset?(v(),z(w,{key:0,type:"success",effect:"dark",size:"small"},{default:s(()=>e[49]||(e[49]=[n("重置前备份")])),_:1})):(v(),z(w,{key:1,type:"info",effect:"plain",size:"small"},{default:s(()=>e[50]||(e[50]=[n("重置前备份")])),_:1})),(oe=(le=u.value)==null?void 0:le.backups)!=null&&oe.beforeResetId?(v(),R("div",Ut,f((ue=(ne=u.value)==null?void 0:ne.backups)==null?void 0:ue.beforeResetId),1)):(v(),R("div",Mt,"未创建备份")),t(_,{size:"small",type:"success",class:"backup-btn before-backup",onClick:e[12]||(e[12]=r=>q(u.value,"before")),disabled:!((de=(ie=u.value)==null?void 0:ie.backups)!=null&&de.beforeReset)},{default:s(()=>e[51]||(e[51]=[n(" 查看备份 ")])),_:1},8,["disabled"])],2),a("div",{class:A(["backup-status-item",{active:(ce=(re=u.value)==null?void 0:re.backups)==null?void 0:ce.afterReset,"not-available":!((pe=(fe=u.value)==null?void 0:fe.backups)!=null&&pe.afterReset)}])},[(be=(me=u.value)==null?void 0:me.backups)!=null&&be.afterReset?(v(),z(w,{key:0,type:"warning",effect:"dark",size:"small"},{default:s(()=>e[52]||(e[52]=[n("重置后备份")])),_:1})):(v(),z(w,{key:1,type:"info",effect:"plain",size:"small"},{default:s(()=>e[53]||(e[53]=[n("重置后备份")])),_:1})),(ve=(ke=u.value)==null?void 0:ke.backups)!=null&&ve.afterResetId?(v(),R("div",Nt,f((_e=(ge=u.value)==null?void 0:ge.backups)==null?void 0:_e.afterResetId),1)):(v(),R("div",qt,"未创建备份")),t(_,{size:"small",type:"warning",class:"backup-btn after-backup",onClick:e[13]||(e[13]=r=>q(u.value,"after")),disabled:!((we=(ye=u.value)==null?void 0:ye.backups)!=null&&we.afterReset)},{default:s(()=>e[54]||(e[54]=[n(" 查看备份 ")])),_:1},8,["disabled"])],2)])])):Y("",!0),a("div",Ht,[e[55]||(e[55]=a("h3",{class:"detail-section-title"},[a("i",{class:"el-icon-info"}),n(" 操作日志 ")],-1)),a("div",Lt,[(v(!0),R(Xe,null,Ze((De=u.value)==null?void 0:De.logs,(r,h)=>(v(),R("div",{key:h,class:A(["log-item",{"log-highlight":ze(r.message)}])},[a("div",Et,f(r.time),1),a("div",Qt,f(r.message),1)],2))),128))])])]}),_:1},8,["modelValue"])])}}},Wt=je(Yt,[["__scopeId","data-v-88b00f3b"]]);export{Wt as default};
