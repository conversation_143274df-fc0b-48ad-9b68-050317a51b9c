const db = require('../database/db');
const { logger } = require('../utils/logger');

class UserPreference {
  /**
   * 根据用户ID获取偏好设置
   * @param {number} userId - 用户ID
   * @returns {Promise<object>} 用户偏好设置
   */
  static async getByUserId(userId) {
    try {
      const query = `
        SELECT * FROM user_preferences
        WHERE user_id = ?
      `;
      
      const [rows] = await db.query(query, [userId]);
      
      if (rows.length === 0) {
        // 如果用户没有偏好设置记录，返回默认值
        return this.getDefaultPreferences();
      }
      
      const result = { ...rows[0] };
      
      // 尝试解析JSON字段
      try {
        // 检查每个JSON字段是否为字符串，且不是"[object Object]"
        const jsonFields = ['theme_settings', 'dashboard_layout', 'notification_settings'];
        
        for (const field of jsonFields) {
          if (result[field]) {
            // 如果字段值是字符串"[object Object]"，则设置为空对象
            if (result[field] === '[object Object]') {
              logger.warn(`用户${userId}的${field}字段存储了无效的值"[object Object]"，使用空对象替代`);
              result[field] = {};
              continue;
            }
            
            // 如果是字符串类型，尝试解析为JSON
            if (typeof result[field] === 'string') {
              try {
                result[field] = JSON.parse(result[field]);
              } catch (parseError) {
                logger.error(`解析用户${userId}的${field}字段失败: ${parseError.message}`);
                result[field] = {}; // 解析失败则设置为空对象
              }
            }
          } else {
            // 如果字段不存在或为null，设置为空对象
            result[field] = {};
          }
        }
      } catch (error) {
        logger.error(`解析用户偏好设置JSON字段失败: ${error.message}`);
        // 确保即使解析失败，也返回有效对象
        result.theme_settings = result.theme_settings || {};
        result.dashboard_layout = result.dashboard_layout || {};
        result.notification_settings = result.notification_settings || {};
      }
      
      return result;
    } catch (error) {
      logger.error(`获取用户(ID: ${userId})偏好设置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户偏好设置
   * @param {number} userId - 用户ID
   * @param {object} preferences - 偏好设置对象
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async update(userId, preferences) {
    try {
      const {
        theme,
        language,
        items_per_page,
        theme_settings,
        dashboard_layout,
        notification_settings,
        system_name
      } = preferences;
      
      // 检查用户是否已有偏好设置记录
      const [existingPrefs] = await db.query(
        'SELECT id FROM user_preferences WHERE user_id = ?',
        [userId]
      );
      
      // 准备数据，确保JSON字段格式正确
      const prefsData = {
        theme: theme || 'default',
        language: language || 'zh_CN',
        items_per_page: items_per_page || 10,
        system_name: system_name || 'Token 管理系统',
        // 对象转JSON字符串，确保不是传入[object Object]
        theme_settings: theme_settings ? 
          (typeof theme_settings === 'string' ? theme_settings : JSON.stringify(theme_settings)) : 
          null,
        dashboard_layout: dashboard_layout ? 
          (typeof dashboard_layout === 'string' ? dashboard_layout : JSON.stringify(dashboard_layout)) : 
          null,
        notification_settings: notification_settings ? 
          (typeof notification_settings === 'string' ? notification_settings : JSON.stringify(notification_settings)) : 
          null,
        updated_at: new Date()
      };
      
      // 记录更新操作的内容，用于调试
      logger.debug(`更新用户偏好设置数据: ${JSON.stringify(prefsData)}`);
      
      if (existingPrefs.length > 0) {
        // 更新现有记录
        const updateQuery = `
          UPDATE user_preferences
          SET theme = ?, language = ?, items_per_page = ?, system_name = ?,
              theme_settings = ?, dashboard_layout = ?, notification_settings = ?,
              updated_at = ?
          WHERE user_id = ?
        `;
        
        const [result] = await db.query(updateQuery, [
          prefsData.theme,
          prefsData.language,
          prefsData.items_per_page,
          prefsData.system_name,
          prefsData.theme_settings,
          prefsData.dashboard_layout,
          prefsData.notification_settings,
          prefsData.updated_at,
          userId
        ]);
        
        return result.affectedRows > 0;
      } else {
        // 创建新记录
        const insertQuery = `
          INSERT INTO user_preferences 
          (user_id, theme, language, items_per_page, system_name, theme_settings, dashboard_layout, notification_settings, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const [result] = await db.query(insertQuery, [
          userId,
          prefsData.theme,
          prefsData.language,
          prefsData.items_per_page,
          prefsData.system_name,
          prefsData.theme_settings,
          prefsData.dashboard_layout,
          prefsData.notification_settings,
          new Date()
        ]);
        
        return result.insertId > 0;
      }
    } catch (error) {
      logger.error(`更新用户(ID: ${userId})偏好设置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 重置用户偏好设置为默认值
   * @param {number} userId - 用户ID
   * @returns {Promise<boolean>} 重置是否成功
   */
  static async resetToDefault(userId) {
    try {
      // 获取默认设置
      const defaultPrefs = this.getDefaultPreferences();
      
      // 更新用户设置为默认值
      return await this.update(userId, defaultPrefs);
    } catch (error) {
      logger.error(`重置用户(ID: ${userId})偏好设置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取默认偏好设置
   * @returns {object} 默认偏好设置
   */
  static getDefaultPreferences() {
    return {
      theme: 'default',
      language: 'zh_CN',
      items_per_page: 10,
      system_name: 'Token 管理系统',
      theme_settings: {
        primary_color: '#1890ff',
        dark_mode: false,
        compact: false
      },
      dashboard_layout: {
        widgets: [
          { id: 'token-stats', position: 0, visible: true },
          { id: 'recent-activity', position: 1, visible: true },
          { id: 'quick-actions', position: 2, visible: true }
        ]
      },
      notification_settings: {
        email_notifications: true,
        browser_notifications: true,
        notify_on_token_create: true,
        notify_on_token_reset: true
      }
    };
  }

  /**
   * 初始化数据库表
   * @returns {Promise<boolean>} 初始化是否成功
   */
  static async initializeTable() {
    try {
      // 创建user_preferences表如果不存在
      await db.query(`
        CREATE TABLE IF NOT EXISTS user_preferences (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          theme VARCHAR(50) DEFAULT 'default',
          language VARCHAR(10) DEFAULT 'zh_CN',
          items_per_page INT DEFAULT 10,
          system_name VARCHAR(100) DEFAULT 'Token 管理系统',
          theme_settings JSON,
          dashboard_layout JSON,
          notification_settings JSON,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          UNIQUE KEY unique_user_pref (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);
      
      logger.info('用户偏好设置表初始化完成');
      return true;
    } catch (error) {
      logger.error(`初始化用户偏好设置表失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = UserPreference; 