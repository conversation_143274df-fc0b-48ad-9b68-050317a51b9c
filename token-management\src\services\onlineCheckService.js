import axios from 'axios';

/**
 * 检查单个token在线状态
 * @param {string} token - 要检查的token
 * @returns {Promise<{success: boolean, status: string, error?: string}>} 返回检查结果
 */
export async function checkTokenOnlineStatus(token) {
  try {
    // 设置请求头，包含AccessToken
    const headers = {
      'AccessToken': token
    };

    // 使用代理地址发送请求到拼多多API
    const response = await axios.get('/pdd-api/proxy/api/api/apollo/v3/user/active', {
      headers,
      timeout: 10000, // 设置10秒超时
    });

    // 判断在线状态：如果没有error_code则在线，否则掉线
    if (response.data && !response.data.error_code) {
      return { success: true, status: '在线' };
    } else {
      return { success: true, status: '掉线' };
    }
  } catch (error) {
    console.error('检查token在线状态失败:', error);
    // 请求出错也视为掉线
    return { success: true, status: '掉线', error: error.message };
  }
}

/**
 * 批量检查多个token的在线状态，使用并发控制避免请求过多导致性能问题
 * @param {Array<{uid: string, token: string}>} tokens - 要检查的token数组
 * @param {number} concurrency - 并发数量，默认5
 * @param {Function} progressCallback - 进度回调函数，用于更新UI
 * @returns {Promise<{success: boolean, results: Array<{id: string, success: boolean, status: string}>}>}
 */
export async function batchCheckOnlineStatus(tokens, concurrency = 5, progressCallback = null) {
  if (!tokens || tokens.length === 0) {
    return { success: false, error: '没有要检查的token' };
  }

  // 结果数组
  const results = [];
  // 进度计数
  let completed = 0;
  const total = tokens.length;

  try {
    // 使用一个队列来控制并发
    const queue = [...tokens];
    const runningPromises = [];

    // 处理单个token的函数
    const processToken = async (tokenData) => {
      try {
        const { uid, token } = tokenData;
        // 检查token状态
        const result = await checkTokenOnlineStatus(token);
        
        // 构建结果对象
        const resultItem = {
          id: uid,
          success: result.success,
          status: result.status
        };
        
        // 添加到结果数组
        results.push(resultItem);
        
        // 更新进度
        completed++;
        if (progressCallback) {
          progressCallback(completed, total);
        }
        
        return resultItem;
      } catch (error) {
        console.error('处理token出错:', error);
        // 处理出错时也返回一个结果
        const errorResult = {
          id: tokenData.uid,
          success: false,
          status: '掉线',
          error: error.message
        };
        
        results.push(errorResult);
        completed++;
        if (progressCallback) {
          progressCallback(completed, total);
        }
        
        return errorResult;
      }
    };

    // 开始处理队列
    while (queue.length > 0 || runningPromises.length > 0) {
      // 当队列还有内容且运行的promise少于并发数时，启动新的处理
      while (queue.length > 0 && runningPromises.length < concurrency) {
        const tokenData = queue.shift();
        // 创建处理Promise并添加到运行中的数组
        const promise = processToken(tokenData).finally(() => {
          // 处理完成后从运行数组中移除
          const index = runningPromises.indexOf(promise);
          if (index !== -1) {
            runningPromises.splice(index, 1);
          }
        });
        
        runningPromises.push(promise);
      }
      
      // 如果有运行中的promise，等待其中任意一个完成
      if (runningPromises.length > 0) {
        await Promise.race(runningPromises);
      }
    }

    // 全部处理完成，返回结果
    return {
      success: true,
      results
    };
  } catch (error) {
    console.error('批量检查在线状态出错:', error);
    return {
      success: false,
      error: error.message,
      results
    };
  }
}

/**
 * 将批量检查结果应用到token数组
 * @param {Array} tokens - 原token数组
 * @param {Array} results - 检查结果数组
 * @returns {Array} 更新后的token数组
 */
export function applyOnlineStatusToTokens(tokens, results) {
  if (!results || !Array.isArray(results)) {
    return tokens;
  }
  
  // 创建一个结果查找Map，提高查找效率
  const resultMap = new Map();
  results.forEach(result => {
    if (result && result.id) {
      resultMap.set(result.id, result);
    }
  });
  
  // 更新token状态
  return tokens.map(token => {
    const result = resultMap.get(token.uid);
    if (result) {
      return {
        ...token,
        status: result.status
      };
    }
    return token;
  });
} 