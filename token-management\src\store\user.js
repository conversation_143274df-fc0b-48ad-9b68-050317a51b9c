import { defineStore } from 'pinia';
import userService from '../services/user';
import { ElMessage } from 'element-plus';

export const useUserStore = defineStore('user', {
  state: () => ({
    currentUser: null,
    userLoaded: false,
    userPreferences: null,
    apiConnected: false
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.currentUser,
    isAdmin: (state) => state.currentUser?.role === 'admin',
    username: (state) => state.currentUser?.username || '游客'
  },
  
  actions: {
    // 测试API连接
    async testApiConnection() {
      try {
        const response = await userService.testApiConnection();
        this.apiConnected = true;
        return response;
      } catch (error) {
        this.apiConnected = false;
        console.error('API连接测试失败:', error);
        return null;
      }
    },
    
    // 登录
    async login(credentials) {
      try {
        const response = await userService.login(credentials);
        if (response.success) {
          localStorage.setItem('token', response.data.tokens.accessToken);
          this.currentUser = response.data.user;
          return true;
        }
        return false;
      } catch (error) {
        ElMessage.error(error.message || '登录失败');
        return false;
      }
    },
    
    // 注销
    logout() {
      localStorage.removeItem('token');
      this.currentUser = null;
      this.userPreferences = null;
      this.userLoaded = false;
      return true;
    },
    
    // 获取当前用户信息
    async fetchCurrentUser() {
      // 如果已经加载过用户信息且有数据，直接返回
      if (this.userLoaded && this.currentUser) return this.currentUser;
      
      try {
        const response = await userService.getCurrentUser();
        if (response && response.success) {
          this.currentUser = response.data;
          this.userLoaded = true;
          return this.currentUser;
        }
        
        // 如果后端返回未登录或授权失败
        if (response && !response.success) {
          localStorage.removeItem('token');
          this.currentUser = null;
          this.userLoaded = false;
          throw new Error(response.message || '未授权访问');
        }
        
        return null;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 清理状态，避免重复尝试
        this.userLoaded = false;
        this.currentUser = null;
        
        if (error.status === 401 || error.response?.status === 401) {
          localStorage.removeItem('token');
        }
        
        // 抛出错误让调用者处理
        throw error;
      }
    },
    
    // 获取用户偏好设置
    async fetchUserPreferences() {
      try {
        const response = await userService.getUserPreferences();
        console.log('获取用户设置响应:', response);
        if (response.success) {
          this.userPreferences = response.data;
          return this.userPreferences;
        }
        return null;
      } catch (error) {
        console.error('获取用户设置失败:', error);
        ElMessage.error('获取用户设置失败');
        return null;
      }
    },
    
    // 更新用户偏好设置
    async updateUserPreferences(preferences) {
      try {
        console.log('正在更新用户设置:', preferences);
        const response = await userService.updateUserPreferences(preferences);
        console.log('更新用户设置响应:', response);
        if (response.success) {
          this.userPreferences = response.data;
          ElMessage.success('设置已保存');
          return true;
        }
        return false;
      } catch (error) {
        console.error('保存设置失败:', error);
        ElMessage.error(error.message || '保存设置失败');
        return false;
      }
    },
    
    // 更新用户资料
    async updateProfile(profileData) {
      try {
        const response = await userService.updateProfile(profileData);
        if (response.success) {
          this.currentUser = {...this.currentUser, ...response.data};
          ElMessage.success('个人资料已更新');
          return true;
        }
        return false;
      } catch (error) {
        console.error('更新资料失败:', error);
        ElMessage.error(error.message || '更新资料失败');
        return false;
      }
    },
    
    // 修改密码
    async changePassword(passwordData) {
      try {
        const response = await userService.changePassword(passwordData);
        if (response.success) {
          ElMessage.success('密码已修改');
          return true;
        }
        return false;
      } catch (error) {
        console.error('密码修改失败:', error);
        ElMessage.error(error.message || '密码修改失败');
        return false;
      }
    }
  }
}); 