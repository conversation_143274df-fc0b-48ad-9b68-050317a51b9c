const axios = require('axios');
const { Worker } = require('worker_threads');
const path = require('path');
const fs = require('fs');
const { logger } = require('../utils/logger');

// 优化的批量查询服务
exports.batchCheckOnlineStatusOptimized = async (tokenDataArray) => {
  // 优化参数：
  const CONCURRENCY = 20;         // 提高并发数
  const TIMEOUT = 5000;           // 减少超时时间
  const WORKER_COUNT = 4;         // 使用4个工作线程
  const RETRY_COUNT = 2;          // 失败重试次数

  // 保证工作线程脚本存在
  const workerFilePath = path.join(__dirname, 'tokenStatusWorker.js');

  try {
    // 将数据分片
    const chunks = [];
    const chunkSize = Math.ceil(tokenDataArray.length / WORKER_COUNT);

    for (let i = 0; i < tokenDataArray.length; i += chunkSize) {
      chunks.push(tokenDataArray.slice(i, i + chunkSize));
    }

    logger.info(`开始并行检查${tokenDataArray.length}个Token状态，分为${chunks.length}个工作线程处理`);

    // 使用多个工作线程并行处理
    const workerPromises = chunks.map((chunk, index) => {
      return new Promise((resolve, reject) => {
        const worker = new Worker(workerFilePath, {
          workerData: {
            tokenChunk: chunk,
            workerId: index,
            concurrency: CONCURRENCY,
            timeout: TIMEOUT,
            retryCount: RETRY_COUNT
          }
        });

        worker.on('message', (data) => {
          // 区分进度消息和结果消息
          if (data && Array.isArray(data)) {
            // 如果是结果数组
            logger.info(`工作线程${index}完成处理，返回${data.length}个结果`);
            resolve(data);
          } else if (data && data.type === 'progress') {
            // 如果是进度消息
            logger.info(`工作线程${index}进度: ${data.processed}/${data.total}`);
          } else if (data && data.type === 'start') {
            // 开始处理消息
            logger.info(`工作线程${index}开始处理${data.tokenCount}个Token`);
          }
        });

        worker.on('error', (err) => {
          logger.error(`工作线程${index}出错:`, err);
          reject(err);
        });

        worker.on('exit', (code) => {
          if (code !== 0) {
            const errMsg = `工作线程${index}异常退出，退出码 ${code}`;
            logger.error(errMsg);
            reject(new Error(errMsg));
          }
        });
      });
    });

    // 等待所有工作线程完成并合并结果
    const results = await Promise.all(workerPromises);
    const flatResults = results.flat();

    logger.info(`完成所有Token状态检查，总共${flatResults.length}个结果`);

    // 统计结果
    const onlineCount = flatResults.filter(r => r.status === '在线').length;
    const offlineCount = flatResults.filter(r => r.status === '掉线').length;

    logger.info(`Token状态统计：${onlineCount}个在线，${offlineCount}个掉线`);

    return flatResults;
  } catch (error) {
    logger.error('批量检查Token状态错误:', error);
    throw error;
  }
};

// 单个Token状态检查，用于少量Token的检查
exports.checkTokenOnlineStatus = async (token, timeout = 5000, retryCount = 1) => {
  try {
    // 设置请求头，包含AccessToken
    const headers = {
      'AccessToken': token,
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X)',
      'Accept': 'application/json',
      'Connection': 'keep-alive'
    };

    // 发送请求到拼多多API
    const response = await axios.get('https://mobile.yangkeduo.com/proxy/api/api/apollo/v3/user/active', {
      headers,
      timeout: timeout,
    });

    // 判断在线状态：如果没有error_code则在线，否则掉线
    if (response.data && !response.data.error_code) {
      return { success: true, status: '在线' };
    } else {
      return { success: true, status: '掉线' };
    }
  } catch (error) {
    logger.error('检查token在线状态失败:', error.message);

    // 如果有重试次数，则重试
    if (retryCount > 0) {
      logger.info(`重试检查Token状态，剩余重试次数:${retryCount-1}`);
      return exports.checkTokenOnlineStatus(token, timeout, retryCount - 1);
    }

    // 请求出错也视为掉线
    return { success: true, status: '掉线', error: error.message };
  }
};

/**
 * 查询Token的用户信息（头像和昵称）
 * @param {string} token - 用户的token
 * @param {number} timeout - 请求超时时间（毫秒）
 * @param {number} retryCount - 重试次数
 * @returns {Promise<Object>} 返回用户信息查询结果
 */
exports.queryTokenUserInfo = async (token, timeout = 5000, retryCount = 1) => {
  try {
    // 设置请求头
    const headers = {
      'AccessToken': token,
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Referer': 'https://mobile.yangkeduo.com/orders.html',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
    };

    // 发送请求到拼多多API
    const response = await axios.get('https://mobile.yangkeduo.com/proxy/api/api/apollo/v3/user/active', {
      headers,
      timeout: timeout,
    });

    // 从响应中提取用户信息
    if (response.data && !response.data.error_code) {
      return {
        success: true,
        data: {
          avatar: response.data.avatar || '',
          nickname: response.data.nickname || '',
          userId: response.data.id || '',
          isOnline: true
        }
      };
    } else {
      logger.warn(`获取Token用户信息失败, error_code: ${response.data?.error_code}`);
      return {
        success: false,
        isOnline: false,
        message: '获取用户信息失败，token可能已失效'
      };
    }
  } catch (error) {
    logger.error('查询token用户信息失败:', error.message);

    // 如果有重试次数，则重试
    if (retryCount > 0) {
      logger.info(`重试查询Token用户信息，剩余重试次数:${retryCount-1}`);
      return exports.queryTokenUserInfo(token, timeout, retryCount - 1);
    }

    // 请求出错返回失败
    return {
      success: false,
      isOnline: false,
      message: `查询用户信息失败: ${error.message}`
    };
  }
};

/**
 * 批量查询用户信息（头像和昵称）
 * @param {Array<{uid: string, token: string}>} tokenDataArray - token数据数组
 * @returns {Promise<Array>} 批量查询结果
 */
exports.batchQueryTokenUserInfo = async (tokenDataArray) => {
  // 优化参数：
  const CONCURRENCY = 10;        // 并发数
  const TIMEOUT = 5000;          // 超时时间
  const RETRY_COUNT = 1;         // 失败重试次数

  logger.info(`开始批量查询${tokenDataArray.length}个Token的用户信息`);

  try {
    // 结果数组
    const results = [];
    // 并发控制
    const batchSize = CONCURRENCY;

    // 分批处理
    for (let i = 0; i < tokenDataArray.length; i += batchSize) {
      const batch = tokenDataArray.slice(i, i + batchSize);
      logger.info(`处理批次 ${i/batchSize + 1}，共${batch.length}个Token`);

      // 并行处理当前批次
      const batchPromises = batch.map(tokenData =>
        exports.queryTokenUserInfo(tokenData.token, TIMEOUT, RETRY_COUNT)
          .then(result => ({
            uid: tokenData.uid,
            ...result
          }))
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 添加短暂延迟以避免API限制
      if (i + batchSize < tokenDataArray.length) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    // 统计在线数量
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    logger.info(`用户信息查询完成：${successCount}个成功，${failCount}个失败`);

    return results;
  } catch (error) {
    logger.error('批量查询Token用户信息错误:', error);
    throw error;
  }
};

/**
 * 查询Token的订单信息
 * @param {string} token - 用户的token
 * @param {number} timeout - 请求超时时间（毫秒）
 * @param {number} retryCount - 重试次数
 * @returns {Promise<Object>} 返回订单查询结果
 */
exports.queryTokenOrderInfo = async (token, timeout = 8000, retryCount = 1) => {
  try {
    // 设置请求头
    const headers = {
      'AccessToken': token,
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      'Referer': 'https://mobile.yangkeduo.com/orders.html',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
    };

    // 首先检查用户状态是否在线
    try {
      const onlineCheckResponse = await axios.get('https://mobile.yangkeduo.com/proxy/api/api/apollo/v3/user/active', {
        headers,
        timeout: 5000
      });

      // 如果用户不在线，直接返回掉线状态
      if (onlineCheckResponse.data && onlineCheckResponse.data.error_code) {
        return {
          success: true,
          isOnline: false,
          status: '掉线',
          message: '用户处于掉线状态'
        };
      }
    } catch (error) {
      // 如果检查在线状态出错，也视为掉线
      return {
        success: true,
        isOnline: false,
        status: '掉线',
        message: '用户处于掉线状态'
      };
    }

    // 用户在线，查询订单信息
    const response = await axios.post('https://mobile.yangkeduo.com/proxy/api/api/aristotle/order_list_v4', {
      type: "all",
      page: 1,
      origin_host_name: "mobile.yangkeduo.com",
      page_from: 0,
      size: 10
    }, {
      headers,
      timeout: timeout
    });

    // 处理响应数据
    if (response.data && !response.data.error_code) {
      const orders = response.data.orders || [];

      if (orders.length > 0) {
        // 返回所有订单信息
        const orderList = orders.map(order => ({
          orderId: order.order_sn || '',
          status: order.order_status_prompt || '',
          amount: order.order_amount / 100 || 0, // 转换为元
          orderTime: order.order_time ? new Date(order.order_time * 1000).toLocaleString() : '',
          goodsInfo: order.order_goods && order.order_goods.length > 0
            ? `${order.order_goods[0].goods_name}` : '未知商品',
          trackingNumber: order.tracking_number || '',
          mallName: order.mall?.mall_name || '',
          orderType: order.type || 0,
          extraInfo: order
        }));

        return {
          success: true,
          isOnline: true,
          status: '在线',
          data: {
            orderCount: orders.length,
            orders: orderList
          }
        };
      } else {
        return {
          success: true,
          isOnline: true,
          status: '在线',
          data: {
            orderCount: 0,
            orders: []
          },
          message: '没有找到订单信息'
        };
      }
    } else {
      logger.warn(`获取Token订单信息失败, error_code: ${response.data?.error_code}`);
      return {
        success: false,
        isOnline: true,
        status: '在线',
        message: '获取订单信息失败，token可能已失效'
      };
    }
  } catch (error) {
    logger.error('查询token订单信息失败:', error.message);

    // 重试机制
    if (retryCount > 0) {
      logger.info(`重试查询Token订单信息，剩余重试次数:${retryCount-1}`);
      return exports.queryTokenOrderInfo(token, timeout, retryCount - 1);
    }

    return {
      success: false,
      status: '未知',
      message: `查询订单信息失败: ${error.message}`
    };
  }
};

/**
 * 批量查询订单信息
 * @param {Array<{uid: string, token: string}>} tokenDataArray - token数据数组
 * @returns {Promise<Object>} 包含查询结果和统计信息
 */
exports.batchQueryTokenOrderInfo = async (tokenDataArray) => {
  // 优化参数 - 增加并发数和工作线程数
  const CONCURRENCY = 20;        // 从10增加到20
  const TIMEOUT = 10000;         // 保持不变
  const RETRY_COUNT = 2;         // 保持不变
  const WORKER_COUNT = 10;       // 从8增加到10

  // 保证工作线程脚本存在
  const workerFilePath = path.join(__dirname, 'orderStatusWorker.js');

  logger.info(`开始并行查询${tokenDataArray.length}个Token的订单信息，使用${WORKER_COUNT}个工作线程，每个工作线程并发数${CONCURRENCY}`);

  try {
    // 将数据分片
    const chunks = [];
    const chunkSize = Math.ceil(tokenDataArray.length / WORKER_COUNT);

    for (let i = 0; i < tokenDataArray.length; i += chunkSize) {
      chunks.push(tokenDataArray.slice(i, i + chunkSize));
    }

    // 使用多个工作线程并行处理
    const workerPromises = chunks.map((chunk, index) => {
      return new Promise((resolve, reject) => {
        const worker = new Worker(workerFilePath, {
          workerData: {
            tokenChunk: chunk,
            workerId: index,
            concurrency: CONCURRENCY,
            timeout: TIMEOUT,
            retryCount: RETRY_COUNT
          }
        });

        worker.on('message', (data) => {
          // 区分进度消息和结果消息
          if (data && Array.isArray(data)) {
            // 如果是结果数组
            logger.info(`订单查询工作线程${index}完成处理，返回${data.length}个结果`);
            resolve(data);
          } else if (data && data.type === 'progress') {
            // 如果是进度消息
            logger.info(`订单查询工作线程${index}进度: ${data.processed}/${data.total}`);
          } else if (data && data.type === 'start') {
            // 开始处理消息
            logger.info(`订单查询工作线程${index}开始处理${data.tokenCount}个Token`);
          }
        });

        worker.on('error', (err) => {
          logger.error(`订单查询工作线程${index}出错:`, err);
          reject(err);
        });

        worker.on('exit', (code) => {
          if (code !== 0) {
            const errMsg = `订单查询工作线程${index}异常退出，退出码 ${code}`;
            logger.error(errMsg);
            reject(new Error(errMsg));
          }
        });
      });
    });

    // 等待所有工作线程完成并合并结果
    const results = await Promise.all(workerPromises);
    const flatResults = results.flat();

    logger.info(`完成所有Token订单查询，总共${flatResults.length}个结果`);

    // 统计结果
    const successCount = flatResults.filter(r => r.success).length;
    const hasOrderCount = flatResults.filter(r => r.success && r.data && r.data.orderCount > 0).length;
    const onlineCount = flatResults.filter(r => r.status === '在线').length;
    const offlineCount = flatResults.filter(r => r.status === '掉线').length;

    logger.info(`订单查询统计：${successCount}个成功，${hasOrderCount}个有订单信息，${onlineCount}个在线，${offlineCount}个掉线`);

    return {
      results: flatResults,
      stats: {
        total: flatResults.length,
        success: successCount,
        hasOrder: hasOrderCount,
        noOrder: successCount - hasOrderCount,
        failed: flatResults.length - successCount,
        online: onlineCount,
        offline: offlineCount
      }
    };
  } catch (error) {
    logger.error('批量查询Token订单信息错误:', error);
    throw error;
  }
};