import axios from 'axios'
import { ElMessage } from 'element-plus'

/**
 * 昵称服务 - 处理昵称相关的功能
 */

// 获取随机昵称
export const getRandomNickname = async () => {
  try {
    // 调用后端API获取随机昵称
    const response = await axios.get('/api/nickname/random')
    
    if (response.data && response.data.success && response.data.nickname) {
      return {
        success: true,
        nickname: response.data.nickname,
        message: '获取随机昵称成功'
      }
    } else {
      throw new Error('获取随机昵称失败')
    }
  } catch (error) {
    console.error('获取随机昵称失败:', error)
    
    // 模拟随机昵称（仅用于演示）
    // 在实际环境中，应该从服务器获取随机昵称
    return simulateRandomNickname()
  }
}

// 模拟随机昵称（仅用于演示）
const simulateRandomNickname = () => {
  const randomNicknames = [
    '快乐的舞步', '真心的爱', '翩若惊鸿', '运气多多', '封笔',
    '灵魂的港湾', '醉笙', '夏至春掩', '色舞天涯', '无道纣王'
  ]
  
  const randomIndex = Math.floor(Math.random() * randomNicknames.length)
  const randomNickname = randomNicknames[randomIndex]
  
  return {
    success: true,
    nickname: randomNickname,
    message: '使用模拟随机昵称'
  }
}

// 修改昵称
export const changeNickname = async (token, nickname) => {
  try {
    // 调用后端API修改昵称
    const response = await axios.post('/api/nickname/change', {
      token,
      nickname
    })
    
    if (response.data && response.data.success) {
      return {
        success: true,
        data: response.data.data,
        message: '修改昵称成功'
      }
    } else {
      throw new Error(response.data?.message || '修改昵称失败')
    }
  } catch (error) {
    console.error('修改昵称失败:', error)
    throw error
  }
}
