/**
 * 未卖Token服务
 * 提供加载未卖Token的功能
 */
import axios from 'axios';
import { ElMessage, ElLoading } from 'element-plus';

/**
 * 加载未卖Token
 * @returns {Promise<Array>} - 返回未卖Token数组
 */
export async function loadUnsoldTokens() {
  try {
    // 显示加载中提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在加载未卖Token...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 调用API获取未卖Token
    // 创建URLSearchParams对象来发送表单数据
    const formData = new URLSearchParams();
    formData.append('username', 'a15604402');
    formData.append('password', 'lijinrong11');

    const response = await axios.post(
      '/wp-json/myplugin/v1/get-delete-tokens',
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    // 关闭加载提示
    loading.close();

    // 处理响应数据
    if (Array.isArray(response.data)) {
      // 格式化Token数据
      const tokens = response.data.map(token => ({
        uid: token.UID || '',
        token: token.token || '',
        user: token.username || '',
        username: token.username || '',
        nickname: '',
        avatar: '',
        status: '未知',
        platform: '',
        purchaseTime: token.import_time || '',  // 使用import_time作为购买时间
        expiryDate: '',
        createTime: token.import_time || '',
        orderId: ''
      }));

      // 返回格式化后的Token数据
      return {
        success: true,
        data: tokens,
        message: `成功加载 ${tokens.length} 个未卖Token`
      };
    } else {
      return {
        success: false,
        data: [],
        message: '返回数据格式错误'
      };
    }
  } catch (error) {
    console.error('加载未卖Token失败:', error);
    return {
      success: false,
      data: [],
      message: `加载未卖Token失败: ${error.message}`
    };
  }
}

/**
 * 处理未卖Token数据并导入到现有Token列表
 * @param {Array} currentTokens - 当前Token列表
 * @param {Array} unsoldTokens - 未卖Token列表
 * @returns {Object} - 返回处理后的Token列表和统计信息
 */
export function processUnsoldTokens(currentTokens, unsoldTokens) {
  // 创建一个集合来存储当前Token的UID，用于去重
  const existingUids = new Set(currentTokens.map(token => token.uid));

  // 过滤掉已存在的Token
  const newTokens = unsoldTokens.filter(token => !existingUids.has(token.uid));

  // 计算重复的Token数量
  const duplicateCount = unsoldTokens.length - newTokens.length;

  // 合并Token列表
  const mergedTokens = [...currentTokens, ...newTokens];

  return {
    tokens: mergedTokens,
    stats: {
      total: unsoldTokens.length,
      new: newTokens.length,
      duplicate: duplicateCount
    }
  };
}

export default {
  loadUnsoldTokens,
  processUnsoldTokens
};
