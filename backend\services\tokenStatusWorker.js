const { workerData, parentPort } = require('worker_threads');
const axios = require('axios');
const https = require('https');

// 创建一个带有较高并发限制和优化的axios实例
const pddApi = axios.create({
  timeout: workerData.timeout || 5000,
  httpsAgent: new https.Agent({ 
    keepAlive: true,
    maxSockets: workerData.concurrency || 20
  })
});

// 检查单个token的在线状态
async function checkTokenStatus(tokenData, retryCount = 0) {
  try {
    const response = await pddApi.get('https://mobile.yangkeduo.com/proxy/api/api/apollo/v3/user/active', {
      headers: {
        'AccessToken': tokenData.token,
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X)',
        'Accept': 'application/json',
        'Connection': 'keep-alive'
      }
    });
    
    // 判断在线状态
    if (response.data && !response.data.error_code) {
      return { id: tokenData.uid, success: true, status: '在线' };
    } else {
      return { id: tokenData.uid, success: true, status: '掉线' };
    }
  } catch (error) {
    // 如果失败且还有重试次数，则重试
    if (retryCount < workerData.retryCount) {
      // 添加随机延迟，避免同时重试导致拒绝
      const delay = Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      return checkTokenStatus(tokenData, retryCount + 1);
    }
    
    return { id: tokenData.uid, success: true, status: '掉线', error: error.message };
  }
}

// 处理工作线程收到的数据
async function processTokenChunk() {
  const { tokenChunk, concurrency } = workerData;
  const results = [];
  
  // 使用Promise.all分批处理，控制并发
  const batchProcess = async (tokens) => {
    return Promise.all(
      tokens.map(token => checkTokenStatus(token))
    );
  };
  
  // 分批处理所有token
  for (let i = 0; i < tokenChunk.length; i += concurrency) {
    const batch = tokenChunk.slice(i, i + concurrency);
    const batchResults = await batchProcess(batch);
    results.push(...batchResults);
    
    // 向主线程报告进度
    parentPort.postMessage({
      type: 'progress',
      processed: i + batch.length,
      total: tokenChunk.length,
      workerId: workerData.workerId
    });
  }
  
  return results;
}

// 向主线程发送开始消息
parentPort.postMessage({
  type: 'start',
  workerId: workerData.workerId,
  tokenCount: workerData.tokenChunk.length
});

// 执行处理并发送结果回主线程
processTokenChunk()
  .then(results => {
    parentPort.postMessage(results);
  })
  .catch(error => {
    console.error(`工作线程 ${workerData.workerId} 错误:`, error);
    parentPort.postMessage([]);
  }); 