<template>
  <div class="settings-container">
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <el-form label-position="top" :model="basicSettings" class="settings-form">
            <el-form-item label="系统名称">
              <el-input v-model="basicSettings.systemName" />
            </el-form-item>
            
            <el-form-item label="主题">
              <el-select v-model="basicSettings.theme" class="full-width">
                <el-option label="默认主题" value="default" />
                <el-option label="暗色主题" value="dark" />
                <el-option label="浅色主题" value="light" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="语言">
              <el-select v-model="basicSettings.language" class="full-width">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="每页显示记录数">
              <el-input-number v-model="basicSettings.pageSize" :min="10" :max="100" :step="10" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings" :loading="loading.basic">保存设置</el-button>
              <el-button @click="resetBasicSettings">重置</el-button>
              <el-button type="info" @click="testApiHealth">测试API连接</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 用户设置 -->
        <el-tab-pane label="用户设置" name="user">
          <el-form label-position="top" :model="userSettings" :rules="userRules" ref="userForm" class="settings-form">
            <el-form-item label="用户名">
              <el-input v-model="userSettings.username" disabled />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userSettings.email" />
            </el-form-item>
            
            <el-divider>修改密码</el-divider>
            
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input v-model="userSettings.currentPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input v-model="userSettings.newPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input v-model="userSettings.confirmPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveUserSettings" :loading="loading.user">保存设置</el-button>
              <el-button @click="resetUserForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <el-form label-position="top" :model="notificationSettings" class="settings-form">
            <el-form-item label="邮件通知">
              <el-switch v-model="notificationSettings.emailEnabled" />
            </el-form-item>
            
            <template v-if="notificationSettings.emailEnabled">
              <el-form-item label="Token到期提醒">
                <el-checkbox-group v-model="notificationSettings.expiryNotification">
                  <el-checkbox :value="30">提前30天</el-checkbox>
                  <el-checkbox :value="15">提前15天</el-checkbox>
                  <el-checkbox :value="7">提前7天</el-checkbox>
                  <el-checkbox :value="1">提前1天</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="备份提醒">
                <el-switch v-model="notificationSettings.backupNotification" />
              </el-form-item>
              
              <el-form-item label="系统事件通知">
                <el-checkbox-group v-model="notificationSettings.systemEvents">
                  <el-checkbox :value="'token_import'">Token导入</el-checkbox>
                  <el-checkbox :value="'token_reset'">Token重置</el-checkbox>
                  <el-checkbox :value="'backup_created'">备份创建</el-checkbox>
                  <el-checkbox :value="'backup_restored'">备份恢复</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </template>
            
            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings" :loading="loading.notification">保存设置</el-button>
              <el-button @click="resetNotificationSettings">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 数据管理 -->
        <el-tab-pane label="数据管理" name="data" v-if="isAdmin">
          <div class="data-management">
            <el-divider>数据导入导出</el-divider>
            
            <div class="action-group">
              <el-button type="primary" @click="exportAllData" :loading="loading.export">
                <el-icon><Download /></el-icon>导出所有数据
              </el-button>
              <el-button type="success" @click="importData" :loading="loading.import">
                <el-icon><Upload /></el-icon>导入数据
              </el-button>
            </div>
            
            <el-divider>数据清理</el-divider>
            
            <div class="action-group">
              <el-button type="danger" @click="clearExpiredTokens" :loading="loading.clearExpired">
                <el-icon><Delete /></el-icon>清理过期Token
              </el-button>
              <el-button type="danger" @click="clearOldBackups" :loading="loading.clearBackups">
                <el-icon><Delete /></el-icon>清理旧备份
              </el-button>
              <el-button type="danger" @click="clearAllData" :loading="loading.clearAll">
                <el-icon><Delete /></el-icon>清理所有数据
              </el-button>
            </div>
            
            <el-divider>数据统计</el-divider>
            
            <el-descriptions border>
              <el-descriptions-item label="Token总数">{{ dataStats.tokenCount }}</el-descriptions-item>
              <el-descriptions-item label="备份总数">{{ dataStats.backupCount }}</el-descriptions-item>
              <el-descriptions-item label="过期Token数量">{{ dataStats.expiredTokenCount }}</el-descriptions-item>
              <el-descriptions-item label="最早Token创建时间">{{ dataStats.firstTokenTime }}</el-descriptions-item>
              <el-descriptions-item label="最近Token更新时间">{{ dataStats.lastTokenTime }}</el-descriptions-item>
              <el-descriptions-item label="数据库大小">{{ dataStats.databaseSize }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useUserStore } from '../store/user';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Download, Upload, Delete } from '@element-plus/icons-vue';
import userService from '../services/user';

// 用户状态store
const userStore = useUserStore();
const route = useRoute();

// 当前激活的标签页
const activeTab = ref('basic');

// 监听路由查询参数变化，自动切换标签页
watch(() => route.query.tab, (newTab) => {
  if (newTab && ['basic', 'user', 'notification', 'data'].includes(newTab)) {
    activeTab.value = newTab;
  }
}, { immediate: true });

// 用户表单引用
const userForm = ref(null);

// 表单验证规则
const userRules = {
  email: [
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { 
      validator: (rule, value, callback) => {
        if (value && value !== userSettings.value.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ]
};

// 是否为管理员
const isAdmin = computed(() => userStore.isAdmin);

// 加载状态
const loading = reactive({
  basic: false,
  user: false,
  notification: false,
  export: false,
  import: false,
  clearExpired: false,
  clearBackups: false,
  clearAll: false
});

// 基本设置
const basicSettings = ref({
  systemName: 'Token 管理系统',
  theme: 'default',
  language: 'zh-CN',
  pageSize: 20
});

// 用户设置
const userSettings = ref({
  username: '',
  email: '',
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 通知设置
const notificationSettings = ref({
  emailEnabled: true,
  expiryNotification: [30, 15, 7, 1],
  backupNotification: true,
  systemEvents: ['token_reset', 'backup_created']
});

// 数据统计
const dataStats = ref({
  tokenCount: 0,
  backupCount: 0,
  expiredTokenCount: 0,
  firstTokenTime: '-',
  lastTokenTime: '-',
  databaseSize: '-'
});

// 生命周期钩子
onMounted(async () => {
  // 测试API连接
  try {
    const response = await userStore.testApiConnection();
    if (response) {
      ElMessage.success('后端服务连接成功');
    } else {
      ElMessage.error('后端服务连接失败，请确保后端服务已启动');
      return; // 如果API连接失败，不继续加载
    }
  } catch (error) {
    console.error('API连接测试失败:', error);
    ElMessage.error('后端服务连接失败，请确保后端服务已启动');
    return; // 如果API连接失败，不继续加载
  }

  // 加载用户信息
  await loadUserData();
  
  // 加载用户偏好设置
  await loadUserPreferences();
  
  // 如果是管理员，加载数据统计
  if (isAdmin.value) {
    await loadDataStats();
  }
});

// 加载用户数据
const loadUserData = async () => {
  // 获取当前用户信息
  const user = await userStore.fetchCurrentUser();
  if (user) {
    userSettings.value.username = user.username;
    userSettings.value.email = user.email || '';
  }
};

// 加载用户偏好设置
const loadUserPreferences = async () => {
  const prefs = await userStore.fetchUserPreferences();
  if (prefs) {
    // 更新基本设置
    basicSettings.value = {
      systemName: prefs.system_name || 'Token 管理系统',
      theme: prefs.theme || 'default',
      language: prefs.language || 'zh-CN',
      pageSize: prefs.items_per_page || 20
    };
    
    // 更新通知设置
    if (prefs.notification_settings) {
      // 解析数值类型的过期提醒天数
      let expiryDays = [];
      if (prefs.notification_settings.expiry_days) {
        expiryDays = prefs.notification_settings.expiry_days.map(day => 
          typeof day === 'string' ? parseInt(day, 10) : day
        );
      } else {
        expiryDays = [7, 1]; // 默认值
      }
      
      notificationSettings.value = {
        emailEnabled: prefs.notification_settings.email_notifications || false,
        expiryNotification: expiryDays,
        backupNotification: prefs.notification_settings.backup_notifications || false,
        systemEvents: prefs.notification_settings.event_types || []
      };
    }
  }
};

// 加载数据统计
const loadDataStats = async () => {
  try {
    // 这里应该请求后端API获取数据统计
    // 临时使用模拟数据
    dataStats.value = {
      tokenCount: 120,
      backupCount: 5,
      expiredTokenCount: 2,
      firstTokenTime: '2024-01-15 10:30:00',
      lastTokenTime: '2024-03-29 15:45:00',
      databaseSize: '2.5 MB'
    };
  } catch (error) {
    ElMessage.error('获取数据统计失败');
  }
};

// 保存基本设置
const saveBasicSettings = async () => {
  try {
    loading.basic = true;
    
    // 构建用户偏好设置对象
    const preferences = {
      theme: basicSettings.value.theme,
      language: basicSettings.value.language,
      items_per_page: basicSettings.value.pageSize,
      system_name: basicSettings.value.systemName
    };
    
    // 调用API更新用户偏好设置
    const success = await userStore.updateUserPreferences(preferences);
    
    if (success) {
      ElMessage.success('基本设置已保存');
    }
  } catch (error) {
    ElMessage.error('保存基本设置失败');
  } finally {
    loading.basic = false;
  }
};

// 重置基本设置
const resetBasicSettings = () => {
  basicSettings.value = {
    systemName: 'Token 管理系统',
    theme: 'default',
    language: 'zh-CN',
    pageSize: 20
  };
};

// 保存用户设置
const saveUserSettings = async () => {
  // 表单验证
  if (!userForm.value) return;
  
  try {
    await userForm.value.validate();
    
    loading.user = true;
    
    // 判断是否需要更新资料
    if (userSettings.value.email) {
      const profileData = {
        email: userSettings.value.email
      };
      
      // 更新用户资料
      await userStore.updateProfile(profileData);
    }
    
    // 判断是否需要更新密码
    if (userSettings.value.currentPassword && userSettings.value.newPassword) {
      const passwordData = {
        currentPassword: userSettings.value.currentPassword,
        newPassword: userSettings.value.newPassword
      };
      
      // 更新密码
      const success = await userStore.changePassword(passwordData);
      
      if (success) {
        // 清空密码字段
        userSettings.value.currentPassword = '';
        userSettings.value.newPassword = '';
        userSettings.value.confirmPassword = '';
      }
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    loading.user = false;
  }
};

// 重置用户表单
const resetUserForm = () => {
  if (userForm.value) {
    userForm.value.resetFields();
  }
  
  // 重新加载用户数据
  loadUserData();
};

// 保存通知设置
const saveNotificationSettings = async () => {
  try {
    loading.notification = true;
    
    // 确保expiryNotification数组中的值为数字类型
    const expiryDays = notificationSettings.value.expiryNotification.map(day => 
      typeof day === 'string' ? parseInt(day, 10) : day
    );
    
    // 构建通知设置对象
    const preferences = {
      notification_settings: {
        email_notifications: notificationSettings.value.emailEnabled,
        expiry_days: expiryDays,
        backup_notifications: notificationSettings.value.backupNotification,
        event_types: notificationSettings.value.systemEvents
      }
    };
    
    // 在发送前确保对象是干净的，不包含循环引用或特殊对象
    console.log('准备发送的通知设置:', JSON.parse(JSON.stringify(preferences)));
    
    // 调用API更新用户偏好设置
    const success = await userStore.updateUserPreferences(preferences);
    
    if (success) {
      ElMessage.success('通知设置已保存');
    }
  } catch (error) {
    console.error('保存通知设置失败:', error);
    ElMessage.error('保存通知设置失败: ' + (error.message || '未知错误'));
  } finally {
    loading.notification = false;
  }
};

// 重置通知设置
const resetNotificationSettings = () => {
  notificationSettings.value = {
    emailEnabled: true,
    expiryNotification: [30, 15, 7, 1],
    backupNotification: true,
    systemEvents: ['token_reset', 'backup_created']
  };
};

// 数据管理相关
const exportAllData = async () => {
  try {
    loading.export = true;
    ElMessage.success('数据导出功能尚未实现');
  } finally {
    loading.export = false;
  }
};

const importData = async () => {
  try {
    loading.import = true;
    ElMessage.success('数据导入功能尚未实现');
  } finally {
    loading.import = false;
  }
};

const clearExpiredTokens = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      '确定要清理所有过期Token吗？此操作不可恢复！',
      '确认操作',
      { type: 'warning' }
    );
    
    if (confirmed) {
      loading.clearExpired = true;
      ElMessage.success('清理过期Token功能尚未实现');
    }
  } catch (error) {
    // 用户取消操作
  } finally {
    loading.clearExpired = false;
  }
};

const clearOldBackups = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      '确定要清理旧备份吗？此操作不可恢复！',
      '确认操作',
      { type: 'warning' }
    );
    
    if (confirmed) {
      loading.clearBackups = true;
      ElMessage.success('清理旧备份功能尚未实现');
    }
  } catch (error) {
    // 用户取消操作
  } finally {
    loading.clearBackups = false;
  }
};

const clearAllData = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      '确定要清理所有数据吗？此操作不可恢复！',
      '确认操作',
      { type: 'warning', confirmButtonText: '确定清理', confirmButtonClass: 'el-button--danger' }
    );
    
    if (confirmed) {
      loading.clearAll = true;
      ElMessage.success('清理所有数据功能尚未实现');
    }
  } catch (error) {
    // 用户取消操作
  } finally {
    loading.clearAll = false;
  }
};

// 测试API连接
const testApiHealth = async () => {
  try {
    const response = await userStore.testApiConnection();
    if (response) {
      ElMessage.success('后端服务连接成功');
    } else {
      ElMessage.error('后端服务连接失败，请确保后端服务已启动');
    }
  } catch (error) {
    console.error('API连接测试失败:', error);
    ElMessage.error('后端服务连接失败，请确保后端服务已启动');
  }
};
</script>

<style scoped>
.settings-container {
  padding: 10px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-form {
  max-width: 600px;
  margin: 20px auto;
}

.full-width {
  width: 100%;
}

.action-group {
  display: flex;
  gap: 15px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.data-management {
  padding: 20px;
}
</style> 