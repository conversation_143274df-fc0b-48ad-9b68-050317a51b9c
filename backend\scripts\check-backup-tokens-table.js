/**
 * 检查backup_tokens表结构
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function checkBackupTokensTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'token_management'
    });
    
    console.log('数据库连接成功');
    
    // 获取表结构
    const [columns] = await connection.query('SHOW COLUMNS FROM backup_tokens');
    
    console.log('backup_tokens表结构:');
    columns.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? '可为空' : '不可为空'} ${column.Key ? `(${column.Key})` : ''}`);
    });
    
    // 检查backup_id列
    const backupIdColumn = columns.find(col => col.Field === 'backup_id');
    if (backupIdColumn) {
      console.log(`\nbackup_id列的详细信息: ${JSON.stringify(backupIdColumn)}`);
      
      // 如果backup_id列不是VARCHAR类型，修改它
      if (!backupIdColumn.Type.includes('varchar')) {
        await connection.query('ALTER TABLE backup_tokens MODIFY COLUMN backup_id VARCHAR(50) NOT NULL');
        console.log('成功将backup_tokens表的backup_id列修改为VARCHAR(50)');
        
        // 验证修改
        const [updatedColumns] = await connection.query('SHOW COLUMNS FROM backup_tokens LIKE "backup_id"');
        console.log(`修改后backup_id列的类型: ${updatedColumns[0].Type}`);
      } else {
        console.log('backup_id列已经是VARCHAR类型，无需修改');
      }
    } else {
      console.error('未找到backup_id列');
    }
    
  } catch (error) {
    console.error('检查backup_tokens表失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行检查
checkBackupTokensTable();
