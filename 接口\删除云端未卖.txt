POST http://aaaaa.pxxcp.com/wp-json/custom/v1/truncate-token HTTP/1.1
Accept: */*
Accept-Encoding: gzip, deflate
Connection: keep-alive
Content-Length: 39
Content-Type: application/x-www-form-urlencoded
Host: aaaaa.pxxcp.com
User-Agent: python-requests/2.32.3

username=a15604402&password=lijinrong11

============返回====================

HTTP/1.1 200 OK
Access-Control-Allow-Headers: Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type
Access-Control-Expose-Headers: X-WP-Total, X-WP-TotalPages, Link
Allow: POST
Cache-Control: no-store, no-cache, must-revalidate
Connection: keep-alive
Content-Type: application/json; charset=UTF-8
Date: Thu, 10 Apr 2025 16:56:24 GMT
Expires: Thu, 19 Nov 1981 08:52:00 GMT
Link: <http://aaaaa.pxxcp.com/index.php/wp-json/>; rel="https://api.w.org/"
Pragma: no-cache
Server: nginx
Set-Cookie: PHPSESSID=0gn7nmk2mkhp99gi7tvq0iks81; path=/
X-Content-Type-Options: nosniff
X-Robots-Tag: noindex



"Token table truncated successfully."