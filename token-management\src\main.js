import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 导入所有 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 创建应用实例
const app = createApp(App)

// 注册所有图标组件（新版命名）
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
  
  // 同时注册兼容旧版命名的组件
  // 将驼峰命名转换为短横线命名，如：Connection => el-icon-connection
  const kebabCaseName = key
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .replace(/^-/, '')
  app.component(`el-icon-${kebabCaseName}`, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 挂载应用
app.mount('#app') 