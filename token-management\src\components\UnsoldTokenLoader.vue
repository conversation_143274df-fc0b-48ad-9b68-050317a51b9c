<template>
  <div class="unsold-token-loader">
    <el-button
      type="primary"
      :icon="Download"
      :loading="loading"
      @click="loadUnsoldTokens"
    >
      加载未卖
    </el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import unsoldTokenService from '../services/unsoldTokenService'

// 定义props
const props = defineProps({
  currentTokens: {
    type: Array,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['update:tokens'])

// 状态变量
const loading = ref(false)

// 加载未卖Token
const loadUnsoldTokens = async () => {
  try {
    // 设置加载状态
    loading.value = true

    // 调用服务加载未卖Token
    const result = await unsoldTokenService.loadUnsoldTokens()

    // 处理结果
    if (result.success) {
      // 处理Token数据并更新
      const { tokens, stats } = unsoldTokenService.processUnsoldTokens(props.currentTokens, result.data)

      // 更新Token列表
      emit('update:tokens', tokens)

      // 显示成功消息
      ElMessage.success(`成功加载 ${stats.new} 个未卖Token，跳过 ${stats.duplicate} 个重复Token`)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('加载未卖Token失败:', error)
    ElMessage.error(`加载未卖Token失败: ${error.message}`)
  } finally {
    // 重置加载状态
    loading.value = false
  }
}
</script>

<style scoped>
.unsold-token-loader {
  display: inline-block;
  margin-right: 10px;
}
</style>
