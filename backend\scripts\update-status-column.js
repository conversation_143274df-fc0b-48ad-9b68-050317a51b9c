/**
 * 更新备份表的status列
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function updateStatusColumn() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'token_management'
    });
    
    console.log('数据库连接成功');
    
    // 获取status列的当前定义
    const [columns] = await connection.query('SHOW COLUMNS FROM backups LIKE "status"');
    
    if (columns.length > 0) {
      const currentType = columns[0].Type;
      console.log(`当前status列的类型: ${currentType}`);
      
      // 修改列定义，改为VARCHAR(50)
      await connection.query('ALTER TABLE backups MODIFY COLUMN status VARCHAR(50) DEFAULT "备份中"');
      console.log('成功将status列修改为VARCHAR(50)');
      
      // 验证修改
      const [updatedColumns] = await connection.query('SHOW COLUMNS FROM backups LIKE "status"');
      console.log(`修改后status列的类型: ${updatedColumns[0].Type}`);
    } else {
      console.error('未找到status列');
    }
    
  } catch (error) {
    console.error('更新status列失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行更新
updateStatusColumn();
