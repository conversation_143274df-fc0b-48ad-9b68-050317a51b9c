# Token 管理系统 - 后端API

Token 管理系统的后端 API 服务，基于 Node.js、Express 和 MySQL 开发。

## 功能概述

本后端 API 为 Token 管理系统提供数据服务和业务逻辑处理，主要功能包括：

- 用户认证与授权
- Token 数据的增删改查
- 备份管理
- 重置记录管理
- 系统设置
- 数据统计分析

## 技术栈

- Node.js
- Express.js
- MySQL (数据库)
- JWT (用户认证)
- Winston (日志记录)
- Bcrypt (密码加密)

## 项目结构

```
backend/
├── config/            # 配置文件
│   ├── database.js    # 数据库配置
│   └── index.js       # 主配置文件
├── controllers/       # 控制器
│   ├── auth.js        # 认证控制器
│   ├── token.js       # Token控制器
│   ├── backup.js      # 备份控制器
│   ├── reset.js       # 重置控制器
│   ├── settings.js    # 设置控制器
│   └── stats.js       # 统计控制器
├── middleware/        # 中间件
│   ├── auth.js        # 认证中间件
│   └── errorHandler.js # 错误处理中间件
├── models/            # 数据模型
│   ├── user.js        # 用户模型
│   ├── token.js       # Token模型
│   ├── backup.js      # 备份模型
│   ├── reset.js       # 重置模型
│   └── setting.js     # 设置模型
├── routes/            # 路由定义
│   ├── index.js       # 路由入口
│   ├── auth.js        # 认证路由
│   ├── token.js       # Token路由
│   ├── backup.js      # 备份路由
│   ├── reset.js       # 重置路由
│   ├── settings.js    # 设置路由
│   └── stats.js       # 统计路由
├── utils/             # 工具函数
│   ├── logger.js      # 日志工具
│   ├── tokenGenerator.js # Token生成器
│   └── exportUtils.js # 导出工具
├── database/          # 数据库相关
│   └── schema.sql     # 数据库结构
├── logs/              # 日志文件
├── .env.example       # 环境变量示例
├── package.json       # 项目依赖
├── server.js          # 服务器入口
└── README.md          # 项目说明
```

## API 文档

### 认证模块 (/api/auth)

| 方法   | 路径            | 描述               | 权限     |
|------|---------------|------------------|--------|
| POST | /register     | 用户注册             | 公开     |
| POST | /login        | 用户登录             | 公开     |
| POST | /logout       | 用户登出             | 用户     |
| POST | /refresh-token | 刷新令牌             | 公开     |
| GET  | /me           | 获取当前用户信息         | 用户     |
| POST | /change-password | 修改密码             | 用户     |
| POST | /request-reset | 请求重置密码           | 公开     |
| POST | /reset-password | 重置密码             | 公开     |
| GET  | /users        | 获取所有用户(分页)       | 管理员    |
| PUT  | /users/:id/role | 更新用户角色           | 管理员    |
| PUT  | /users/:id/disable | 禁用用户             | 管理员    |

### Token管理 (/api/tokens)

| 方法    | 路径            | 描述             | 权限  |
|-------|---------------|----------------|-----|
| GET   | /             | 获取所有Token(分页)   | 用户  |
| GET   | /:id          | 获取单个Token      | 用户  |
| POST  | /             | 创建新Token       | 用户  |
| POST  | /batch        | 批量创建Token      | 用户  |
| PUT   | /:id          | 更新Token        | 用户  |
| DELETE| /:id          | 删除Token        | 用户  |
| POST  | /batch-delete | 批量删除Token      | 用户  |
| POST  | /:id/reset    | 重置Token的值      | 用户  |
| POST  | /batch-reset  | 批量重置Token      | 用户  |
| POST  | /validate     | 验证Token的合法性    | 用户  |
| GET   | /export/:format | 导出Token(CSV/Excel) | 用户  |
| POST  | /import       | 导入Token        | 用户  |
| GET   | /stats/overview | 获取Token统计信息    | 用户  |

### 备份管理 (/api/backups)

| 方法    | 路径                | 描述          | 权限  |
|-------|-------------------|-------------|-----|
| GET   | /                 | 获取所有备份(分页)   | 用户  |
| GET   | /:id              | 获取单个备份详情    | 用户  |
| GET   | /:id/tokens       | 获取备份中的token列表 | 用户  |
| POST  | /                 | 创建新备份       | 用户  |
| PUT   | /:id              | 更新备份信息      | 用户  |
| DELETE| /:id              | 删除备份        | 用户  |
| POST  | /:id/mark-for-deletion | 标记备份为待删除   | 用户  |
| POST  | /:id/cancel-deletion | 取消备份的删除标记   | 用户  |
| PUT   | /:id/status       | 更新备份状态      | 用户  |
| GET   | /stats/overview   | 获取备份统计信息    | 用户  |
| GET   | /export/:format   | 导出备份记录      | 用户  |

### 重置记录 (/api/reset)

| 方法   | 路径              | 描述           | 权限  |
|------|-----------------|--------------|-----|
| GET  | /               | 获取所有重置记录(分页) | 用户  |
| GET  | /:id            | 获取单个重置记录详情  | 用户  |
| GET  | /token/:tokenId | 获取特定token的重置历史 | 用户  |
| GET  | /stats/overview | 获取重置统计信息    | 用户  |
| POST | /batch          | 批量创建重置记录    | 管理员 |
| GET  | /export/:format | 导出重置记录      | 用户  |

### 统计信息 (/api/stats)

| 方法  | 路径              | 描述          | 权限  |
|-----|-----------------|-------------|-----|
| GET | /dashboard      | 获取仪表盘统计数据   | 用户  |
| GET | /tokens         | 获取token统计数据 | 用户  |
| GET | /backups        | 获取备份统计数据    | 用户  |
| GET | /resets         | 获取重置统计数据    | 用户  |
| GET | /users          | 获取用户统计数据    | 管理员 |
| GET | /activity       | 获取活动日志统计数据  | 管理员 |
| GET | /trends/:days   | 获取过去N天的活动趋势 | 用户  |
| GET | /export/:type/:format | 导出统计数据     | 用户  |

### 系统设置 (/api/settings)

| 方法   | 路径                | 描述        | 权限  |
|------|-------------------|-----------|-----|
| GET  | /                 | 获取系统设置    | 用户  |
| GET  | /:key             | 获取特定设置    | 用户  |
| PUT  | /:key             | 更新设置      | 管理员 |
| POST | /batch-update     | 批量更新设置    | 管理员 |
| GET  | /system/config    | 获取当前系统配置  | 管理员 |
| PUT  | /system/config    | 更新系统配置    | 管理员 |
| GET  | /user/preferences | 获取用户个人设置  | 用户  |
| PUT  | /user/preferences | 更新用户个人设置  | 用户  |

## 安装与运行

### 前置条件

- Node.js (v16 或更高版本)
- MySQL (v8 或更高版本)
- npm 或 yarn

### 安装步骤

1. 克隆仓库并进入后端目录
```bash
cd backend
```

2. 安装依赖
```bash
npm install
```

3. 创建环境配置文件
```bash
cp .env.example .env
```

4. 修改 `.env` 文件中的配置：
```
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=token_management

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# 跨域配置
CORS_ORIGIN=http://localhost:5173
```

5. 创建数据库和表
```bash
mysql -u your_db_user -p < database/schema.sql
```

### 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

服务将在配置的端口上运行（默认为 3000）。

## 更新记录

### [v1.0.0] - 2023-06-15
- 初始版本API发布
- 基础CRUD功能实现

### [v1.1.0] - 2023-08-01
- 添加备份管理API
- 添加统计功能
- 优化错误处理

### [v1.2.0] - 2023-10-12
- 添加Token状态过滤API
- 优化备份管理功能
- 添加Token批量操作API
- 修复已知BUG

### [v1.3.0] - 2023-12-20
- 添加完整的错误处理中间件
- 实现认证中间件和权限控制
- 完善模型层的Token、Backup和Reset功能
- 添加路由层结构，支持所有API端点
- 实现控制器层的业务逻辑处理

## 贡献

欢迎提交问题和拉取请求来帮助改进项目。

## 常见问题解决方案

### 1. "Unknown column 'system_name' in 'field list"错误

#### 问题描述
在保存用户设置（特别是页面大小设置）时，可能会遇到以下错误：
```
error: 更新用户(ID: 1)偏好设置失败: Unknown column 'system_name' in 'field list'
error: 更新用户个人设置失败: Unknown column 'system_name' in 'field list'
```

#### 原因
这是因为`user_preferences`表的数据库结构中缺少`system_name`字段，但代码中尝试保存这个字段。

#### 解决方案
有两种方法可以解决这个问题：

##### 方法1：使用更新脚本（推荐）
1. 在系统根目录打开终端
2. 进入backend目录: `cd backend`
3. 运行更新脚本: `node scripts/update_db.js`
4. 脚本将自动添加缺失的字段并显示成功消息

##### 方法2：手动执行SQL语句
1. 使用MySQL客户端连接到数据库
2. 执行以下SQL语句：
```sql
USE token_management;
ALTER TABLE user_preferences ADD COLUMN system_name VARCHAR(100) DEFAULT 'Token 管理系统' AFTER items_per_page;
```

#### 验证
设置修复后，您应该能够：
1. 在"系统设置"页面中更改"每页显示记录数"设置
2. 点击"保存设置"按钮
3. 查看Token列表页面，确认表格页面大小已按您的设置更改

## 其他文档

// ... existing content ... 