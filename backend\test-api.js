const http = require('http');

// 简单的 HTTP GET 请求函数
function httpGet(url) {
  return new Promise((resolve, reject) => {
    http.get(url, (res) => {
      const { statusCode } = res;
      
      if (statusCode !== 200) {
        console.error(`请求失败，状态码: ${statusCode}`);
        res.resume();
        reject(new Error(`请求失败，状态码: ${statusCode}`));
        return;
      }
      
      res.setEncoding('utf8');
      let rawData = '';
      
      res.on('data', (chunk) => { rawData += chunk; });
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(rawData);
          resolve(parsedData);
        } catch (e) {
          console.error('解析响应数据失败:', e.message);
          reject(e);
        }
      });
    }).on('error', (e) => {
      console.error(`请求出错: ${e.message}`);
      reject(e);
    });
  });
}

// 测试健康检查 API
async function testHealthApi() {
  try {
    console.log('测试健康检查 API...');
    const response = await httpGet('http://localhost:3000/api/health');
    console.log('健康检查响应:', response);
    return response;
  } catch (error) {
    console.error('健康检查测试失败:', error.message);
    return null;
  }
}

// 测试 place-order API 的测试端点
async function testPlaceOrderApi() {
  try {
    console.log('测试 place-order API...');
    
    // 构建请求选项
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/tokens/test/place-order',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    // 请求数据
    const requestData = JSON.stringify({
      tokens: [
        {
          UID: '测试UID123',
          token: '测试Token123',
          username: '测试用户'
        }
      ]
    });
    
    // 发送请求
    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        const { statusCode } = res;
        
        console.log(`状态码: ${statusCode}`);
        
        res.setEncoding('utf8');
        let rawData = '';
        
        res.on('data', (chunk) => { rawData += chunk; });
        res.on('end', () => {
          try {
            const parsedData = JSON.parse(rawData);
            resolve({ statusCode, data: parsedData });
          } catch (e) {
            console.error('解析响应数据失败:', e.message);
            reject(e);
          }
        });
      });
      
      req.on('error', (error) => {
        console.error('请求失败:', error.message);
        reject(error);
      });
      
      // 写入请求数据
      req.write(requestData);
      req.end();
    });
    
    console.log('place-order 响应:', response);
    return response;
  } catch (error) {
    console.error('place-order 测试失败:', error.message);
    return null;
  }
}

// 执行测试
async function runTests() {
  await testHealthApi();
  await testPlaceOrderApi();
}

runTests(); 