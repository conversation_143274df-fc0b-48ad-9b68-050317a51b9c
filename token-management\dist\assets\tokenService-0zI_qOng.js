import{C as r}from"./index-CckIkgn1.js";const n="http://localhost:3000/api";async function d(s){var t,a;try{console.log("发送查询用户信息请求到后端，数据:",s);const e=localStorage.getItem("token"),o=await r.post(`${n}/tokens/query/userinfo`,{token_data:s},{headers:{"Content-Type":"application/json",Authorization:e?`Bearer ${e}`:""}});return o.data.success?{success:!0,results:((t=o.data.data)==null?void 0:t.results)||[],stats:((a=o.data.data)==null?void 0:a.stats)||{},message:"查询用户信息成功"}:{success:!1,message:o.data.message||"查询用户信息失败"}}catch(e){return console.error("查询用户信息失败:",e),{success:!1,error:e,message:c(e)}}}async function p(s){var t;try{console.log("发送查询在线状态请求到后端，数据:",s);const a=localStorage.getItem("token"),e=await r.post(`${n}/tokens/query/online-optimized`,{token_data:s},{headers:{"Content-Type":"application/json",Authorization:a?`Bearer ${a}`:""}});return e.data.success?{success:!0,results:((t=e.data.data)==null?void 0:t.results)||[],message:"查询在线状态成功"}:{success:!1,message:e.data.message||"查询在线状态失败"}}catch(a){return console.error("查询在线状态失败:",a),{success:!1,error:a,message:c(a)}}}async function g(s){var t,a;try{console.log("发送查询订单请求到后端，数据:",s);const e=localStorage.getItem("token"),o=await r.post(`${n}/tokens/query/order`,{token_data:s},{headers:{"Content-Type":"application/json",Authorization:e?`Bearer ${e}`:""}});return o.data.success?{success:!0,results:((t=o.data.data)==null?void 0:t.results)||[],stats:((a=o.data.data)==null?void 0:a.stats)||{},message:"查询订单信息成功"}:{success:!1,message:o.data.message||"查询订单信息失败"}}catch(e){return console.error("查询订单信息失败:",e),{success:!1,error:e,message:c(e)}}}const m=async(s,t,a)=>{try{return(await r.post(`${n}/tokens/delete-order`,{uid:s,token:t,orderSn:a},{headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")?`Bearer ${localStorage.getItem("token")}`:""}})).data}catch(e){return console.error("删除订单出错:",e),{success:!1,message:c(e)}}},f=async s=>{try{return(await r.post(`${n}/tokens/batch-delete-orders`,{orderItems:s},{headers:{"Content-Type":"application/json",Authorization:localStorage.getItem("token")?`Bearer ${localStorage.getItem("token")}`:""}})).data}catch(t){return console.error("批量删除订单出错:",t),{success:!1,message:c(t),results:[]}}};function c(s){var t,a,e,o,u,l;if(s.isAxiosError){if(console.log("详细API错误:",(t=s.response)==null?void 0:t.data),(e=(a=s.response)==null?void 0:a.data)!=null&&e.message)return`操作失败: ${s.response.data.message}`;if(((o=s.response)==null?void 0:o.status)===500)return"服务器内部错误，请检查数据格式是否正确";if(((u=s.response)==null?void 0:u.status)===404)return"API不存在，请联系管理员配置正确的API";if(((l=s.response)==null?void 0:l.status)===401)return"未授权访问，请先登录系统"}return s.message||"操作失败，请稍后再试"}export{d as a,f as b,p as c,m as d,g as q};
