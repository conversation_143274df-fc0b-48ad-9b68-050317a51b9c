const Token = require('../models/token');
const { logger } = require('../utils/logger');
const { generateToken } = require('../utils/tokenGenerator');
const { exportToCSV, exportToExcel } = require('../utils/exportUtils');
const tokenStatusService = require('../services/tokenStatusService');
const tokenOrderService = require('../services/tokenOrderService');
const db = require('../database/db');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { Worker } = require('worker_threads');

class TokenController {
  /**
   * 获取所有token
   */
  static async getAllTokens(req, res, next) {
    try {
      const { page = 1, limit = 10, uid, status, platform } = req.query;
      
      const filters = {};
      if (uid) filters.uid = uid;
      if (status) filters.status = status;
      if (platform) filters.platform = platform;
      
      const result = await Token.getAll(parseInt(page), parseInt(limit), filters);
      
      res.json({
        success: true,
        data: {
          tokens: result.tokens,
          pagination: {
            total: result.total,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(result.total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      logger.error(`获取所有token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 根据ID获取单个token
   */
  static async getTokenById(req, res, next) {
    try {
      const { id } = req.params;
      const token = await Token.getById(id);
      
      if (!token) {
        return res.status(404).json({
          success: false,
          message: 'Token不存在'
        });
      }
      
      res.json({
        success: true,
        data: token
      });
    } catch (error) {
      logger.error(`获取token(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 创建新token
   */
  static async createToken(req, res, next) {
    try {
      const { platform, uid, description, expiry_date, status = 'active', custom_token_value } = req.body;
      
      // 验证必填字段
      if (!platform || !uid) {
        return res.status(400).json({
          success: false,
          message: '平台和UID为必填项'
        });
      }
      
      // 生成或使用自定义token值
      const tokenValue = custom_token_value || generateToken();
      
      const tokenData = {
        platform,
        uid,
        token_value: tokenValue,
        description,
        expiry_date: expiry_date ? new Date(expiry_date) : null,
        status,
        created_at: new Date()
      };
      
      const tokenId = await Token.create(tokenData, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 创建了新token (ID: ${tokenId})`);
      
      res.status(201).json({
        success: true,
        message: 'Token创建成功',
        data: {
          id: tokenId,
          ...tokenData
        }
      });
    } catch (error) {
      logger.error(`创建token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 批量创建token
   */
  static async batchCreateTokens(req, res, next) {
    try {
      const { tokens } = req.body;
      
      // 验证请求格式
      if (!Array.isArray(tokens) || tokens.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的tokens数组'
        });
      }
      
      // 验证每个token的必填字段
      for (const token of tokens) {
        if (!token.platform || !token.uid) {
          return res.status(400).json({
            success: false,
            message: '每个token必须包含platform和uid字段'
          });
        }
      }
      
      // 准备token数据
      const tokensData = tokens.map(token => ({
        platform: token.platform,
        uid: token.uid,
        token_value: token.custom_token_value || generateToken(),
        description: token.description || null,
        expiry_date: token.expiry_date ? new Date(token.expiry_date) : null,
        status: token.status || 'active',
        created_at: new Date()
      }));
      
      const result = await Token.bulkCreate(tokensData, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 批量创建了 ${result.length} 个token`);
      
      res.status(201).json({
        success: true,
        message: `成功创建 ${result.length} 个token`,
        data: {
          tokens: result
        }
      });
    } catch (error) {
      logger.error(`批量创建token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 更新token
   */
  static async updateToken(req, res, next) {
    try {
      const { id } = req.params;
      const { description, expiry_date, status } = req.body;
      
      // 检查token是否存在
      const token = await Token.getById(id);
      if (!token) {
        return res.status(404).json({
          success: false,
          message: 'Token不存在'
        });
      }
      
      // 准备更新数据
      const tokenData = {};
      if (description !== undefined) tokenData.description = description;
      if (expiry_date !== undefined) tokenData.expiry_date = expiry_date ? new Date(expiry_date) : null;
      if (status !== undefined) tokenData.status = status;
      
      // 仅当有数据更新时才进行更新
      if (Object.keys(tokenData).length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有提供要更新的字段'
        });
      }
      
      await Token.update(id, tokenData, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 更新了token (ID: ${id})`);
      
      // 获取更新后的token
      const updatedToken = await Token.getById(id);
      
      res.json({
        success: true,
        message: 'Token更新成功',
        data: updatedToken
      });
    } catch (error) {
      logger.error(`更新token(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 删除token
   */
  static async deleteToken(req, res, next) {
    try {
      const { id } = req.params;
      
      // 检查token是否存在
      const token = await Token.getById(id);
      if (!token) {
        return res.status(404).json({
          success: false,
          message: 'Token不存在'
        });
      }
      
      await Token.delete(id, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 删除了token (ID: ${id})`);
      
      res.json({
        success: true,
        message: 'Token删除成功'
      });
    } catch (error) {
      logger.error(`删除token(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 批量删除token
   */
  static async batchDeleteTokens(req, res, next) {
    try {
      const { ids } = req.body;
      
      // 验证请求格式
      if (!Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的token ID数组'
        });
      }
      
      const result = await Token.bulkDelete(ids, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 批量删除了 ${result} 个token`);
      
      res.json({
        success: true,
        message: `成功删除 ${result} 个token`
      });
    } catch (error) {
      logger.error(`批量删除token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 重置token的值
   */
  static async resetToken(req, res, next) {
    try {
      const { id } = req.params;
      const { custom_token_value } = req.body;
      
      // 检查token是否存在
      const token = await Token.getById(id);
      if (!token) {
        return res.status(404).json({
          success: false,
          message: 'Token不存在'
        });
      }
      
      // 生成新的token值或使用自定义token值
      const newTokenValue = custom_token_value || generateToken();
      
      await Token.reset(id, newTokenValue, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 重置了token (ID: ${id}) 的值`);
      
      // 获取更新后的token
      const updatedToken = await Token.getById(id);
      
      res.json({
        success: true,
        message: 'Token值重置成功',
        data: updatedToken
      });
    } catch (error) {
      logger.error(`重置token值(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 批量重置token
   */
  static async batchResetTokens(req, res, next) {
    try {
      const { tokens } = req.body;
      
      // 验证请求格式
      if (!Array.isArray(tokens) || tokens.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的token数组'
        });
      }
      
      // 验证每个token的必填字段
      for (const token of tokens) {
        if (!token.id) {
          return res.status(400).json({
            success: false,
            message: '每个token必须包含id字段'
          });
        }
      }
      
      // 准备token ID和新值
      const ids = tokens.map(token => token.id);
      const newTokenValues = tokens.map(token => token.custom_token_value || generateToken());
      
      await Token.bulkReset(ids, newTokenValues, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 批量重置了 ${ids.length} 个token的值`);
      
      res.json({
        success: true,
        message: `成功重置 ${ids.length} 个token的值`
      });
    } catch (error) {
      logger.error(`批量重置token值失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 验证token的合法性
   */
  static async validateToken(req, res, next) {
    try {
      const { token_value, platform, uid } = req.body;
      
      // 验证必填字段
      if (!token_value) {
        return res.status(400).json({
          success: false,
          message: 'token_value为必填项'
        });
      }
      
      // 构建查询条件
      const filters = { token_value };
      if (platform) filters.platform = platform;
      if (uid) filters.uid = uid;
      
      // 查询token
      const result = await Token.getAll(1, 1, filters);
      
      if (!result.tokens || result.tokens.length === 0) {
        return res.json({
          success: true,
          valid: false,
          message: 'Token无效'
        });
      }
      
      const token = result.tokens[0];
      
      // 检查token状态和过期时间
      let valid = token.status === 'active';
      let message = valid ? 'Token有效' : 'Token已禁用';
      
      if (valid && token.expiry_date && new Date(token.expiry_date) < new Date()) {
        valid = false;
        message = 'Token已过期';
      }
      
      res.json({
        success: true,
        valid,
        message,
        data: valid ? {
          id: token.id,
          platform: token.platform,
          uid: token.uid,
          status: token.status,
          expiry_date: token.expiry_date
        } : null
      });
    } catch (error) {
      logger.error(`验证token合法性失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 导出token
   */
  static async exportTokens(req, res, next) {
    try {
      const { format } = req.params;
      const { uid, status, platform } = req.query;
      
      // 构建查询条件
      const filters = {};
      if (uid) filters.uid = uid;
      if (status) filters.status = status;
      if (platform) filters.platform = platform;
      
      // 获取所有token（不分页）
      const result = await Token.getAll(1, 100000, filters);
      
      // 准备导出数据
      const tokens = result.tokens.map(token => ({
        ID: token.id,
        平台: token.platform,
        UID: token.uid,
        Token值: token.token_value,
        状态: token.status,
        描述: token.description || '',
        过期时间: token.expiry_date ? new Date(token.expiry_date).toLocaleString() : '',
        创建时间: new Date(token.created_at).toLocaleString(),
        创建者: token.created_by
      }));
      
      // 导出文件名
      const fileName = `tokens_export_${new Date().toISOString().slice(0, 10)}`;
      
      if (format === 'csv') {
        const csv = await exportToCSV(tokens);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.csv`);
        return res.send(csv);
      } else if (format === 'excel') {
        const excel = await exportToExcel(tokens, 'Tokens');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        return res.send(excel);
      } else {
        return res.status(400).json({
          success: false,
          message: '不支持的导出格式，请使用csv或excel'
        });
      }
    } catch (error) {
      logger.error(`导出token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 导入token
   */
  static async importTokens(req, res, next) {
    try {
      // 此处应有文件上传处理逻辑
      const { tokens } = req.body;
      
      if (!Array.isArray(tokens) || tokens.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的tokens数组'
        });
      }
      
      // 处理导入数据
      const tokensData = tokens.map(token => ({
        platform: token.platform || token['平台'],
        uid: token.uid || token['UID'],
        token_value: token.token_value || token['Token值'] || generateToken(),
        description: token.description || token['描述'] || null,
        expiry_date: token.expiry_date || token['过期时间'] ? new Date(token.expiry_date || token['过期时间']) : null,
        status: token.status || token['状态'] || 'active',
        created_at: new Date()
      }));
      
      // 验证必填字段
      for (const token of tokensData) {
        if (!token.platform || !token.uid) {
          return res.status(400).json({
            success: false,
            message: '每个token必须包含platform(平台)和uid(UID)字段'
          });
        }
      }
      
      const result = await Token.bulkCreate(tokensData, req.user.id);
      logger.info(`用户 ${req.user.username} (ID: ${req.user.id}) 导入了 ${result.length} 个token`);
      
      res.status(201).json({
        success: true,
        message: `成功导入 ${result.length} 个token`,
        data: {
          tokens: result
        }
      });
    } catch (error) {
      logger.error(`导入token失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取token统计信息
   */
  static async getTokenStats(req, res, next) {
    try {
      const stats = await Token.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取token统计信息失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 优化版的查询在线状态
   * 使用多线程和并发控制提高处理速度
   */
  static async queryOnlineStatusOptimized(req, res, next) {
    try {
      const { token_data } = req.body;
      
      if (!token_data || !Array.isArray(token_data) || token_data.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的token数据'
        });
      }
      
      // 对大量Token进行限制，防止滥用
      const MAX_TOKENS = 5000;
      if (token_data.length > MAX_TOKENS) {
        return res.status(400).json({
          success: false,
          message: `一次最多处理${MAX_TOKENS}个Token`
        });
      }
      
      // 添加认证检查，确保req.user存在
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求批量查询${token_data.length}个Token的在线状态`);
      
      // 使用优化的服务函数进行批量查询
      const results = await tokenStatusService.batchCheckOnlineStatusOptimized(token_data);
      
      // 统计结果
      const onlineCount = results.filter(r => r.status === '在线').length;
      const offlineCount = results.filter(r => r.status === '掉线').length;
      
      logger.info(`完成查询: 总共${results.length}个Token，${onlineCount}个在线，${offlineCount}个掉线`);
      
      return res.json({
        success: true,
        message: '查询在线状态成功',
        data: {
          results,
          stats: {
            total: results.length,
            online: onlineCount,
            offline: offlineCount
          }
        }
      });
    } catch (error) {
      logger.error('查询在线状态错误:', error);
      return res.status(500).json({
        success: false,
        message: '查询在线状态失败: ' + error.message
      });
    }
  }

  /**
   * 批量查询Token用户信息（头像、昵称）
   * 使用并发控制提高处理速度
   */
  static async queryTokenUserInfo(req, res, next) {
    try {
      const { token_data } = req.body;
      
      if (!token_data || !Array.isArray(token_data) || token_data.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的token数据'
        });
      }
      
      // 对大量Token进行限制，防止滥用
      const MAX_TOKENS = 500;
      if (token_data.length > MAX_TOKENS) {
        return res.status(400).json({
          success: false,
          message: `一次最多处理${MAX_TOKENS}个Token`
        });
      }
      
      // 添加认证检查，确保req.user存在
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求批量查询${token_data.length}个Token的用户信息`);
      
      // 使用服务函数进行批量查询
      const results = await tokenStatusService.batchQueryTokenUserInfo(token_data);
      
      // 统计结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;
      
      logger.info(`完成用户信息查询: 总共${results.length}个Token，${successCount}个成功，${failCount}个失败`);
      
      return res.json({
        success: true,
        message: '查询用户信息成功',
        data: {
          results,
          stats: {
            total: results.length,
            success: successCount,
            fail: failCount
          }
        }
      });
    } catch (error) {
      logger.error('查询Token用户信息错误:', error);
      return res.status(500).json({
        success: false,
        message: '查询用户信息失败: ' + error.message
      });
    }
  }

  /**
   * 批量查询Token订单信息
   * 使用并发控制提高处理速度
   */
  static async queryTokenOrderInfo(req, res, next) {
    try {
      const { token_data } = req.body;
      
      if (!token_data || !Array.isArray(token_data) || token_data.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的token数据'
        });
      }
      
      // 对大量Token进行限制，防止滥用
      const MAX_TOKENS = 100; // 订单查询建议使用更小的限制
      if (token_data.length > MAX_TOKENS) {
        return res.status(400).json({
          success: false,
          message: `一次最多处理${MAX_TOKENS}个Token`
        });
      }
      
      // 添加认证检查，确保req.user存在
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求批量查询${token_data.length}个Token的订单信息`);
      
      // 使用服务函数进行批量查询
      const result = await tokenStatusService.batchQueryTokenOrderInfo(token_data);
      
      logger.info(`完成订单信息查询: 总共${result.stats.total}个Token，${result.stats.success}个成功，${result.stats.hasOrder}个有订单`);
      
      return res.json({
        success: true,
        message: '查询订单信息成功',
        data: {
          results: result.results,
          stats: result.stats
        }
      });
    } catch (error) {
      logger.error('查询Token订单信息错误:', error);
      return res.status(500).json({
        success: false,
        message: '查询订单信息失败: ' + error.message
      });
    }
  }

  /**
   * 删除订单
   * 使用拼多多API删除指定订单
   */
  static async deleteOrder(req, res) {
    try {
      const { uid, token, orderSn } = req.body;
      
      if (!uid || !token || !orderSn) {
        return res.status(400).json({
          success: false,
          message: '参数不完整，需要提供uid、token和orderSn'
        });
      }
      
      // 添加认证检查日志
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求删除订单 ${orderSn}`);
      
      // 调用服务函数进行删除
      const result = await tokenOrderService.deleteOrder({ uid, token, orderSn });
      
      if (result.success) {
        logger.info(`成功删除订单 ${orderSn}`);
        return res.json({
          success: true,
          message: '订单删除成功',
          orderSn: orderSn
        });
      } else {
        logger.warn(`删除订单失败 ${orderSn}: ${result.message}`);
        return res.json({
          success: false,
          message: result.message || '删除订单失败',
          orderSn: orderSn,
          errorCode: result.errorCode
        });
      }
    } catch (error) {
      logger.error('删除订单错误:', error);
      return res.status(500).json({
        success: false,
        message: '删除订单失败: ' + error.message
      });
    }
  }
  
  /**
   * 批量删除订单
   * 并发处理多个删除订单请求
   */
  static async batchDeleteOrders(req, res) {
    try {
      const { orderItems } = req.body;
      
      if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
        return res.status(400).json({
          success: false,
          message: '参数不完整或格式错误，需要提供orderItems数组'
        });
      }
      
      // 对大量订单进行限制，防止滥用
      const MAX_ORDERS = 50;
      if (orderItems.length > MAX_ORDERS) {
        return res.status(400).json({
          success: false,
          message: `一次最多处理${MAX_ORDERS}个订单`
        });
      }
      
      // 添加认证检查日志
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求批量删除${orderItems.length}个订单`);
      
      // 使用服务函数进行批量删除
      const result = await tokenOrderService.batchDeleteOrders(orderItems);
      
      const successCount = result.results.filter(r => r.success).length;
      logger.info(`批量删除订单完成: 总共${result.results.length}个订单，成功删除${successCount}个`);
      
      return res.json({
        success: true,
        message: `成功删除 ${successCount}/${result.results.length} 个订单`,
        results: result.results
      });
    } catch (error) {
      logger.error('批量删除订单错误:', error);
      return res.status(500).json({
        success: false,
        message: '批量删除订单失败: ' + error.message,
        results: []
      });
    }
  }

  /**
   * 新Token置入账号内订单
   * 使用外部API将新Token置入账号内订单
   */
  static async placeOrder(req, res) {
    try {
      const { tokens } = req.body;
      
      if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的Token数据'
        });
      }
      
      // 添加认证检查日志
      const username = req.user ? req.user.username : '未认证用户';
      const userId = req.user ? req.user.id : '未知';
      
      logger.info(`用户 ${username} (ID: ${userId}) 请求将${tokens.length}个Token置入订单`);
      
      // 调用服务函数进行处理
      const result = await tokenOrderService.placeOrder(tokens);
      
      // 返回结果
      return res.json(result);
    } catch (error) {
      logger.error('新Token置入订单错误:', error);
      return res.status(500).json({
        success: false,
        message: '新Token置入订单失败: ' + error.message
      });
    }
  }
}

module.exports = TokenController; 