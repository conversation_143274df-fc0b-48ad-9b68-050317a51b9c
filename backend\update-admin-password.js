const mysql = require('mysql2/promise');

async function updateAdminPassword() {
  const pool = await mysql.createPool({
    host: 'localhost',
    user: 'root',
    password: 'qq@666666',
    database: 'token_management'
  });

  try {
    await pool.query(
      'UPDATE users SET password = ? WHERE username = ?',
      ['$2a$10$0YRiRUz1swgxlLZTM.7Wi.nb5ZuK..gbHMSq6acMMSqBV2kCb88/q', 'admin']
    );
    console.log('管理员密码更新成功');
  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await pool.end();
  }
}

updateAdminPassword(); 