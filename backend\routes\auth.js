const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const AuthController = require('../controllers/auth');

// 用户注册
router.post('/register', AuthController.register);

// 用户登录
router.post('/login', AuthController.login);

// 用户登出
router.post('/logout', authenticate, AuthController.logout);

// 刷新令牌
router.post('/refresh-token', AuthController.refreshToken);

// 获取当前用户信息
router.get('/me', authenticate, AuthController.getCurrentUser);

// 修改密码
router.post('/change-password', authenticate, AuthController.changePassword);

// 更新用户个人资料
router.put('/profile', authenticate, AuthController.updateProfile);

// 请求重置密码
router.post('/request-reset', AuthController.requestPasswordReset);

// 重置密码
router.post('/reset-password', AuthController.resetPassword);

// 管理员：获取所有用户
router.get('/users', authenticate, isAdmin, AuthController.getAllUsers);

// 管理员：更新用户角色
router.put('/users/:id/role', authenticate, isAdmin, AuthController.updateUserRole);

// 管理员：禁用用户
router.put('/users/:id/disable', authenticate, isAdmin, AuthController.disableUser);

module.exports = router; 