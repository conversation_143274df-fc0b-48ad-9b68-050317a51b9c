const pool = require('../database/db');
const { logger } = require('../utils/logger');

class Backup {
  // 获取所有备份
  static async getAll(page = 1, limit = 20, filters = {}) {
    try {
      // 确保参数是数字
      page = parseInt(page) || 1;
      limit = parseInt(limit) || 20;
      const offset = (page - 1) * limit;

      // 基础查询
      let query = 'SELECT * FROM backups WHERE 1=1';
      const params = [];

      // 添加过滤条件
      if (filters.backup_type) {
        query += ' AND backup_type = ?';
        params.push(filters.backup_type);
      }

      if (filters.task_id) {
        query += ' AND task_id = ?';
        params.push(filters.task_id);
      }

      if (filters.type) {
        query += ' AND type = ?';
        params.push(filters.type);
      }

      if (filters.status) {
        query += ' AND status = ?';
        params.push(filters.status);
      }

      // 添加分页
      query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      // 确保limit和offset是数字
      params.push(Number(limit), Number(offset));

      console.log('执行查询:', query);
      console.log('查询参数:', params);

      // 执行查询 - 使用query而不是execute方法
      const [backups] = await pool.query(query, params);

      // 获取总数
      let countQuery = 'SELECT COUNT(*) as total FROM backups WHERE 1=1';
      const countParams = [];

      if (filters.backup_type) {
        countQuery += ' AND backup_type = ?';
        countParams.push(filters.backup_type);
      }

      if (filters.task_id) {
        countQuery += ' AND task_id = ?';
        countParams.push(filters.task_id);
      }

      if (filters.type) {
        countQuery += ' AND type = ?';
        countParams.push(filters.type);
      }

      if (filters.status) {
        countQuery += ' AND status = ?';
        countParams.push(filters.status);
      }

      console.log('执行计数查询:', countQuery);
      console.log('计数查询参数:', countParams);

      // 使用query而不是execute方法
      const [countResult] = await pool.query(countQuery, countParams);
      const total = countResult[0].total;

      return {
        backups,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('获取备份列表失败:', error);
      throw error;
    }
  }

  // 获取单个备份
  static async getById(id) {
    try {
      console.log('根据ID获取备份:', id);
      const [rows] = await pool.query(
        'SELECT * FROM backups WHERE id = ?',
        [id]
      );

      return rows[0];
    } catch (error) {
      logger.error(`获取备份(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 获取单个备份根据备份ID
  static async getByBackupId(backupId) {
    try {
      console.log('根据备份ID获取备份:', backupId);
      const [rows] = await pool.query(
        'SELECT * FROM backups WHERE backup_id = ?',
        [backupId]
      );

      return rows[0];
    } catch (error) {
      logger.error(`获取备份(备份ID: ${backupId})失败:`, error);
      throw error;
    }
  }

  // 获取备份的Token列表
  static async getBackupTokens(backupId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      console.log(`获取备份(ID: ${backupId})的Token列表，页码: ${page}, 每页数量: ${limit}`);

      // 直接从backup_tokens_complete表获取数据
      let tokens = [];
      let total = 0;

      try {
        // 查询完整备份表中的数据
        const completeQuery = `
          SELECT
            btc.uid,
            btc.token,
            btc.uid as id,
            btc.status,
            btc.platform,
            btc.username,
            btc.nickname,
            btc.avatar,
            btc.created_time as created_at,
            btc.expiry_time as expiry_date,
            btc.platform as source,
            btc.order_id,
            '' as remark
          FROM backup_tokens_complete btc
          WHERE btc.backup_id = ?
          ORDER BY btc.id
          LIMIT ? OFFSET ?
        `;

        console.log('从完整备份表获取Tokens');
        const [completeTokens] = await pool.query(completeQuery, [backupId, Number(limit), Number(offset)]);
        tokens = completeTokens;

        // 获取总数
        const [countResult] = await pool.query(
          'SELECT COUNT(*) as total FROM backup_tokens_complete WHERE backup_id = ?',
          [backupId]
        );
        total = countResult[0].total;

        console.log(`找到 ${total} 个Token，当前页返回 ${tokens.length} 个`);
      } catch (error) {
        logger.error(`从完整备份表获取Token失败: ${error.message}`);

        // 如果从完整备份表获取失败，尝试从原始表获取（向后兼容）
        try {
          console.log('尝试从原始表获取Token数据');
          const query = `
            SELECT t.*
            FROM tokens t
            JOIN backup_tokens bt ON t.id = bt.token_id
            WHERE bt.backup_id = ?
            ORDER BY t.id
            LIMIT ? OFFSET ?
          `;

          const [normalTokens] = await pool.query(query, [backupId, Number(limit), Number(offset)]);
          tokens = normalTokens;

          // 获取总数
          const [countResult] = await pool.query(
            `SELECT COUNT(*) as total FROM backup_tokens WHERE backup_id = ?`,
            [backupId]
          );

          total = countResult[0].total;
          console.log(`从原始表找到 ${total} 个Token，当前页返回 ${tokens.length} 个`);
        } catch (fallbackError) {
          logger.error(`从原始表获取Token也失败: ${fallbackError.message}`);
          // 如果两种方式都失败，返回空数组
          tokens = [];
          total = 0;
        }
      }

      return {
        tokens,
        is_string_tokens: false, // 不再使用字符串表，始终返回false
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取备份(${backupId})的Token列表失败:`, error);
      throw error;
    }
  }

  // 创建备份
  static async create(backupData, tokenIds, userId) {
    const { Worker } = require('worker_threads');
    const path = require('path');
    const os = require('os');

    try {
      // 开始事务 - 只用于创建备份记录
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        const {
          backup_id,
          name,
          backup_type,
          task_id,
          description,
          status
        } = backupData;

        // 插入备份记录
        console.log('创建备份:', name);
        const [backupResult] = await connection.query(
          `INSERT INTO backups (
            backup_id, name, backup_type, task_id,
            description, status, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            backup_id,
            name,
            backup_type,
            task_id,
            description,
            '备份中', // 初始状态设为"备份中"
            userId
          ]
        );

        // 提交事务 - 备份记录已创建
        await connection.commit();
        connection.release();

        const backupId = backupResult.insertId;

        // 多线程处理配置
        const CPU_COUNT = os.cpus().length;
        console.log(`系统CPU核心数: ${CPU_COUNT}`);

        // 减少Worker线程数量，避免过多并发导致死锁
        // 根据token数量动态调整线程数，但最多不超过3个线程
        const WORKER_COUNT = Math.min(
          Math.max(Math.ceil(tokenIds.length / 2000), 1), // 至少1个线程
          Math.min(CPU_COUNT - 1, 3), // 最多使用3个线程，避免过多并发
          3 // 硬限制最大不超过3个线程
        );

        // 根据token数量动态调整批处理大小
        // 使用较小的批处理大小，减少单次操作的锁竞争
        const BATCH_SIZE = Math.min(
          Math.max(Math.ceil(tokenIds.length / WORKER_COUNT / 10), 200), // 每个线程至少处理10批，每批至少200个
          500 // 最大不超过500个/批，减少单次操作的锁竞争
        );

        console.log(`开始多线程处理 ${tokenIds.length} 个Token，使用 ${WORKER_COUNT} 个线程，每批 ${BATCH_SIZE} 个`);
        console.log(`系统CPU核心数: ${CPU_COUNT}`);

        // 准备token数据 - 使用更高效的方式
        const tokenDataArray = [];
        for (const tokenId of tokenIds) {
          const tokenInfo = backupData.token_info && backupData.token_info[tokenId]
            ? backupData.token_info[tokenId]
            : {};
          tokenDataArray.push({ id: tokenId, info: tokenInfo });
        }

        // 将数据分片 - 尽量均匀分配
        const chunks = [];
        const chunkSize = Math.ceil(tokenDataArray.length / WORKER_COUNT);

        for (let i = 0; i < tokenDataArray.length; i += chunkSize) {
          chunks.push(tokenDataArray.slice(i, i + chunkSize));
        }

        // 获取数据库配置
        const dbConfig = {
          host: process.env.DB_HOST || 'localhost',
          user: process.env.DB_USER || 'root',
          password: process.env.DB_PASSWORD || 'qq@666666',
          database: process.env.DB_NAME || 'token_management'
        };

        // 使用Worker线程并行处理
        console.log(`准备启动 ${chunks.length} 个工作线程`);

        // 创建一个工作线程
        const createWorker = (chunk, index) => {
          return new Promise((resolve, reject) => {
            // 添加错开启动的延迟，减少并发冲突
            const startupDelay = index * 1000; // 每个Worker错开1秒启动

            console.log(`创建工作线程 ${index}，处理 ${chunk.length} 个Token，将在 ${startupDelay}ms 后启动`);

            setTimeout(() => {
              const workerFilePath = path.join(__dirname, '../services/backupWorker.js');

              // 创建Worker线程
              const worker = new Worker(workerFilePath, {
                workerData: {
                  tokenChunk: chunk,
                  backupId,
                  workerId: index,
                  dbConfig,
                  batchSize: BATCH_SIZE
                }
              });

              // 设置超时处理
              let timeoutId = setTimeout(() => {
                console.log(`工作线程 ${index} 处理超时，强制终止`);
                worker.terminate();
                reject(new Error(`工作线程 ${index} 处理超时`));
              }, 600000); // 10分钟超时

              // 处理Worker消息
              worker.on('message', (data) => {
                if (data.type === 'error') {
                  clearTimeout(timeoutId);
                  logger.error(`Worker ${index} 错误: ${data.error}`);
                  reject(new Error(data.error));
                } else if (data.type === 'retry') {
                  // 处理重试消息
                  clearTimeout(timeoutId);
                  // 设置新的超时
                  timeoutId = setTimeout(() => {
                    console.log(`工作线程 ${index} 处理超时，强制终止`);
                    worker.terminate();
                    reject(new Error(`工作线程 ${index} 处理超时`));
                  }, 300000); // 5分钟超时

                  console.log(`Worker ${index} 遇到死锁，正在进行第 ${data.retryCount} 次重试...`);
                  logger.warn(`Worker ${index} 遇到死锁，正在重试: ${data.error}`);
                } else if (data.type === 'progress') {
                  // 更新超时计时器
                  clearTimeout(timeoutId);
                  timeoutId = setTimeout(() => {
                    console.log(`工作线程 ${index} 处理超时，强制终止`);
                    worker.terminate();
                    reject(new Error(`工作线程 ${index} 处理超时`));
                  }, 300000); // 5分钟超时

                  console.log(`Worker ${index} 进度: ${data.processed}/${data.total} (${Math.round(data.processed/data.total*100)}%)`);
                } else if (data.type === 'start') {
                  console.log(`Worker ${index} 开始处理 ${data.tokenCount} 个Token`);
                } else if (data.type === 'complete' || data.type === 'success') {
                  clearTimeout(timeoutId);
                  console.log(`Worker ${index} 完成处理 ${data.processed || chunk.length} 个Token`);
                  resolve(data);
                }
              });

              // 处理Worker错误
              worker.on('error', (err) => {
                clearTimeout(timeoutId);
                logger.error(`Worker ${index} 错误:`, err);
                reject(err);
              });

              // 处理Worker退出
              worker.on('exit', (code) => {
                clearTimeout(timeoutId);
                if (code !== 0) {
                  const errMsg = `Worker ${index} 异常退出，退出码 ${code}`;
                  logger.error(errMsg);
                  reject(new Error(errMsg));
                } else {
                  console.log(`Worker ${index} 正常退出`);
                  // 如果Worker正常退出但没有发送完成消息，也视为成功
                  resolve({ type: 'success', workerId: index });
                }
              });
            }, startupDelay);
          });
        };

        // 创建所有工作线程的Promise
        const workerPromises = chunks.map((chunk, index) => createWorker(chunk, index));

        try {
          // 使用Promise.allSettled而不是Promise.all，这样即使有部分Worker失败，也能获取所有Worker的结果
          console.log(`等待 ${workerPromises.length} 个工作线程完成处理...`);
          const startTime = Date.now();

          const results = await Promise.allSettled(workerPromises);

          const endTime = Date.now();
          const totalTime = (endTime - startTime) / 1000;
          console.log(`所有工作线程处理完成，总耗时: ${totalTime.toFixed(2)}秒`);

          // 检查结果
          const fulfilled = results.filter(r => r.status === 'fulfilled').length;
          const rejected = results.filter(r => r.status === 'rejected').length;

          console.log(`处理结果: ${fulfilled} 个成功, ${rejected} 个失败`);

          // 如果有失败的Worker，记录错误信息
          if (rejected > 0) {
            const errors = results
              .filter(r => r.status === 'rejected')
              .map(r => r.reason.message);

            logger.error(`部分工作线程处理失败: ${errors.join('; ')}`);

            // 如果所有Worker都失败，则整个备份失败
            if (fulfilled === 0) {
              throw new Error(`所有工作线程处理失败: ${errors[0]}`);
            }

            // 如果只有部分Worker失败，记录警告但继续处理
            logger.warn(`部分工作线程处理失败，但备份将继续完成`);
          }

          // 更新备份状态
          const status = rejected > 0 ? '部分完成' : '已完成';

          // 更新备份状态
          const updateConn = await pool.getConnection();
          try {
            await updateConn.query(
              'UPDATE backups SET status = ?, updated_at = NOW() WHERE id = ?',
              [status, backupId]
            );
            updateConn.release();
            console.log(`备份状态已更新为: ${status}`);
          } catch (updateError) {
            updateConn.release();
            logger.error(`更新备份状态失败: ${updateError.message}`);
            // 即使更新状态失败，备份操作本身已经成功
          }

          // 计算处理速度
          const tokensPerSecond = Math.round(tokenIds.length / totalTime);
          console.log(`处理速度: ${tokensPerSecond} 个Token/秒`);

          return {
            id: backupId,
            backup_id,
            token_count: tokenIds.length,
            status,
            processing_time: totalTime,
            tokens_per_second: tokensPerSecond
          };
        } catch (workerError) {
          // Worker处理失败
          logger.error(`Worker处理失败: ${workerError.message}`);

          // 尝试更新备份状态为失败
          try {
            const updateConn = await pool.getConnection();
            await updateConn.query(
              'UPDATE backups SET status = ?, updated_at = NOW() WHERE id = ?',
              ['失败', backupId]
            );
            updateConn.release();
            console.log(`备份状态已更新为: 失败`);
          } catch (updateError) {
            logger.error(`更新备份状态失败: ${updateError.message}`);
          }

          throw workerError;
        }
      } catch (error) {
        // 回滚事务
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      logger.error('创建备份失败:', error);
      throw error;
    }
  }

  // 更新备份
  static async update(id, backupData) {
    try {
      const {
        name,
        description,
        remark,
        status,
        delete_status,
        scheduled_delete_time
      } = backupData;

      console.log('更新备份:', id, backupData);
      const [result] = await pool.query(
        `UPDATE backups SET
          name = ?,
          description = ?,
          remark = ?,
          status = ?,
          delete_status = ?,
          scheduled_delete_time = ?,
          updated_at = NOW()
        WHERE id = ?`,
        [
          name,
          description,
          remark,
          status,
          delete_status !== undefined ? delete_status : false,
          scheduled_delete_time,
          id
        ]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`更新备份(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 删除备份
  static async delete(id) {
    try {
      // 开始事务
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // 删除备份-Token关联记录（完整备份表）
        console.log('删除备份-Token关联（完整备份表）:', id);
        await connection.query(
          'DELETE FROM backup_tokens_complete WHERE backup_id = ?',
          [id]
        );

        // 删除备份-Token关联记录（数字类型，向后兼容）
        console.log('删除备份-Token关联（数字类型）:', id);
        await connection.query(
          'DELETE FROM backup_tokens WHERE backup_id = ?',
          [id]
        );

        // 删除备份记录
        console.log('删除备份:', id);
        const [result] = await connection.query(
          'DELETE FROM backups WHERE id = ?',
          [id]
        );

        // 提交事务
        await connection.commit();
        connection.release();

        return { affected: result.affectedRows };
      } catch (error) {
        // 回滚事务
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      logger.error(`删除备份(ID: ${id})失败:`, error);
      throw error;
    }
  }

  // 获取备份统计信息
  static async getStats() {
    try {
      // 获取总数
      console.log('获取备份统计信息');
      const [totalResult] = await pool.query(
        'SELECT COUNT(*) as total FROM backups'
      );

      // 获取备份类型分布
      const [typeResult] = await pool.query(
        'SELECT backup_type, COUNT(*) as count FROM backups GROUP BY backup_type'
      );

      // 获取自动/手动分布
      const [sourceResult] = await pool.query(
        'SELECT type, COUNT(*) as count FROM backups GROUP BY type'
      );

      // 获取今日备份数量
      const [todayResult] = await pool.query(
        'SELECT COUNT(*) as count FROM backups WHERE DATE(created_at) = CURDATE()'
      );

      return {
        total: totalResult[0].total,
        typeDistribution: typeResult,
        sourceDistribution: sourceResult,
        todayCount: todayResult[0].count
      };
    } catch (error) {
      logger.error('获取备份统计信息失败:', error);
      throw error;
    }
  }

  // 获取关联ID
  static async getRelationIds() {
    try {
      const [rows] = await pool.execute(
        'SELECT task_id, COUNT(*) as count FROM backups WHERE task_id IS NOT NULL GROUP BY task_id'
      );

      return rows;
    } catch (error) {
      logger.error('获取备份关联ID失败:', error);
      throw error;
    }
  }

  // 根据关联ID获取备份
  static async getByTaskId(taskId) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM backups WHERE task_id = ? ORDER BY backup_type',
        [taskId]
      );

      return rows;
    } catch (error) {
      logger.error(`获取关联ID(${taskId})的备份失败:`, error);
      throw error;
    }
  }

  // 更新备份状态
  static async updateStatus(id, status) {
    try {
      const [result] = await pool.execute(
        'UPDATE backups SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, id]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`更新备份(ID: ${id})状态失败:`, error);
      throw error;
    }
  }

  // 标记为待删除
  static async markForDeletion(id, daysToDelete = 2) {
    try {
      const deleteDate = new Date();
      deleteDate.setDate(deleteDate.getDate() + daysToDelete);

      const [result] = await pool.execute(
        `UPDATE backups SET
          delete_status = TRUE,
          scheduled_delete_time = ?,
          updated_at = NOW()
        WHERE id = ?`,
        [deleteDate, id]
      );

      return {
        affected: result.affectedRows,
        scheduled_delete_time: deleteDate
      };
    } catch (error) {
      logger.error(`标记备份(ID: ${id})为待删除失败:`, error);
      throw error;
    }
  }

  // 取消删除标记
  static async cancelDeletion(id) {
    try {
      const [result] = await pool.execute(
        `UPDATE backups SET
          delete_status = FALSE,
          scheduled_delete_time = NULL,
          updated_at = NOW()
        WHERE id = ?`,
        [id]
      );

      return { affected: result.affectedRows };
    } catch (error) {
      logger.error(`取消备份(ID: ${id})的删除标记失败:`, error);
      throw error;
    }
  }

  // 获取所有待删除的备份
  static async getScheduledForDeletion() {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM backups WHERE delete_status = TRUE AND scheduled_delete_time <= NOW()'
      );

      return rows;
    } catch (error) {
      logger.error('获取待删除备份失败:', error);
      throw error;
    }
  }
}

module.exports = Backup;