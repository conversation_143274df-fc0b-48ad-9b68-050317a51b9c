// 直接打开文件选择器
const handleImportToken = () => {
  fileInputRef.value.click();
};

// 文件选择变化处理
const handleFileInputChange = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  selectedFile.value = file;
  processSelectedFile(file);

  // 重置文件输入，以便下次选择同一文件时也能触发事件
  event.target.value = '';
};

// 处理选中的文件
const processSelectedFile = async (file) => {
  const fileName = file.name;
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.txt')) {
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在导入数据...'
    });

    try {
      const parsedData = await parseImportedFile(file);

      if (parsedData.length === 0) {
        ElMessage.warning('文件中没有有效数据');
        loadingInstance.close();
        return;
      }

      const { newTokens, duplicateCount } = processAndDeduplicateTokens(parsedData);

      // 修改：保存当前页码
      const currentPageBeforeUpdate = currentPage.value;

      // 设置不重置页码的标志
      shouldResetPage.value = false;

      tokens.value = newTokens;
      totalTokenCount.value = newTokens.length;

      let successMsg = `成功导入 ${newTokens.length} 个Token`;
      if (duplicateCount > 0) {
        successMsg += `，已自动去除 ${duplicateCount} 个重复Token`;
      }

      ElMessage.success(successMsg);
      loadingInstance.close();

      // 恢复之前的页码，如果页码超出范围，filterTokens会自动调整
      nextTick(() => {
        currentPage.value = currentPageBeforeUpdate;
      });
    } catch (error) {
      console.error('导入失败:', error);
      ElMessage.error('导入失败: ' + (error.message || '无效的数据格式'));
      loadingInstance.close();
    }
  } else {
    ElMessage.error('不支持的文件格式，仅支持Excel(.xlsx, .xls)和文本文件(.txt)');
  }
};

// 公共操作对话框
const showOperationDialog = (title, message, operation) => {
  operationTitle.value = title;
  operationMessage.value = message;
  currentOperation.value = operation;
  operationDialogVisible.value = true;
};

// 优化：处理订单被删除的回调
const handleOrderDeleted = (data) => {
  // 使用现有的updateLocalOrdersAfterDeletion方法更新本地数据
  if (data && data.results) {
    updateLocalOrdersAfterDeletion(data.results);
  }
};

// 清空表格
const handleClearTable = () => {
  ElMessageBox.confirm(
    '确定要清空当前表格中的所有数据吗？此操作不可恢复！',
    '清空表格确认',
    {
      confirmButtonText: '确定清空',
      cancelButtonText: '取消',
      type: 'warning',
      distinguishCancelAndClose: true,
      customClass: 'warning-confirm-dialog'
    }
  )
    .then(() => {
      // 清空数据数组
      tokens.value = [];
      // 重置相关状态
      selectedTokens.value = [];
      totalTokenCount.value = 0;
      filteredTokensCache.value = [];

      // 清空表格是用户明确的操作，所以在此情况下重置页码是合理的
      currentPage.value = 1;

      // 清除表格选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 刷新筛选状态
      filterTokens();

      ElMessage.success('表格数据已全部清空');
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    });
};

// 全选所有数据的功能
const selectAllTokens = () => {
  try {
    // 先显示加载中
    loading.value = true;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 直接将所有筛选结果作为选中项
    selectedTokens.value = JSON.parse(JSON.stringify(filteredTokensCache.value));

    // 设置定时器，确保后续操作在DOM更新后执行
    setTimeout(() => {
      // 强制为当前页的每一行都设置选中状态
      if (tokenTableRef.value) {
        tableData.value.forEach(row => {
          tokenTableRef.value.toggleRowSelection(row, true);
        });
      }

      // 显示成功消息
      ElMessage.success(`已选中全部 ${selectedTokens.value.length} 条数据`);
      loading.value = false;
    }, 100);
  } catch (error) {
    console.error('全选操作错误:', error);
    loading.value = false;
    ElMessage.error('全选操作失败，请重试');
  }
};

// 清除所有选择
const clearAllSelection = () => {
  try {
    // 记录当前页码，确保清除选择后不会改变页码
    const currentPageBeforeClearing = currentPage.value;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 清除选择数据
    selectedTokens.value = [];

    // 不再重置这些值，只清除选择
    // totalTokenCount.value = 0;
    // filteredTokensCache.value = [];

    // 不再重置页码
    // currentPage.value = 1;

    // 确保表格选择状态被清除
    if (tokenTableRef.value) {
      tokenTableRef.value.clearSelection();
    }

    // 设置不重置页码的标志
    shouldResetPage.value = false;

    // 轻量级刷新，只刷新表格选择状态，不重置页码或过滤条件
    nextTick(() => {
      // 恢复原页码
      currentPage.value = currentPageBeforeClearing;
      // 应用表格选择状态
      applyTableSelectionStatus();
    });

    ElMessage.success('已清除所有选择');
  } catch (error) {
    console.error('清除选择错误:', error);
    ElMessage.error('清除选择失败，请重试');
  }
};

// 全选可删除的订单
const selectAllDeletableOrders = () => {
  // 先清除所有选择
  orderTableRef.value?.clearSelection();

  // 然后选中所有可删除的订单
  currentOrders.value.forEach(order => {
    if (isDeletableOrder(order.status)) {
      orderTableRef.value?.toggleRowSelection(order, true);
    }
  });
};

// 判断订单是否可删除（只允许已评价的订单）
const isDeletableOrder = (status) => {
  if (!status) return false;

  // 严格匹配"已评价"状态
  // 只有完全匹配"已评价"的订单才能删除，根据拼多多API的要求
  if (status === '已评价') {
    return true;
  }

  // 包含"已评价"的状态也可能可以删除
  if (status.includes('已评价')) {
    return true;
  }

  // 其他状态不可删除
  return false;
};

// 获取可删除的订单数量
const getDeletableCount = (row) => {
  if (!row.orderInfo || !row.orderInfo.orders) return 0;
  return row.orderInfo.orders.filter(order => isDeletableOrder(order.status)).length;
};

// 修复页面大小变化处理
const handleSizeChange = (size) => {
  // 记住之前的选择
  const previousSelection = [...selectedTokens.value];

  // 更新页大小
  pageSize.value = size;

  // 延时处理，避免DOM更新冲突
  setTimeout(() => {
    try {
      // 清除表格UI上的选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 如果当前页码超出范围则重置
      const totalPages = Math.ceil(filteredTokensCache.value.length / size) || 1;
      if (currentPage.value > totalPages) {
        currentPage.value = 1;
      }

      // 重新设置选中状态
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 为当前页的每行检查是否在选中列表中
          tableData.value.forEach(row => {
            // 查找当前行是否在选中列表中
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 手动设置选中状态
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('页面大小变化错误:', error);
    }
  }, 50);
};

// 处理单个订单删除
const handleDeleteSingleOrder = async (orderDetail) => {
  if (!orderDetail || !orderDetail.uid || !orderDetail.tokenValue || !orderDetail.orderId) {
    ElMessage.warning('订单信息不完整，无法删除');
    return;
  }

  // 确认是否可删除
  if (!isDeletableOrder(orderDetail.status)) {
    ElMessage.warning(`只能删除已评价的订单，当前订单状态为"${orderDetail.status}"`);
    return;
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除订单 ${orderDetail.orderId} 吗？此操作不可恢复！`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 调用API删除订单
      const result = await tokenService.deleteOrder(
        orderDetail.uid,
        orderDetail.tokenValue,
        orderDetail.orderId
      );

      if (result.success) {
        // 删除成功，关闭订单详情对话框
        orderDetailDialogVisible.value = false;

        // 更新本地数据
        updateLocalOrdersAfterDeletion([{
          success: true,
          orderSn: orderDetail.orderId
        }]);

        ElMessage.success('订单删除成功');

        // 如果主订单列表对话框也打开，更新它
        if (ordersDialogVisible.value && currentOrders.value.length > 0) {
          // 从当前列表中过滤掉已删除的订单
          currentOrders.value = currentOrders.value.filter(
            order => order.orderId !== orderDetail.orderId
          );
        }
      } else {
        // 增强错误信息显示
        const errorMsg = result.message || '删除失败';
        ElMessage.error(errorMsg);

        // 构建更详细的错误提示
        let detailsHtml = `
          <div style="text-align: left; margin: 15px 0;">
            <h3 style="margin-bottom: 10px;">删除失败详情</h3>
            <div style="color: #f56c6c;">
              <p>${errorMsg}${result.errorCode ? ` (错误码: ${result.errorCode})` : ''}</p>
            </div>

            <div style="margin-top: 15px; font-size: 14px;">
              <p>可能的原因：</p>
              <ul style="padding-left: 20px; margin-top: 5px;">
                <li>订单状态不是"已评价"(拼多多要求必须是严格的"已评价")</li>
                <li>Token已失效或没有足够权限</li>
                <li>网络问题或API限流</li>
              </ul>
            </div>
        `;

        if (result.responseData) {
          detailsHtml += `
            <div style="margin-top: 15px;">
              <details>
                <summary>API响应详情</summary>
                <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(result.responseData, null, 2)}</pre>
              </details>
            </div>
          `;
        }

        detailsHtml += `</div>`;

        // 显示详细错误
        ElMessageBox.alert(detailsHtml, '删除失败', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        });
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 批量删除选中的订单
const batchDeleteSelectedOrders = async () => {
  if (selectedOrdersInList.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单');
    return;
  }

  try {
    // 确认是否删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedOrdersInList.value.length} 个订单吗？`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 构建删除请求数据
      const orderItems = selectedOrdersInList.value.map(order => ({
        uid: order.uid,
        token: order.tokenValue,
        orderSn: order.orderId
      }));

      // 调用批量删除API
      const result = await tokenService.batchDeleteOrders(orderItems);

      if (result.success) {
        // 统计结果
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 更新本地数据，从订单列表中移除已删除的订单
        updateLocalOrdersAfterDeletion(result.results);

        // 清除表格选择
        selectedOrdersInList.value = [];
        orderTableRef.value?.clearSelection();

        // 使用通知显示详细结果
        ElNotification({
          title: '删除订单完成',
          message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
          type: 'success',
          duration: 5000
        });

        // 强制刷新表格数据
        refreshTokens();
      } else {
        ElMessage.error(`批量删除失败: ${result.message || '未知错误'}`);
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 一键删除订单处理函数
const handleQuickDeleteOrder = async (token) => {
  // 防止重复点击
  if (token.orderDeleteLoading) return;

  // 设置loading状态
  token.orderDeleteLoading = true;

  try {
    // 询问用户是否确认删除
    await ElMessageBox.confirm(
      `确定要删除此Token的订单吗？将会自动查询并使用并发模式删除所有可删除的订单。`,
      '删除订单确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示加载中状态
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在处理订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 使用并发查询和删除
      const result = await orderService.concurrentQueryAndDeleteOrders(
        [token],
        {
          batchSize: 20, // 每批20个订单
          concurrency: 5  // 5个并发请求
        }
      );

      if (result.success) {
        // 检查是否有成功删除的订单
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 更新本地数据
        updateLocalOrdersAfterDeletion(result.results);

        // 处理查询结果，更新UI显示
        if (result.queriedTokens && result.queriedTokens.length > 0) {
          // 更新已查询但没有可删除订单的Token状态
          updateTokensAfterQuery(result.queriedTokens);
        }

        // 使用通知显示结果
        if (successCount > 0) {
          ElNotification({
            title: '删除订单完成',
            message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
            type: 'success',
            duration: 5000
          });
        } else if (result.results.length > 0) {
          // 有订单但全部删除失败
          ElNotification({
            title: '删除订单未成功',
            message: `未能成功删除任何订单，${failCount} 个删除失败`,
            type: 'warning',
            duration: 5000
          });
        } else {
          // 无可删除订单
          ElNotification({
            title: '无可删除订单',
            message: '查询完成，但没有找到可删除的订单（只有已评价的订单可以删除）',
            type: 'info',
            duration: 5000
          });
        }

        // 强制刷新表格数据
        refreshTokens();
      } else {
        // API调用失败
        ElMessage.error(result.message || '操作失败');
      }
    } finally {
      loadingInstance.close();
      token.orderDeleteLoading = false;
    }
  } catch (error) {
    token.orderDeleteLoading = false;
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};