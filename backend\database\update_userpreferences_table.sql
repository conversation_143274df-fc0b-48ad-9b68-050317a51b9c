-- 更新user_preferences表添加system_name字段
-- 执行此SQL语句以修复"Unknown column 'system_name' in 'field list"错误

USE token_management;

-- 检查表中是否已存在system_name字段，如果不存在则添加
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'token_management' 
AND TABLE_NAME = 'user_preferences' 
AND COLUMN_NAME = 'system_name';

-- 如果字段不存在，添加它
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE user_preferences ADD COLUMN system_name VARCHAR(100) DEFAULT \'Token 管理系统\' AFTER items_per_page',
    'SELECT \'系统名称字段已存在，无需修改\' AS Message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新完成后的验证查询
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    COLUMN_DEFAULT 
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'token_management' 
    AND TABLE_NAME = 'user_preferences'
ORDER BY 
    ORDINAL_POSITION; 