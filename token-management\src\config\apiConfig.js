/**
 * API URL配置文件
 * 集中管理所有外部API URL，方便统一修改
 */

// 基础API URL
const BASE_API_URL = 'http://110.42.32.166:3080';

// WordPress REST API前缀
const WP_API_PREFIX = '/wp-json';

// 完整的WordPress API基础URL
const WP_API_BASE_URL = `${BASE_API_URL}${WP_API_PREFIX}`;

// 各种具体API端点
const API_ENDPOINTS = {
  // Token相关
  TOKEN: {
    // 批量更新数据API（Token置入账号内订单）
    BULK_UPDATE: `${WP_API_BASE_URL}/custom-jwt-plugin/v1/bulk-update-data`,
    
    // 加载未卖Token
    GET_UNSOLD: `${WP_API_BASE_URL}/myplugin/v1/get-delete-tokens`,
    
    // 加载已卖Token
    GET_SOLD: `${WP_API_BASE_URL}/myplugin/v1/get-time-tokens/`,
    
    // 导入Token
    IMPORT: `${WP_API_BASE_URL}/custom-token-plugin/v1/import`,
    
    // 清空未卖Token
    TRUNCATE_UNSOLD: `${WP_API_BASE_URL}/custom/v1/truncate-token`,
  },
};

// 凭证信息（注意：实际项目中应避免将凭证硬编码在前端代码中）
// 这里仅作为示例，建议使用环境变量或后端鉴权机制
const API_CREDENTIALS = {
  USERNAME: 'a15604402',
  PASSWORD: 'lijinrong11',
};

export {
  BASE_API_URL,
  WP_API_BASE_URL,
  API_ENDPOINTS,
  API_CREDENTIALS,
};

// 默认导出完整配置对象
export default {
  baseUrl: BASE_API_URL,
  wpApiBaseUrl: WP_API_BASE_URL,
  endpoints: API_ENDPOINTS,
  credentials: API_CREDENTIALS,
}; 