const db = require('../database/db');
const { logger } = require('../utils/logger');

class Reset {
  /**
   * 获取所有重置记录，支持分页和筛选
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {object} filters - 筛选条件
   * @returns {Promise<{resets: Array, total: number}>} - 重置记录列表和总数
   */
  static async getAll(page = 1, limit = 10, filters = {}) {
    try {
      let query = `
        SELECT r.*, u.username as reset_by_username, t.platform, t.uid
        FROM resets r
        LEFT JOIN users u ON r.reset_by = u.id
        LEFT JOIN tokens t ON r.token_id = t.id
        WHERE 1=1
      `;
      const params = [];

      // 应用筛选条件
      if (filters.token_id) {
        query += ' AND r.token_id = ?';
        params.push(filters.token_id);
      }

      if (filters.platform) {
        query += ' AND t.platform = ?';
        params.push(filters.platform);
      }

      if (filters.status) {
        query += ' AND r.status = ?';
        params.push(filters.status);
      }

      if (filters.date_from) {
        query += ' AND r.reset_at >= ?';
        params.push(filters.date_from);
      }

      if (filters.date_to) {
        query += ' AND r.reset_at <= ?';
        params.push(filters.date_to);
      }

      // 获取总记录数
      const countQuery = query.replace('r.*, u.username as reset_by_username, t.platform, t.uid', 'COUNT(*) as total');
      const [countResult] = await db.query(countQuery, params);
      const total = countResult[0].total;

      // 应用分页
      query += ' ORDER BY r.reset_at DESC LIMIT ? OFFSET ?';
      params.push(limit, (page - 1) * limit);

      // 执行查询
      const [rows] = await db.query(query, params);

      return {
        resets: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取重置记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据ID获取单个重置记录
   * @param {number|string} id - 重置记录ID
   * @returns {Promise<object|null>} - 重置记录对象或null
   */
  static async getById(id) {
    try {
      const query = `
        SELECT r.*, u.username as reset_by_username, t.platform, t.uid
        FROM resets r
        LEFT JOIN users u ON r.reset_by = u.id
        LEFT JOIN tokens t ON r.token_id = t.id
        WHERE r.id = ?
      `;
      
      const [rows] = await db.query(query, [id]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      logger.error(`获取重置记录(ID: ${id})失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取与特定token关联的重置历史
   * @param {number|string} tokenId - Token ID
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<{resets: Array, total: number}>} - 重置记录列表和总数
   */
  static async getByTokenId(tokenId, page = 1, limit = 10) {
    try {
      // 获取总记录数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM resets
        WHERE token_id = ?
      `;
      
      const [countResult] = await db.query(countQuery, [tokenId]);
      const total = countResult[0].total;
      
      // 获取重置记录
      const query = `
        SELECT r.*, u.username as reset_by_username, t.platform, t.uid
        FROM resets r
        LEFT JOIN users u ON r.reset_by = u.id
        LEFT JOIN tokens t ON r.token_id = t.id
        WHERE r.token_id = ?
        ORDER BY r.reset_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const [rows] = await db.query(query, [tokenId, limit, (page - 1) * limit]);
      
      return {
        resets: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取token(ID: ${tokenId})的重置历史失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建新的重置记录
   * @param {object} resetData - 重置记录数据
   * @returns {Promise<number>} - 新创建的重置记录ID
   */
  static async create(resetData) {
    try {
      const { token_id, old_value, new_value, reason, reset_by, reset_at = new Date() } = resetData;
      
      const query = `
        INSERT INTO resets (token_id, old_value, new_value, reason, reset_by, reset_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      const [result] = await db.query(query, [token_id, old_value, new_value, reason, reset_by, reset_at]);
      
      return result.insertId;
    } catch (error) {
      logger.error(`创建重置记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量创建重置记录
   * @param {Array} resetsData - 重置记录数据数组
   * @returns {Promise<Array>} - 新创建的重置记录ID数组
   */
  static async bulkCreate(resetsData) {
    try {
      if (!Array.isArray(resetsData) || resetsData.length === 0) {
        throw new Error('无效的重置记录数据');
      }
      
      // 开始事务
      await db.query('START TRANSACTION');
      
      const createdResets = [];
      
      for (const resetData of resetsData) {
        const { token_id, old_value, new_value, reason, reset_by, reset_at = new Date() } = resetData;
        
        const query = `
          INSERT INTO resets (token_id, old_value, new_value, reason, reset_by, reset_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        const [result] = await db.query(query, [token_id, old_value, new_value, reason, reset_by, reset_at]);
        
        createdResets.push({
          id: result.insertId,
          ...resetData
        });
      }
      
      // 提交事务
      await db.query('COMMIT');
      
      return createdResets;
    } catch (error) {
      // 回滚事务
      await db.query('ROLLBACK');
      logger.error(`批量创建重置记录失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取重置记录统计信息
   * @returns {Promise<object>} - 统计信息
   */
  static async getStats() {
    try {
      // 总重置次数
      const [totalCountResult] = await db.query('SELECT COUNT(*) as total FROM resets');
      
      // 各平台重置分布
      const [platformStatsResult] = await db.query(`
        SELECT t.platform, COUNT(*) as count
        FROM resets r
        JOIN tokens t ON r.token_id = t.id
        GROUP BY t.platform
      `);
      
      // 最近7天每日重置数量
      const [dailyResetCounts] = await db.query(`
        SELECT DATE(reset_at) as date, COUNT(*) as count
        FROM resets
        WHERE reset_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(reset_at)
        ORDER BY date ASC
      `);
      
      return {
        total: totalCountResult[0].total,
        by_platform: platformStatsResult,
        daily_trend: dailyResetCounts
      };
    } catch (error) {
      logger.error(`获取重置统计信息失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = Reset; 