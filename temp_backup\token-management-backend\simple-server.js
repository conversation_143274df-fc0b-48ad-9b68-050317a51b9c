const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// 创建Express应用
const app = express();
const PORT = 3001;

// 配置中间件
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 简单的请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// 获取随机昵称
app.get('/api/nickname/random', (req, res) => {
  try {
    // 网名文件路径
    const nicknamePath = path.resolve(process.cwd(), '..', '网名.txt');
    console.log('网名文件路径:', nicknamePath);
    
    // 读取文件内容
    fs.readFile(nicknamePath, 'utf8', (err, data) => {
      if (err) {
        console.error('读取网名文件失败:', err);
        return res.status(500).json({
          success: false,
          message: '读取网名文件失败'
        });
      }
      
      // 按行分割
      const nicknames = data.split('\n').filter(line => line.trim() !== '');
      
      if (nicknames.length === 0) {
        return res.status(404).json({
          success: false,
          message: '网名文件中没有有效的昵称'
        });
      }
      
      // 随机选择一个昵称
      const randomIndex = Math.floor(Math.random() * nicknames.length);
      const randomNickname = nicknames[randomIndex].trim();
      
      // 返回随机昵称
      res.json({
        success: true,
        nickname: randomNickname
      });
    });
  } catch (error) {
    console.error('获取随机昵称失败:', error);
    res.status(500).json({
      success: false,
      message: `获取随机昵称失败: ${error.message}`
    });
  }
});

// 修改昵称
app.post('/api/nickname/change', async (req, res) => {
  try {
    const { token, nickname } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: '缺少token参数'
      });
    }
    
    if (!nickname) {
      return res.status(400).json({
        success: false,
        message: '缺少nickname参数'
      });
    }
    
    // 调用外部API修改昵称
    const response = await axios.post('https://apiv3.yangkeduo.com/user/profile/update/nickname',
      {
        "nickname": nickname
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://apiv3.yangkeduo.com/user/profile/update/nickname?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 5000
      }
    );
    
    // 返回修改结果
    res.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('修改昵称失败:', error);
    
    // 返回错误信息
    res.status(500).json({
      success: false,
      message: `修改昵称失败: ${error.message}`,
      error: error.response ? error.response.data : null
    });
  }
});

// 基础API状态路由
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: '简易服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 默认路由
app.get('/', (req, res) => {
  res.send({
    message: '简易Token管理系统API服务正常运行',
    status: 'ok',
    time: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `路径 ${req.url} 不存在`
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: err.message
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`简易服务器运行在端口 ${PORT}`);
});
