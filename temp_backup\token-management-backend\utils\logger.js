/**
 * 简单的日志模块
 */

// 日志级别
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// 当前日志级别 (从环境变量获取或默认为INFO)
const currentLevel = process.env.LOG_LEVEL 
  ? (LOG_LEVELS[process.env.LOG_LEVEL.toUpperCase()] || LOG_LEVELS.INFO)
  : LOG_LEVELS.INFO;

/**
 * 格式化日期时间
 * @returns {string} 格式化的日期时间
 */
function getFormattedDateTime() {
  const now = new Date();
  return now.toISOString();
}

/**
 * 创建日志条目
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 */
function log(level, message) {
  const logLevel = LOG_LEVELS[level.toUpperCase()];
  
  if (logLevel <= currentLevel) {
    const timestamp = getFormattedDateTime();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }
}

// 日志接口
const logger = {
  error: (message) => log('ERROR', message),
  warn: (message) => log('WARN', message),
  info: (message) => log('INFO', message),
  debug: (message) => log('DEBUG', message)
};

module.exports = logger; 