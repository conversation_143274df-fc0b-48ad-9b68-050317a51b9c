const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const BackupController = require('../controllers/backup');

// 获取所有备份（带分页和筛选）
router.get('/', BackupController.getAllBackups);

// 获取单个备份详情
router.get('/:id', BackupController.getBackupById);

// 获取备份中的token列表
router.get('/:id/tokens', BackupController.getBackupTokens);

// 创建新备份
router.post('/', authenticate, BackupController.createBackup);

// 测试用，创建备份（无需身份验证）
router.post('/test/create', BackupController.createBackup);

// 更新备份信息
router.put('/:id', authenticate, BackupController.updateBackup);

// 删除备份
router.delete('/:id', BackupController.deleteBackup);

// 标记备份为待删除（指定天数后删除）
router.post('/:id/mark-for-deletion', authenticate, BackupController.markBackupForDeletion);

// 取消备份的删除标记
router.post('/:id/cancel-deletion', authenticate, BackupController.cancelBackupDeletion);

// 更新备份状态
router.put('/:id/status', authenticate, BackupController.updateBackupStatus);

// 获取备份统计信息
router.get('/stats/overview', authenticate, BackupController.getBackupStats);

// 导出备份记录（CSV/Excel）
router.get('/export/:format', authenticate, BackupController.exportBackups);

module.exports = router;