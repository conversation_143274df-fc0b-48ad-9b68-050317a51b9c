import{_ as ne,K as ie,r as b,w as re,c as ue,L as de,o as fe,E as u,a as O,b as U,e as a,f as l,g as d,A as me,k as pe,p as q,h as s,F as ce,d as P,i as C,M as ve,N as ge,O as A,t as k,q as B}from"./index-CckIkgn1.js";const _e={class:"settings-container"},ye={class:"data-management"},be={class:"action-group"},ke={class:"action-group"},we={__name:"Settings",setup(Ve){const v=ie(),J=me(),S=b("basic");re(()=>J.query.tab,t=>{t&&["basic","user","notification","data"].includes(t)&&(S.value=t)},{immediate:!0});const w=b(null),R={email:[{type:"email",message:"请输入有效的邮箱地址",trigger:"blur"}],currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{min:6,message:"密码长度至少为6个字符",trigger:"blur"}],confirmPassword:[{validator:(t,e,p)=>{e&&e!==i.value.newPassword?p(new Error("两次输入的密码不一致")):p()},trigger:"blur"}]},z=ue(()=>v.isAdmin),n=de({basic:!1,user:!1,notification:!1,export:!1,import:!1,clearExpired:!1,clearBackups:!1,clearAll:!1}),m=b({systemName:"Token 管理系统",theme:"default",language:"zh-CN",pageSize:20}),i=b({username:"",email:"",currentPassword:"",newPassword:"",confirmPassword:""}),f=b({emailEnabled:!0,expiryNotification:[30,15,7,1],backupNotification:!0,systemEvents:["token_reset","backup_created"]}),_=b({tokenCount:0,backupCount:0,expiredTokenCount:0,firstTokenTime:"-",lastTokenTime:"-",databaseSize:"-"});fe(async()=>{try{if(await v.testApiConnection())u.success("后端服务连接成功");else{u.error("后端服务连接失败，请确保后端服务已启动");return}}catch(t){console.error("API连接测试失败:",t),u.error("后端服务连接失败，请确保后端服务已启动");return}await D(),await H(),z.value&&await K()});const D=async()=>{const t=await v.fetchCurrentUser();t&&(i.value.username=t.username,i.value.email=t.email||"")},H=async()=>{const t=await v.fetchUserPreferences();if(t&&(m.value={systemName:t.system_name||"Token 管理系统",theme:t.theme||"default",language:t.language||"zh-CN",pageSize:t.items_per_page||20},t.notification_settings)){let e=[];t.notification_settings.expiry_days?e=t.notification_settings.expiry_days.map(p=>typeof p=="string"?parseInt(p,10):p):e=[7,1],f.value={emailEnabled:t.notification_settings.email_notifications||!1,expiryNotification:e,backupNotification:t.notification_settings.backup_notifications||!1,systemEvents:t.notification_settings.event_types||[]}}},K=async()=>{try{_.value={tokenCount:120,backupCount:5,expiredTokenCount:2,firstTokenTime:"2024-01-15 10:30:00",lastTokenTime:"2024-03-29 15:45:00",databaseSize:"2.5 MB"}}catch{u.error("获取数据统计失败")}},L=async()=>{try{n.basic=!0;const t={theme:m.value.theme,language:m.value.language,items_per_page:m.value.pageSize,system_name:m.value.systemName};await v.updateUserPreferences(t)&&u.success("基本设置已保存")}catch{u.error("保存基本设置失败")}finally{n.basic=!1}},j=()=>{m.value={systemName:"Token 管理系统",theme:"default",language:"zh-CN",pageSize:20}},G=async()=>{if(w.value)try{if(await w.value.validate(),n.user=!0,i.value.email){const t={email:i.value.email};await v.updateProfile(t)}if(i.value.currentPassword&&i.value.newPassword){const t={currentPassword:i.value.currentPassword,newPassword:i.value.newPassword};await v.changePassword(t)&&(i.value.currentPassword="",i.value.newPassword="",i.value.confirmPassword="")}}catch(t){t.message&&u.error(t.message)}finally{n.user=!1}},Q=()=>{w.value&&w.value.resetFields(),D()},W=async()=>{try{n.notification=!0;const t=f.value.expiryNotification.map(r=>typeof r=="string"?parseInt(r,10):r),e={notification_settings:{email_notifications:f.value.emailEnabled,expiry_days:t,backup_notifications:f.value.backupNotification,event_types:f.value.systemEvents}};console.log("准备发送的通知设置:",JSON.parse(JSON.stringify(e))),await v.updateUserPreferences(e)&&u.success("通知设置已保存")}catch(t){console.error("保存通知设置失败:",t),u.error("保存通知设置失败: "+(t.message||"未知错误"))}finally{n.notification=!1}},X=()=>{f.value={emailEnabled:!0,expiryNotification:[30,15,7,1],backupNotification:!0,systemEvents:["token_reset","backup_created"]}},Y=async()=>{try{n.export=!0,u.success("数据导出功能尚未实现")}finally{n.export=!1}},Z=async()=>{try{n.import=!0,u.success("数据导入功能尚未实现")}finally{n.import=!1}},$=async()=>{try{await B.confirm("确定要清理所有过期Token吗？此操作不可恢复！","确认操作",{type:"warning"})&&(n.clearExpired=!0,u.success("清理过期Token功能尚未实现"))}catch{}finally{n.clearExpired=!1}},h=async()=>{try{await B.confirm("确定要清理旧备份吗？此操作不可恢复！","确认操作",{type:"warning"})&&(n.clearBackups=!0,u.success("清理旧备份功能尚未实现"))}catch{}finally{n.clearBackups=!1}},ee=async()=>{try{await B.confirm("确定要清理所有数据吗？此操作不可恢复！","确认操作",{type:"warning",confirmButtonText:"确定清理",confirmButtonClass:"el-button--danger"})&&(n.clearAll=!0,u.success("清理所有数据功能尚未实现"))}catch{}finally{n.clearAll=!1}},ae=async()=>{try{await v.testApiConnection()?u.success("后端服务连接成功"):u.error("后端服务连接失败，请确保后端服务已启动")}catch(t){console.error("API连接测试失败:",t),u.error("后端服务连接失败，请确保后端服务已启动")}};return(t,e)=>{const p=d("el-input"),r=d("el-form-item"),V=d("el-option"),F=d("el-select"),le=d("el-input-number"),c=d("el-button"),E=d("el-form"),N=d("el-tab-pane"),T=d("el-divider"),I=d("el-switch"),g=d("el-checkbox"),M=d("el-checkbox-group"),x=d("el-icon"),y=d("el-descriptions-item"),te=d("el-descriptions"),se=d("el-tabs"),oe=d("el-card");return U(),O("div",_e,[a(oe,{class:"settings-card"},{header:l(()=>e[14]||(e[14]=[P("div",{class:"card-header"},[P("span",null,"系统设置")],-1)])),default:l(()=>[a(se,{modelValue:S.value,"onUpdate:modelValue":e[13]||(e[13]=o=>S.value=o)},{default:l(()=>[a(N,{label:"基本设置",name:"basic"},{default:l(()=>[a(E,{"label-position":"top",model:m.value,class:"settings-form"},{default:l(()=>[a(r,{label:"系统名称"},{default:l(()=>[a(p,{modelValue:m.value.systemName,"onUpdate:modelValue":e[0]||(e[0]=o=>m.value.systemName=o)},null,8,["modelValue"])]),_:1}),a(r,{label:"主题"},{default:l(()=>[a(F,{modelValue:m.value.theme,"onUpdate:modelValue":e[1]||(e[1]=o=>m.value.theme=o),class:"full-width"},{default:l(()=>[a(V,{label:"默认主题",value:"default"}),a(V,{label:"暗色主题",value:"dark"}),a(V,{label:"浅色主题",value:"light"})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"语言"},{default:l(()=>[a(F,{modelValue:m.value.language,"onUpdate:modelValue":e[2]||(e[2]=o=>m.value.language=o),class:"full-width"},{default:l(()=>[a(V,{label:"简体中文",value:"zh-CN"}),a(V,{label:"English",value:"en-US"})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"每页显示记录数"},{default:l(()=>[a(le,{modelValue:m.value.pageSize,"onUpdate:modelValue":e[3]||(e[3]=o=>m.value.pageSize=o),min:10,max:100,step:10},null,8,["modelValue"])]),_:1}),a(r,null,{default:l(()=>[a(c,{type:"primary",onClick:L,loading:n.basic},{default:l(()=>e[15]||(e[15]=[s("保存设置")])),_:1},8,["loading"]),a(c,{onClick:j},{default:l(()=>e[16]||(e[16]=[s("重置")])),_:1}),a(c,{type:"info",onClick:ae},{default:l(()=>e[17]||(e[17]=[s("测试API连接")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(N,{label:"用户设置",name:"user"},{default:l(()=>[a(E,{"label-position":"top",model:i.value,rules:R,ref_key:"userForm",ref:w,class:"settings-form"},{default:l(()=>[a(r,{label:"用户名"},{default:l(()=>[a(p,{modelValue:i.value.username,"onUpdate:modelValue":e[4]||(e[4]=o=>i.value.username=o),disabled:""},null,8,["modelValue"])]),_:1}),a(r,{label:"邮箱",prop:"email"},{default:l(()=>[a(p,{modelValue:i.value.email,"onUpdate:modelValue":e[5]||(e[5]=o=>i.value.email=o)},null,8,["modelValue"])]),_:1}),a(T,null,{default:l(()=>e[18]||(e[18]=[s("修改密码")])),_:1}),a(r,{label:"当前密码",prop:"currentPassword"},{default:l(()=>[a(p,{modelValue:i.value.currentPassword,"onUpdate:modelValue":e[6]||(e[6]=o=>i.value.currentPassword=o),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(r,{label:"新密码",prop:"newPassword"},{default:l(()=>[a(p,{modelValue:i.value.newPassword,"onUpdate:modelValue":e[7]||(e[7]=o=>i.value.newPassword=o),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(r,{label:"确认新密码",prop:"confirmPassword"},{default:l(()=>[a(p,{modelValue:i.value.confirmPassword,"onUpdate:modelValue":e[8]||(e[8]=o=>i.value.confirmPassword=o),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),a(r,null,{default:l(()=>[a(c,{type:"primary",onClick:G,loading:n.user},{default:l(()=>e[19]||(e[19]=[s("保存设置")])),_:1},8,["loading"]),a(c,{onClick:Q},{default:l(()=>e[20]||(e[20]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(N,{label:"通知设置",name:"notification"},{default:l(()=>[a(E,{"label-position":"top",model:f.value,class:"settings-form"},{default:l(()=>[a(r,{label:"邮件通知"},{default:l(()=>[a(I,{modelValue:f.value.emailEnabled,"onUpdate:modelValue":e[9]||(e[9]=o=>f.value.emailEnabled=o)},null,8,["modelValue"])]),_:1}),f.value.emailEnabled?(U(),O(ce,{key:0},[a(r,{label:"Token到期提醒"},{default:l(()=>[a(M,{modelValue:f.value.expiryNotification,"onUpdate:modelValue":e[10]||(e[10]=o=>f.value.expiryNotification=o)},{default:l(()=>[a(g,{value:30},{default:l(()=>e[21]||(e[21]=[s("提前30天")])),_:1}),a(g,{value:15},{default:l(()=>e[22]||(e[22]=[s("提前15天")])),_:1}),a(g,{value:7},{default:l(()=>e[23]||(e[23]=[s("提前7天")])),_:1}),a(g,{value:1},{default:l(()=>e[24]||(e[24]=[s("提前1天")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"备份提醒"},{default:l(()=>[a(I,{modelValue:f.value.backupNotification,"onUpdate:modelValue":e[11]||(e[11]=o=>f.value.backupNotification=o)},null,8,["modelValue"])]),_:1}),a(r,{label:"系统事件通知"},{default:l(()=>[a(M,{modelValue:f.value.systemEvents,"onUpdate:modelValue":e[12]||(e[12]=o=>f.value.systemEvents=o)},{default:l(()=>[a(g,{value:"token_import"},{default:l(()=>e[25]||(e[25]=[s("Token导入")])),_:1}),a(g,{value:"token_reset"},{default:l(()=>e[26]||(e[26]=[s("Token重置")])),_:1}),a(g,{value:"backup_created"},{default:l(()=>e[27]||(e[27]=[s("备份创建")])),_:1}),a(g,{value:"backup_restored"},{default:l(()=>e[28]||(e[28]=[s("备份恢复")])),_:1})]),_:1},8,["modelValue"])]),_:1})],64)):q("",!0),a(r,null,{default:l(()=>[a(c,{type:"primary",onClick:W,loading:n.notification},{default:l(()=>e[29]||(e[29]=[s("保存设置")])),_:1},8,["loading"]),a(c,{onClick:X},{default:l(()=>e[30]||(e[30]=[s("重置")])),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),z.value?(U(),pe(N,{key:0,label:"数据管理",name:"data"},{default:l(()=>[P("div",ye,[a(T,null,{default:l(()=>e[31]||(e[31]=[s("数据导入导出")])),_:1}),P("div",be,[a(c,{type:"primary",onClick:Y,loading:n.export},{default:l(()=>[a(x,null,{default:l(()=>[a(C(ve))]),_:1}),e[32]||(e[32]=s("导出所有数据 "))]),_:1},8,["loading"]),a(c,{type:"success",onClick:Z,loading:n.import},{default:l(()=>[a(x,null,{default:l(()=>[a(C(ge))]),_:1}),e[33]||(e[33]=s("导入数据 "))]),_:1},8,["loading"])]),a(T,null,{default:l(()=>e[34]||(e[34]=[s("数据清理")])),_:1}),P("div",ke,[a(c,{type:"danger",onClick:$,loading:n.clearExpired},{default:l(()=>[a(x,null,{default:l(()=>[a(C(A))]),_:1}),e[35]||(e[35]=s("清理过期Token "))]),_:1},8,["loading"]),a(c,{type:"danger",onClick:h,loading:n.clearBackups},{default:l(()=>[a(x,null,{default:l(()=>[a(C(A))]),_:1}),e[36]||(e[36]=s("清理旧备份 "))]),_:1},8,["loading"]),a(c,{type:"danger",onClick:ee,loading:n.clearAll},{default:l(()=>[a(x,null,{default:l(()=>[a(C(A))]),_:1}),e[37]||(e[37]=s("清理所有数据 "))]),_:1},8,["loading"])]),a(T,null,{default:l(()=>e[38]||(e[38]=[s("数据统计")])),_:1}),a(te,{border:""},{default:l(()=>[a(y,{label:"Token总数"},{default:l(()=>[s(k(_.value.tokenCount),1)]),_:1}),a(y,{label:"备份总数"},{default:l(()=>[s(k(_.value.backupCount),1)]),_:1}),a(y,{label:"过期Token数量"},{default:l(()=>[s(k(_.value.expiredTokenCount),1)]),_:1}),a(y,{label:"最早Token创建时间"},{default:l(()=>[s(k(_.value.firstTokenTime),1)]),_:1}),a(y,{label:"最近Token更新时间"},{default:l(()=>[s(k(_.value.lastTokenTime),1)]),_:1}),a(y,{label:"数据库大小"},{default:l(()=>[s(k(_.value.databaseSize),1)]),_:1})]),_:1})])]),_:1})):q("",!0)]),_:1},8,["modelValue"])]),_:1})])}}},Pe=ne(we,[["__scopeId","data-v-d4e9820c"]]);export{Pe as default};
