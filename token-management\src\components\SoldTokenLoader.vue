<template>
  <div class="sold-token-loader">
    <el-button
      type="success"
      :icon="Calendar"
      :loading="loading"
      @click="showDatePicker"
    >
      加载已卖
    </el-button>

    <!-- 日期选择对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择日期"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="date-picker-container">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :clearable="false"
          :editable="false"
          style="width: 100%"
        />
        <div class="date-description">
          选择要加载的已卖Token的日期
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="loadSoldTokens" :loading="loading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar } from '@element-plus/icons-vue'
import soldTokenService from '../services/soldTokenService'

// 定义props
const props = defineProps({
  currentTokens: {
    type: Array,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['update:tokens'])

// 状态变量
const loading = ref(false)
const dialogVisible = ref(false)
const selectedDate = ref(new Date().toISOString().split('T')[0]) // 默认为今天

// 显示日期选择器
const showDatePicker = () => {
  dialogVisible.value = true
}

// 格式化日期为接口所需的格式
const formatDateForApi = (dateStr) => {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  // 获取星期几
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[date.getDay()]
  
  return `${year}-${month}-${day}-${weekday}`
}

// 加载已卖Token
const loadSoldTokens = async () => {
  if (!selectedDate.value) {
    ElMessage.warning('请选择日期')
    return
  }
  
  try {
    // 设置加载状态
    loading.value = true
    dialogVisible.value = false
    
    // 格式化日期
    const formattedDate = formatDateForApi(selectedDate.value)
    
    // 调用服务加载已卖Token
    const result = await soldTokenService.loadSoldTokens(formattedDate)
    
    // 处理结果
    if (result.success) {
      // 处理Token数据并更新
      const { tokens, stats } = soldTokenService.processSoldTokens(props.currentTokens, result.data)
      
      // 更新Token列表
      emit('update:tokens', tokens)
      
      // 显示成功消息
      ElMessage.success(`成功加载 ${stats.new} 个已卖Token，跳过 ${stats.duplicate} 个重复Token`)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('加载已卖Token失败:', error)
    ElMessage.error(`加载已卖Token失败: ${error.message}`)
  } finally {
    // 重置加载状态
    loading.value = false
  }
}
</script>

<style scoped>
.sold-token-loader {
  display: inline-block;
  margin-right: 10px;
}

.date-picker-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.date-description {
  color: #909399;
  font-size: 14px;
}
</style>
