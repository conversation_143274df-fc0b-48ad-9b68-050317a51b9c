const { logger } = require('../utils/logger');

// 错误处理中间件
const errorHandler = (err, req, res, next) => {
  // 记录错误日志
  logger.error(`${err.name}: ${err.message}\nStack: ${err.stack}`);
  
  // 确定状态码
  const statusCode = err.statusCode || 500;
  
  // 返回错误响应
  res.status(statusCode).json({
    success: false,
    error: {
      message: process.env.NODE_ENV === 'production' 
        ? '服务器错误' 
        : err.message,
      ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
    }
  });
};

module.exports = errorHandler; 