-- Token管理系统完整数据库结构
-- 整合自各个SQL文件和代码中的表结构定义
-- 创建日期：2024年05月
-- 更新日期：2024年05月 - 将所有ENUM字段改为VARCHAR以增加灵活性

-- 创建数据库
CREATE DATABASE IF NOT EXISTS token_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE token_management;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    role VARCHAR(20) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    avatar_url VARCHAR(255),
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Token表 (整合了多个版本的字段)
CREATE TABLE IF NOT EXISTS tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(100) NOT NULL,
    token_value TEXT NOT NULL,
    username VARCHAR(100),
    nickname VARCHAR(100),
    avatar_url TEXT,
    avatar_query_id VARCHAR(100),
    platform VARCHAR(50) NOT NULL,
    purchase_time DATETIME,
    expiry_date DATETIME,
    status VARCHAR(20) DEFAULT '正常',
    account_id VARCHAR(50),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_used_time DATETIME,
    deleted_at DATETIME,
    created_by INT,
    updated_by INT,
    remarks TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tokens_uid (uid),
    INDEX idx_tokens_status (status),
    INDEX idx_tokens_purchase_time (purchase_time),
    INDEX idx_tokens_expiry_date (expiry_date),
    INDEX idx_tokens_platform_status (platform, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 备份表
CREATE TABLE IF NOT EXISTS backups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    backup_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    backup_type VARCHAR(20), -- 重置前，重置后
    task_id VARCHAR(50),     -- 关联ID
    token_count INT DEFAULT 0,
    size VARCHAR(20),
    type VARCHAR(20),        -- 自动，手动
    description TEXT,
    remark TEXT,             -- 备注
    status VARCHAR(20) DEFAULT '备份中',
    delete_status BOOLEAN DEFAULT FALSE,
    scheduled_delete_time DATETIME,
    backup_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_backups_backup_id (backup_id),
    INDEX idx_backups_backup_type (backup_type),
    INDEX idx_backups_task_id (task_id),
    INDEX idx_backups_status (status),
    INDEX idx_backups_delete_status (delete_status),
    INDEX idx_backups_scheduled_delete_time (scheduled_delete_time),
    INDEX idx_backups_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 备份-Token关联表
CREATE TABLE IF NOT EXISTS backup_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    backup_id VARCHAR(50) NOT NULL,
    token_id BIGINT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_backup_tokens_backup_id (backup_id),
    INDEX idx_backup_tokens_token_id (token_id),
    FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 完整的Token备份表
CREATE TABLE IF NOT EXISTS backup_tokens_complete (
    id INT AUTO_INCREMENT PRIMARY KEY,
    backup_id INT NOT NULL,
    uid VARCHAR(255) NOT NULL,
    token VARCHAR(1000) NOT NULL,
    username VARCHAR(255),
    nickname VARCHAR(255),
    avatar VARCHAR(255),
    status VARCHAR(50),
    platform VARCHAR(50),
    purchase_time DATETIME,
    expiry_time DATETIME,
    order_id VARCHAR(255),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE,
    INDEX idx_backup_tokens_complete_backup_id (backup_id),
    INDEX idx_backup_tokens_complete_uid (uid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 字符串类型的Token ID备份表
CREATE TABLE IF NOT EXISTS backup_tokens_string (
    id INT AUTO_INCREMENT PRIMARY KEY,
    backup_id INT NOT NULL,
    token_id_string VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE,
    INDEX idx_backup_tokens_string_backup_id (backup_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 重置记录表
CREATE TABLE IF NOT EXISTS resets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    token_id INT NOT NULL,
    old_value TEXT NOT NULL,
    new_value TEXT NOT NULL,
    reason TEXT,
    reset_by INT,
    reset_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE,
    FOREIGN KEY (reset_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 活动日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action_type VARCHAR(50) NOT NULL, -- Token重置, Token备份, Token导入, Token删除
    content TEXT,
    status VARCHAR(20) DEFAULT '成功',
    ip_address VARCHAR(45),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_activity_logs_user_id (user_id),
    INDEX idx_activity_logs_action_type (action_type),
    INDEX idx_activity_logs_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 通知设置表
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_enabled BOOLEAN DEFAULT TRUE,
    expiry_notification JSON, -- 存储过期提醒天数，例如: ["7", "1"]
    backup_notification BOOLEAN DEFAULT TRUE,
    system_events JSON, -- 存储系统事件类型，例如: ["token_reset", "backup_created"]
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 自动重置任务表
CREATE TABLE IF NOT EXISTS auto_reset_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    name VARCHAR(100),
    status VARCHAR(20) DEFAULT '待执行',
    schedule_type VARCHAR(20), -- 单次, 每日, 每周, 每月
    scheduled_time DATETIME,
    load_days INT, -- 加载天数设置
    platform VARCHAR(50),
    token_count INT DEFAULT 0,
    before_backup_id VARCHAR(50),
    after_backup_id VARCHAR(50),
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at DATETIME,
    remarks TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_auto_reset_tasks_task_id (task_id),
    INDEX idx_auto_reset_tasks_status (status),
    INDEX idx_auto_reset_tasks_scheduled_time (scheduled_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 初始化管理员用户
INSERT INTO users (username, password, email, role) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin');

-- 初始化系统设置
INSERT INTO settings (setting_key, setting_value, description) VALUES
('system_name', 'Token 管理系统', '系统名称'),
('theme', 'default', '系统主题'),
('language', 'zh-CN', '系统语言'),
('page_size', '20', '默认分页大小'),
('backup_retention_days', '30', '备份保留天数'),
('token_cleanup_days', '60', '过期Token清理天数');

-- 添加注释说明数据库结构的重要修改
/*
主要修改历史：
1. 2024年05月28日：解决了备份创建时的字段类型问题
   - 将 backups 表的 backup_type 字段从 ENUM 修改为 VARCHAR(20)
   - 将 backups 表的 status 字段从 ENUM 修改为 VARCHAR(20)
   - 将 users 表的 role 和 status 字段从 ENUM 修改为 VARCHAR(20)，增加灵活性

经验教训：
- 尽量避免使用 ENUM 类型，以增加灵活性，使用 VARCHAR 代替
- 将来如有数据类型相关错误，可参考 fixStatusField.js 和 checkBackupTypeField.js 中的修复方法
*/ 