<template>
  <div class="token-backups-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">Token备份管理</h1>
      <div class="page-desc">查看并恢复Token备份，确保您的数据安全</div>
    </div>

    <!-- 备份列表卡片 -->
    <el-card class="backups-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <i class="el-icon-document-copy header-icon"></i>
            <span>备份列表</span>
            <span class="result-count" v-if="backupsList.length > 0">{{ backupsList.length }}条记录</span>
          </div>
          <div class="header-actions">
            <el-button-group>
              <el-button type="primary" @click="refreshBackups" :loading="loading">
                <el-icon><el-icon-refresh /></el-icon>刷新
              </el-button>
              <el-button type="danger" @click="clearLocalBackups">
                <el-icon><el-icon-delete /></el-icon>清空本地备份
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="backupsList.length === 0" class="empty-result">
        <el-empty description="暂无备份记录" />
      </div>

      <el-table
        v-else
        ref="backupsTable"
        :data="backupsList"
        style="width: 95%; margin: 0 auto;"
        border
        stripe
        highlight-current-row
        size="medium"
        :header-cell-style="{background: '#f5f7fa', color: '#303133', fontSize: '15px', height: '56px'}"
        :cell-style="{fontSize: '14px', padding: '12px 0'}"
      >
        <el-table-column prop="backupId" label="备份ID" width="240" sortable>
          <template #default="scope">
            <div class="backup-id-cell">
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  <div>
                    <p><strong>备份 ID:</strong> {{ scope.row.backupId }}</p>
                    <p><strong>创建时间:</strong> {{ scope.row.createTime }}</p>
                    <p><strong>备份类型:</strong> {{ scope.row.backupType }}</p>
                    <p><strong>Token 数量:</strong> {{ scope.row.tokenCount }}</p>
                    <p><strong>来源:</strong> {{ scope.row.sourceType }}</p>
                    <p><strong>描述:</strong> {{ scope.row.description || '-' }}</p>
                  </div>
                </template>
                <span class="backup-id-text">{{ scope.row.backupId }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="backupType" label="备份类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.backupType === '重置前' ? 'success' : 'warning'" size="medium">
              {{ scope.row.backupType }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="备份描述" min-width="200">
          <template #default="scope">
            <div class="backup-description">{{ scope.row.description || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable />
        <el-table-column prop="tokenCount" label="Token数量" width="100" align="center" />
        <el-table-column prop="sourceType" label="来源" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.sourceType === '自动' ? 'warning' : 'primary'" size="medium">
              {{ scope.row.sourceType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="loadDate" label="已卖日期" width="110" sortable />
        <el-table-column prop="resetDate" label="重置日期" width="110" sortable />
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                class="restore-btn action-inline-btn"
                @click="restoreBackup(scope.row)"
                :loading="scope.row.loading"
              >
                恢复
              </el-button>
              <el-button
                type="danger"
                size="small"
                class="delete-btn action-inline-btn"
                @click="deleteBackup(scope.row)"
                :loading="scope.row.deleting"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 恢复的备份数据展示 -->
    <el-card class="restored-data-card" v-if="restoredTokens.length > 0">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <i class="el-icon-document-checked header-icon"></i>
            <span>备份数据</span>
            <span class="backup-source">(ID: {{ activeBackupId }})</span>
          </div>
          <div class="header-actions">
            <el-button @click="clearRestoredData" type="danger" size="small">
              清除数据
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="restoredTable"
        :data="restoredTokens"
        style="width: 95%; margin: 0 auto;"
        border
        stripe
        highlight-current-row
        size="medium"
        :header-cell-style="{background: '#f5f7fa', color: '#303133', fontSize: '15px', height: '56px'}"
        :cell-style="{fontSize: '14px', padding: '12px 0'}"
      >
        <el-table-column prop="accountId" label="账号ID" width="120" />
        <el-table-column prop="tokenValue" label="Token" width="220">
          <template #default="scope">
            <div class="token-value">{{ scope.row.tokenValue }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="loadDate" label="已卖日期" width="110" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getTokenStatusType(scope.row.status)" size="medium">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="120" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="createTime" label="创建时间" width="180" sortable />
        <el-table-column prop="lastUsedTime" label="最后使用" width="180" sortable />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTokenStore } from '@/store/token'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import axios from 'axios'

const tokenStore = useTokenStore()
const route = useRoute()

// 数据加载状态
const loading = ref(false)

// 备份列表数据
const backupsList = ref([])

// 当前恢复的备份数据
const restoredTokens = ref([])
const activeBackupId = ref('')

// API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'

// 获取备份列表
const fetchBackups = async () => {
  loading.value = true;
  restoredTokens.value = []; // 清空恢复的Token数据
  activeBackupId.value = ''; // 重置当前活动备份
  try {
    // 1. 获取本地备份
    const localBackups = JSON.parse(localStorage.getItem('token_backups') || '[]');
    const formattedLocalBackups = localBackups.map(backup => ({
      backupId: backup.id,
      backupType: '本地备份',
      createTime: new Date(backup.created_at).toLocaleString(),
      tokenCount: backup.tokens.length,
      sourceType: '手动',
      loadDate: '-',
      resetDate: '-',
      isLocal: true, // 标记为本地备份
      localData: backup, // 保存完整备份数据
      description: backup.description || '本地创建的备份'
    }));

    // 2. 获取远程备份
    let remoteBackups = [];
    try {
      const response = await axios.get(`${apiBaseUrl}/backups`);
      if (response.data.success) {
        console.log('远程备份原始数据:', response.data.data.backups);
        remoteBackups = (response.data.data.backups || []).map(backup => ({
          backupId: backup.backup_id || backup.id,
          backupType: backup.backup_type || '远程备份',
          createTime: new Date(backup.created_at).toLocaleString(),
          tokenCount: backup.token_count || 0,
          sourceType: backup.type === 'manual' ? '手动' : '自动',
          loadDate: backup.load_date || '-',
          resetDate: backup.reset_date || '-',
          isLocal: false, // 标记为远程备份
          description: backup.description || '-'
        }));
      }
    } catch (error) {
      console.error('获取远程备份失败:', error);
      // 如果远程备份获取失败，只显示本地备份，并显示警告
      ElMessage.warning('远程备份获取失败，仅显示本地备份');
    }

    // 3. 合并本地和远程备份，本地备份显示在前面
    backupsList.value = [...formattedLocalBackups, ...remoteBackups];
  } catch (error) {
    console.error('获取备份列表失败:', error);
    ElMessage.error('获取备份列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新备份列表
const refreshBackups = () => {
  fetchBackups()
}

// 清空本地备份
const clearLocalBackups = () => {
  ElMessageBox.confirm('确定要清空所有本地备份吗？此操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStorage.removeItem('token_backups');
    ElMessage.success('本地备份已清空');
    fetchBackups(); // 刷新列表
  }).catch(() => {
    ElMessage.info('已取消清空操作');
  });
}

// 删除备份
const deleteBackup = async (backup) => {
  // 设置当前备份的删除状态
  const index = backupsList.value.findIndex(item => item.backupId === backup.backupId);
  if (index !== -1) {
    backupsList.value[index] = { ...backupsList.value[index], deleting: true };
  }

  try {
    // 处理本地备份和远程备份的删除
    if (backup.isLocal) {
      // 本地备份直接从 localStorage 删除
      const localBackups = JSON.parse(localStorage.getItem('token_backups') || '[]');
      const updatedBackups = localBackups.filter(b => b.id !== backup.backupId);
      localStorage.setItem('token_backups', JSON.stringify(updatedBackups));

      // 从列表中移除
      backupsList.value = backupsList.value.filter(b => b.backupId !== backup.backupId);
      ElMessage.success(`成功删除本地备份: ${backup.backupId}`);
    } else {
      // 远程备份通过 API 删除
      try {
        console.log('删除远程备份:', backup.backupId);
        console.log('备份完整数据:', backup);

        // 尝试使用完整的备份对象中的id属性
        const backupId = backup.id || backup.backupId;
        console.log('删除请求URL:', `${apiBaseUrl}/backups/${backupId}`);

        const response = await axios.delete(`${apiBaseUrl}/backups/${backupId}`);
        console.log('删除响应:', response.data);

        if (response.data.success) {
          // 从列表中移除
          backupsList.value = backupsList.value.filter(b => b.backupId !== backup.backupId);
          ElMessage.success(`成功删除远程备份: ${backup.backupId}`);

          // 刷新备份列表
          setTimeout(() => fetchBackups(), 1000);
        } else {
          ElMessage.error(response.data.message || '删除备份失败');
        }
      } catch (error) {
        console.error('删除远程备份失败:', error);
        ElMessage.error(`删除备份失败: ${error.message}`);
      }
    }
  } catch (error) {
    console.error('删除备份失败:', error);
    ElMessage.error(`删除备份失败: ${error.message}`);

    // 重置删除状态
    if (index !== -1) {
      backupsList.value[index] = { ...backupsList.value[index], deleting: false };
    }
  }
};

// 恢复备份
const restoreBackup = async (backup) => {
  // 设置当前备份的加载状态
  const index = backupsList.value.findIndex(item => item.backupId === backup.backupId);
  if (index !== -1) {
    backupsList.value[index] = { ...backupsList.value[index], loading: true };
  }

  try {
    // 处理本地备份和远程备份的恢复
    if (backup.isLocal) {
      // 本地备份直接从localStorage获取数据
      const localBackups = JSON.parse(localStorage.getItem('token_backups') || '[]');
      const backupData = localBackups.find(b => b.id === backup.backupId);

      if (backupData) {
        restoredTokens.value = backupData.tokens.map(token => ({
          accountId: token.accountId || '-',
          tokenValue: token.token || token.tokenValue || '-',
          loadDate: token.loadDate || token.purchaseTime || '-',
          status: token.status || '正常',
          platform: token.platform || '-',
          username: token.user || token.username || '-',
          createTime: token.createTime || new Date().toLocaleString(),
          lastUsedTime: token.lastUsedTime || '-'
        }));
        activeBackupId.value = backup.backupId;
        ElMessage.success(`成功恢复本地备份: ${backup.backupId}`);
      } else {
        ElMessage.error('找不到备份数据');
      }
    } else {
      // 远程备份通过API获取
      try {
        console.log('正在获取备份数据:', backup.backupId);
        const response = await axios.get(`${apiBaseUrl}/backups/${backup.backupId}/tokens`);
        console.log('备份数据响应:', response.data);

        if (response.data.success) {
          // 处理返回的Token数据，确保字段名称一致
          restoredTokens.value = (response.data.data.tokens || []).map(token => ({
            accountId: token.account_id || token.accountId || '-',
            tokenValue: token.token_value || token.tokenValue || '-',
            loadDate: token.purchase_time ? new Date(token.purchase_time).toLocaleString() : '-',
            status: token.status || '正常',
            platform: token.platform || '-',
            username: token.username || '-',
            createTime: token.created_at ? new Date(token.created_at).toLocaleString() : '-',
            lastUsedTime: token.last_used_time ? new Date(token.last_used_time).toLocaleString() : '-',
            uid: token.uid || '-'
          }));

          activeBackupId.value = backup.backupId;
          ElMessage.success(`成功恢复远程备份: ${backup.backupId}`);
        } else {
          ElMessage.error(response.data.message || '恢复备份失败');
        }
      } catch (error) {
        console.error('获取备份数据失败:', error);
        ElMessage.error(`获取备份数据失败: ${error.message}`);
      }
    }
  } catch (error) {
    console.error('恢复备份失败:', error);
    ElMessage.error('恢复备份失败');
  } finally {
    // 重置加载状态
    if (index !== -1) {
      backupsList.value[index] = { ...backupsList.value[index], loading: false };
    }
  }
};

// 清除恢复的数据
const clearRestoredData = () => {
  restoredTokens.value = []
  activeBackupId.value = ''
  ElMessage.info('已清除恢复的数据')
}

// 获取Token状态对应的标签类型
const getTokenStatusType = (status) => {
  const statusMap = {
    '正常': 'success',
    '已重置': 'info',
    '失效': 'danger',
    '失败': 'warning'
  }
  return statusMap[status] || 'info'
}

// 页面加载时获取备份列表
onMounted(() => {
  fetchBackups()

  // 如果URL中包含备份ID参数，自动恢复该备份
  const backupIdParam = route.params.backupId || route.query.backupId
  if (backupIdParam) {
    ElMessage.info(`正在自动加载备份: ${backupIdParam}`)
    // 等待备份列表加载完成后再恢复备份
    const checkAndRestore = setInterval(() => {
      if (!loading.value && backupsList.value.length > 0) {
        clearInterval(checkAndRestore)
        const targetBackup = backupsList.value.find(b => b.backupId === backupIdParam)
        if (targetBackup) {
          restoreBackup(targetBackup)
        } else {
          ElMessage.warning(`找不到指定的备份: ${backupIdParam}`)
        }
      }
    }, 500)
  }
})
</script>

<style scoped>
.token-backups-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px);
  background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.8), rgba(240, 242, 245, 0.5));
}

.backups-card,
.restored-data-card {
  margin-bottom: 20px;
  border-radius: 6px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.backups-card::before,
.restored-data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.restored-data-card::before {
  background: linear-gradient(90deg, #409eff, #e6a23c);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 22px;
  color: #409eff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.result-count,
.backup-source {
  font-size: 14px;
  font-weight: normal;
  color: #606266;
  margin-left: 10px;
  background-color: #f2f6fc;
  padding: 3px 10px;
  border-radius: 12px;
}

.backup-source {
  font-family: Consolas, monospace;
  padding: 4px 12px;
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

.empty-result {
  padding: 60px 0;
  background-color: #fbfbfb;
  border-radius: 0 0 6px 6px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.page-desc {
  font-size: 16px;
  color: #606266;
  margin-bottom: 16px;
}

/* 表格样式 */
:deep(.el-table) {
  font-size: 14px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table::before) {
  display: none;
}

:deep(.el-table th) {
  background: linear-gradient(to right, #f5f7fa, #e4e7ed);
  font-weight: 600;
  color: #303133;
  height: 56px;
  padding: 8px 0;
  font-size: 15px;
  border-bottom: 2px solid #e4e7ed;
}

:deep(.el-table--medium .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__row:hover td) {
  background-color: #f0f7ff !important;
}

:deep(.el-tag) {
  font-weight: 500;
  font-size: 13px;
  padding: 0 10px;
  height: 26px;
  line-height: 26px;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.restore-btn {
  padding: 6px 15px;
  font-weight: 500;
  font-size: 14px;
  color: #fff;
  border: none;
  background: linear-gradient(to right, #409eff, #1989fa);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.delete-btn {
  padding: 6px 15px;
  font-weight: 500;
  font-size: 14px;
  color: #fff;
  border: none;
  background: linear-gradient(to right, #f56c6c, #e74c3c);
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.backup-id-text {
  text-decoration: underline;
  text-decoration-style: dotted;
  cursor: pointer;
}

.backup-description {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 当鼠标悬停时显示完整内容 */
.backup-description:hover {
  white-space: normal;
  word-break: break-word;
}

.token-value {
  font-family: Consolas, monospace;
  color: #606266;
  font-size: 14px;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.backup-id-cell {
  font-family: Consolas, monospace;
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.25s ease;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-button--primary) {
  background: linear-gradient(to right, #409eff, #1989fa);
  border: none;
}

:deep(.el-button--success) {
  background: linear-gradient(to right, #67c23a, #4caf50);
  border: none;
}

:deep(.el-button--danger) {
  background: linear-gradient(to right, #f56c6c, #e53935);
  border: none;
}

.action-inline-btn {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 0;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .token-backups-container {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }
}

.loading-container {
  padding: 20px;
}
</style>