import{l as ua,m as Qi,_ as N0,c as rt,g as Ie,a as sr,b as $e,d as se,t as Ue,k as wt,f as q,p as dt,e as j,h as Ee,r as He,F as _0,q as Tr,w as ec,o as rc,E as De,s as Tt,v as zt,x as tc,y as un,n as ac,j as hn,z as nc}from"./index-CckIkgn1.js";import{q as b0,b as P0,a as sc,c as ic,d as cc}from"./tokenService-0zI_qOng.js";/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var ps=1252,fc=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],L0={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},B0=function(e){fc.indexOf(e)!=-1&&(ps=L0[0]=e)};function oc(){B0(1252)}var at=function(e){B0(e)};function gs(){at(1200),oc()}function xn(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function lc(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function ms(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var ca=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?lc(e.slice(2)):t==254&&r==255?ms(e.slice(2)):t==65279?e.slice(1):e},Pa=function(t){return String.fromCharCode(t)},dn=function(t){return String.fromCharCode(t)},T0,At="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function vn(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0,l=0,f=0;f<e.length;)r=e.charCodeAt(f++),s=r>>2,a=e.charCodeAt(f++),i=(r&3)<<4|a>>4,n=e.charCodeAt(f++),c=(a&15)<<2|n>>6,l=n&63,isNaN(a)?c=l=64:isNaN(n)&&(l=64),t+=At.charAt(s)+At.charAt(i)+At.charAt(c)+At.charAt(l);return t}function Mr(e){var t="",r=0,a=0,n=0,s=0,i=0,c=0,l=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var f=0;f<e.length;)s=At.indexOf(e.charAt(f++)),i=At.indexOf(e.charAt(f++)),r=s<<2|i>>4,t+=String.fromCharCode(r),c=At.indexOf(e.charAt(f++)),a=(i&15)<<4|c>>2,c!==64&&(t+=String.fromCharCode(a)),l=At.indexOf(e.charAt(f++)),n=(c&3)<<6|l,l!==64&&(t+=String.fromCharCode(n));return t}var be=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Ht=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function yt(e){return be?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function pn(e){return be?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var tt=function(t){return be?Ht(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function Vt(e){if(Array.isArray(e))return e.map(function(a){return String.fromCharCode(a)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function M0(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return M0(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var Et=be?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Ht(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function uc(e){for(var t=[],r=0,a=e.length+250,n=yt(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|i&63;else if(i>=55296&&i<57344){i=(i&1023)+64;var c=e.charCodeAt(++s)&1023;n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|c>>6&15|(i&3)<<4,n[r++]=128|c&63}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|i&63;r>a&&(t.push(n.slice(0,r)),r=0,n=yt(65535),a=65530)}return t.push(n.slice(0,r)),Et(t)}var Dr=/\u0000/g,fa=/[\u0001-\u0006]/g;function Jt(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function nt(e,t){var r=""+e;return r.length>=t?r:rr("0",t-r.length)+r}function U0(e,t){var r=""+e;return r.length>=t?r:rr(" ",t-r.length)+r}function Ga(e,t){var r=""+e;return r.length>=t?r:r+rr(" ",t-r.length)}function hc(e,t){var r=""+Math.round(e);return r.length>=t?r:rr("0",t-r.length)+r}function xc(e,t){var r=""+e;return r.length>=t?r:rr("0",t-r.length)+r}var gn=Math.pow(2,32);function Kt(e,t){if(e>gn||e<-gn)return hc(e,t);var r=Math.round(e);return xc(r,t)}function Xa(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var mn=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],o0=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function dc(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Ce={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},_n={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},vc={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function za(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,c=0,l=1,f=0,o=0,u=Math.floor(n);f<t&&(u=Math.floor(n),c=u*i+s,o=u*f+l,!(n-u<5e-8));)n=1/(n-u),s=i,i=c,l=f,f=o;if(o>t&&(f>t?(o=l,c=s):(o=f,c=i)),!r)return[0,a*c,o];var v=Math.floor(a*c/o);return[v,a*c-v*o,o]}function bt(e,t,r){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),s=0,i=[],c={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),t&&t.date1904&&(a+=1462),c.u>.9999&&(c.u=0,++n==86400&&(c.T=n=0,++a,++c.D)),a===60)i=r?[1317,10,29]:[1900,2,29],s=3;else if(a===0)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var l=new Date(1900,0,1);l.setDate(l.getDate()+a-1),i=[l.getFullYear(),l.getMonth()+1,l.getDate()],s=l.getDay(),a<60&&(s=(s+6)%7),r&&(s=kc(l,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=n%60,n=Math.floor(n/60),c.M=n%60,n=Math.floor(n/60),c.H=n,c.q=s,c}var _s=new Date(1899,11,31,0,0,0),pc=_s.getTime(),gc=new Date(1900,2,1,0,0,0);function Ts(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=gc&&(r+=24*60*60*1e3),(r-(pc+(e.getTimezoneOffset()-_s.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function H0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function mc(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function _c(e){var t=e<0?12:11,r=H0(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Tc(e){var t=H0(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function ka(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=_c(e):t===10?r=e.toFixed(10).substr(0,12):r=Tc(e),H0(mc(r.toUpperCase()))}function Bt(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):ka(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return zr(14,Ts(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function kc(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Ec(e,t,r,a){var n="",s=0,i=0,c=r.y,l,f=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:l=c%100,f=2;break;default:l=c%1e4,f=4;break}break;case 109:switch(t.length){case 1:case 2:l=r.m,f=t.length;break;case 3:return o0[r.m-1][1];case 5:return o0[r.m-1][0];default:return o0[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:l=r.d,f=t.length;break;case 3:return mn[r.q][0];default:return mn[r.q][1]}break;case 104:switch(t.length){case 1:case 2:l=1+(r.H+11)%12,f=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:l=r.H,f=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:l=r.M,f=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?nt(r.S,t.length):(a>=2?i=a===3?1e3:100:i=a===1?10:1,s=Math.round(i*(r.S+r.u)),s>=60*i&&(s=0),t==="s"?s===0?"0":""+s/i:(n=nt(s,2+a),t==="ss"?n.substr(0,2):"."+n.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":l=r.D*24+r.H;break;case"[m]":case"[mm]":l=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":l=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}f=t.length===3?1:2;break;case 101:l=c,f=1;break}var o=f>0?nt(l,f):"";return o}function Ft(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,a=e.substr(0,r);r!=e.length;r+=t)a+=(a.length>0?",":"")+e.substr(r,t);return a}var ks=/%/g;function wc(e,t,r){var a=t.replace(ks,""),n=t.length-a.length;return pt(e,a,r*Math.pow(10,2*n))+rr("%",n)}function Ac(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return pt(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function Es(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Es(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),r.indexOf("e")===-1){var i=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,l,f,o){return l+f+o.substr(0,(n+s)%n)+"."+o.substr(s)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ws=/# (\?+)( ?)\/( ?)(\d+)/;function Fc(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,c=a;return r+(s===0?"":""+s)+" "+(i===0?rr(" ",e[1].length+1+e[4].length):U0(i,e[1].length)+e[2]+"/"+e[3]+nt(c,e[4].length))}function Sc(e,t,r){return r+(t===0?"":""+t)+rr(" ",e[1].length+2+e[4].length)}var As=/^#*0*\.([0#]+)/,Fs=/\).*[0#]/,Ss=/\(###\) ###\\?-####/;function yr(e){for(var t="",r,a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function Tn(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function kn(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function yc(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Cc(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Gr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Fs)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Gr("n",a,r):"("+Gr("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Ac(e,t,r);if(t.indexOf("%")!==-1)return wc(e,t,r);if(t.indexOf("E")!==-1)return Es(t,r);if(t.charCodeAt(0)===36)return"$"+Gr(e,t.substr(t.charAt(1)==" "?2:1),r);var n,s,i,c,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+Kt(l,t.length);if(t.match(/^[#?]+$/))return n=Kt(r,0),n==="0"&&(n=""),n.length>t.length?n:yr(t.substr(0,t.length-n.length))+n;if(s=t.match(ws))return Fc(s,l,f);if(t.match(/^#+0+$/))return f+Kt(l,t.length-t.indexOf("0"));if(s=t.match(As))return n=Tn(r,s[1].length).replace(/^([^\.]+)$/,"$1."+yr(s[1])).replace(/\.$/,"."+yr(s[1])).replace(/\.(\d*)$/,function(x,d){return"."+d+rr("0",yr(s[1]).length-d.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return f+Tn(l,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return f+Ft(Kt(l,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Gr(e,t,-r):Ft(""+(Math.floor(r)+yc(r,s[1].length)))+"."+nt(kn(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return Gr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=Jt(Gr(e,t.replace(/[\\-]/g,""),r)),i=0,Jt(Jt(t.replace(/\\/g,"")).replace(/[0#]/g,function(x){return i<n.length?n.charAt(i++):x==="0"?"0":""}));if(t.match(Ss))return n=Gr(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var o="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=za(l,Math.pow(10,i)-1,!1),n=""+f,o=pt("n",s[1],c[1]),o.charAt(o.length-1)==" "&&(o=o.substr(0,o.length-1)+"0"),n+=o+s[2]+"/"+s[3],o=Ga(c[2],i),o.length<s[4].length&&(o=yr(s[4].substr(s[4].length-o.length))+o),n+=o,n;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=za(l,Math.pow(10,i)-1,!0),f+(c[0]||(c[1]?"":"0"))+" "+(c[1]?U0(c[1],i)+s[2]+"/"+s[3]+Ga(c[2],i):rr(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=Kt(r,0),t.length<=n.length?n:yr(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var u=t.indexOf(".")-i,v=t.length-n.length-u;return yr(t.substr(0,u)+n+t.substr(t.length-v))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=kn(r,s[1].length),r<0?"-"+Gr(e,t,-r):Ft(Cc(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(x){return"00,"+(x.length<3?nt(0,3-x.length):"")+x})+"."+nt(i,s[1].length);switch(t){case"###,##0.00":return Gr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var h=Ft(Kt(l,0));return h!=="0"?f+h:"";case"###,###.00":return Gr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Gr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Oc(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return pt(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function Dc(e,t,r){var a=t.replace(ks,""),n=t.length-a.length;return pt(e,a,r*Math.pow(10,2*n))+rr("%",n)}function ys(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+ys(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),!r.match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,l,f,o){return l+f+o.substr(0,(n+s)%n)+"."+o.substr(s)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function ot(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Fs)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ot("n",a,r):"("+ot("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Oc(e,t,r);if(t.indexOf("%")!==-1)return Dc(e,t,r);if(t.indexOf("E")!==-1)return ys(t,r);if(t.charCodeAt(0)===36)return"$"+ot(e,t.substr(t.charAt(1)==" "?2:1),r);var n,s,i,c,l=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+nt(l,t.length);if(t.match(/^[#?]+$/))return n=""+r,r===0&&(n=""),n.length>t.length?n:yr(t.substr(0,t.length-n.length))+n;if(s=t.match(ws))return Sc(s,l,f);if(t.match(/^#+0+$/))return f+nt(l,t.length-t.indexOf("0"));if(s=t.match(As))return n=(""+r).replace(/^([^\.]+)$/,"$1."+yr(s[1])).replace(/\.$/,"."+yr(s[1])),n=n.replace(/\.(\d*)$/,function(x,d){return"."+d+rr("0",yr(s[1]).length-d.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return f+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return f+Ft(""+l);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ot(e,t,-r):Ft(""+r)+"."+rr("0",s[1].length);if(s=t.match(/^#,#*,#0/))return ot(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=Jt(ot(e,t.replace(/[\\-]/g,""),r)),i=0,Jt(Jt(t.replace(/\\/g,"")).replace(/[0#]/g,function(x){return i<n.length?n.charAt(i++):x==="0"?"0":""}));if(t.match(Ss))return n=ot(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var o="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=za(l,Math.pow(10,i)-1,!1),n=""+f,o=pt("n",s[1],c[1]),o.charAt(o.length-1)==" "&&(o=o.substr(0,o.length-1)+"0"),n+=o+s[2]+"/"+s[3],o=Ga(c[2],i),o.length<s[4].length&&(o=yr(s[4].substr(s[4].length-o.length))+o),n+=o,n;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=za(l,Math.pow(10,i)-1,!0),f+(c[0]||(c[1]?"":"0"))+" "+(c[1]?U0(c[1],i)+s[2]+"/"+s[3]+Ga(c[2],i):rr(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:yr(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var u=t.indexOf(".")-i,v=t.length-n.length-u;return yr(t.substr(0,u)+n+t.substr(t.length-v))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+ot(e,t,-r):Ft(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(x){return"00,"+(x.length<3?nt(0,3-x.length):"")+x})+"."+nt(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var h=Ft(""+l);return h!=="0"?f+h:"";default:if(t.match(/\.[0#?]*$/))return ot(e,t.slice(0,t.lastIndexOf(".")),r)+yr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function pt(e,t,r){return(r|0)===r?ot(e,t,r):Gr(e,t,r)}function Ic(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Cs=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ra(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":Xa(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(a=r;e.charAt(t++)!=="]"&&t<e.length;)a+=e.charAt(t);if(a.match(Cs))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function Rc(e,t,r,a){for(var n=[],s="",i=0,c="",l="t",f,o,u,v="H";i<e.length;)switch(c=e.charAt(i)){case"G":if(!Xa(e,i))throw new Error("unrecognized character "+c+" in "+e);n[n.length]={t:"G",v:"General"},i+=7;break;case'"':for(s="";(u=e.charCodeAt(++i))!==34&&i<e.length;)s+=String.fromCharCode(u);n[n.length]={t:"t",v:s},++i;break;case"\\":var h=e.charAt(++i),x=h==="("||h===")"?h:"t";n[n.length]={t:x,v:h},++i;break;case"_":n[n.length]={t:"t",v:" "},i+=2;break;case"@":n[n.length]={t:"T",v:t},++i;break;case"B":case"b":if(e.charAt(i+1)==="1"||e.charAt(i+1)==="2"){if(f==null&&(f=bt(t,r,e.charAt(i+1)==="2"),f==null))return"";n[n.length]={t:"X",v:e.substr(i,2)},l=c,i+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||f==null&&(f=bt(t,r),f==null))return"";for(s=c;++i<e.length&&e.charAt(i).toLowerCase()===c;)s+=c;c==="m"&&l.toLowerCase()==="h"&&(c="M"),c==="h"&&(c=v),n[n.length]={t:c,v:s},l=c;break;case"A":case"a":case"上":var d={t:c,v:c};if(f==null&&(f=bt(t,r)),e.substr(i,3).toUpperCase()==="A/P"?(f!=null&&(d.v=f.H>=12?"P":"A"),d.t="T",v="h",i+=3):e.substr(i,5).toUpperCase()==="AM/PM"?(f!=null&&(d.v=f.H>=12?"PM":"AM"),d.t="T",i+=5,v="h"):e.substr(i,5).toUpperCase()==="上午/下午"?(f!=null&&(d.v=f.H>=12?"下午":"上午"),d.t="T",i+=5,v="h"):(d.t="t",++i),f==null&&d.t==="T")return"";n[n.length]=d,l=c;break;case"[":for(s=c;e.charAt(i++)!=="]"&&i<e.length;)s+=e.charAt(i);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(Cs)){if(f==null&&(f=bt(t,r),f==null))return"";n[n.length]={t:"Z",v:s.toLowerCase()},l=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",ra(e)||(n[n.length]={t:"t",v:s}));break;case".":if(f!=null){for(s=c;++i<e.length&&(c=e.charAt(i))==="0";)s+=c;n[n.length]={t:"s",v:s};break}case"0":case"#":for(s=c;++i<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(i))>-1;)s+=c;n[n.length]={t:"n",v:s};break;case"?":for(s=c;e.charAt(++i)===c;)s+=c;n[n.length]={t:c,v:s},l=c;break;case"*":++i,(e.charAt(i)==" "||e.charAt(i)=="*")&&++i;break;case"(":case")":n[n.length]={t:a===1?"t":c,v:c},++i;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=c;i<e.length&&"0123456789".indexOf(e.charAt(++i))>-1;)s+=e.charAt(i);n[n.length]={t:"D",v:s};break;case" ":n[n.length]={t:c,v:c},++i;break;case"$":n[n.length]={t:"t",v:"$"},++i;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c)===-1)throw new Error("unrecognized character "+c+" in "+e);n[n.length]={t:"t",v:c},++i;break}var p=0,A=0,O;for(i=n.length-1,l="t";i>=0;--i)switch(n[i].t){case"h":case"H":n[i].t=v,l="h",p<1&&(p=1);break;case"s":(O=n[i].v.match(/\.0+$/))&&(A=Math.max(A,O[0].length-1)),p<3&&(p=3);case"d":case"y":case"M":case"e":l=n[i].t;break;case"m":l==="s"&&(n[i].t="M",p<2&&(p=2));break;case"X":break;case"Z":p<1&&n[i].v.match(/[Hh]/)&&(p=1),p<2&&n[i].v.match(/[Mm]/)&&(p=2),p<3&&n[i].v.match(/[Ss]/)&&(p=3)}switch(p){case 0:break;case 1:f.u>=.5&&(f.u=0,++f.S),f.S>=60&&(f.S=0,++f.M),f.M>=60&&(f.M=0,++f.H);break;case 2:f.u>=.5&&(f.u=0,++f.S),f.S>=60&&(f.S=0,++f.M);break}var _="",b;for(i=0;i<n.length;++i)switch(n[i].t){case"t":case"T":case" ":case"D":break;case"X":n[i].v="",n[i].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":n[i].v=Ec(n[i].t.charCodeAt(0),n[i].v,f,A),n[i].t="t";break;case"n":case"?":for(b=i+1;n[b]!=null&&((c=n[b].t)==="?"||c==="D"||(c===" "||c==="t")&&n[b+1]!=null&&(n[b+1].t==="?"||n[b+1].t==="t"&&n[b+1].v==="/")||n[i].t==="("&&(c===" "||c==="n"||c===")")||c==="t"&&(n[b].v==="/"||n[b].v===" "&&n[b+1]!=null&&n[b+1].t=="?"));)n[i].v+=n[b].v,n[b]={v:"",t:";"},++b;_+=n[i].v,i=b-1;break;case"G":n[i].t="t",n[i].v=Bt(t,r);break}var U="",R,F;if(_.length>0){_.charCodeAt(0)==40?(R=t<0&&_.charCodeAt(0)===45?-t:t,F=pt("n",_,R)):(R=t<0&&a>1?-t:t,F=pt("n",_,R),R<0&&n[0]&&n[0].t=="t"&&(F=F.substr(1),n[0].v="-"+n[0].v)),b=F.length-1;var X=n.length;for(i=0;i<n.length;++i)if(n[i]!=null&&n[i].t!="t"&&n[i].v.indexOf(".")>-1){X=i;break}var I=n.length;if(X===n.length&&F.indexOf("E")===-1){for(i=n.length-1;i>=0;--i)n[i]==null||"n?".indexOf(n[i].t)===-1||(b>=n[i].v.length-1?(b-=n[i].v.length,n[i].v=F.substr(b+1,n[i].v.length)):b<0?n[i].v="":(n[i].v=F.substr(0,b+1),b=-1),n[i].t="t",I=i);b>=0&&I<n.length&&(n[I].v=F.substr(0,b+1)+n[I].v)}else if(X!==n.length&&F.indexOf("E")===-1){for(b=F.indexOf(".")-1,i=X;i>=0;--i)if(!(n[i]==null||"n?".indexOf(n[i].t)===-1)){for(o=n[i].v.indexOf(".")>-1&&i===X?n[i].v.indexOf(".")-1:n[i].v.length-1,U=n[i].v.substr(o+1);o>=0;--o)b>=0&&(n[i].v.charAt(o)==="0"||n[i].v.charAt(o)==="#")&&(U=F.charAt(b--)+U);n[i].v=U,n[i].t="t",I=i}for(b>=0&&I<n.length&&(n[I].v=F.substr(0,b+1)+n[I].v),b=F.indexOf(".")+1,i=X;i<n.length;++i)if(!(n[i]==null||"n?(".indexOf(n[i].t)===-1&&i!==X)){for(o=n[i].v.indexOf(".")>-1&&i===X?n[i].v.indexOf(".")+1:0,U=n[i].v.substr(0,o);o<n[i].v.length;++o)b<F.length&&(U+=F.charAt(b++));n[i].v=U,n[i].t="t",I=i}}}for(i=0;i<n.length;++i)n[i]!=null&&"n?".indexOf(n[i].t)>-1&&(R=a>1&&t<0&&i>0&&n[i-1].v==="-"?-t:t,n[i].v=pt(n[i].t,n[i].v,R),n[i].t="t");var G="";for(i=0;i!==n.length;++i)n[i]!=null&&(G+=n[i].v);return G}var En=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function wn(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Nc(e,t){var r=Ic(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[a,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var i=r[0].match(En),c=r[1].match(En);return wn(t,i)?[a,r[0]]:wn(t,c)?[a,r[1]]:[a,r[i!=null&&c!=null?2:1]]}return[a,s]}function zr(e,t,r){r==null&&(r={});var a="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?a=r.dateNF:a=e;break;case"number":e==14&&r.dateNF?a=r.dateNF:a=(r.table!=null?r.table:Ce)[e],a==null&&(a=r.table&&r.table[_n[e]]||Ce[_n[e]]),a==null&&(a=vc[e]||"General");break}if(Xa(a,0))return Bt(t,r);t instanceof Date&&(t=Ts(t,r.date1904));var n=Nc(a,t);if(Xa(n[1]))return Bt(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return Rc(n[1],t,r,n[0])}function Pt(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Ce[r]==null){t<0&&(t=r);continue}if(Ce[r]==e){t=r;break}}t<0&&(t=391)}return Ce[t]=e,t}function Os(){Ce=dc()}var bc={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Ds=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Pc(e){var t=typeof e=="number"?Ce[e]:e;return t=t.replace(Ds,"(\\d+)"),new RegExp("^"+t+"$")}function Lc(e,t,r){var a=-1,n=-1,s=-1,i=-1,c=-1,l=-1;(t.match(Ds)||[]).forEach(function(u,v){var h=parseInt(r[v+1],10);switch(u.toLowerCase().charAt(0)){case"y":a=h;break;case"d":s=h;break;case"h":i=h;break;case"s":l=h;break;case"m":i>=0?c=h:n=h;break}}),l>=0&&c==-1&&n>=0&&(c=n,n=-1);var f=(""+(a>=0?a:new Date().getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);f.length==7&&(f="0"+f),f.length==8&&(f="20"+f);var o=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2);return i==-1&&c==-1&&l==-1?f:a==-1&&n==-1&&s==-1?o:f+"T"+o}var Bc=function(){var e={};e.version="1.2.0";function t(){for(var F=0,X=new Array(256),I=0;I!=256;++I)F=I,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,F=F&1?-306674912^F>>>1:F>>>1,X[I]=F;return typeof Int32Array<"u"?new Int32Array(X):X}var r=t();function a(F){var X=0,I=0,G=0,Y=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(G=0;G!=256;++G)Y[G]=F[G];for(G=0;G!=256;++G)for(I=F[G],X=256+G;X<4096;X+=256)I=Y[X]=I>>>8^F[I&255];var P=[];for(G=1;G!=16;++G)P[G-1]=typeof Int32Array<"u"?Y.subarray(G*256,G*256+256):Y.slice(G*256,G*256+256);return P}var n=a(r),s=n[0],i=n[1],c=n[2],l=n[3],f=n[4],o=n[5],u=n[6],v=n[7],h=n[8],x=n[9],d=n[10],p=n[11],A=n[12],O=n[13],_=n[14];function b(F,X){for(var I=X^-1,G=0,Y=F.length;G<Y;)I=I>>>8^r[(I^F.charCodeAt(G++))&255];return~I}function U(F,X){for(var I=X^-1,G=F.length-15,Y=0;Y<G;)I=_[F[Y++]^I&255]^O[F[Y++]^I>>8&255]^A[F[Y++]^I>>16&255]^p[F[Y++]^I>>>24]^d[F[Y++]]^x[F[Y++]]^h[F[Y++]]^v[F[Y++]]^u[F[Y++]]^o[F[Y++]]^f[F[Y++]]^l[F[Y++]]^c[F[Y++]]^i[F[Y++]]^s[F[Y++]]^r[F[Y++]];for(G+=15;Y<G;)I=I>>>8^r[(I^F[Y++])&255];return~I}function R(F,X){for(var I=X^-1,G=0,Y=F.length,P=0,te=0;G<Y;)P=F.charCodeAt(G++),P<128?I=I>>>8^r[(I^P)&255]:P<2048?(I=I>>>8^r[(I^(192|P>>6&31))&255],I=I>>>8^r[(I^(128|P&63))&255]):P>=55296&&P<57344?(P=(P&1023)+64,te=F.charCodeAt(G++)&1023,I=I>>>8^r[(I^(240|P>>8&7))&255],I=I>>>8^r[(I^(128|P>>2&63))&255],I=I>>>8^r[(I^(128|te>>6&15|(P&3)<<4))&255],I=I>>>8^r[(I^(128|te&63))&255]):(I=I>>>8^r[(I^(224|P>>12&15))&255],I=I>>>8^r[(I^(128|P>>6&63))&255],I=I>>>8^r[(I^(128|P&63))&255]);return~I}return e.table=r,e.bstr=b,e.buf=U,e.str=R,e}(),Pe=function(){var t={};t.version="1.2.1";function r(g,k){for(var m=g.split("/"),T=k.split("/"),E=0,w=0,M=Math.min(m.length,T.length);E<M;++E){if(w=m[E].length-T[E].length)return w;if(m[E]!=T[E])return m[E]<T[E]?-1:1}return m.length-T.length}function a(g){if(g.charAt(g.length-1)=="/")return g.slice(0,-1).indexOf("/")===-1?g:a(g.slice(0,-1));var k=g.lastIndexOf("/");return k===-1?g:g.slice(0,k+1)}function n(g){if(g.charAt(g.length-1)=="/")return n(g.slice(0,-1));var k=g.lastIndexOf("/");return k===-1?g:g.slice(k+1)}function s(g,k){typeof k=="string"&&(k=new Date(k));var m=k.getHours();m=m<<6|k.getMinutes(),m=m<<5|k.getSeconds()>>>1,g.write_shift(2,m);var T=k.getFullYear()-1980;T=T<<4|k.getMonth()+1,T=T<<5|k.getDate(),g.write_shift(2,T)}function i(g){var k=g.read_shift(2)&65535,m=g.read_shift(2)&65535,T=new Date,E=m&31;m>>>=5;var w=m&15;m>>>=4,T.setMilliseconds(0),T.setFullYear(m+1980),T.setMonth(w-1),T.setDate(E);var M=k&31;k>>>=5;var z=k&63;return k>>>=6,T.setHours(k),T.setMinutes(z),T.setSeconds(M<<1),T}function c(g){pr(g,0);for(var k={},m=0;g.l<=g.length-4;){var T=g.read_shift(2),E=g.read_shift(2),w=g.l+E,M={};switch(T){case 21589:m=g.read_shift(1),m&1&&(M.mtime=g.read_shift(4)),E>5&&(m&2&&(M.atime=g.read_shift(4)),m&4&&(M.ctime=g.read_shift(4))),M.mtime&&(M.mt=new Date(M.mtime*1e3));break}g.l=w,k[T]=M}return k}var l;function f(){return l||(l={})}function o(g,k){if(g[0]==80&&g[1]==75)return Q(g,k);if((g[0]|32)==109&&(g[1]|32)==105)return qr(g,k);if(g.length<512)throw new Error("CFB file size "+g.length+" < 512");var m=3,T=512,E=0,w=0,M=0,z=0,B=0,H=[],W=g.slice(0,512);pr(W,0);var Z=u(W);switch(m=Z[0],m){case 3:T=512;break;case 4:T=4096;break;case 0:if(Z[1]==0)return Q(g,k);default:throw new Error("Major Version: Expected 3 or 4 saw "+m)}T!==512&&(W=g.slice(0,T),pr(W,28));var ce=g.slice(0,T);v(W,m);var xe=W.read_shift(4,"i");if(m===3&&xe!==0)throw new Error("# Directory Sectors: Expected 0 saw "+xe);W.l+=4,M=W.read_shift(4,"i"),W.l+=4,W.chk("00100000","Mini Stream Cutoff Size: "),z=W.read_shift(4,"i"),E=W.read_shift(4,"i"),B=W.read_shift(4,"i"),w=W.read_shift(4,"i");for(var ae=-1,he=0;he<109&&(ae=W.read_shift(4,"i"),!(ae<0));++he)H[he]=ae;var Se=h(g,T);p(B,w,Se,T,H);var je=O(Se,M,H,T);je[M].name="!Directory",E>0&&z!==te&&(je[z].name="!MiniFAT"),je[H[0]].name="!FAT",je.fat_addrs=H,je.ssz=T;var ne={},Ve=[],Zr=[],sa=[];_(M,je,Se,Ve,E,ne,Zr,z),x(Zr,sa,Ve),Ve.shift();var ia={FileIndex:Zr,FullPaths:sa};return k&&k.raw&&(ia.raw={header:ce,sectors:Se}),ia}function u(g){if(g[g.l]==80&&g[g.l+1]==75)return[0,0];g.chk(_e,"Header Signature: "),g.l+=16;var k=g.read_shift(2,"u");return[g.read_shift(2,"u"),k]}function v(g,k){var m=9;switch(g.l+=2,m=g.read_shift(2)){case 9:if(k!=3)throw new Error("Sector Shift: Expected 9 saw "+m);break;case 12:if(k!=4)throw new Error("Sector Shift: Expected 12 saw "+m);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+m)}g.chk("0600","Mini Sector Shift: "),g.chk("000000000000","Reserved: ")}function h(g,k){for(var m=Math.ceil(g.length/k)-1,T=[],E=1;E<m;++E)T[E-1]=g.slice(E*k,(E+1)*k);return T[m-1]=g.slice(m*k),T}function x(g,k,m){for(var T=0,E=0,w=0,M=0,z=0,B=m.length,H=[],W=[];T<B;++T)H[T]=W[T]=T,k[T]=m[T];for(;z<W.length;++z)T=W[z],E=g[T].L,w=g[T].R,M=g[T].C,H[T]===T&&(E!==-1&&H[E]!==E&&(H[T]=H[E]),w!==-1&&H[w]!==w&&(H[T]=H[w])),M!==-1&&(H[M]=T),E!==-1&&T!=H[T]&&(H[E]=H[T],W.lastIndexOf(E)<z&&W.push(E)),w!==-1&&T!=H[T]&&(H[w]=H[T],W.lastIndexOf(w)<z&&W.push(w));for(T=1;T<B;++T)H[T]===T&&(w!==-1&&H[w]!==w?H[T]=H[w]:E!==-1&&H[E]!==E&&(H[T]=H[E]));for(T=1;T<B;++T)if(g[T].type!==0){if(z=T,z!=H[z])do z=H[z],k[T]=k[z]+"/"+k[T];while(z!==0&&H[z]!==-1&&z!=H[z]);H[T]=-1}for(k[0]+="/",T=1;T<B;++T)g[T].type!==2&&(k[T]+="/")}function d(g,k,m){for(var T=g.start,E=g.size,w=[],M=T;m&&E>0&&M>=0;)w.push(k.slice(M*P,M*P+P)),E-=P,M=Nt(m,M*4);return w.length===0?or(0):Et(w).slice(0,g.size)}function p(g,k,m,T,E){var w=te;if(g===te){if(k!==0)throw new Error("DIFAT chain shorter than expected")}else if(g!==-1){var M=m[g],z=(T>>>2)-1;if(!M)return;for(var B=0;B<z&&(w=Nt(M,B*4))!==te;++B)E.push(w);p(Nt(M,T-4),k-1,m,T,E)}}function A(g,k,m,T,E){var w=[],M=[];E||(E=[]);var z=T-1,B=0,H=0;for(B=k;B>=0;){E[B]=!0,w[w.length]=B,M.push(g[B]);var W=m[Math.floor(B*4/T)];if(H=B*4&z,T<4+H)throw new Error("FAT boundary crossed: "+B+" 4 "+T);if(!g[W])break;B=Nt(g[W],H)}return{nodes:w,data:Nn([M])}}function O(g,k,m,T){var E=g.length,w=[],M=[],z=[],B=[],H=T-1,W=0,Z=0,ce=0,xe=0;for(W=0;W<E;++W)if(z=[],ce=W+k,ce>=E&&(ce-=E),!M[ce]){B=[];var ae=[];for(Z=ce;Z>=0;){ae[Z]=!0,M[Z]=!0,z[z.length]=Z,B.push(g[Z]);var he=m[Math.floor(Z*4/T)];if(xe=Z*4&H,T<4+xe)throw new Error("FAT boundary crossed: "+Z+" 4 "+T);if(!g[he]||(Z=Nt(g[he],xe),ae[Z]))break}w[ce]={nodes:z,data:Nn([B])}}return w}function _(g,k,m,T,E,w,M,z){for(var B=0,H=T.length?2:0,W=k[g].data,Z=0,ce=0,xe;Z<W.length;Z+=128){var ae=W.slice(Z,Z+128);pr(ae,64),ce=ae.read_shift(2),xe=X0(ae,0,ce-H),T.push(xe);var he={name:xe,type:ae.read_shift(1),color:ae.read_shift(1),L:ae.read_shift(4,"i"),R:ae.read_shift(4,"i"),C:ae.read_shift(4,"i"),clsid:ae.read_shift(16),state:ae.read_shift(4,"i"),start:0,size:0},Se=ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2);Se!==0&&(he.ct=b(ae,ae.l-8));var je=ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2)+ae.read_shift(2);je!==0&&(he.mt=b(ae,ae.l-8)),he.start=ae.read_shift(4,"i"),he.size=ae.read_shift(4,"i"),he.size<0&&he.start<0&&(he.size=he.type=0,he.start=te,he.name=""),he.type===5?(B=he.start,E>0&&B!==te&&(k[B].name="!StreamData")):he.size>=4096?(he.storage="fat",k[he.start]===void 0&&(k[he.start]=A(m,he.start,k.fat_addrs,k.ssz)),k[he.start].name=he.name,he.content=k[he.start].data.slice(0,he.size)):(he.storage="minifat",he.size<0?he.size=0:B!==te&&he.start!==te&&k[B]&&(he.content=d(he,k[B].data,(k[z]||{}).data))),he.content&&pr(he.content,0),w[xe]=he,M.push(he)}}function b(g,k){return new Date((Lr(g,k+4)/1e7*Math.pow(2,32)+Lr(g,k)/1e7-11644473600)*1e3)}function U(g,k){return f(),o(l.readFileSync(g),k)}function R(g,k){var m=k&&k.type;switch(m||be&&Buffer.isBuffer(g)&&(m="buffer"),m||"base64"){case"file":return U(g,k);case"base64":return o(tt(Mr(g)),k);case"binary":return o(tt(g),k)}return o(g,k)}function F(g,k){var m=k||{},T=m.root||"Root Entry";if(g.FullPaths||(g.FullPaths=[]),g.FileIndex||(g.FileIndex=[]),g.FullPaths.length!==g.FileIndex.length)throw new Error("inconsistent CFB structure");g.FullPaths.length===0&&(g.FullPaths[0]=T+"/",g.FileIndex[0]={name:T,type:5}),m.CLSID&&(g.FileIndex[0].clsid=m.CLSID),X(g)}function X(g){var k="Sh33tJ5";if(!Pe.find(g,"/"+k)){var m=or(4);m[0]=55,m[1]=m[3]=50,m[2]=54,g.FileIndex.push({name:k,type:2,content:m,size:4,L:69,R:69,C:69}),g.FullPaths.push(g.FullPaths[0]+k),I(g)}}function I(g,k){F(g);for(var m=!1,T=!1,E=g.FullPaths.length-1;E>=0;--E){var w=g.FileIndex[E];switch(w.type){case 0:T?m=!0:(g.FileIndex.pop(),g.FullPaths.pop());break;case 1:case 2:case 5:T=!0,isNaN(w.R*w.L*w.C)&&(m=!0),w.R>-1&&w.L>-1&&w.R==w.L&&(m=!0);break;default:m=!0;break}}if(!(!m&&!k)){var M=new Date(1987,1,19),z=0,B=Object.create?Object.create(null):{},H=[];for(E=0;E<g.FullPaths.length;++E)B[g.FullPaths[E]]=!0,g.FileIndex[E].type!==0&&H.push([g.FullPaths[E],g.FileIndex[E]]);for(E=0;E<H.length;++E){var W=a(H[E][0]);T=B[W],T||(H.push([W,{name:n(W).replace("/",""),type:1,clsid:me,ct:M,mt:M,content:null}]),B[W]=!0)}for(H.sort(function(xe,ae){return r(xe[0],ae[0])}),g.FullPaths=[],g.FileIndex=[],E=0;E<H.length;++E)g.FullPaths[E]=H[E][0],g.FileIndex[E]=H[E][1];for(E=0;E<H.length;++E){var Z=g.FileIndex[E],ce=g.FullPaths[E];if(Z.name=n(ce).replace("/",""),Z.L=Z.R=Z.C=-(Z.color=1),Z.size=Z.content?Z.content.length:0,Z.start=0,Z.clsid=Z.clsid||me,E===0)Z.C=H.length>1?1:-1,Z.size=0,Z.type=5;else if(ce.slice(-1)=="/"){for(z=E+1;z<H.length&&a(g.FullPaths[z])!=ce;++z);for(Z.C=z>=H.length?-1:z,z=E+1;z<H.length&&a(g.FullPaths[z])!=a(ce);++z);Z.R=z>=H.length?-1:z,Z.type=1}else a(g.FullPaths[E+1]||"")==a(ce)&&(Z.R=E+1),Z.type=2}}}function G(g,k){var m=k||{};if(m.fileType=="mad")return Wr(g,m);switch(I(g),m.fileType){case"zip":return ue(g,m)}var T=function(xe){for(var ae=0,he=0,Se=0;Se<xe.FileIndex.length;++Se){var je=xe.FileIndex[Se];if(je.content){var ne=je.content.length;ne>0&&(ne<4096?ae+=ne+63>>6:he+=ne+511>>9)}}for(var Ve=xe.FullPaths.length+3>>2,Zr=ae+7>>3,sa=ae+127>>7,ia=Zr+he+Ve+sa,Rt=ia+127>>7,f0=Rt<=109?0:Math.ceil((Rt-109)/127);ia+Rt+f0+127>>7>Rt;)f0=++Rt<=109?0:Math.ceil((Rt-109)/127);var xt=[1,f0,Rt,sa,Ve,he,ae,0];return xe.FileIndex[0].size=ae<<6,xt[7]=(xe.FileIndex[0].start=xt[0]+xt[1]+xt[2]+xt[3]+xt[4]+xt[5])+(xt[6]+7>>3),xt}(g),E=or(T[7]<<9),w=0,M=0;{for(w=0;w<8;++w)E.write_shift(1,ie[w]);for(w=0;w<8;++w)E.write_shift(2,0);for(E.write_shift(2,62),E.write_shift(2,3),E.write_shift(2,65534),E.write_shift(2,9),E.write_shift(2,6),w=0;w<3;++w)E.write_shift(2,0);for(E.write_shift(4,0),E.write_shift(4,T[2]),E.write_shift(4,T[0]+T[1]+T[2]+T[3]-1),E.write_shift(4,0),E.write_shift(4,4096),E.write_shift(4,T[3]?T[0]+T[1]+T[2]-1:te),E.write_shift(4,T[3]),E.write_shift(-4,T[1]?T[0]-1:te),E.write_shift(4,T[1]),w=0;w<109;++w)E.write_shift(-4,w<T[2]?T[1]+w:-1)}if(T[1])for(M=0;M<T[1];++M){for(;w<236+M*127;++w)E.write_shift(-4,w<T[2]?T[1]+w:-1);E.write_shift(-4,M===T[1]-1?te:M+1)}var z=function(xe){for(M+=xe;w<M-1;++w)E.write_shift(-4,w+1);xe&&(++w,E.write_shift(-4,te))};for(M=w=0,M+=T[1];w<M;++w)E.write_shift(-4,pe.DIFSECT);for(M+=T[2];w<M;++w)E.write_shift(-4,pe.FATSECT);z(T[3]),z(T[4]);for(var B=0,H=0,W=g.FileIndex[0];B<g.FileIndex.length;++B)W=g.FileIndex[B],W.content&&(H=W.content.length,!(H<4096)&&(W.start=M,z(H+511>>9)));for(z(T[6]+7>>3);E.l&511;)E.write_shift(-4,pe.ENDOFCHAIN);for(M=w=0,B=0;B<g.FileIndex.length;++B)W=g.FileIndex[B],W.content&&(H=W.content.length,!(!H||H>=4096)&&(W.start=M,z(H+63>>6)));for(;E.l&511;)E.write_shift(-4,pe.ENDOFCHAIN);for(w=0;w<T[4]<<2;++w){var Z=g.FullPaths[w];if(!Z||Z.length===0){for(B=0;B<17;++B)E.write_shift(4,0);for(B=0;B<3;++B)E.write_shift(4,-1);for(B=0;B<12;++B)E.write_shift(4,0);continue}W=g.FileIndex[w],w===0&&(W.start=W.size?W.start-1:te);var ce=w===0&&m.root||W.name;if(H=2*(ce.length+1),E.write_shift(64,ce,"utf16le"),E.write_shift(2,H),E.write_shift(1,W.type),E.write_shift(1,W.color),E.write_shift(-4,W.L),E.write_shift(-4,W.R),E.write_shift(-4,W.C),W.clsid)E.write_shift(16,W.clsid,"hex");else for(B=0;B<4;++B)E.write_shift(4,0);E.write_shift(4,W.state||0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,0),E.write_shift(4,W.start),E.write_shift(4,W.size),E.write_shift(4,0)}for(w=1;w<g.FileIndex.length;++w)if(W=g.FileIndex[w],W.size>=4096)if(E.l=W.start+1<<9,be&&Buffer.isBuffer(W.content))W.content.copy(E,E.l,0,W.size),E.l+=W.size+511&-512;else{for(B=0;B<W.size;++B)E.write_shift(1,W.content[B]);for(;B&511;++B)E.write_shift(1,0)}for(w=1;w<g.FileIndex.length;++w)if(W=g.FileIndex[w],W.size>0&&W.size<4096)if(be&&Buffer.isBuffer(W.content))W.content.copy(E,E.l,0,W.size),E.l+=W.size+63&-64;else{for(B=0;B<W.size;++B)E.write_shift(1,W.content[B]);for(;B&63;++B)E.write_shift(1,0)}if(be)E.l=E.length;else for(;E.l<E.length;)E.write_shift(1,0);return E}function Y(g,k){var m=g.FullPaths.map(function(B){return B.toUpperCase()}),T=m.map(function(B){var H=B.split("/");return H[H.length-(B.slice(-1)=="/"?2:1)]}),E=!1;k.charCodeAt(0)===47?(E=!0,k=m[0].slice(0,-1)+k):E=k.indexOf("/")!==-1;var w=k.toUpperCase(),M=E===!0?m.indexOf(w):T.indexOf(w);if(M!==-1)return g.FileIndex[M];var z=!w.match(fa);for(w=w.replace(Dr,""),z&&(w=w.replace(fa,"!")),M=0;M<m.length;++M)if((z?m[M].replace(fa,"!"):m[M]).replace(Dr,"")==w||(z?T[M].replace(fa,"!"):T[M]).replace(Dr,"")==w)return g.FileIndex[M];return null}var P=64,te=-2,_e="d0cf11e0a1b11ae1",ie=[208,207,17,224,161,177,26,225],me="00000000000000000000000000000000",pe={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:te,FREESECT:-1,HEADER_SIGNATURE:_e,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:me,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Ge(g,k,m){f();var T=G(g,m);l.writeFileSync(k,T)}function K(g){for(var k=new Array(g.length),m=0;m<g.length;++m)k[m]=String.fromCharCode(g[m]);return k.join("")}function ke(g,k){var m=G(g,k);switch(k&&k.type||"buffer"){case"file":return f(),l.writeFileSync(k.filename,m),m;case"binary":return typeof m=="string"?m:K(m);case"base64":return vn(typeof m=="string"?m:K(m));case"buffer":if(be)return Buffer.isBuffer(m)?m:Ht(m);case"array":return typeof m=="string"?tt(m):m}return m}var Ae;function y(g){try{var k=g.InflateRaw,m=new k;if(m._processChunk(new Uint8Array([3,0]),m._finishFlushFlag),m.bytesRead)Ae=g;else throw new Error("zlib does not expose bytesRead")}catch(T){console.error("cannot use native zlib: "+(T.message||T))}}function V(g,k){if(!Ae)return L(g,k);var m=Ae.InflateRaw,T=new m,E=T._processChunk(g.slice(g.l),T._finishFlushFlag);return g.l+=T.bytesRead,E}function N(g){return Ae?Ae.deflateRawSync(g):Oe(g)}var D=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],J=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],oe=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function de(g){var k=(g<<1|g<<11)&139536|(g<<5|g<<15)&558144;return(k>>16|k>>8|k)&255}for(var fe=typeof Uint8Array<"u",ee=fe?new Uint8Array(256):[],Re=0;Re<256;++Re)ee[Re]=de(Re);function C(g,k){var m=ee[g&255];return k<=8?m>>>8-k:(m=m<<8|ee[g>>8&255],k<=16?m>>>16-k:(m=m<<8|ee[g>>16&255],m>>>24-k))}function Ze(g,k){var m=k&7,T=k>>>3;return(g[T]|(m<=6?0:g[T+1]<<8))>>>m&3}function Ne(g,k){var m=k&7,T=k>>>3;return(g[T]|(m<=5?0:g[T+1]<<8))>>>m&7}function Be(g,k){var m=k&7,T=k>>>3;return(g[T]|(m<=4?0:g[T+1]<<8))>>>m&15}function Te(g,k){var m=k&7,T=k>>>3;return(g[T]|(m<=3?0:g[T+1]<<8))>>>m&31}function le(g,k){var m=k&7,T=k>>>3;return(g[T]|(m<=1?0:g[T+1]<<8))>>>m&127}function Xe(g,k,m){var T=k&7,E=k>>>3,w=(1<<m)-1,M=g[E]>>>T;return m<8-T||(M|=g[E+1]<<8-T,m<16-T)||(M|=g[E+2]<<16-T,m<24-T)||(M|=g[E+3]<<24-T),M&w}function Ye(g,k,m){var T=k&7,E=k>>>3;return T<=5?g[E]|=(m&7)<<T:(g[E]|=m<<T&255,g[E+1]=(m&7)>>8-T),k+3}function Ur(g,k,m){var T=k&7,E=k>>>3;return m=(m&1)<<T,g[E]|=m,k+1}function Yr(g,k,m){var T=k&7,E=k>>>3;return m<<=T,g[E]|=m&255,m>>>=8,g[E+1]=m,k+8}function Dt(g,k,m){var T=k&7,E=k>>>3;return m<<=T,g[E]|=m&255,m>>>=8,g[E+1]=m&255,g[E+2]=m>>>8,k+16}function it(g,k){var m=g.length,T=2*m>k?2*m:k+5,E=0;if(m>=k)return g;if(be){var w=pn(T);if(g.copy)g.copy(w);else for(;E<g.length;++E)w[E]=g[E];return w}else if(fe){var M=new Uint8Array(T);if(M.set)M.set(g);else for(;E<m;++E)M[E]=g[E];return M}return g.length=T,g}function hr(g){for(var k=new Array(g),m=0;m<g;++m)k[m]=0;return k}function jr(g,k,m){var T=1,E=0,w=0,M=0,z=0,B=g.length,H=fe?new Uint16Array(32):hr(32);for(w=0;w<32;++w)H[w]=0;for(w=B;w<m;++w)g[w]=0;B=g.length;var W=fe?new Uint16Array(B):hr(B);for(w=0;w<B;++w)H[E=g[w]]++,T<E&&(T=E),W[w]=0;for(H[0]=0,w=1;w<=T;++w)H[w+16]=z=z+H[w-1]<<1;for(w=0;w<B;++w)z=g[w],z!=0&&(W[w]=H[z+16]++);var Z=0;for(w=0;w<B;++w)if(Z=g[w],Z!=0)for(z=C(W[w],T)>>T-Z,M=(1<<T+4-Z)-1;M>=0;--M)k[z|M<<Z]=Z&15|w<<4;return T}var Jr=fe?new Uint16Array(512):hr(512),ct=fe?new Uint16Array(32):hr(32);if(!fe){for(var Qe=0;Qe<512;++Qe)Jr[Qe]=0;for(Qe=0;Qe<32;++Qe)ct[Qe]=0}(function(){for(var g=[],k=0;k<32;k++)g.push(5);jr(g,ct,32);var m=[];for(k=0;k<=143;k++)m.push(8);for(;k<=255;k++)m.push(9);for(;k<=279;k++)m.push(7);for(;k<=287;k++)m.push(8);jr(m,Jr,288)})();var Pr=function(){for(var k=fe?new Uint8Array(32768):[],m=0,T=0;m<oe.length-1;++m)for(;T<oe[m+1];++T)k[T]=m;for(;T<32768;++T)k[T]=29;var E=fe?new Uint8Array(259):[];for(m=0,T=0;m<J.length-1;++m)for(;T<J[m+1];++T)E[T]=m;function w(z,B){for(var H=0;H<z.length;){var W=Math.min(65535,z.length-H),Z=H+W==z.length;for(B.write_shift(1,+Z),B.write_shift(2,W),B.write_shift(2,~W&65535);W-- >0;)B[B.l++]=z[H++]}return B.l}function M(z,B){for(var H=0,W=0,Z=fe?new Uint16Array(32768):[];W<z.length;){var ce=Math.min(65535,z.length-W);if(ce<10){for(H=Ye(B,H,+(W+ce==z.length)),H&7&&(H+=8-(H&7)),B.l=H/8|0,B.write_shift(2,ce),B.write_shift(2,~ce&65535);ce-- >0;)B[B.l++]=z[W++];H=B.l*8;continue}H=Ye(B,H,+(W+ce==z.length)+2);for(var xe=0;ce-- >0;){var ae=z[W];xe=(xe<<5^ae)&32767;var he=-1,Se=0;if((he=Z[xe])&&(he|=W&-32768,he>W&&(he-=32768),he<W))for(;z[he+Se]==z[W+Se]&&Se<250;)++Se;if(Se>2){ae=E[Se],ae<=22?H=Yr(B,H,ee[ae+1]>>1)-1:(Yr(B,H,3),H+=5,Yr(B,H,ee[ae-23]>>5),H+=3);var je=ae<8?0:ae-4>>2;je>0&&(Dt(B,H,Se-J[ae]),H+=je),ae=k[W-he],H=Yr(B,H,ee[ae]>>3),H-=3;var ne=ae<4?0:ae-2>>1;ne>0&&(Dt(B,H,W-he-oe[ae]),H+=ne);for(var Ve=0;Ve<Se;++Ve)Z[xe]=W&32767,xe=(xe<<5^z[W])&32767,++W;ce-=Se-1}else ae<=143?ae=ae+48:H=Ur(B,H,1),H=Yr(B,H,ee[ae]),Z[xe]=W&32767,++W}H=Yr(B,H,0)-1}return B.l=(H+7)/8|0,B.l}return function(B,H){return B.length<8?w(B,H):M(B,H)}}();function Oe(g){var k=or(50+Math.floor(g.length*1.1)),m=Pr(g,k);return k.slice(0,m)}var er=fe?new Uint16Array(32768):hr(32768),Rr=fe?new Uint16Array(32768):hr(32768),ze=fe?new Uint16Array(128):hr(128),Hr=1,vr=1;function It(g,k){var m=Te(g,k)+257;k+=5;var T=Te(g,k)+1;k+=5;var E=Be(g,k)+4;k+=4;for(var w=0,M=fe?new Uint8Array(19):hr(19),z=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],B=1,H=fe?new Uint8Array(8):hr(8),W=fe?new Uint8Array(8):hr(8),Z=M.length,ce=0;ce<E;++ce)M[D[ce]]=w=Ne(g,k),B<w&&(B=w),H[w]++,k+=3;var xe=0;for(H[0]=0,ce=1;ce<=B;++ce)W[ce]=xe=xe+H[ce-1]<<1;for(ce=0;ce<Z;++ce)(xe=M[ce])!=0&&(z[ce]=W[xe]++);var ae=0;for(ce=0;ce<Z;++ce)if(ae=M[ce],ae!=0){xe=ee[z[ce]]>>8-ae;for(var he=(1<<7-ae)-1;he>=0;--he)ze[xe|he<<ae]=ae&7|ce<<3}var Se=[];for(B=1;Se.length<m+T;)switch(xe=ze[le(g,k)],k+=xe&7,xe>>>=3){case 16:for(w=3+Ze(g,k),k+=2,xe=Se[Se.length-1];w-- >0;)Se.push(xe);break;case 17:for(w=3+Ne(g,k),k+=3;w-- >0;)Se.push(0);break;case 18:for(w=11+le(g,k),k+=7;w-- >0;)Se.push(0);break;default:Se.push(xe),B<xe&&(B=xe);break}var je=Se.slice(0,m),ne=Se.slice(m);for(ce=m;ce<286;++ce)je[ce]=0;for(ce=T;ce<30;++ce)ne[ce]=0;return Hr=jr(je,er,286),vr=jr(ne,Rr,30),k}function Vr(g,k){if(g[0]==3&&!(g[1]&3))return[yt(k),2];for(var m=0,T=0,E=pn(k||1<<18),w=0,M=E.length>>>0,z=0,B=0;(T&1)==0;){if(T=Ne(g,m),m+=3,T>>>1)T>>1==1?(z=9,B=5):(m=It(g,m),z=Hr,B=vr);else{m&7&&(m+=8-(m&7));var H=g[m>>>3]|g[(m>>>3)+1]<<8;if(m+=32,H>0)for(!k&&M<w+H&&(E=it(E,w+H),M=E.length);H-- >0;)E[w++]=g[m>>>3],m+=8;continue}for(;;){!k&&M<w+32767&&(E=it(E,w+32767),M=E.length);var W=Xe(g,m,z),Z=T>>>1==1?Jr[W]:er[W];if(m+=Z&15,Z>>>=4,(Z>>>8&255)===0)E[w++]=Z;else{if(Z==256)break;Z-=257;var ce=Z<8?0:Z-4>>2;ce>5&&(ce=0);var xe=w+J[Z];ce>0&&(xe+=Xe(g,m,ce),m+=ce),W=Xe(g,m,B),Z=T>>>1==1?ct[W]:Rr[W],m+=Z&15,Z>>>=4;var ae=Z<4?0:Z-2>>1,he=oe[Z];for(ae>0&&(he+=Xe(g,m,ae),m+=ae),!k&&M<xe&&(E=it(E,xe+100),M=E.length);w<xe;)E[w]=E[w-he],++w}}}return k?[E,m+7>>>3]:[E.slice(0,w),m+7>>>3]}function L(g,k){var m=g.slice(g.l||0),T=Vr(m,k);return g.l+=T[1],T[0]}function S(g,k){if(g)typeof console<"u"&&console.error(k);else throw new Error(k)}function Q(g,k){var m=g;pr(m,0);var T=[],E=[],w={FileIndex:T,FullPaths:E};F(w,{root:k.root});for(var M=m.length-4;(m[M]!=80||m[M+1]!=75||m[M+2]!=5||m[M+3]!=6)&&M>=0;)--M;m.l=M+4,m.l+=4;var z=m.read_shift(2);m.l+=6;var B=m.read_shift(4);for(m.l=B,M=0;M<z;++M){m.l+=20;var H=m.read_shift(4),W=m.read_shift(4),Z=m.read_shift(2),ce=m.read_shift(2),xe=m.read_shift(2);m.l+=8;var ae=m.read_shift(4),he=c(m.slice(m.l+Z,m.l+Z+ce));m.l+=Z+ce+xe;var Se=m.l;m.l=ae+4,re(m,H,W,w,he),m.l=Se}return w}function re(g,k,m,T,E){g.l+=2;var w=g.read_shift(2),M=g.read_shift(2),z=i(g);if(w&8257)throw new Error("Unsupported ZIP encryption");for(var B=g.read_shift(4),H=g.read_shift(4),W=g.read_shift(4),Z=g.read_shift(2),ce=g.read_shift(2),xe="",ae=0;ae<Z;++ae)xe+=String.fromCharCode(g[g.l++]);if(ce){var he=c(g.slice(g.l,g.l+ce));(he[21589]||{}).mt&&(z=he[21589].mt),((E||{})[21589]||{}).mt&&(z=E[21589].mt)}g.l+=ce;var Se=g.slice(g.l,g.l+H);switch(M){case 8:Se=V(g,W);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+M)}var je=!1;w&8&&(B=g.read_shift(4),B==134695760&&(B=g.read_shift(4),je=!0),H=g.read_shift(4),W=g.read_shift(4)),H!=k&&S(je,"Bad compressed size: "+k+" != "+H),W!=m&&S(je,"Bad uncompressed size: "+m+" != "+W),na(T,xe,Se,{unsafe:!0,mt:z})}function ue(g,k){var m=k||{},T=[],E=[],w=or(1),M=m.compression?8:0,z=0,B=0,H=0,W=0,Z=0,ce=g.FullPaths[0],xe=ce,ae=g.FileIndex[0],he=[],Se=0;for(B=1;B<g.FullPaths.length;++B)if(xe=g.FullPaths[B].slice(ce.length),ae=g.FileIndex[B],!(!ae.size||!ae.content||xe=="Sh33tJ5")){var je=W,ne=or(xe.length);for(H=0;H<xe.length;++H)ne.write_shift(1,xe.charCodeAt(H)&127);ne=ne.slice(0,ne.l),he[Z]=Bc.buf(ae.content,0);var Ve=ae.content;M==8&&(Ve=N(Ve)),w=or(30),w.write_shift(4,67324752),w.write_shift(2,20),w.write_shift(2,z),w.write_shift(2,M),ae.mt?s(w,ae.mt):w.write_shift(4,0),w.write_shift(-4,he[Z]),w.write_shift(4,Ve.length),w.write_shift(4,ae.content.length),w.write_shift(2,ne.length),w.write_shift(2,0),W+=w.length,T.push(w),W+=ne.length,T.push(ne),W+=Ve.length,T.push(Ve),w=or(46),w.write_shift(4,33639248),w.write_shift(2,0),w.write_shift(2,20),w.write_shift(2,z),w.write_shift(2,M),w.write_shift(4,0),w.write_shift(-4,he[Z]),w.write_shift(4,Ve.length),w.write_shift(4,ae.content.length),w.write_shift(2,ne.length),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(4,0),w.write_shift(4,je),Se+=w.l,E.push(w),Se+=ne.length,E.push(ne),++Z}return w=or(22),w.write_shift(4,101010256),w.write_shift(2,0),w.write_shift(2,0),w.write_shift(2,Z),w.write_shift(2,Z),w.write_shift(4,Se),w.write_shift(4,W),w.write_shift(2,0),Et([Et(T),Et(E),w])}var ve={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Fe(g,k){if(g.ctype)return g.ctype;var m=g.name||"",T=m.match(/\.([^\.]+)$/);return T&&ve[T[1]]||k&&(T=(m=k).match(/[\.\\]([^\.\\])+$/),T&&ve[T[1]])?ve[T[1]]:"application/octet-stream"}function We(g){for(var k=vn(g),m=[],T=0;T<k.length;T+=76)m.push(k.slice(T,T+76));return m.join(`\r
`)+`\r
`}function ar(g){var k=g.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(H){var W=H.charCodeAt(0).toString(16).toUpperCase();return"="+(W.length==1?"0"+W:W)});k=k.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),k.charAt(0)==`
`&&(k="=0D"+k.slice(1)),k=k.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var m=[],T=k.split(`\r
`),E=0;E<T.length;++E){var w=T[E];if(w.length==0){m.push("");continue}for(var M=0;M<w.length;){var z=76,B=w.slice(M,M+z);B.charAt(z-1)=="="?z--:B.charAt(z-2)=="="?z-=2:B.charAt(z-3)=="="&&(z-=3),B=w.slice(M,M+z),M+=z,M<w.length&&(B+="="),m.push(B)}}return m.join(`\r
`)}function ge(g){for(var k=[],m=0;m<g.length;++m){for(var T=g[m];m<=g.length&&T.charAt(T.length-1)=="=";)T=T.slice(0,T.length-1)+g[++m];k.push(T)}for(var E=0;E<k.length;++E)k[E]=k[E].replace(/[=][0-9A-Fa-f]{2}/g,function(w){return String.fromCharCode(parseInt(w.slice(1),16))});return tt(k.join(`\r
`))}function Ke(g,k,m){for(var T="",E="",w="",M,z=0;z<10;++z){var B=k[z];if(!B||B.match(/^\s*$/))break;var H=B.match(/^(.*?):\s*([^\s].*)$/);if(H)switch(H[1].toLowerCase()){case"content-location":T=H[2].trim();break;case"content-type":w=H[2].trim();break;case"content-transfer-encoding":E=H[2].trim();break}}switch(++z,E.toLowerCase()){case"base64":M=tt(Mr(k.slice(z).join("")));break;case"quoted-printable":M=ge(k.slice(z));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+E)}var W=na(g,T.slice(m.length),M,{unsafe:!0});w&&(W.ctype=w)}function qr(g,k){if(K(g.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var m=k&&k.root||"",T=(be&&Buffer.isBuffer(g)?g.toString("binary"):K(g)).split(`\r
`),E=0,w="";for(E=0;E<T.length;++E)if(w=T[E],!!/^Content-Location:/i.test(w)&&(w=w.slice(w.indexOf("file")),m||(m=w.slice(0,w.lastIndexOf("/")+1)),w.slice(0,m.length)!=m))for(;m.length>0&&(m=m.slice(0,m.length-1),m=m.slice(0,m.lastIndexOf("/")+1),w.slice(0,m.length)!=m););var M=(T[1]||"").match(/boundary="(.*?)"/);if(!M)throw new Error("MAD cannot find boundary");var z="--"+(M[1]||""),B=[],H=[],W={FileIndex:B,FullPaths:H};F(W);var Z,ce=0;for(E=0;E<T.length;++E){var xe=T[E];xe!==z&&xe!==z+"--"||(ce++&&Ke(W,T.slice(Z,E),m),Z=E)}return W}function Wr(g,k){var m=k||{},T=m.boundary||"SheetJS";T="------="+T;for(var E=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+T.slice(2)+'"',"","",""],w=g.FullPaths[0],M=w,z=g.FileIndex[0],B=1;B<g.FullPaths.length;++B)if(M=g.FullPaths[B].slice(w.length),z=g.FileIndex[B],!(!z.size||!z.content||M=="Sh33tJ5")){M=M.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(Se){return"_x"+Se.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(Se){return"_u"+Se.charCodeAt(0).toString(16)+"_"});for(var H=z.content,W=be&&Buffer.isBuffer(H)?H.toString("binary"):K(H),Z=0,ce=Math.min(1024,W.length),xe=0,ae=0;ae<=ce;++ae)(xe=W.charCodeAt(ae))>=32&&xe<128&&++Z;var he=Z>=ce*4/5;E.push(T),E.push("Content-Location: "+(m.root||"file:///C:/SheetJS/")+M),E.push("Content-Transfer-Encoding: "+(he?"quoted-printable":"base64")),E.push("Content-Type: "+Fe(z,M)),E.push(""),E.push(he?ar(W):We(W))}return E.push(T+`--\r
`),E.join(`\r
`)}function Nr(g){var k={};return F(k,g),k}function na(g,k,m,T){var E=T&&T.unsafe;E||F(g);var w=!E&&Pe.find(g,k);if(!w){var M=g.FullPaths[0];k.slice(0,M.length)==M?M=k:(M.slice(-1)!="/"&&(M+="/"),M=(M+k).replace("//","/")),w={name:n(k),type:2},g.FileIndex.push(w),g.FullPaths.push(M),E||Pe.utils.cfb_gc(g)}return w.content=m,w.size=m?m.length:0,T&&(T.CLSID&&(w.clsid=T.CLSID),T.mt&&(w.mt=T.mt),T.ct&&(w.ct=T.ct)),w}function s0(g,k){F(g);var m=Pe.find(g,k);if(m){for(var T=0;T<g.FileIndex.length;++T)if(g.FileIndex[T]==m)return g.FileIndex.splice(T,1),g.FullPaths.splice(T,1),!0}return!1}function i0(g,k,m){F(g);var T=Pe.find(g,k);if(T){for(var E=0;E<g.FileIndex.length;++E)if(g.FileIndex[E]==T)return g.FileIndex[E].name=n(m),g.FullPaths[E]=m,!0}return!1}function c0(g){I(g,!0)}return t.find=Y,t.read=R,t.parse=o,t.write=ke,t.writeFile=Ge,t.utils={cfb_new:Nr,cfb_add:na,cfb_del:s0,cfb_mov:i0,cfb_gc:c0,ReadShift:ha,CheckField:Zs,prep_blob:pr,bconcat:Et,use_zlib:y,_deflateRaw:Oe,_inflateRaw:L,consts:pe},t}();function Mc(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function lt(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function V0(e){for(var t=[],r=lt(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}var Ka=new Date(1899,11,30,0,0,0);function Ir(e,t){var r=e.getTime(),a=Ka.getTime()+(e.getTimezoneOffset()-Ka.getTimezoneOffset())*6e4;return(r-a)/(24*60*60*1e3)}var Is=new Date,Uc=Ka.getTime()+(Is.getTimezoneOffset()-Ka.getTimezoneOffset())*6e4,An=Is.getTimezoneOffset();function r0(e){var t=new Date;return t.setTime(e*24*60*60*1e3+Uc),t.getTimezoneOffset()!==An&&t.setTime(t.getTime()+(t.getTimezoneOffset()-An)*6e4),t}function Hc(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}var Fn=new Date("2017-02-19T19:06:09.000Z"),Rs=isNaN(Fn.getFullYear())?new Date("2/19/17"):Fn,Vc=Rs.getFullYear()==2017;function dr(e,t){var r=new Date(e);if(Vc)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Rs.getFullYear()==1917&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function Mt(e,t){if(be&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return la(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return la(ms(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return la(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return la(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function gr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=gr(e[r]));return t}function rr(e,t){for(var r="";r.length<t;)r+=e;return r}function st(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(a))||(a=a.replace(/[(](.*)[)]/,function(n,s){return r=-r,s}),!isNaN(t=Number(a)))?t/r:t}var Wc=["january","february","march","april","may","june","july","august","september","october","november","december"];function Qt(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&Wc.indexOf(i)==-1)return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&a!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var $c=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,a,n){if(e||typeof a=="string")return r.split(a);for(var s=r.split(a),i=[s[0]],c=1;c<s.length;++c)i.push(n),i.push(s[c]);return i}}();function Ns(e){return e?e.content&&e.type?Mt(e.content,!0):e.data?ca(e.data):e.asNodeBuffer&&be?ca(e.asNodeBuffer().toString("binary")):e.asBinary?ca(e.asBinary()):e._data&&e._data.getContent?ca(Mt(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function bs(e){if(!e)return null;if(e.data)return xn(e.data);if(e.asNodeBuffer&&be)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?xn(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Gc(e){return e&&e.name.slice(-4)===".bin"?bs(e):Ns(e)}function Xr(e,t){for(var r=e.FullPaths||lt(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function W0(e,t){var r=Xr(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function cr(e,t,r){if(!r)return Gc(W0(e,t));if(!t)return null;try{return cr(e,t)}catch{return null}}function Br(e,t,r){if(!r)return Ns(W0(e,t));if(!t)return null;try{return Br(e,t)}catch{return null}}function Xc(e,t,r){return bs(W0(e,t))}function Sn(e){for(var t=e.FullPaths||lt(e.files),r=[],a=0;a<t.length;++a)t[a].slice(-1)!="/"&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function zc(e,t,r){if(e.FullPaths){if(typeof r=="string"){var a;return be?a=Ht(r):a=uc(r),Pe.utils.cfb_add(e,t,a)}Pe.utils.cfb_add(e,t,r)}else e.file(t,r)}function Ps(e,t){switch(t.type){case"base64":return Pe.read(e,{type:"base64"});case"binary":return Pe.read(e,{type:"binary"});case"buffer":case"array":return Pe.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function oa(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var a=e.split("/");a.length!==0;){var n=a.shift();n===".."?r.pop():n!=="."&&r.push(n)}return r.join("/")}var Ls=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Kc=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,yn=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,Yc=/<[^>]*>/g,Sr=Ls.match(yn)?yn:Yc,jc=/<\w*:/,Jc=/<(\/?)\w+:/;function we(e,t,r){for(var a={},n=0,s=0;n!==e.length&&!((s=e.charCodeAt(n))===32||s===10||s===13);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(Kc),c=0,l="",f=0,o="",u="",v=1;if(i)for(f=0;f!=i.length;++f){for(u=i[f],s=0;s!=u.length&&u.charCodeAt(s)!==61;++s);for(o=u.slice(0,s).trim();u.charCodeAt(s+1)==32;)++s;for(v=(n=u.charCodeAt(s+1))==34||n==39?1:0,l=u.slice(s+1+v,u.length-v),c=0;c!=o.length&&o.charCodeAt(c)!==58;++c);if(c===o.length)o.indexOf("_")>0&&(o=o.slice(0,o.indexOf("_"))),a[o]=l,a[o.toLowerCase()]=l;else{var h=(c===5&&o.slice(0,5)==="xmlns"?"xmlns":"")+o.slice(c+1);if(a[h]&&o.slice(c-3,c)=="ext")continue;a[h]=l,a[h.toLowerCase()]=l}}return a}function ut(e){return e.replace(Jc,"<$1")}var Bs={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},qc=V0(Bs),Me=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(s==-1)return n.replace(e,function(c,l){return Bs[c]||String.fromCharCode(parseInt(l,c.indexOf("x")>-1?16:10))||c}).replace(t,function(c,l){return String.fromCharCode(parseInt(l,16))});var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),Zc=/[&<>'"]/g,Qc=/[\u0000-\u001f]/g;function $0(e){var t=e+"";return t.replace(Zc,function(r){return qc[r]}).replace(/\n/g,"<br/>").replace(Qc,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}var Cn=function(){var e=/&#(\d+);/g;function t(r,a){return String.fromCharCode(parseInt(a,10))}return function(a){return a.replace(e,t)}}();function qe(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function l0(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0;r<e.length;){if(a=e.charCodeAt(r++),a<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){i=(a&31)<<6,i|=n&63,t+=String.fromCharCode(i);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((a&15)<<12|(n&63)<<6|s&63);continue}i=e.charCodeAt(r++),c=((a&7)<<18|(n&63)<<12|(s&63)<<6|i&63)-65536,t+=String.fromCharCode(55296+(c>>>10&1023)),t+=String.fromCharCode(56320+(c&1023))}return t}function On(e){var t=yt(2*e.length),r,a,n=1,s=0,i=0,c;for(a=0;a<e.length;a+=n)n=1,(c=e.charCodeAt(a))<128?r=c:c<224?(r=(c&31)*64+(e.charCodeAt(a+1)&63),n=2):c<240?(r=(c&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63),n=3):(n=4,r=(c&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63),r-=65536,i=55296+(r>>>10&1023),r=56320+(r&1023)),i!==0&&(t[s++]=i&255,t[s++]=i>>>8,i=0),t[s++]=r%256,t[s++]=r>>>8;return t.slice(0,s).toString("ucs2")}function Dn(e){return Ht(e,"binary").toString("utf8")}var La="foo bar bazâð£",Je=be&&(Dn(La)==l0(La)&&Dn||On(La)==l0(La)&&On)||l0,la=be?function(e){return Ht(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(a&63)));break;case(a>=55296&&a<57344):a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)))}return t.join("")},Ea=function(){var e={};return function(r,a){var n=r+"|"+(a||"");return e[n]?e[n]:e[n]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",a||"")}}(),Ms=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var a=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}(),ef=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),rf=/<\/?(?:vt:)?variant>/g,tf=/<(?:vt:)([^>]*)>([\s\S]*)</;function In(e,t){var r=we(e),a=e.match(ef(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(s){var i=s.replace(rf,"").match(tf);i&&n.push({v:Je(i[2]),t:i[1]})}),n}var af=/(^\s|\s$|\n)/;function nf(e){return lt(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function sf(e,t,r){return"<"+e+(r!=null?nf(r):"")+(t!=null?(t.match(af)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function G0(e){if(be&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return Je(Vt(M0(e)));throw new Error("Bad input format: expected Buffer or string")}var wa=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,cf={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},ff=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];function of(e,t){for(var r=1-2*(e[t+7]>>>7),a=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),n=e[t+6]&15,s=5;s>=0;--s)n=n*256+e[t+s];return a==2047?n==0?r*(1/0):NaN:(a==0?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}function lf(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?i==0?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var c=0;c<=5;++c,s/=256)e[r+c]=s&255;e[r+6]=(n&15)<<4|s&15,e[r+7]=n>>4|a}var Rn=function(e){for(var t=[],r=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,s=e[0][a].length;n<s;n+=r)t.push.apply(t,e[0][a].slice(n,n+r));return t},Nn=be?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Ht(t)})):Rn(e)}:Rn,bn=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(vt(e,n)));return a.join("").replace(Dr,"")},X0=be?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Dr,""):bn(e,t,r)}:bn,Pn=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Us=be?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Pn(e,t,r)}:Pn,Ln=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Yt(e,n)));return a.join("")},Oa=be?function(t,r,a){return Buffer.isBuffer(t)?t.toString("utf8",r,a):Ln(t,r,a)}:Ln,Hs=function(e,t){var r=Lr(e,t);return r>0?Oa(e,t+4,t+4+r-1):""},Vs=Hs,Ws=function(e,t){var r=Lr(e,t);return r>0?Oa(e,t+4,t+4+r-1):""},$s=Ws,Gs=function(e,t){var r=2*Lr(e,t);return r>0?Oa(e,t+4,t+4+r-1):""},Xs=Gs,zs=function(t,r){var a=Lr(t,r);return a>0?X0(t,r+4,r+4+a):""},Ks=zs,Ys=function(e,t){var r=Lr(e,t);return r>0?Oa(e,t+4,t+4+r):""},js=Ys,Js=function(e,t){return of(e,t)},Ya=Js,qs=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};be&&(Vs=function(t,r){if(!Buffer.isBuffer(t))return Hs(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},$s=function(t,r){if(!Buffer.isBuffer(t))return Ws(t,r);var a=t.readUInt32LE(r);return a>0?t.toString("utf8",r+4,r+4+a-1):""},Xs=function(t,r){if(!Buffer.isBuffer(t))return Gs(t,r);var a=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a-1)},Ks=function(t,r){if(!Buffer.isBuffer(t))return zs(t,r);var a=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+a)},js=function(t,r){if(!Buffer.isBuffer(t))return Ys(t,r);var a=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+a)},Ya=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Js(t,r)},qs=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var Yt=function(e,t){return e[t]},vt=function(e,t){return e[t+1]*256+e[t]},uf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Lr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Nt=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},hf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function ha(e,t){var r="",a,n,s=[],i,c,l,f;switch(t){case"dbcs":if(f=this.l,be&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(l=0;l<e;++l)r+=String.fromCharCode(vt(this,f)),f+=2;e*=2;break;case"utf8":r=Oa(this,this.l,this.l+e);break;case"utf16le":e*=2,r=X0(this,this.l,this.l+e);break;case"wstr":return ha.call(this,e,"dbcs");case"lpstr-ansi":r=Vs(this,this.l),e=4+Lr(this,this.l);break;case"lpstr-cp":r=$s(this,this.l),e=4+Lr(this,this.l);break;case"lpwstr":r=Xs(this,this.l),e=4+2*Lr(this,this.l);break;case"lpp4":e=4+Lr(this,this.l),r=Ks(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Lr(this,this.l),r=js(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(i=Yt(this,this.l+e++))!==0;)s.push(Pa(i));r=s.join("");break;case"_wstr":for(e=0,r="";(i=vt(this,this.l+e))!==0;)s.push(Pa(i)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",f=this.l,l=0;l<e;++l){if(this.lens&&this.lens.indexOf(f)!==-1)return i=Yt(this,f),this.l=f+1,c=ha.call(this,e-l,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(Pa(vt(this,f))),f+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",f=this.l,l=0;l!=e;++l){if(this.lens&&this.lens.indexOf(f)!==-1)return i=Yt(this,f),this.l=f+1,c=ha.call(this,e-l,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(Pa(Yt(this,f))),f+=1}r=s.join("");break;default:switch(e){case 1:return a=Yt(this,this.l),this.l++,a;case 2:return a=(t==="i"?uf:vt)(this,this.l),this.l+=2,a;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(a=(e>0?Nt:hf)(this,this.l),this.l+=4,a):(n=Lr(this,this.l),this.l+=4,n);case 8:case-8:if(t==="f")return e==8?n=Ya(this,this.l):n=Ya([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:r=Us(this,this.l,e);break}}return this.l+=e,r}var xf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},df=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},vf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function pf(e,t,r){var a=0,n=0;if(r==="dbcs"){for(n=0;n!=t.length;++n)vf(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=t.charCodeAt(n)&255;a=t.length}else if(r==="hex"){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var i=t.charCodeAt(n);this[this.l++]=i&255,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=t&255;break;case 2:a=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:a=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:a=4,xf(this,t,this.l);break;case 8:if(a=8,r==="f"){lf(this,t,this.l);break}case 16:break;case-4:a=4,df(this,t,this.l);break}return this.l+=a,this}function Zs(e,t){var r=Us(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function pr(e,t){e.l=t,e.read_shift=ha,e.chk=Zs,e.write_shift=pf}function Fr(e,t){e.l+=t}function or(e){var t=yt(e);return pr(t,0),t}function _t(e,t,r){if(e){var a,n,s;pr(e,e.l||0);for(var i=e.length,c=0,l=0;e.l<i;){c=e.read_shift(1),c&128&&(c=(c&127)+((e.read_shift(1)&127)<<7));var f=e0[c]||e0[65535];for(a=e.read_shift(1),s=a&127,n=1;n<4&&a&128;++n)s+=((a=e.read_shift(1))&127)<<7*n;l=e.l+s;var o=f.f&&f.f(e,s,r);if(e.l=l,t(o,f,c))return}}}function k0(){var e=[],t=be?256:2048,r=function(f){var o=or(f);return pr(o,0),o},a=r(t),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},s=function(f){return a&&f<a.length-a.l?a:(n(),a=r(Math.max(f+1,t)))},i=function(){return n(),Et(e)},c=function(f){n(),a=f,a.l==null&&(a.l=a.length),s(t)};return{next:s,push:c,end:i,_bufs:e}}function xa(e,t,r){var a=gr(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Bn(e,t,r){var a=gr(e);return a.s=xa(a.s,t.s,r),a.e=xa(a.e,t.s,r),a}function da(e,t){if(e.cRel&&e.c<0)for(e=gr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=gr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=ye(e);return!e.cRel&&e.cRel!=null&&(r=_f(r)),!e.rRel&&e.rRel!=null&&(r=gf(r)),r}function u0(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+lr(e.s.c)+":"+(e.e.cRel?"":"$")+lr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+mr(e.s.r)+":"+(e.e.rRel?"":"$")+mr(e.e.r):da(e.s,t.biff)+":"+da(e.e,t.biff)}function z0(e){return parseInt(mf(e),10)-1}function mr(e){return""+(e+1)}function gf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function mf(e){return e.replace(/\$(\d+)$/,"$1")}function K0(e){for(var t=Tf(e),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function lr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function _f(e){return e.replace(/^([A-Z])/,"$$$1")}function Tf(e){return e.replace(/^\$([A-Z])/,"$1")}function kf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Or(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function ye(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function ta(e){var t=e.indexOf(":");return t==-1?{s:Or(e),e:Or(e)}:{s:Or(e.slice(0,t)),e:Or(e.slice(t+1))}}function Le(e,t){return typeof t>"u"||typeof t=="number"?Le(e.s,e.e):(typeof e!="string"&&(e=ye(e)),typeof t!="string"&&(t=ye(t)),e==t?e:e+":"+t)}function tr(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||n!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function Mn(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=zr(e.z,r?Ir(t):t)}catch{}try{return e.w=zr((e.XF||{}).numFmtId||(r?14:0),r?Ir(t):t)}catch{return""+t}}function mt(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Gt[e.v]||e.v:t==null?Mn(e,e.v):Mn(e,t))}function Ot(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function Qs(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,c=0;if(s&&a.origin!=null){if(typeof a.origin=="number")i=a.origin;else{var l=typeof a.origin=="string"?Or(a.origin):a.origin;i=l.r,c=l.c}s["!ref"]||(s["!ref"]="A1:A1")}var f={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var o=tr(s["!ref"]);f.s.c=o.s.c,f.s.r=o.s.r,f.e.c=Math.max(f.e.c,o.e.c),f.e.r=Math.max(f.e.r,o.e.r),i==-1&&(f.e.r=i=o.e.r+1)}for(var u=0;u!=t.length;++u)if(t[u]){if(!Array.isArray(t[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var v=0;v!=t[u].length;++v)if(!(typeof t[u][v]>"u")){var h={v:t[u][v]},x=i+u,d=c+v;if(f.s.r>x&&(f.s.r=x),f.s.c>d&&(f.s.c=d),f.e.r<x&&(f.e.r=x),f.e.c<d&&(f.e.c=d),t[u][v]&&typeof t[u][v]=="object"&&!Array.isArray(t[u][v])&&!(t[u][v]instanceof Date))h=t[u][v];else if(Array.isArray(h.v)&&(h.f=t[u][v][1],h.v=h.v[0]),h.v===null)if(h.f)h.t="n";else if(a.nullError)h.t="e",h.v=0;else if(a.sheetStubs)h.t="z";else continue;else typeof h.v=="number"?h.t="n":typeof h.v=="boolean"?h.t="b":h.v instanceof Date?(h.z=a.dateNF||Ce[14],a.cellDates?(h.t="d",h.w=zr(h.z,Ir(h.v))):(h.t="n",h.v=Ir(h.v),h.w=zr(h.z,h.v))):h.t="s";if(n)s[x]||(s[x]=[]),s[x][d]&&s[x][d].z&&(h.z=s[x][d].z),s[x][d]=h;else{var p=ye({c:d,r:x});s[p]&&s[p].z&&(h.z=s[p].z),s[p]=h}}}return f.s.c<1e7&&(s["!ref"]=Le(f)),s}function aa(e,t){return Qs(null,e,t)}function Ef(e){return e.read_shift(4,"i")}function Ar(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function wf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Y0(e,t){var r=e.l,a=e.read_shift(1),n=Ar(e),s=[],i={t:n,h:n};if((a&1)!==0){for(var c=e.read_shift(4),l=0;l!=c;++l)s.push(wf(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var Af=Y0;function Kr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Wt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}var Ff=Ar;function j0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}var Sf=Ar,E0=j0;function J0(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,a=t[0]&2;e.l+=4;var n=a===0?Ya([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):Nt(t,0)>>2;return r?n/100:n}function ei(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var $t=ei;function Er(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function yf(e){var t={},r=e.read_shift(1),a=r>>>1,n=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),c=e.read_shift(1),l=e.read_shift(1);switch(e.l++,a){case 0:t.auto=1;break;case 1:t.index=n;var f=Lt[n];f&&(t.rgb=Fa(f));break;case 2:t.rgb=Fa([i,c,l]);break;case 3:t.theme=n;break}return s!=0&&(t.tint=s>0?s/32767:s/32768),t}function Cf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function ri(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function Of(e){return ri(e,1)}function Df(e){return ri(e,2)}var q0=2,br=3,Ba=11,Un=12,ja=19,Ma=64,If=65,Rf=71,Nf=4108,bf=4126,xr=80,ti=81,Pf=[xr,ti],Lf={1:{n:"CodePage",t:q0},2:{n:"Category",t:xr},3:{n:"PresentationFormat",t:xr},4:{n:"ByteCount",t:br},5:{n:"LineCount",t:br},6:{n:"ParagraphCount",t:br},7:{n:"SlideCount",t:br},8:{n:"NoteCount",t:br},9:{n:"HiddenCount",t:br},10:{n:"MultimediaClipCount",t:br},11:{n:"ScaleCrop",t:Ba},12:{n:"HeadingPairs",t:Nf},13:{n:"TitlesOfParts",t:bf},14:{n:"Manager",t:xr},15:{n:"Company",t:xr},16:{n:"LinksUpToDate",t:Ba},17:{n:"CharacterCount",t:br},19:{n:"SharedDoc",t:Ba},22:{n:"HyperlinksChanged",t:Ba},23:{n:"AppVersion",t:br,p:"version"},24:{n:"DigSig",t:If},26:{n:"ContentType",t:xr},27:{n:"ContentStatus",t:xr},28:{n:"Language",t:xr},29:{n:"Version",t:xr},255:{},2147483648:{n:"Locale",t:ja},2147483651:{n:"Behavior",t:ja},1919054434:{}},Bf={1:{n:"CodePage",t:q0},2:{n:"Title",t:xr},3:{n:"Subject",t:xr},4:{n:"Author",t:xr},5:{n:"Keywords",t:xr},6:{n:"Comments",t:xr},7:{n:"Template",t:xr},8:{n:"LastAuthor",t:xr},9:{n:"RevNumber",t:xr},10:{n:"EditTime",t:Ma},11:{n:"LastPrinted",t:Ma},12:{n:"CreatedDate",t:Ma},13:{n:"ModifiedDate",t:Ma},14:{n:"PageCount",t:br},15:{n:"WordCount",t:br},16:{n:"CharCount",t:br},17:{n:"Thumbnail",t:Rf},18:{n:"Application",t:xr},19:{n:"DocSecurity",t:br},255:{},2147483648:{n:"Locale",t:ja},2147483651:{n:"Behavior",t:ja},1919054434:{}},Hn={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Mf=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Uf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Hf=Uf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Lt=gr(Hf),Gt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},ai={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},Vn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};function Vf(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Wf(e){var t=Vf();if(!e||!e.match)return t;var r={};if((e.match(Sr)||[]).forEach(function(a){var n=we(a);switch(n[0].replace(jc,"<")){case"<?xml":break;case"<Types":t.xmlns=n["xmlns"+(n[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[n.Extension]=n.ContentType;break;case"<Override":t[Vn[n.ContentType]]!==void 0&&t[Vn[n.ContentType]].push(n.PartName);break}}),t.xmlns!==cf.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}var jt={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function w0(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function va(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var a={};return(e.match(Sr)||[]).forEach(function(n){var s=we(n);if(s[0]==="<Relationship"){var i={};i.Type=s.Type,i.Target=s.Target,i.Id=s.Id,s.TargetMode&&(i.TargetMode=s.TargetMode);var c=s.TargetMode==="External"?s.Target:oa(s.Target,t);r[c]=i,a[s.Id]=i}}),r["!id"]=a,r}var $f="application/vnd.oasis.opendocument.spreadsheet";function Gf(e,t){for(var r=G0(e),a,n;a=wa.exec(r);)switch(a[3]){case"manifest":break;case"file-entry":if(n=we(a[0],!1),n.path=="/"&&n.type!==$f)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw a}}var pa=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],Xf=function(){for(var e=new Array(pa.length),t=0;t<pa.length;++t){var r=pa[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function ni(e){var t={};e=Je(e);for(var r=0;r<pa.length;++r){var a=pa[r],n=e.match(Xf[r]);n!=null&&n.length>0&&(t[a[1]]=Me(n[1])),a[2]==="date"&&t[a[1]]&&(t[a[1]]=dr(t[a[1]]))}return t}var zf=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function si(e,t,r,a){var n=[];if(typeof e=="string")n=In(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map(function(o){return{v:o}}));var i=typeof t=="string"?In(t,a).map(function(o){return o.v}):t,c=0,l=0;if(i.length>0)for(var f=0;f!==n.length;f+=2){switch(l=+n[f+1].v,n[f].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=l,r.SheetNames=i.slice(c,c+l);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=l,r.DefinedNames=i.slice(c,c+l);break;case"Charts":case"Diagramme":r.Chartsheets=l,r.ChartNames=i.slice(c,c+l);break}c+=l}}function Kf(e,t,r){var a={};return t||(t={}),e=Je(e),zf.forEach(function(n){var s=(e.match(Ea(n[0]))||[])[1];switch(n[2]){case"string":s&&(t[n[1]]=Me(s));break;case"bool":t[n[1]]=s==="true";break;case"raw":var i=e.match(new RegExp("<"+n[0]+"[^>]*>([\\s\\S]*?)</"+n[0]+">"));i&&i.length>0&&(a[n[1]]=i[1]);break}}),a.HeadingPairs&&a.TitlesOfParts&&si(a.HeadingPairs,a.TitlesOfParts,t,r),t}var Yf=/<[^>]+>[^<]*/g;function jf(e,t){var r={},a="",n=e.match(Yf);if(n)for(var s=0;s!=n.length;++s){var i=n[s],c=we(i);switch(c[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Me(c.name);break;case"</property>":a=null;break;default:if(i.indexOf("<vt:")===0){var l=i.split(">"),f=l[0].slice(4),o=l[1];switch(f){case"lpstr":case"bstr":case"lpwstr":r[a]=Me(o);break;case"bool":r[a]=qe(o);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(o,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(o);break;case"filetime":case"date":r[a]=dr(o);break;case"cy":case"error":r[a]=Me(o);break;default:if(f.slice(-1)=="/")break;t.WTF&&typeof console<"u"&&console.warn("Unexpected",i,f,l)}}else if(i.slice(0,2)!=="</"){if(t.WTF)throw new Error(i)}}}return r}var Jf={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},h0;function qf(e,t,r){h0||(h0=V0(Jf)),t=h0[t]||t,e[t]=r}function Z0(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function ii(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function ci(e,t,r){var a=e.read_shift(0,"lpwstr");return a}function fi(e,t,r){return t===31?ci(e):ii(e,t,r)}function A0(e,t,r){return fi(e,t,r===!1?0:4)}function Zf(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return fi(e,t,0)}function Qf(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(Dr,""),e.l-n&2&&(e.l+=2)}return r}function eo(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(Dr,"");return r}function ro(e){var t=e.l,r=Ja(e,ti);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var a=Ja(e,br);return[r,a]}function to(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(ro(e));return r}function Wn(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,t===1200?"utf16le":"utf8").replace(Dr,"").replace(fa,"!"),t===1200&&i%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),a}function oi(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function ao(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function Ja(e,t,r){var a=e.read_shift(2),n,s=r||{};if(e.l+=2,t!==Un&&a!==t&&Pf.indexOf(t)===-1&&!((t&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+t+" saw "+a);switch(t===Un?a:t){case 2:return n=e.read_shift(2,"i"),s.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i"),n;case 11:return e.read_shift(4)!==0;case 19:return n=e.read_shift(4),n;case 30:return ii(e,a,4).replace(Dr,"");case 31:return ci(e);case 64:return Z0(e);case 65:return oi(e);case 71:return ao(e);case 80:return A0(e,a,!s.raw).replace(Dr,"");case 81:return Zf(e,a).replace(Dr,"");case 4108:return to(e);case 4126:case 4127:return a==4127?Qf(e):eo(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+a)}}function $n(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,c=0,l=-1,f={};for(i=0;i!=n;++i){var o=e.read_shift(4),u=e.read_shift(4);s[i]=[o,u+r]}s.sort(function(O,_){return O[1]-_[1]});var v={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var h=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,h=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],h=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],h=!1);break}if((!t||i==0)&&e.l<=s[i][1]&&(h=!1,e.l=s[i][1]),h)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var x=t[s[i][0]];if(v[x.n]=Ja(e,x.t,{raw:!0}),x.p==="version"&&(v[x.n]=String(v[x.n]>>16)+"."+("0000"+String(v[x.n]&65535)).slice(-4)),x.n=="CodePage")switch(v[x.n]){case 0:v[x.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:at(c=v[x.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+v[x.n])}}else if(s[i][0]===1){if(c=v.CodePage=Ja(e,q0),at(c),l!==-1){var d=e.l;e.l=s[l][1],f=Wn(e,c),e.l=d}}else if(s[i][0]===0){if(c===0){l=i,e.l=s[i+1][1];continue}f=Wn(e,c)}else{var p=f[s[i][0]],A;switch(e[e.l]){case 65:e.l+=4,A=oi(e);break;case 30:e.l+=4,A=A0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,A=A0(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,A=e.read_shift(4,"i");break;case 19:e.l+=4,A=e.read_shift(4);break;case 5:e.l+=4,A=e.read_shift(8,"f");break;case 11:e.l+=4,A=nr(e,4);break;case 64:e.l+=4,A=dr(Z0(e));break;default:throw new Error("unparsed value: "+e[e.l])}v[p]=A}}return e.l=r+a,v}function Gn(e,t,r){var a=e.content;if(!a)return{};pr(a,0);var n,s,i,c,l=0;a.chk("feff","Byte Order: "),a.read_shift(2);var f=a.read_shift(4),o=a.read_shift(16);if(o!==Pe.utils.consts.HEADER_CLSID&&o!==r)throw new Error("Bad PropertySet CLSID "+o);if(n=a.read_shift(4),n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),c=a.read_shift(4),n===1&&c!==a.l)throw new Error("Length mismatch: "+c+" !== "+a.l);n===2&&(i=a.read_shift(16),l=a.read_shift(4));var u=$n(a,t),v={SystemIdentifier:f};for(var h in u)v[h]=u[h];if(v.FMTID=s,n===1)return v;if(l-a.l==2&&(a.l+=2),a.l!==l)throw new Error("Length mismatch 2: "+a.l+" !== "+l);var x;try{x=$n(a,null)}catch{}for(h in x)v[h]=x[h];return v.FMTID=[s,i],v}function kt(e,t){return e.read_shift(t),null}function no(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function nr(e,t){return e.read_shift(t)===1}function fr(e){return e.read_shift(2,"u")}function li(e,t){return no(e,t,fr)}function so(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function Da(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(n="dbcs-cont")}else r.biff==12&&(n="wstr");r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return i}function io(e){var t=e.read_shift(2),r=e.read_shift(1),a=r&4,n=r&8,s=1+(r&1),i=0,c,l={};n&&(i=e.read_shift(2)),a&&(c=e.read_shift(4));var f=s==2?"dbcs-cont":"sbcs-cont",o=t===0?"":e.read_shift(t,f);return n&&(e.l+=4*i),a&&(e.l+=c),l.t=o,n||(l.raw="<t>"+l.t+"</t>",l.r=l.t),l}function Ut(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var n=e.read_shift(1);return n===0?a=e.read_shift(t,"sbcs-cont"):a=e.read_shift(t,"dbcs-cont"),a}function Ia(e,t,r){var a=e.read_shift(r&&r.biff==2?1:2);return a===0?(e.l++,""):Ut(e,a,r)}function Xt(e,t,r){if(r.biff>5)return Ia(e,t,r);var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function co(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function fo(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(Dr,"");return a&&(e.l+=24),n}function oo(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(n===0)return r+a.replace(/\\/g,"/");var s=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace(Dr,"");return r+i}function lo(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return fo(e);case"0303000000000000c000000000000046":return oo(e);default:throw new Error("Unsupported Moniker "+r)}}function Ua(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace(Dr,""):"";return r}function uo(e,t){var r=e.l+t,a=e.read_shift(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,c,l,f="",o,u;n&16&&(s=Ua(e,r-e.l)),n&128&&(i=Ua(e,r-e.l)),(n&257)===257&&(c=Ua(e,r-e.l)),(n&257)===1&&(l=lo(e,r-e.l)),n&8&&(f=Ua(e,r-e.l)),n&32&&(o=e.read_shift(16)),n&64&&(u=Z0(e)),e.l=r;var v=i||c||l||"";v&&f&&(v+="#"+f),v||(v="#"+f),n&2&&v.charAt(0)=="/"&&v.charAt(1)!="/"&&(v="file://"+v);var h={Target:v};return o&&(h.guid=o),u&&(h.time=u),s&&(h.Tooltip=s),h}function ui(e){var t=e.read_shift(1),r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[t,r,a,n]}function hi(e,t){var r=ui(e);return r[3]=0,r}function ht(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return{r:t,c:r,ixfe:a}}function ho(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function xo(e,t,r){return t===0?"":Xt(e,t,r)}function vo(e,t,r){var a=r.biff>8?4:2,n=e.read_shift(a),s=e.read_shift(a,"i"),i=e.read_shift(a,"i");return[n,s,i]}function xi(e){var t=e.read_shift(2),r=J0(e);return[t,r]}function po(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Da(e,t,r),s=e.read_shift(2);if(a-=e.l,s!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}function t0(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:t},e:{c:n,r}}}function di(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:t},e:{c:n,r}}}var go=di;function vi(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function mo(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function _o(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function _r(e){e.l+=2,e.l+=e.read_shift(2)}var To={0:_r,4:_r,5:_r,6:_r,7:_o,8:_r,9:_r,10:_r,11:_r,12:_r,13:mo,14:_r,15:_r,16:_r,17:_r,18:_r,19:_r,20:_r,21:vi};function ko(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(To[n](e,r-e.l))}catch{return e.l=r,a}}return e.l!=r&&(e.l=r),a}function Ha(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function Eo(e,t){return t===0||e.read_shift(2),1200}function wo(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=Xt(e,0,r);return e.read_shift(t+a-e.l),n}function Ao(e,t,r){var a=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function Fo(e,t,r){var a=e.read_shift(4),n=e.read_shift(1)&3,s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=Da(e,0,r);return i.length===0&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}function So(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(io(e));return s.Count=a,s.Unique=n,s}function yo(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function Co(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,a&7&&(t.level=a&7),a&32&&(t.hidden=!0),a&64&&(t.hpt=r/20),t}function Oo(e){var t=ho(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function Do(e){return e.read_shift(2),e.read_shift(4)}function Xn(e,t,r){var a=0;r&&r.biff==2||(a=e.read_shift(2));var n=e.read_shift(2);r&&r.biff==2&&(a=1-(n>>15),n&=32767);var s={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[s,n]}function Io(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=e.read_shift(2),l=e.read_shift(2),f=e.read_shift(2);return{Pos:[t,r],Dim:[a,n],Flags:s,CurTab:i,FirstTab:c,Selected:l,TabRatio:f}}function Ro(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var a=e.read_shift(2);return{RTL:a&64}}function No(){}function bo(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=Da(e,0,r),a}function Po(e){var t=ht(e);return t.isst=e.read_shift(4),t}function Lo(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=e.l+t,n=ht(e);r.biff==2&&e.l++;var s=Ia(e,a-e.l,r);return n.val=s,n}function Bo(e,t,r){var a=e.read_shift(2),n=Xt(e,0,r);return[a,n]}var Mo=Xt;function zn(e,t,r){var a=e.l+t,n=r.biff==8||!r.biff?4:2,s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(2),l=e.read_shift(2);return e.l=a,{s:{r:s,c},e:{r:i,c:l}}}function Uo(e){var t=e.read_shift(2),r=e.read_shift(2),a=xi(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}function Ho(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(xi(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}function Vo(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}function Wo(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),l=e.read_shift(2);return n.patternType=Mf[c>>26],a.cellStyles&&(n.alc=s&7,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=i&15,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=c&127,n.icvBottom=c>>7&127,n.icvDiag=c>>14&127,n.dgDiag=c>>21&15,n.icvFore=l&127,n.icvBack=l>>7&127,n.fsxButton=l>>14&1),n}function $o(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,t-=6,a.data=Wo(e,t,a.fStyle,r),a}function Go(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function Kn(e,t,r){var a=ht(e);(r.biff==2||t==9)&&++e.l;var n=so(e);return a.val=n,a.t=n===!0||n===!1?"b":"e",a}function Xo(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=ht(e),n=Er(e);return a.val=n,a}var Yn=xo;function zo(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,s==1025||s==14849)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Ut(e,s),c=[];a>e.l;)c.push(Ia(e));return[s,n,i,c]}function jn(e,t,r){var a=e.read_shift(2),n,s={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return r.sbcch===14849&&(n=po(e,t-2,r)),s.body=n||e.read_shift(t-2),typeof n=="string"&&(s.Name=n),s}var Ko=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Jn(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(r&&r.biff==2?1:2),l=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),l=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var f=Ut(e,i,r);n&32&&(f=Ko[f.charCodeAt(0)]);var o=a-e.l;r&&r.biff==2&&--o;var u=a==e.l||c===0||!(o>0)?[]:Ch(e,o,r,c);return{chKey:s,Name:f,itab:l,rgce:u}}function pi(e,t,r){if(r.biff<8)return Yo(e,t,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);s--!==0;)a.push(vo(e,r.biff>8?12:6,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Yo(e,t,r){e[e.l+1]==3&&e[e.l]++;var a=Da(e,t,r);return a.charCodeAt(0)==3?a.slice(1):a}function jo(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2),s=Ut(e,a,r),i=Ut(e,n,r);return[s,i]}function Jo(e,t,r){var a=di(e);e.l++;var n=e.read_shift(1);return t-=8,[Oh(e,t,r),n,a]}function qn(e,t,r){var a=go(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,Sh(e,t,r)]}function qo(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,a=e.read_shift(4);return[t,r,a]}function Zo(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=Xt(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},c,i,s]}}function Qo(e,t,r){return Zo(e,t,r)}function el(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(t0(e));return r}function rl(e,t,r){if(r&&r.biff<8)return al(e,t,r);var a=vi(e),n=ko(e,t-22,a[1]);return{cmo:a,ft:n}}var tl={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function al(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((tl[a]||Fr)(e,t,r)),{cmo:[n,a,s],ft:i}}function nl(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1],i;[0,5,7,11,12,14].indexOf(s)==-1?e.l+=6:i=co(e,6,r);var c=e.read_shift(2);e.read_shift(2),fr(e,2);var l=e.read_shift(2);e.l+=l;for(var f=1;f<e.lens.length-1;++f){if(e.l-a!=e.lens[f])throw new Error("TxO: bad continue record");var o=e[e.l],u=Ut(e,e.lens[f+1]-e.lens[f]-1);if(n+=u,n.length>=(o?c:2*c))break}if(n.length!==c&&n.length!==c*2)throw new Error("cchText: "+c+" != "+n.length);return e.l=a+t,{t:n}}catch{return e.l=a+t,{t:n}}}function sl(e,t){var r=t0(e);e.l+=16;var a=uo(e,t-24);return[r,a]}function il(e,t){e.read_shift(2);var r=t0(e),a=e.read_shift((t-10)/2,"dbcs-cont");return a=a.replace(Dr,""),[r,a]}function cl(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=Hn[r]||r,r=e.read_shift(2),t[1]=Hn[r]||r,t}function fl(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(hi(e));return r}function ol(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(hi(e));return r}function ll(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function gi(e,t,r){if(!r.cellStyles)return Fr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(a),l=e.read_shift(2);a==2&&(e.l+=2);var f={s:n,e:s,w:i,ixfe:c,flags:l};return(r.biff>=5||!r.biff)&&(f.level=l>>8&7),f}function ul(e,t){var r={};return t<32||(e.l+=16,r.header=Er(e),r.footer=Er(e),e.l+=2),r}function hl(e,t,r){var a={area:!1};if(r.biff!=5)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,n&16&&(a.area=!0),a}var xl=ht,dl=li,vl=Ia;function pl(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function gl(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var a=ht(e);++e.l;var n=Xt(e,t-7,r);return a.t="str",a.val=n,a}function ml(e){var t=ht(e);++e.l;var r=Er(e);return t.t="n",t.val=r,t}function _l(e){var t=ht(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function Tl(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function kl(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function El(e,t,r){var a=e.l+t,n=ht(e),s=e.read_shift(2),i=Ut(e,s,r);return e.l=a,n.t="str",n.val=i,n}var wl=[2,3,48,49,131,139,140,245],Zn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=V0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(c,l){var f=[],o=yt(1);switch(l.type){case"base64":o=tt(Mr(c));break;case"binary":o=tt(c);break;case"buffer":case"array":o=c;break}pr(o,0);var u=o.read_shift(1),v=!!(u&136),h=!1,x=!1;switch(u){case 2:break;case 3:break;case 48:h=!0,v=!0;break;case 49:h=!0,v=!0;break;case 131:break;case 139:break;case 140:x=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+u.toString(16))}var d=0,p=521;u==2&&(d=o.read_shift(2)),o.l+=3,u!=2&&(d=o.read_shift(4)),d>1048576&&(d=1e6),u!=2&&(p=o.read_shift(2));var A=o.read_shift(2),O=l.codepage||1252;u!=2&&(o.l+=16,o.read_shift(1),o[o.l]!==0&&(O=e[o[o.l]]),o.l+=1,o.l+=2),x&&(o.l+=36);for(var _=[],b={},U=Math.min(o.length,u==2?521:p-10-(h?264:0)),R=x?32:11;o.l<U&&o[o.l]!=13;)switch(b={},b.name=T0.utils.decode(O,o.slice(o.l,o.l+R)).replace(/[\u0000\r\n].*$/g,""),o.l+=R,b.type=String.fromCharCode(o.read_shift(1)),u!=2&&!x&&(b.offset=o.read_shift(4)),b.len=o.read_shift(1),u==2&&(b.offset=o.read_shift(2)),b.dec=o.read_shift(1),b.name.length&&_.push(b),u!=2&&(o.l+=x?13:14),b.type){case"B":(!h||b.len!=8)&&l.WTF&&console.log("Skipping "+b.name+":"+b.type);break;case"G":case"P":l.WTF&&console.log("Skipping "+b.name+":"+b.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+b.type)}if(o[o.l]!==13&&(o.l=p-1),o.read_shift(1)!==13)throw new Error("DBF Terminator not found "+o.l+" "+o[o.l]);o.l=p;var F=0,X=0;for(f[0]=[],X=0;X!=_.length;++X)f[0][X]=_[X].name;for(;d-- >0;){if(o[o.l]===42){o.l+=A;continue}for(++o.l,f[++F]=[],X=0,X=0;X!=_.length;++X){var I=o.slice(o.l,o.l+_[X].len);o.l+=_[X].len,pr(I,0);var G=T0.utils.decode(O,I);switch(_[X].type){case"C":G.trim().length&&(f[F][X]=G.replace(/\s+$/,""));break;case"D":G.length===8?f[F][X]=new Date(+G.slice(0,4),+G.slice(4,6)-1,+G.slice(6,8)):f[F][X]=G;break;case"F":f[F][X]=parseFloat(G.trim());break;case"+":case"I":f[F][X]=x?I.read_shift(-4,"i")^2147483648:I.read_shift(4,"i");break;case"L":switch(G.trim().toUpperCase()){case"Y":case"T":f[F][X]=!0;break;case"N":case"F":f[F][X]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+G+"|")}break;case"M":if(!v)throw new Error("DBF Unexpected MEMO for type "+u.toString(16));f[F][X]="##MEMO##"+(x?parseInt(G.trim(),10):I.read_shift(4));break;case"N":G=G.replace(/\u0000/g,"").trim(),G&&G!="."&&(f[F][X]=+G||0);break;case"@":f[F][X]=new Date(I.read_shift(-8,"f")-621356832e5);break;case"T":f[F][X]=new Date((I.read_shift(4)-2440588)*864e5+I.read_shift(4));break;case"Y":f[F][X]=I.read_shift(4,"i")/1e4+I.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":f[F][X]=-I.read_shift(-8,"f");break;case"B":if(h&&_[X].len==8){f[F][X]=I.read_shift(8,"f");break}case"G":case"P":I.l+=_[X].len;break;case"0":if(_[X].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+_[X].type)}}}if(u!=2&&o.l<o.length&&o[o.l++]!=26)throw new Error("DBF EOF Marker missing "+(o.l-1)+" of "+o.length+" "+o[o.l-1].toString(16));return l&&l.sheetRows&&(f=f.slice(0,l.sheetRows)),l.DBF=_,f}function a(c,l){var f=l||{};f.dateNF||(f.dateNF="yyyymmdd");var o=aa(r(c,f),f);return o["!cols"]=f.DBF.map(function(u){return{wch:u.len,DBF:u}}),delete f.DBF,o}function n(c,l){try{return Ot(a(c,l),l)}catch(f){if(l&&l.WTF)throw f}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function i(c,l){var f=l||{};if(+f.codepage>=0&&at(+f.codepage),f.type=="string")throw new Error("Cannot write DBF to JS string");var o=k0(),u=I0(c,{header:1,raw:!0,cellDates:!0}),v=u[0],h=u.slice(1),x=c["!cols"]||[],d=0,p=0,A=0,O=1;for(d=0;d<v.length;++d){if(((x[d]||{}).DBF||{}).name){v[d]=x[d].DBF.name,++A;continue}if(v[d]!=null){if(++A,typeof v[d]=="number"&&(v[d]=v[d].toString(10)),typeof v[d]!="string")throw new Error("DBF Invalid column name "+v[d]+" |"+typeof v[d]+"|");if(v.indexOf(v[d])!==d){for(p=0;p<1024;++p)if(v.indexOf(v[d]+"_"+p)==-1){v[d]+="_"+p;break}}}}var _=tr(c["!ref"]),b=[],U=[],R=[];for(d=0;d<=_.e.c-_.s.c;++d){var F="",X="",I=0,G=[];for(p=0;p<h.length;++p)h[p][d]!=null&&G.push(h[p][d]);if(G.length==0||v[d]==null){b[d]="?";continue}for(p=0;p<G.length;++p){switch(typeof G[p]){case"number":X="B";break;case"string":X="C";break;case"boolean":X="L";break;case"object":X=G[p]instanceof Date?"D":"C";break;default:X="C"}I=Math.max(I,String(G[p]).length),F=F&&F!=X?"C":X}I>250&&(I=250),X=((x[d]||{}).DBF||{}).type,X=="C"&&x[d].DBF.len>I&&(I=x[d].DBF.len),F=="B"&&X=="N"&&(F="N",R[d]=x[d].DBF.dec,I=x[d].DBF.len),U[d]=F=="C"||X=="N"?I:s[F]||0,O+=U[d],b[d]=F}var Y=o.next(32);for(Y.write_shift(4,318902576),Y.write_shift(4,h.length),Y.write_shift(2,296+32*A),Y.write_shift(2,O),d=0;d<4;++d)Y.write_shift(4,0);for(Y.write_shift(4,0|(+t[ps]||3)<<8),d=0,p=0;d<v.length;++d)if(v[d]!=null){var P=o.next(32),te=(v[d].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);P.write_shift(1,te,"sbcs"),P.write_shift(1,b[d]=="?"?"C":b[d],"sbcs"),P.write_shift(4,p),P.write_shift(1,U[d]||s[b[d]]||0),P.write_shift(1,R[d]||0),P.write_shift(1,2),P.write_shift(4,0),P.write_shift(1,0),P.write_shift(4,0),P.write_shift(4,0),p+=U[d]||s[b[d]]||0}var _e=o.next(264);for(_e.write_shift(4,13),d=0;d<65;++d)_e.write_shift(4,0);for(d=0;d<h.length;++d){var ie=o.next(O);for(ie.write_shift(1,0),p=0;p<v.length;++p)if(v[p]!=null)switch(b[p]){case"L":ie.write_shift(1,h[d][p]==null?63:h[d][p]?84:70);break;case"B":ie.write_shift(8,h[d][p]||0,"f");break;case"N":var me="0";for(typeof h[d][p]=="number"&&(me=h[d][p].toFixed(R[p]||0)),A=0;A<U[p]-me.length;++A)ie.write_shift(1,32);ie.write_shift(1,me,"sbcs");break;case"D":h[d][p]?(ie.write_shift(4,("0000"+h[d][p].getFullYear()).slice(-4),"sbcs"),ie.write_shift(2,("00"+(h[d][p].getMonth()+1)).slice(-2),"sbcs"),ie.write_shift(2,("00"+h[d][p].getDate()).slice(-2),"sbcs")):ie.write_shift(8,"00000000","sbcs");break;case"C":var pe=String(h[d][p]!=null?h[d][p]:"").slice(0,U[p]);for(ie.write_shift(1,pe,"sbcs"),A=0;A<U[p]-pe.length;++A)ie.write_shift(1,32);break}}return o.next(1).write_shift(1,26),o.end()}return{to_workbook:n,to_sheet:a,from_sheet:i}}(),Al=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+lt(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(v,h){var x=e[h];return typeof x=="number"?dn(x):x},a=function(v,h,x){var d=h.charCodeAt(0)-32<<4|x.charCodeAt(0)-48;return d==59?v:dn(d)};e["|"]=254;function n(v,h){switch(h.type){case"base64":return s(Mr(v),h);case"binary":return s(v,h);case"buffer":return s(be&&Buffer.isBuffer(v)?v.toString("binary"):Vt(v),h);case"array":return s(Mt(v),h)}throw new Error("Unrecognized type "+h.type)}function s(v,h){var x=v.split(/[\n\r]+/),d=-1,p=-1,A=0,O=0,_=[],b=[],U=null,R={},F=[],X=[],I=[],G=0,Y;for(+h.codepage>=0&&at(+h.codepage);A!==x.length;++A){G=0;var P=x[A].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),te=P.replace(/;;/g,"\0").split(";").map(function(D){return D.replace(/\u0000/g,";")}),_e=te[0],ie;if(P.length>0)switch(_e){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":te[1].charAt(0)=="P"&&b.push(P.slice(3).replace(/;;/g,";"));break;case"C":var me=!1,pe=!1,Ge=!1,K=!1,ke=-1,Ae=-1;for(O=1;O<te.length;++O)switch(te[O].charAt(0)){case"A":break;case"X":p=parseInt(te[O].slice(1))-1,pe=!0;break;case"Y":for(d=parseInt(te[O].slice(1))-1,pe||(p=0),Y=_.length;Y<=d;++Y)_[Y]=[];break;case"K":ie=te[O].slice(1),ie.charAt(0)==='"'?ie=ie.slice(1,ie.length-1):ie==="TRUE"?ie=!0:ie==="FALSE"?ie=!1:isNaN(st(ie))?isNaN(Qt(ie).getDate())||(ie=dr(ie)):(ie=st(ie),U!==null&&ra(U)&&(ie=r0(ie))),me=!0;break;case"E":K=!0;var y=qt(te[O].slice(1),{r:d,c:p});_[d][p]=[_[d][p],y];break;case"S":Ge=!0,_[d][p]=[_[d][p],"S5S"];break;case"G":break;case"R":ke=parseInt(te[O].slice(1))-1;break;case"C":Ae=parseInt(te[O].slice(1))-1;break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+P)}if(me&&(_[d][p]&&_[d][p].length==2?_[d][p][0]=ie:_[d][p]=ie,U=null),Ge){if(K)throw new Error("SYLK shared formula cannot have own formula");var V=ke>-1&&_[ke][Ae];if(!V||!V[1])throw new Error("SYLK shared formula cannot find base");_[d][p][1]=yi(V[1],{r:d-ke,c:p-Ae})}break;case"F":var N=0;for(O=1;O<te.length;++O)switch(te[O].charAt(0)){case"X":p=parseInt(te[O].slice(1))-1,++N;break;case"Y":for(d=parseInt(te[O].slice(1))-1,Y=_.length;Y<=d;++Y)_[Y]=[];break;case"M":G=parseInt(te[O].slice(1))/20;break;case"F":break;case"G":break;case"P":U=b[parseInt(te[O].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(I=te[O].slice(1).split(" "),Y=parseInt(I[0],10);Y<=parseInt(I[1],10);++Y)G=parseInt(I[2],10),X[Y-1]=G===0?{hidden:!0}:{wch:G},ea(X[Y-1]);break;case"C":p=parseInt(te[O].slice(1))-1,X[p]||(X[p]={});break;case"R":d=parseInt(te[O].slice(1))-1,F[d]||(F[d]={}),G>0?(F[d].hpt=G,F[d].hpx=Sa(G)):G===0&&(F[d].hidden=!0);break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+P)}N<1&&(U=null);break;default:if(h&&h.WTF)throw new Error("SYLK bad record "+P)}}return F.length>0&&(R["!rows"]=F),X.length>0&&(R["!cols"]=X),h&&h.sheetRows&&(_=_.slice(0,h.sheetRows)),[_,R]}function i(v,h){var x=n(v,h),d=x[0],p=x[1],A=aa(d,h);return lt(p).forEach(function(O){A[O]=p[O]}),A}function c(v,h){return Ot(i(v,h),h)}function l(v,h,x,d){var p="C;Y"+(x+1)+";X"+(d+1)+";K";switch(v.t){case"n":p+=v.v||0,v.f&&!v.F&&(p+=";E"+l1(v.f,{r:x,c:d}));break;case"b":p+=v.v?"TRUE":"FALSE";break;case"e":p+=v.w||v.v;break;case"d":p+='"'+(v.w||v.v)+'"';break;case"s":p+='"'+v.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return p}function f(v,h){h.forEach(function(x,d){var p="F;W"+(d+1)+" "+(d+1)+" ";x.hidden?p+="0":(typeof x.width=="number"&&!x.wpx&&(x.wpx=Za(x.width)),typeof x.wpx=="number"&&!x.wch&&(x.wch=Qa(x.wpx)),typeof x.wch=="number"&&(p+=Math.round(x.wch))),p.charAt(p.length-1)!=" "&&v.push(p)})}function o(v,h){h.forEach(function(x,d){var p="F;";x.hidden?p+="M0;":x.hpt?p+="M"+20*x.hpt+";":x.hpx&&(p+="M"+20*Ai(x.hpx)+";"),p.length>2&&v.push(p+"R"+(d+1))})}function u(v,h){var x=["ID;PWXL;N;E"],d=[],p=tr(v["!ref"]),A,O=Array.isArray(v),_=`\r
`;x.push("P;PGeneral"),x.push("F;P0;DG0G8;M255"),v["!cols"]&&f(x,v["!cols"]),v["!rows"]&&o(x,v["!rows"]),x.push("B;Y"+(p.e.r-p.s.r+1)+";X"+(p.e.c-p.s.c+1)+";D"+[p.s.c,p.s.r,p.e.c,p.e.r].join(" "));for(var b=p.s.r;b<=p.e.r;++b)for(var U=p.s.c;U<=p.e.c;++U){var R=ye({r:b,c:U});A=O?(v[b]||[])[U]:v[R],!(!A||A.v==null&&(!A.f||A.F))&&d.push(l(A,v,b,U))}return x.join(_)+_+d.join(_)+_+"E"+_}return{to_workbook:c,to_sheet:i,from_sheet:u}}(),Fl=function(){function e(s,i){switch(i.type){case"base64":return t(Mr(s),i);case"binary":return t(s,i);case"buffer":return t(be&&Buffer.isBuffer(s)?s.toString("binary"):Vt(s),i);case"array":return t(Mt(s),i)}throw new Error("Unrecognized type "+i.type)}function t(s,i){for(var c=s.split(`
`),l=-1,f=-1,o=0,u=[];o!==c.length;++o){if(c[o].trim()==="BOT"){u[++l]=[],f=0;continue}if(!(l<0)){var v=c[o].trim().split(","),h=v[0],x=v[1];++o;for(var d=c[o]||"";(d.match(/["]/g)||[]).length&1&&o<c.length-1;)d+=`
`+c[++o];switch(d=d.trim(),+h){case-1:if(d==="BOT"){u[++l]=[],f=0;continue}else if(d!=="EOD")throw new Error("Unrecognized DIF special command "+d);break;case 0:d==="TRUE"?u[l][f]=!0:d==="FALSE"?u[l][f]=!1:isNaN(st(x))?isNaN(Qt(x).getDate())?u[l][f]=x:u[l][f]=dr(x):u[l][f]=st(x),++f;break;case 1:d=d.slice(1,d.length-1),d=d.replace(/""/g,'"'),d&&d.match(/^=".*"$/)&&(d=d.slice(2,-1)),u[l][f++]=d!==""?d:null;break}if(d==="EOD")break}}return i&&i.sheetRows&&(u=u.slice(0,i.sheetRows)),u}function r(s,i){return aa(e(s,i),i)}function a(s,i){return Ot(r(s,i),i)}var n=function(){var s=function(l,f,o,u,v){l.push(f),l.push(o+","+u),l.push('"'+v.replace(/"/g,'""')+'"')},i=function(l,f,o,u){l.push(f+","+o),l.push(f==1?'"'+u.replace(/"/g,'""')+'"':u)};return function(l){var f=[],o=tr(l["!ref"]),u,v=Array.isArray(l);s(f,"TABLE",0,1,"sheetjs"),s(f,"VECTORS",0,o.e.r-o.s.r+1,""),s(f,"TUPLES",0,o.e.c-o.s.c+1,""),s(f,"DATA",0,0,"");for(var h=o.s.r;h<=o.e.r;++h){i(f,-1,0,"BOT");for(var x=o.s.c;x<=o.e.c;++x){var d=ye({r:h,c:x});if(u=v?(l[h]||[])[x]:l[d],!u){i(f,1,0,"");continue}switch(u.t){case"n":var p=u.w;!p&&u.v!=null&&(p=u.v),p==null?u.f&&!u.F?i(f,1,0,"="+u.f):i(f,1,0,""):i(f,0,p,"V");break;case"b":i(f,0,u.v?1:0,u.v?"TRUE":"FALSE");break;case"s":i(f,1,0,isNaN(u.v)?u.v:'="'+u.v+'"');break;case"d":u.w||(u.w=zr(u.z||Ce[14],Ir(dr(u.v)))),i(f,0,u.w,"V");break;default:i(f,1,0,"")}}}i(f,-1,0,"EOD");var A=`\r
`,O=f.join(A);return O}}();return{to_workbook:a,to_sheet:r,from_sheet:n}}(),Sl=function(){function e(u){return u.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(u){return u.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(u,v){for(var h=u.split(`
`),x=-1,d=-1,p=0,A=[];p!==h.length;++p){var O=h[p].trim().split(":");if(O[0]==="cell"){var _=Or(O[1]);if(A.length<=_.r)for(x=A.length;x<=_.r;++x)A[x]||(A[x]=[]);switch(x=_.r,d=_.c,O[2]){case"t":A[x][d]=e(O[3]);break;case"v":A[x][d]=+O[3];break;case"vtf":var b=O[O.length-1];case"vtc":switch(O[3]){case"nl":A[x][d]=!!+O[4];break;default:A[x][d]=+O[4];break}O[2]=="vtf"&&(A[x][d]=[A[x][d],b])}}}return v&&v.sheetRows&&(A=A.slice(0,v.sheetRows)),A}function a(u,v){return aa(r(u,v),v)}function n(u,v){return Ot(a(u,v),v)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,c=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),l="--SocialCalcSpreadsheetControlSave--";function f(u){if(!u||!u["!ref"])return"";for(var v=[],h=[],x,d="",p=ta(u["!ref"]),A=Array.isArray(u),O=p.s.r;O<=p.e.r;++O)for(var _=p.s.c;_<=p.e.c;++_)if(d=ye({r:O,c:_}),x=A?(u[O]||[])[_]:u[d],!(!x||x.v==null||x.t==="z")){switch(h=["cell",d,"t"],x.t){case"s":case"str":h.push(t(x.v));break;case"n":x.f?(h[2]="vtf",h[3]="n",h[4]=x.v,h[5]=t(x.f)):(h[2]="v",h[3]=x.v);break;case"b":h[2]="vt"+(x.f?"f":"c"),h[3]="nl",h[4]=x.v?"1":"0",h[5]=t(x.f||(x.v?"TRUE":"FALSE"));break;case"d":var b=Ir(dr(x.v));h[2]="vtc",h[3]="nd",h[4]=""+b,h[5]=x.w||zr(x.z||Ce[14],b);break;case"e":continue}v.push(h.join(":"))}return v.push("sheet:c:"+(p.e.c-p.s.c+1)+":r:"+(p.e.r-p.s.r+1)+":tvf:1"),v.push("valueformat:1:text-wiki"),v.join(`
`)}function o(u){return[s,i,c,i,f(u),l].join(`
`)}return{to_workbook:n,to_sheet:a,from_sheet:o}}(),Aa=function(){function e(o,u,v,h,x){x.raw?u[v][h]=o:o===""||(o==="TRUE"?u[v][h]=!0:o==="FALSE"?u[v][h]=!1:isNaN(st(o))?isNaN(Qt(o).getDate())?u[v][h]=o:u[v][h]=dr(o):u[v][h]=st(o))}function t(o,u){var v=u||{},h=[];if(!o||o.length===0)return h;for(var x=o.split(/[\r\n]/),d=x.length-1;d>=0&&x[d].length===0;)--d;for(var p=10,A=0,O=0;O<=d;++O)A=x[O].indexOf(" "),A==-1?A=x[O].length:A++,p=Math.max(p,A);for(O=0;O<=d;++O){h[O]=[];var _=0;for(e(x[O].slice(0,p).trim(),h,O,_,v),_=1;_<=(x[O].length-p)/10+1;++_)e(x[O].slice(p+(_-1)*10,p+_*10).trim(),h,O,_,v)}return v.sheetRows&&(h=h.slice(0,v.sheetRows)),h}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(o){for(var u={},v=!1,h=0,x=0;h<o.length;++h)(x=o.charCodeAt(h))==34?v=!v:!v&&x in r&&(u[x]=(u[x]||0)+1);x=[];for(h in u)Object.prototype.hasOwnProperty.call(u,h)&&x.push([u[h],h]);if(!x.length){u=a;for(h in u)Object.prototype.hasOwnProperty.call(u,h)&&x.push([u[h],h])}return x.sort(function(d,p){return d[0]-p[0]||a[d[1]]-a[p[1]]}),r[x.pop()[1]]||44}function s(o,u){var v=u||{},h="",x=v.dense?[]:{},d={s:{c:0,r:0},e:{c:0,r:0}};o.slice(0,4)=="sep="?o.charCodeAt(5)==13&&o.charCodeAt(6)==10?(h=o.charAt(4),o=o.slice(7)):o.charCodeAt(5)==13||o.charCodeAt(5)==10?(h=o.charAt(4),o=o.slice(6)):h=n(o.slice(0,1024)):v&&v.FS?h=v.FS:h=n(o.slice(0,1024));var p=0,A=0,O=0,_=0,b=0,U=h.charCodeAt(0),R=!1,F=0,X=o.charCodeAt(0);o=o.replace(/\r\n/mg,`
`);var I=v.dateNF!=null?Pc(v.dateNF):null;function G(){var Y=o.slice(_,b),P={};if(Y.charAt(0)=='"'&&Y.charAt(Y.length-1)=='"'&&(Y=Y.slice(1,-1).replace(/""/g,'"')),Y.length===0)P.t="z";else if(v.raw)P.t="s",P.v=Y;else if(Y.trim().length===0)P.t="s",P.v=Y;else if(Y.charCodeAt(0)==61)Y.charCodeAt(1)==34&&Y.charCodeAt(Y.length-1)==34?(P.t="s",P.v=Y.slice(2,-1).replace(/""/g,'"')):h1(Y)?(P.t="n",P.f=Y.slice(1)):(P.t="s",P.v=Y);else if(Y=="TRUE")P.t="b",P.v=!0;else if(Y=="FALSE")P.t="b",P.v=!1;else if(!isNaN(O=st(Y)))P.t="n",v.cellText!==!1&&(P.w=Y),P.v=O;else if(!isNaN(Qt(Y).getDate())||I&&Y.match(I)){P.z=v.dateNF||Ce[14];var te=0;I&&Y.match(I)&&(Y=Lc(Y,v.dateNF,Y.match(I)||[]),te=1),v.cellDates?(P.t="d",P.v=dr(Y,te)):(P.t="n",P.v=Ir(dr(Y,te))),v.cellText!==!1&&(P.w=zr(P.z,P.v instanceof Date?Ir(P.v):P.v)),v.cellNF||delete P.z}else P.t="s",P.v=Y;if(P.t=="z"||(v.dense?(x[p]||(x[p]=[]),x[p][A]=P):x[ye({c:A,r:p})]=P),_=b+1,X=o.charCodeAt(_),d.e.c<A&&(d.e.c=A),d.e.r<p&&(d.e.r=p),F==U)++A;else if(A=0,++p,v.sheetRows&&v.sheetRows<=p)return!0}e:for(;b<o.length;++b)switch(F=o.charCodeAt(b)){case 34:X===34&&(R=!R);break;case U:case 10:case 13:if(!R&&G())break e;break}return b-_>0&&G(),x["!ref"]=Le(d),x}function i(o,u){return!(u&&u.PRN)||u.FS||o.slice(0,4)=="sep="||o.indexOf("	")>=0||o.indexOf(",")>=0||o.indexOf(";")>=0?s(o,u):aa(t(o,u),u)}function c(o,u){var v="",h=u.type=="string"?[0,0,0,0]:cn(o,u);switch(u.type){case"base64":v=Mr(o);break;case"binary":v=o;break;case"buffer":u.codepage==65001?v=o.toString("utf8"):(u.codepage,v=be&&Buffer.isBuffer(o)?o.toString("binary"):Vt(o));break;case"array":v=Mt(o);break;case"string":v=o;break;default:throw new Error("Unrecognized type "+u.type)}return h[0]==239&&h[1]==187&&h[2]==191?v=Je(v.slice(3)):u.type!="string"&&u.type!="buffer"&&u.codepage==65001?v=Je(v):u.type=="binary",v.slice(0,19)=="socialcalc:version:"?Sl.to_sheet(u.type=="string"?v:Je(v),u):i(v,u)}function l(o,u){return Ot(c(o,u),u)}function f(o){for(var u=[],v=tr(o["!ref"]),h,x=Array.isArray(o),d=v.s.r;d<=v.e.r;++d){for(var p=[],A=v.s.c;A<=v.e.c;++A){var O=ye({r:d,c:A});if(h=x?(o[d]||[])[A]:o[O],!h||h.v==null){p.push("          ");continue}for(var _=(h.w||(mt(h),h.w)||"").slice(0,10);_.length<10;)_+=" ";p.push(_+(A===0?" ":""))}u.push(p.join(""))}return u.join(`
`)}return{to_workbook:l,to_sheet:c,from_sheet:f}}();function yl(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Al.to_workbook(e,r);return r.WTF=a,n}catch(s){if(r.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return Aa.to_workbook(e,t)}}var ga=function(){function e(y,V,N){if(y){pr(y,y.l||0);for(var D=N.Enum||ke;y.l<y.length;){var J=y.read_shift(2),oe=D[J]||D[65535],de=y.read_shift(2),fe=y.l+de,ee=oe.f&&oe.f(y,de,N);if(y.l=fe,V(ee,oe,J))return}}}function t(y,V){switch(V.type){case"base64":return r(tt(Mr(y)),V);case"binary":return r(tt(y),V);case"buffer":case"array":return r(y,V)}throw"Unsupported type "+V.type}function r(y,V){if(!y)return y;var N=V||{},D=N.dense?[]:{},J="Sheet1",oe="",de=0,fe={},ee=[],Re=[],C={s:{r:0,c:0},e:{r:0,c:0}},Ze=N.sheetRows||0;if(y[2]==0&&(y[3]==8||y[3]==9)&&y.length>=16&&y[14]==5&&y[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(y[2]==2)N.Enum=ke,e(y,function(le,Xe,Ye){switch(Ye){case 0:N.vers=le,le>=4096&&(N.qpro=!0);break;case 6:C=le;break;case 204:le&&(oe=le);break;case 222:oe=le;break;case 15:case 51:N.qpro||(le[1].v=le[1].v.slice(1));case 13:case 14:case 16:Ye==14&&(le[2]&112)==112&&(le[2]&15)>1&&(le[2]&15)<15&&(le[1].z=N.dateNF||Ce[14],N.cellDates&&(le[1].t="d",le[1].v=r0(le[1].v))),N.qpro&&le[3]>de&&(D["!ref"]=Le(C),fe[J]=D,ee.push(J),D=N.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},de=le[3],J=oe||"Sheet"+(de+1),oe="");var Ur=N.dense?(D[le[0].r]||[])[le[0].c]:D[ye(le[0])];if(Ur){Ur.t=le[1].t,Ur.v=le[1].v,le[1].z!=null&&(Ur.z=le[1].z),le[1].f!=null&&(Ur.f=le[1].f);break}N.dense?(D[le[0].r]||(D[le[0].r]=[]),D[le[0].r][le[0].c]=le[1]):D[ye(le[0])]=le[1];break}},N);else if(y[2]==26||y[2]==14)N.Enum=Ae,y[2]==14&&(N.qpro=!0,y.l=0),e(y,function(le,Xe,Ye){switch(Ye){case 204:J=le;break;case 22:le[1].v=le[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(le[3]>de&&(D["!ref"]=Le(C),fe[J]=D,ee.push(J),D=N.dense?[]:{},C={s:{r:0,c:0},e:{r:0,c:0}},de=le[3],J="Sheet"+(de+1)),Ze>0&&le[0].r>=Ze)break;N.dense?(D[le[0].r]||(D[le[0].r]=[]),D[le[0].r][le[0].c]=le[1]):D[ye(le[0])]=le[1],C.e.c<le[0].c&&(C.e.c=le[0].c),C.e.r<le[0].r&&(C.e.r=le[0].r);break;case 27:le[14e3]&&(Re[le[14e3][0]]=le[14e3][1]);break;case 1537:Re[le[0]]=le[1],le[0]==de&&(J=le[1]);break}},N);else throw new Error("Unrecognized LOTUS BOF "+y[2]);if(D["!ref"]=Le(C),fe[oe||J]=D,ee.push(oe||J),!Re.length)return{SheetNames:ee,Sheets:fe};for(var Ne={},Be=[],Te=0;Te<Re.length;++Te)fe[ee[Te]]?(Be.push(Re[Te]||ee[Te]),Ne[Re[Te]]=fe[Re[Te]]||fe[ee[Te]]):(Be.push(Re[Te]),Ne[Re[Te]]={"!ref":"A1"});return{SheetNames:Be,Sheets:Ne}}function a(y,V){var N=V||{};if(+N.codepage>=0&&at(+N.codepage),N.type=="string")throw new Error("Cannot write WK1 to JS string");var D=k0(),J=tr(y["!ref"]),oe=Array.isArray(y),de=[];et(D,0,s(1030)),et(D,6,l(J));for(var fe=Math.min(J.e.r,8191),ee=J.s.r;ee<=fe;++ee)for(var Re=mr(ee),C=J.s.c;C<=J.e.c;++C){ee===J.s.r&&(de[C]=lr(C));var Ze=de[C]+Re,Ne=oe?(y[ee]||[])[C]:y[Ze];if(!(!Ne||Ne.t=="z"))if(Ne.t=="n")(Ne.v|0)==Ne.v&&Ne.v>=-32768&&Ne.v<=32767?et(D,13,h(ee,C,Ne.v)):et(D,14,d(ee,C,Ne.v));else{var Be=mt(Ne);et(D,15,u(ee,C,Be.slice(0,239)))}}return et(D,1),D.end()}function n(y,V){var N=V||{};if(+N.codepage>=0&&at(+N.codepage),N.type=="string")throw new Error("Cannot write WK3 to JS string");var D=k0();et(D,0,i(y));for(var J=0,oe=0;J<y.SheetNames.length;++J)(y.Sheets[y.SheetNames[J]]||{})["!ref"]&&et(D,27,K(y.SheetNames[J],oe++));var de=0;for(J=0;J<y.SheetNames.length;++J){var fe=y.Sheets[y.SheetNames[J]];if(!(!fe||!fe["!ref"])){for(var ee=tr(fe["!ref"]),Re=Array.isArray(fe),C=[],Ze=Math.min(ee.e.r,8191),Ne=ee.s.r;Ne<=Ze;++Ne)for(var Be=mr(Ne),Te=ee.s.c;Te<=ee.e.c;++Te){Ne===ee.s.r&&(C[Te]=lr(Te));var le=C[Te]+Be,Xe=Re?(fe[Ne]||[])[Te]:fe[le];if(!(!Xe||Xe.t=="z"))if(Xe.t=="n")et(D,23,G(Ne,Te,de,Xe.v));else{var Ye=mt(Xe);et(D,22,F(Ne,Te,de,Ye.slice(0,239)))}}++de}}return et(D,1),D.end()}function s(y){var V=or(2);return V.write_shift(2,y),V}function i(y){var V=or(26);V.write_shift(2,4096),V.write_shift(2,4),V.write_shift(4,0);for(var N=0,D=0,J=0,oe=0;oe<y.SheetNames.length;++oe){var de=y.SheetNames[oe],fe=y.Sheets[de];if(!(!fe||!fe["!ref"])){++J;var ee=ta(fe["!ref"]);N<ee.e.r&&(N=ee.e.r),D<ee.e.c&&(D=ee.e.c)}}return N>8191&&(N=8191),V.write_shift(2,N),V.write_shift(1,J),V.write_shift(1,D),V.write_shift(2,0),V.write_shift(2,0),V.write_shift(1,1),V.write_shift(1,2),V.write_shift(4,0),V.write_shift(4,0),V}function c(y,V,N){var D={s:{c:0,r:0},e:{c:0,r:0}};return V==8&&N.qpro?(D.s.c=y.read_shift(1),y.l++,D.s.r=y.read_shift(2),D.e.c=y.read_shift(1),y.l++,D.e.r=y.read_shift(2),D):(D.s.c=y.read_shift(2),D.s.r=y.read_shift(2),V==12&&N.qpro&&(y.l+=2),D.e.c=y.read_shift(2),D.e.r=y.read_shift(2),V==12&&N.qpro&&(y.l+=2),D.s.c==65535&&(D.s.c=D.e.c=D.s.r=D.e.r=0),D)}function l(y){var V=or(8);return V.write_shift(2,y.s.c),V.write_shift(2,y.s.r),V.write_shift(2,y.e.c),V.write_shift(2,y.e.r),V}function f(y,V,N){var D=[{c:0,r:0},{t:"n",v:0},0,0];return N.qpro&&N.vers!=20768?(D[0].c=y.read_shift(1),D[3]=y.read_shift(1),D[0].r=y.read_shift(2),y.l+=2):(D[2]=y.read_shift(1),D[0].c=y.read_shift(2),D[0].r=y.read_shift(2)),D}function o(y,V,N){var D=y.l+V,J=f(y,V,N);if(J[1].t="s",N.vers==20768){y.l++;var oe=y.read_shift(1);return J[1].v=y.read_shift(oe,"utf8"),J}return N.qpro&&y.l++,J[1].v=y.read_shift(D-y.l,"cstr"),J}function u(y,V,N){var D=or(7+N.length);D.write_shift(1,255),D.write_shift(2,V),D.write_shift(2,y),D.write_shift(1,39);for(var J=0;J<D.length;++J){var oe=N.charCodeAt(J);D.write_shift(1,oe>=128?95:oe)}return D.write_shift(1,0),D}function v(y,V,N){var D=f(y,V,N);return D[1].v=y.read_shift(2,"i"),D}function h(y,V,N){var D=or(7);return D.write_shift(1,255),D.write_shift(2,V),D.write_shift(2,y),D.write_shift(2,N,"i"),D}function x(y,V,N){var D=f(y,V,N);return D[1].v=y.read_shift(8,"f"),D}function d(y,V,N){var D=or(13);return D.write_shift(1,255),D.write_shift(2,V),D.write_shift(2,y),D.write_shift(8,N,"f"),D}function p(y,V,N){var D=y.l+V,J=f(y,V,N);if(J[1].v=y.read_shift(8,"f"),N.qpro)y.l=D;else{var oe=y.read_shift(2);b(y.slice(y.l,y.l+oe),J),y.l+=oe}return J}function A(y,V,N){var D=V&32768;return V&=-32769,V=(D?y:0)+(V>=8192?V-16384:V),(D?"":"$")+(N?lr(V):mr(V))}var O={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},_=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function b(y,V){pr(y,0);for(var N=[],D=0,J="",oe="",de="",fe="";y.l<y.length;){var ee=y[y.l++];switch(ee){case 0:N.push(y.read_shift(8,"f"));break;case 1:oe=A(V[0].c,y.read_shift(2),!0),J=A(V[0].r,y.read_shift(2),!1),N.push(oe+J);break;case 2:{var Re=A(V[0].c,y.read_shift(2),!0),C=A(V[0].r,y.read_shift(2),!1);oe=A(V[0].c,y.read_shift(2),!0),J=A(V[0].r,y.read_shift(2),!1),N.push(Re+C+":"+oe+J)}break;case 3:if(y.l<y.length){console.error("WK1 premature formula end");return}break;case 4:N.push("("+N.pop()+")");break;case 5:N.push(y.read_shift(2));break;case 6:{for(var Ze="";ee=y[y.l++];)Ze+=String.fromCharCode(ee);N.push('"'+Ze.replace(/"/g,'""')+'"')}break;case 8:N.push("-"+N.pop());break;case 23:N.push("+"+N.pop());break;case 22:N.push("NOT("+N.pop()+")");break;case 20:case 21:fe=N.pop(),de=N.pop(),N.push(["AND","OR"][ee-20]+"("+de+","+fe+")");break;default:if(ee<32&&_[ee])fe=N.pop(),de=N.pop(),N.push(de+_[ee]+fe);else if(O[ee]){if(D=O[ee][1],D==69&&(D=y[y.l++]),D>N.length){console.error("WK1 bad formula parse 0x"+ee.toString(16)+":|"+N.join("|")+"|");return}var Ne=N.slice(-D);N.length-=D,N.push(O[ee][0]+"("+Ne.join(",")+")")}else return ee<=7?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=24?console.error("WK1 unsupported op "+ee.toString(16)):ee<=30?console.error("WK1 invalid opcode "+ee.toString(16)):ee<=115?console.error("WK1 unsupported function opcode "+ee.toString(16)):console.error("WK1 unrecognized opcode "+ee.toString(16))}}N.length==1?V[1].f=""+N[0]:console.error("WK1 bad formula parse |"+N.join("|")+"|")}function U(y){var V=[{c:0,r:0},{t:"n",v:0},0];return V[0].r=y.read_shift(2),V[3]=y[y.l++],V[0].c=y[y.l++],V}function R(y,V){var N=U(y);return N[1].t="s",N[1].v=y.read_shift(V-4,"cstr"),N}function F(y,V,N,D){var J=or(6+D.length);J.write_shift(2,y),J.write_shift(1,N),J.write_shift(1,V),J.write_shift(1,39);for(var oe=0;oe<D.length;++oe){var de=D.charCodeAt(oe);J.write_shift(1,de>=128?95:de)}return J.write_shift(1,0),J}function X(y,V){var N=U(y);N[1].v=y.read_shift(2);var D=N[1].v>>1;if(N[1].v&1)switch(D&7){case 0:D=(D>>3)*5e3;break;case 1:D=(D>>3)*500;break;case 2:D=(D>>3)/20;break;case 3:D=(D>>3)/200;break;case 4:D=(D>>3)/2e3;break;case 5:D=(D>>3)/2e4;break;case 6:D=(D>>3)/16;break;case 7:D=(D>>3)/64;break}return N[1].v=D,N}function I(y,V){var N=U(y),D=y.read_shift(4),J=y.read_shift(4),oe=y.read_shift(2);if(oe==65535)return D===0&&J===3221225472?(N[1].t="e",N[1].v=15):D===0&&J===3489660928?(N[1].t="e",N[1].v=42):N[1].v=0,N;var de=oe&32768;return oe=(oe&32767)-16446,N[1].v=(1-de*2)*(J*Math.pow(2,oe+32)+D*Math.pow(2,oe)),N}function G(y,V,N,D){var J=or(14);if(J.write_shift(2,y),J.write_shift(1,N),J.write_shift(1,V),D==0)return J.write_shift(4,0),J.write_shift(4,0),J.write_shift(2,65535),J;var oe=0,de=0,fe=0,ee=0;return D<0&&(oe=1,D=-D),de=Math.log2(D)|0,D/=Math.pow(2,de-31),ee=D>>>0,(ee&2147483648)==0&&(D/=2,++de,ee=D>>>0),D-=ee,ee|=2147483648,ee>>>=0,D*=Math.pow(2,32),fe=D>>>0,J.write_shift(4,fe),J.write_shift(4,ee),de+=16383+(oe?32768:0),J.write_shift(2,de),J}function Y(y,V){var N=I(y);return y.l+=V-14,N}function P(y,V){var N=U(y),D=y.read_shift(4);return N[1].v=D>>6,N}function te(y,V){var N=U(y),D=y.read_shift(8,"f");return N[1].v=D,N}function _e(y,V){var N=te(y);return y.l+=V-10,N}function ie(y,V){return y[y.l+V-1]==0?y.read_shift(V,"cstr"):""}function me(y,V){var N=y[y.l++];N>V-1&&(N=V-1);for(var D="";D.length<N;)D+=String.fromCharCode(y[y.l++]);return D}function pe(y,V,N){if(!(!N.qpro||V<21)){var D=y.read_shift(1);y.l+=17,y.l+=1,y.l+=2;var J=y.read_shift(V-21,"cstr");return[D,J]}}function Ge(y,V){for(var N={},D=y.l+V;y.l<D;){var J=y.read_shift(2);if(J==14e3){for(N[J]=[0,""],N[J][0]=y.read_shift(2);y[y.l];)N[J][1]+=String.fromCharCode(y[y.l]),y.l++;y.l++}}return N}function K(y,V){var N=or(5+y.length);N.write_shift(2,14e3),N.write_shift(2,V);for(var D=0;D<y.length;++D){var J=y.charCodeAt(D);N[N.l++]=J>127?95:J}return N[N.l++]=0,N}var ke={0:{n:"BOF",f:fr},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:c},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:v},14:{n:"NUMBER",f:x},15:{n:"LABEL",f:o},16:{n:"FORMULA",f:p},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:o},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ie},222:{n:"SHEETNAMELP",f:me},65535:{n:""}},Ae={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:R},23:{n:"NUMBER17",f:I},24:{n:"NUMBER18",f:X},25:{n:"FORMULA19",f:Y},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Ge},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:P},38:{n:"??"},39:{n:"NUMBER27",f:te},40:{n:"FORMULA28",f:_e},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ie},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:pe},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:t}}();function Cl(e){var t={},r=e.match(Sr),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=we(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;t.cp=L0[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(s[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+s[0])}}return t}var Ol=function(){var e=Ea("t"),t=Ea("rPr");function r(s){var i=s.match(e);if(!i)return{t:"s",v:""};var c={t:"s",v:Me(i[1])},l=s.match(t);return l&&(c.s=Cl(l[1])),c}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(i){return i.replace(a,"").split(n).map(r).filter(function(c){return c.v})}}(),Dl=function(){var t=/(\r\n|\n)/g;function r(n,s,i){var c=[];n.u&&c.push("text-decoration: underline;"),n.uval&&c.push("text-underline-style:"+n.uval+";"),n.sz&&c.push("font-size:"+n.sz+"pt;"),n.outline&&c.push("text-effect: outline;"),n.shadow&&c.push("text-shadow: auto;"),s.push('<span style="'+c.join("")+'">'),n.b&&(s.push("<b>"),i.push("</b>")),n.i&&(s.push("<i>"),i.push("</i>")),n.strike&&(s.push("<s>"),i.push("</s>"));var l=n.valign||"";return l=="superscript"||l=="super"?l="sup":l=="subscript"&&(l="sub"),l!=""&&(s.push("<"+l+">"),i.push("</"+l+">")),i.push("</span>"),n}function a(n){var s=[[],n.v,[]];return n.v?(n.s&&r(n.s,s[0],s[2]),s[0].join("")+s[1].replace(t,"<br/>")+s[2].join("")):""}return function(s){return s.map(a).join("")}}(),Il=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Rl=/<(?:\w+:)?r>/,Nl=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Q0(e,t){var r=t?t.cellHTML:!0,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Me(Je(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=Je(e),r&&(a.h=$0(a.t))):e.match(Rl)&&(a.r=Je(e),a.t=Me(Je((e.replace(Nl,"").match(Il)||[]).join("").replace(Sr,""))),r&&(a.h=Dl(Ol(a.r)))),a):{t:""}}var bl=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Pl=/<(?:\w+:)?(?:si|sstItem)>/g,Ll=/<\/(?:\w+:)?(?:si|sstItem)>/;function Bl(e,t){var r=[],a="";if(!e)return r;var n=e.match(bl);if(n){a=n[2].replace(Pl,"").split(Ll);for(var s=0;s!=a.length;++s){var i=Q0(a[s].trim(),t);i!=null&&(r[r.length]=i)}n=we(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}function Ml(e){return[e.read_shift(4),e.read_shift(4)]}function Ul(e,t){var r=[],a=!1;return _t(e,function(s,i,c){switch(c){case 159:r.Count=s[0],r.Unique=s[1];break;case 19:r.push(s);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(i.T,!a||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r}function mi(e){for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function gt(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function Hl(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=gt(e,4),t.U=gt(e,4),t.W=gt(e,4),t}function Vl(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function Wl(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(Vl(e));return t}function $l(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function Gl(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=gt(e,4),t.U=gt(e,4),t.W=gt(e,4),t}function Xl(e){var t=Gl(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function _i(e,t){var r=e.l+t,a={};a.Flags=e.read_shift(4)&63,e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function Ti(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function zl(e){var t=gt(e);switch(t.Minor){case 2:return[t.Minor,Kl(e)];case 3:return[t.Minor,Yl()];case 4:return[t.Minor,jl(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function Kl(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),a=_i(e,r),n=Ti(e,e.length-e.l);return{t:"Std",h:a,v:n}}function Yl(){throw new Error("File is password-protected: ECMA-376 Extensible")}function jl(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(Sr,function(s){var i=we(s);switch(ut(i[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(c){a[c]=i[c]});break;case"<dataIntegrity":a.encryptedHmacKey=i.encryptedHmacKey,a.encryptedHmacValue=i.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=i.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(i);break;default:throw i[0]}}),a}function Jl(e,t){var r={},a=r.EncryptionVersionInfo=gt(e,4);if(t-=4,a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=_i(e,n),t-=n,r.EncryptionVerifier=Ti(e,t),r}function ql(e){var t={},r=t.EncryptionVersionInfo=gt(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function Zl(e){var t=0,r,a=mi(e),n=a.length+1,s,i,c,l,f;for(r=yt(n),r[0]=a.length,s=1;s!=n;++s)r[s]=a[s-1];for(s=n-1;s>=0;--s)i=r[s],c=(t&16384)===0?0:1,l=t<<1&32767,f=c|l,t=f^i;return t^52811}var ki=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(i){return(i/2|i*128)&255},n=function(i,c){return a(i^c)},s=function(i){for(var c=t[i.length-1],l=104,f=i.length-1;f>=0;--f)for(var o=i[f],u=0;u!=7;++u)o&64&&(c^=r[l]),o*=2,--l;return c};return function(i){for(var c=mi(i),l=s(c),f=c.length,o=yt(16),u=0;u!=16;++u)o[u]=0;var v,h,x;for((f&1)===1&&(v=l>>8,o[f]=n(e[0],v),--f,v=l&255,h=c[c.length-1],o[f]=n(h,v));f>0;)--f,v=l>>8,o[f]=n(c[f],v),--f,v=l&255,o[f]=n(c[f],v);for(f=15,x=15-c.length;x>0;)v=l>>8,o[f]=n(e[x],v),--f,--x,v=l&255,o[f]=n(c[f],v),--f,--x;return o}}(),Ql=function(e,t,r,a,n){n||(n=t),a||(a=ki(e));var s,i;for(s=0;s!=t.length;++s)i=t[s],i^=a[r],i=(i>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},eu=function(e){var t=0,r=ki(e);return function(a){var n=Ql("",a,t,r);return t=n[1],n[0]}};function ru(e,t,r,a){var n={key:fr(e),verificationBytes:fr(e)};return r.password&&(n.verifier=Zl(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=eu(r.password)),n}function tu(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,a.Info===1?a.Data=ql(e):a.Data=Jl(e,t),a}function au(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?tu(e,t-2,a):ru(e,r.biff>=8?t:t-2,r,a),a}var nu=function(){function e(n,s){switch(s.type){case"base64":return t(Mr(n),s);case"binary":return t(n,s);case"buffer":return t(be&&Buffer.isBuffer(n)?n.toString("binary"):Vt(n),s);case"array":return t(Mt(n),s)}throw new Error("Unrecognized type "+s.type)}function t(n,s){var i=s||{},c=i.dense?[]:{},l=n.match(/\\trowd.*?\\row\b/g);if(!l.length)throw new Error("RTF missing table");var f={s:{c:0,r:0},e:{c:0,r:l.length-1}};return l.forEach(function(o,u){Array.isArray(c)&&(c[u]=[]);for(var v=/\\\w+\b/g,h=0,x,d=-1;x=v.exec(o);){switch(x[0]){case"\\cell":var p=o.slice(h,v.lastIndex-x[0].length);if(p[0]==" "&&(p=p.slice(1)),++d,p.length){var A={v:p,t:"s"};Array.isArray(c)?c[u][d]=A:c[ye({r:u,c:d})]=A}break}h=v.lastIndex}d>f.e.c&&(f.e.c=d)}),c["!ref"]=Le(f),c}function r(n,s){return Ot(e(n,s),s)}function a(n){for(var s=["{\\rtf1\\ansi"],i=tr(n["!ref"]),c,l=Array.isArray(n),f=i.s.r;f<=i.e.r;++f){s.push("\\trowd\\trautofit1");for(var o=i.s.c;o<=i.e.c;++o)s.push("\\cellx"+(o+1));for(s.push("\\pard\\intbl"),o=i.s.c;o<=i.e.c;++o){var u=ye({r:f,c:o});c=l?(n[f]||[])[o]:n[u],!(!c||c.v==null&&(!c.f||c.F))&&(s.push(" "+(c.w||(mt(c),c.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:a}}();function su(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function Fa(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function iu(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(i===0)return[0,0,t];var c=0,l=0,f=n+s;switch(l=i/(f>1?2-f:f),n){case t:c=((r-a)/i+6)%6;break;case r:c=(a-t)/i+2;break;case a:c=(t-r)/i+4;break}return[c/6,l,f/2]}function cu(e){var t=e[0],r=e[1],a=e[2],n=r*2*(a<.5?a:1-a),s=a-n/2,i=[s,s,s],c=6*t,l;if(r!==0)switch(c|0){case 0:case 6:l=n*c,i[0]+=n,i[1]+=l;break;case 1:l=n*(2-c),i[0]+=l,i[1]+=n;break;case 2:l=n*(c-2),i[1]+=n,i[2]+=l;break;case 3:l=n*(4-c),i[1]+=l,i[2]+=n;break;case 4:l=n*(c-4),i[2]+=n,i[0]+=l;break;case 5:l=n*(6-c),i[2]+=l,i[0]+=n;break}for(var f=0;f!=3;++f)i[f]=Math.round(i[f]*255);return i}function qa(e,t){if(t===0)return e;var r=iu(su(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),Fa(cu(r))}var Ei=6,fu=15,ou=1,Cr=Ei;function Za(e){return Math.floor((e+Math.round(128/Cr)/256)*Cr)}function Qa(e){return Math.floor((e-5)/Cr*100+.5)/100}function F0(e){return Math.round((e*Cr+5)/Cr*256)/256}function x0(e){return F0(Qa(Za(e)))}function en(e){var t=Math.abs(e-x0(e)),r=Cr;if(t>.005)for(Cr=ou;Cr<fu;++Cr)Math.abs(e-x0(e))<=t&&(t=Math.abs(e-x0(e)),r=Cr);Cr=r}function ea(e){e.width?(e.wpx=Za(e.width),e.wch=Qa(e.wpx),e.MDW=Cr):e.wpx?(e.wch=Qa(e.wpx),e.width=F0(e.wch),e.MDW=Cr):typeof e.wch=="number"&&(e.width=F0(e.wch),e.wpx=Za(e.width),e.MDW=Cr),e.customWidth&&delete e.customWidth}var lu=96,wi=lu;function Ai(e){return e*96/wi}function Sa(e){return e*wi/96}var uu={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function hu(e,t,r,a){t.Borders=[];var n={},s=!1;(e[0].match(Sr)||[]).forEach(function(i){var c=we(i);switch(ut(c[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},c.diagonalUp&&(n.diagonalUp=qe(c.diagonalUp)),c.diagonalDown&&(n.diagonalDown=qe(c.diagonalDown)),t.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+c[0]+" in borders")}})}function xu(e,t,r,a){t.Fills=[];var n={},s=!1;(e[0].match(Sr)||[]).forEach(function(i){var c=we(i);switch(ut(c[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":c.patternType&&(n.patternType=c.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),c.indexed&&(n.bgColor.indexed=parseInt(c.indexed,10)),c.theme&&(n.bgColor.theme=parseInt(c.theme,10)),c.tint&&(n.bgColor.tint=parseFloat(c.tint)),c.rgb&&(n.bgColor.rgb=c.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),c.theme&&(n.fgColor.theme=parseInt(c.theme,10)),c.tint&&(n.fgColor.tint=parseFloat(c.tint)),c.rgb!=null&&(n.fgColor.rgb=c.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fills")}})}function du(e,t,r,a){t.Fonts=[];var n={},s=!1;(e[0].match(Sr)||[]).forEach(function(i){var c=we(i);switch(ut(c[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":c.val&&(n.name=Je(c.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=c.val?qe(c.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=c.val?qe(c.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(c.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=c.val?qe(c.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=c.val?qe(c.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=c.val?qe(c.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=c.val?qe(c.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=c.val?qe(c.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":c.val&&(n.sz=+c.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":c.val&&(n.vertAlign=c.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":c.val&&(n.family=parseInt(c.val,10));break;case"<family/>":case"</family>":break;case"<scheme":c.val&&(n.scheme=c.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(c.val=="1")break;c.codepage=L0[parseInt(c.val,10)];break;case"<color":if(n.color||(n.color={}),c.auto&&(n.color.auto=qe(c.auto)),c.rgb)n.color.rgb=c.rgb.slice(-6);else if(c.indexed){n.color.index=parseInt(c.indexed,10);var l=Lt[n.color.index];n.color.index==81&&(l=Lt[1]),l||(l=Lt[1]),n.color.rgb=l[0].toString(16)+l[1].toString(16)+l[2].toString(16)}else c.theme&&(n.color.theme=parseInt(c.theme,10),c.tint&&(n.color.tint=parseFloat(c.tint)),c.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=qa(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fonts")}})}function vu(e,t,r){t.NumberFmt=[];for(var a=lt(Ce),n=0;n<a.length;++n)t.NumberFmt[a[n]]=Ce[a[n]];var s=e[0].match(Sr);if(s)for(n=0;n<s.length;++n){var i=we(s[n]);switch(ut(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var c=Me(Je(i.formatCode)),l=parseInt(i.numFmtId,10);if(t.NumberFmt[l]=c,l>0){if(l>392){for(l=392;l>60&&t.NumberFmt[l]!=null;--l);t.NumberFmt[l]=c}Pt(c,l)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}var Va=["numFmtId","fillId","fontId","borderId","xfId"],Wa=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function pu(e,t,r){t.CellXf=[];var a,n=!1;(e[0].match(Sr)||[]).forEach(function(s){var i=we(s),c=0;switch(ut(i[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=i,delete a[0],c=0;c<Va.length;++c)a[Va[c]]&&(a[Va[c]]=parseInt(a[Va[c]],10));for(c=0;c<Wa.length;++c)a[Wa[c]]&&(a[Wa[c]]=qe(a[Wa[c]]));if(t.NumberFmt&&a.numFmtId>392){for(c=392;c>60;--c)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[c]){a.numFmtId=c;break}}t.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var l={};i.vertical&&(l.vertical=i.vertical),i.horizontal&&(l.horizontal=i.horizontal),i.textRotation!=null&&(l.textRotation=i.textRotation),i.indent&&(l.indent=i.indent),i.wrapText&&(l.wrapText=qe(i.wrapText)),a.alignment=l;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+i[0]+" in cellXfs")}})}var gu=function(){var t=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,a=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,n=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,s=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(c,l,f){var o={};if(!c)return o;c=c.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var u;return(u=c.match(t))&&vu(u,o,f),(u=c.match(n))&&du(u,o,l,f),(u=c.match(a))&&xu(u,o,l,f),(u=c.match(s))&&hu(u,o,l,f),(u=c.match(r))&&pu(u,o,f),o}}();function mu(e,t){var r=e.read_shift(2),a=Ar(e);return[r,a]}function _u(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=Cf(e);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var s=e.read_shift(2);switch(s===700&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var i=e.read_shift(1);i!=0&&(a.underline=i);var c=e.read_shift(1);c>0&&(a.family=c);var l=e.read_shift(1);switch(l>0&&(a.charset=l),e.l++,a.color=yf(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=Ar(e),a}var Tu=Fr;function ku(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}var Eu=Fr;function wu(e,t,r){var a={};a.NumberFmt=[];for(var n in Ce)a.NumberFmt[n]=Ce[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return _t(e,function(l,f,o){switch(o){case 44:a.NumberFmt[l[0]]=l[1],Pt(l[1],l[0]);break;case 43:a.Fonts.push(l),l.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(l.color.rgb=qa(t.themeElements.clrScheme[l.color.theme].rgb,l.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:s[s.length-1]==617&&a.CellXf.push(l);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(f.T>0)s.push(o);else if(f.T<0)s.pop();else if(!i||r.WTF&&s[s.length-1]!=37)throw new Error("Unexpected record 0x"+o.toString(16))}}),a}var Au=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Fu(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(Sr)||[]).forEach(function(n){var s=we(n);switch(s[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=s.val;break;case"<a:sysClr":a.rgb=s.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":s[0].charAt(1)==="/"?(t.themeElements.clrScheme[Au.indexOf(s[0])]=a,a={}):a.name=s[0].slice(3,s[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+s[0]+" in clrScheme")}})}function Su(){}function yu(){}var Cu=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,Ou=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Du=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function Iu(e,t,r){t.themeElements={};var a;[["clrScheme",Cu,Fu],["fontScheme",Ou,Su],["fmtScheme",Du,yu]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)})}var Ru=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Fi(e,t){(!e||e.length===0)&&(e=Nu());var r,a={};if(!(r=e.match(Ru)))throw new Error("themeElements not found in theme");return Iu(r[0],a,t),a.raw=e,a}function Nu(e,t){var r=[Ls];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function bu(e,t,r){var a=e.l+t,n=e.read_shift(4);if(n!==124226){if(!r.cellStyles){e.l=a;return}var s=e.slice(e.l);e.l=a;var i;try{i=Ps(s,{type:"array"})}catch{return}var c=Br(i,"theme/theme/theme1.xml",!0);if(c)return Fi(c,r)}}function Pu(e){return e.read_shift(4)}function Lu(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=Bu(e,4);break;case 2:t.xclrValue=ui(e);break;case 3:t.xclrValue=Pu(e);break;case 4:e.l+=4;break}return e.l+=8,t}function Bu(e,t){return Fr(e,t)}function Mu(e,t){return Fr(e,t)}function Uu(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=Lu(e);break;case 6:a[1]=Mu(e,r);break;case 14:case 15:a[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function Hu(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(Uu(e,r-e.l));return{ixfe:a,ext:s}}function Vu(e,t){t.forEach(function(r){r[0]})}function Wu(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ar(e)}}function $u(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function Gu(e){return e.l+=4,e.read_shift(4)!=0}function Xu(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,c=2;return _t(e,function(l,f,o){switch(o){case 335:a.Types.push({name:l.name});break;case 51:l.forEach(function(u){c==1?a.Cell.push({type:a.Types[u[0]-1].name,index:u[1]}):c==0&&a.Value.push({type:a.Types[u[0]-1].name,index:u[1]})});break;case 337:c=l?1:0;break;case 338:c=2;break;case 35:s.push(o),i=!0;break;case 36:s.pop(),i=!1;break;default:if(!f.T){if(!i||n.WTF&&s[s.length-1]!=35)throw new Error("Unexpected record 0x"+o.toString(16))}}}),a}function zu(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=!1,s=2,i;return e.replace(Sr,function(c){var l=we(c);switch(ut(l[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:l.name});break;case"</metadataType>":break;case"<futureMetadata":for(var f=0;f<a.Types.length;++f)a.Types[f].name==l.name&&(i=a.Types[f]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":s==1?a.Cell.push({type:a.Types[l.t-1].name,index:+l.v}):s==0&&a.Value.push({type:a.Types[l.t-1].name,index:+l.v});break;case"</rc>":break;case"<cellMetadata":s=1;break;case"</cellMetadata>":s=2;break;case"<valueMetadata":s=0;break;case"</valueMetadata>":s=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+l.i);break;default:if(!n&&r.WTF)throw new Error("unrecognized "+l[0]+" in metadata")}return c}),a}function Ku(e){var t=[];if(!e)return t;var r=1;return(e.match(Sr)||[]).forEach(function(a){var n=we(a);switch(n[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete n[0],n.i?r=n.i:n.i=r,t.push(n);break}}),t}function Yu(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=ye(r);var a=e.read_shift(1);return a&2&&(t.l="1"),a&8&&(t.a="1"),t}function ju(e,t,r){var a=[];return _t(e,function(s,i,c){switch(c){case 63:a.push(s);break;default:if(!i.T)throw new Error("Unexpected record 0x"+c.toString(16))}}),a}function Ju(e,t,r,a){if(!e)return e;var n=a||{},s=!1;_t(e,function(c,l,f){switch(f){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(!l.T){if(!s||n.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}},n)}function qu(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}function Qn(e,t,r,a){var n=Array.isArray(e),s;t.forEach(function(i){var c=Or(i.ref);if(n?(e[c.r]||(e[c.r]=[]),s=e[c.r][c.c]):s=e[i.ref],!s){s={t:"z"},n?e[c.r][c.c]=s:e[i.ref]=s;var l=tr(e["!ref"]||"BDWGO1000001:A1");l.s.r>c.r&&(l.s.r=c.r),l.e.r<c.r&&(l.e.r=c.r),l.s.c>c.c&&(l.s.c=c.c),l.e.c<c.c&&(l.e.c=c.c);var f=Le(l);f!==e["!ref"]&&(e["!ref"]=f)}s.c||(s.c=[]);var o={a:i.author,t:i.t,r:i.r,T:r};i.h&&(o.h=i.h);for(var u=s.c.length-1;u>=0;--u){if(!r&&s.c[u].T)return;r&&!s.c[u].T&&s.c.splice(u,1)}if(r&&a){for(u=0;u<a.length;++u)if(o.a==a[u].id){o.a=a[u].name||o.a;break}}s.c.push(o)})}function Zu(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?author[^>]*>(.*)/);c&&r.push(c[1])}});var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?comment[^>]*>/);if(c){var l=we(c[0]),f={author:l.authorId&&r[l.authorId]||"sheetjsghost",ref:l.ref,guid:l.guid},o=Or(l.ref);if(!(t.sheetRows&&t.sheetRows<=o.r)){var u=i.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),v=!!u&&!!u[1]&&Q0(u[1])||{r:"",t:"",h:""};f.r=v.r,v.r=="<t></t>"&&(v.t=v.h=""),f.t=(v.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(f.h=v.h),a.push(f)}}}}),a}function Qu(e,t){var r=[],a=!1,n={},s=0;return e.replace(Sr,function(c,l){var f=we(c);switch(ut(f[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:f.personId,guid:f.id,ref:f.ref,T:1};break;case"</threadedComment>":n.t!=null&&r.push(n);break;case"<text>":case"<text":s=l+c.length;break;case"</text>":n.t=e.slice(s,l).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+f[0]+" in threaded comments")}return c}),r}function e1(e,t){var r=[],a=!1;return e.replace(Sr,function(s){var i=we(s);switch(ut(i[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:i.displayname,id:i.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+i[0]+" in threaded comments")}return s}),r}function r1(e){var t={};t.iauthor=e.read_shift(4);var r=$t(e);return t.rfx=r.s,t.ref=ye(r.s),e.l+=16,t}var t1=Ar;function a1(e,t){var r=[],a=[],n={},s=!1;return _t(e,function(c,l,f){switch(f){case 632:a.push(c);break;case 635:n=c;break;case 637:n.t=c.t,n.h=c.h,n.r=c.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!l.T){if(!s||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}}),r}var n1="application/vnd.ms-office.vbaProject";function s1(e){var t=Pe.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,a){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Pe.utils.cfb_add(t,n,e.FileIndex[a].content)}}),Pe.write(t)}function i1(){return{"!type":"dialog"}}function c1(){return{"!type":"dialog"}}function f1(){return{"!type":"macro"}}function o1(){return{"!type":"macro"}}var qt=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(a,n,s,i){var c=!1,l=!1;s.length==0?l=!0:s.charAt(0)=="["&&(l=!0,s=s.slice(1,-1)),i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1));var f=s.length>0?parseInt(s,10)|0:0,o=i.length>0?parseInt(i,10)|0:0;return c?o+=t.c:--o,l?f+=t.r:--f,n+(c?"":"$")+lr(o)+(l?"":"$")+mr(f)}return function(n,s){return t=s,n.replace(e,r)}}(),Si=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,l1=function(){return function(t,r){return t.replace(Si,function(a,n,s,i,c,l){var f=K0(i)-(s?0:r.c),o=z0(l)-(c?0:r.r),u=o==0?"":c?o+1:"["+o+"]",v=f==0?"":s?f+1:"["+f+"]";return n+"R"+u+"C"+v})}}();function yi(e,t){return e.replace(Si,function(r,a,n,s,i,c){return a+(n=="$"?n+s:lr(K0(s)+t.c))+(i=="$"?i+c:mr(z0(c)+t.r))})}function u1(e,t,r){var a=ta(t),n=a.s,s=Or(r),i={r:s.r-n.r,c:s.c-n.c};return yi(e,i)}function h1(e){return e.length!=1}function es(e){return e.replace(/_xlfn\./g,"")}function ir(e){e.l+=1}function Ct(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Ci(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Oi(e);r.biff==12&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=Ct(e),c=Ct(e);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Oi(e){var t=Ct(e),r=Ct(e),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function x1(e,t,r){if(r.biff<8)return Oi(e);var a=e.read_shift(r.biff==12?4:2),n=e.read_shift(r.biff==12?4:2),s=Ct(e),i=Ct(e);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}function Di(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return d1(e);var a=e.read_shift(r&&r.biff==12?4:2),n=Ct(e);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function d1(e){var t=Ct(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function v1(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function p1(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return g1(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(s&16384)>>14,c=(s&32768)>>15;if(s&=16383,c==1)for(;n>524287;)n-=1048576;if(i==1)for(;s>8191;)s=s-16384;return{r:n,c:s,cRel:i,rRel:c}}function g1(e){var t=e.read_shift(2),r=e.read_shift(1),a=(t&32768)>>15,n=(t&16384)>>14;return t&=16383,a==1&&t>=8192&&(t=t-16384),n==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:n,rRel:a}}function m1(e,t,r){var a=(e[e.l++]&96)>>5,n=Ci(e,r.biff>=2&&r.biff<=5?6:8,r);return[a,n]}function _1(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=Ci(e,s,r);return[a,n,i]}function T1(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}function k1(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[a,n]}function E1(e,t,r){var a=(e[e.l++]&96)>>5,n=x1(e,t-1,r);return[a,n]}function w1(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[a]}function rs(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function A1(e,t,r){e.l+=2;for(var a=e.read_shift(r&&r.biff==2?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&r.biff==2?1:2));return n}function F1(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function S1(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function y1(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function C1(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[a]}function Ii(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function O1(e){return e.read_shift(2),Ii(e)}function D1(e){return e.read_shift(2),Ii(e)}function I1(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Di(e,0,r);return[a,n]}function R1(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=p1(e,0,r);return[a,n]}function N1(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=Di(e,0,r);return[a,n,s]}function b1(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[Ph[n],bi[n],a]}function P1(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[a==88?-1:0,e.read_shift(1)]:L1(e);return[n,(s[0]===0?bi:bh)[s[1]]]}function L1(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function B1(e,t,r){e.l+=r&&r.biff==2?3:4}function M1(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function U1(e){return e.l++,Gt[e.read_shift(1)]}function H1(e){return e.l++,e.read_shift(2)}function V1(e){return e.l++,e.read_shift(1)!==0}function W1(e){return e.l++,Er(e)}function $1(e,t,r){return e.l++,Da(e,t-1,r)}function G1(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=nr(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Gt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Er(e);break;case 2:r[1]=Xt(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function X1(e,t,r){for(var a=e.read_shift(r.biff==12?4:2),n=[],s=0;s!=a;++s)n.push((r.biff==12?$t:t0)(e));return n}function z1(e,t,r){var a=0,n=0;r.biff==12?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,--n==0&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var c=0;c!=n;++c)i[s][c]=G1(e,r.biff);return i}function K1(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,s]}function Y1(e,t,r){if(r.biff==5)return j1(e);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),s=e.read_shift(4);return[a,n,s]}function j1(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}function J1(e,t,r){var a=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function q1(e,t,r){var a=e.read_shift(1)>>>5&3,n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function Z1(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[a]}function Q1(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[a,n]}var eh=Fr,rh=Fr,th=Fr;function Ra(e,t,r){return e.l+=2,[v1(e)]}function rn(e){return e.l+=6,[]}var ah=Ra,nh=rn,sh=rn,ih=Ra;function Ri(e){return e.l+=2,[fr(e),e.read_shift(2)&1]}var ch=Ra,fh=Ri,oh=rn,lh=Ra,uh=Ra,hh=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function xh(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=hh[r>>2&31];return{ixti:t,coltype:r&3,rt:i,idx:a,c:n,C:s}}function dh(e){return e.l+=2,[e.read_shift(4)]}function vh(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function ph(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function gh(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function mh(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function _h(e){return e.l+=4,[0,0]}var ts={1:{n:"PtgExp",f:M1},2:{n:"PtgTbl",f:th},3:{n:"PtgAdd",f:ir},4:{n:"PtgSub",f:ir},5:{n:"PtgMul",f:ir},6:{n:"PtgDiv",f:ir},7:{n:"PtgPower",f:ir},8:{n:"PtgConcat",f:ir},9:{n:"PtgLt",f:ir},10:{n:"PtgLe",f:ir},11:{n:"PtgEq",f:ir},12:{n:"PtgGe",f:ir},13:{n:"PtgGt",f:ir},14:{n:"PtgNe",f:ir},15:{n:"PtgIsect",f:ir},16:{n:"PtgUnion",f:ir},17:{n:"PtgRange",f:ir},18:{n:"PtgUplus",f:ir},19:{n:"PtgUminus",f:ir},20:{n:"PtgPercent",f:ir},21:{n:"PtgParen",f:ir},22:{n:"PtgMissArg",f:ir},23:{n:"PtgStr",f:$1},26:{n:"PtgSheet",f:vh},27:{n:"PtgEndSheet",f:ph},28:{n:"PtgErr",f:U1},29:{n:"PtgBool",f:V1},30:{n:"PtgInt",f:H1},31:{n:"PtgNum",f:W1},32:{n:"PtgArray",f:w1},33:{n:"PtgFunc",f:b1},34:{n:"PtgFuncVar",f:P1},35:{n:"PtgName",f:K1},36:{n:"PtgRef",f:I1},37:{n:"PtgArea",f:m1},38:{n:"PtgMemArea",f:J1},39:{n:"PtgMemErr",f:eh},40:{n:"PtgMemNoMem",f:rh},41:{n:"PtgMemFunc",f:q1},42:{n:"PtgRefErr",f:Z1},43:{n:"PtgAreaErr",f:T1},44:{n:"PtgRefN",f:R1},45:{n:"PtgAreaN",f:E1},46:{n:"PtgMemAreaN",f:gh},47:{n:"PtgMemNoMemN",f:mh},57:{n:"PtgNameX",f:Y1},58:{n:"PtgRef3d",f:N1},59:{n:"PtgArea3d",f:_1},60:{n:"PtgRefErr3d",f:Q1},61:{n:"PtgAreaErr3d",f:k1},255:{}},Th={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},kh={1:{n:"PtgElfLel",f:Ri},2:{n:"PtgElfRw",f:lh},3:{n:"PtgElfCol",f:ah},6:{n:"PtgElfRwV",f:uh},7:{n:"PtgElfColV",f:ih},10:{n:"PtgElfRadical",f:ch},11:{n:"PtgElfRadicalS",f:oh},13:{n:"PtgElfColS",f:nh},15:{n:"PtgElfColSV",f:sh},16:{n:"PtgElfRadicalLel",f:fh},25:{n:"PtgList",f:xh},29:{n:"PtgSxName",f:dh},255:{}},Eh={0:{n:"PtgAttrNoop",f:_h},1:{n:"PtgAttrSemi",f:C1},2:{n:"PtgAttrIf",f:S1},4:{n:"PtgAttrChoose",f:A1},8:{n:"PtgAttrGoto",f:F1},16:{n:"PtgAttrSum",f:B1},32:{n:"PtgAttrBaxcel",f:rs},33:{n:"PtgAttrBaxcel",f:rs},64:{n:"PtgAttrSpace",f:O1},65:{n:"PtgAttrSpaceSemi",f:D1},128:{n:"PtgAttrIfError",f:y1},255:{}};function Na(e,t,r,a){if(a.biff<8)return Fr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=z1(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=X1(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&a.biff==12&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return t=n-e.l,t!==0&&s.push(Fr(e,t)),s}function ba(e,t,r){for(var a=e.l+t,n,s,i=[];a!=e.l;)t=a-e.l,s=e[e.l],n=ts[s]||ts[Th[s]],(s===24||s===25)&&(n=(s===24?kh:Eh)[e[e.l+1]]),!n||!n.f?Fr(e,t):i.push([n.n,n.f(e,t,r)]);return i}function wh(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];if(i)switch(i[0]){case 2:n.push('"'+i[1].replace(/"/g,'""')+'"');break;default:n.push(i[1])}else n.push("")}t.push(n.join(","))}return t.join(";")}var Ah={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Fh(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ni(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=a[1]==-1?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[a[0]][0][3]?(n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function as(e,t,r){var a=Ni(e,t,r);return a=="#REF"?a:Fh(a,r)}function kr(e,t,r,a,n){var s=n&&n.biff||8,i={s:{c:0,r:0}},c=[],l,f,o,u=0,v=0,h,x="";if(!e[0]||!e[0][0])return"";for(var d=-1,p="",A=0,O=e[0].length;A<O;++A){var _=e[0][A];switch(_[0]){case"PtgUminus":c.push("-"+c.pop());break;case"PtgUplus":c.push("+"+c.pop());break;case"PtgPercent":c.push(c.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(l=c.pop(),f=c.pop(),d>=0){switch(e[0][d][1][0]){case 0:p=rr(" ",e[0][d][1][1]);break;case 1:p=rr("\r",e[0][d][1][1]);break;default:if(p="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][d][1][0])}f=f+p,d=-1}c.push(f+Ah[_[0]]+l);break;case"PtgIsect":l=c.pop(),f=c.pop(),c.push(f+" "+l);break;case"PtgUnion":l=c.pop(),f=c.pop(),c.push(f+","+l);break;case"PtgRange":l=c.pop(),f=c.pop(),c.push(f+":"+l);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":o=xa(_[1][1],i,n),c.push(da(o,s));break;case"PtgRefN":o=r?xa(_[1][1],r,n):_[1][1],c.push(da(o,s));break;case"PtgRef3d":u=_[1][1],o=xa(_[1][2],i,n),x=as(a,u,n),c.push(x+"!"+da(o,s));break;case"PtgFunc":case"PtgFuncVar":var b=_[1][0],U=_[1][1];b||(b=0),b&=127;var R=b==0?[]:c.slice(-b);c.length-=b,U==="User"&&(U=R.shift()),c.push(U+"("+R.join(",")+")");break;case"PtgBool":c.push(_[1]?"TRUE":"FALSE");break;case"PtgInt":c.push(_[1]);break;case"PtgNum":c.push(String(_[1]));break;case"PtgStr":c.push('"'+_[1].replace(/"/g,'""')+'"');break;case"PtgErr":c.push(_[1]);break;case"PtgAreaN":h=Bn(_[1][1],r?{s:r}:i,n),c.push(u0(h,n));break;case"PtgArea":h=Bn(_[1][1],i,n),c.push(u0(h,n));break;case"PtgArea3d":u=_[1][1],h=_[1][2],x=as(a,u,n),c.push(x+"!"+u0(h,n));break;case"PtgAttrSum":c.push("SUM("+c.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":v=_[1][2];var F=(a.names||[])[v-1]||(a[0]||[])[v],X=F?F.Name:"SH33TJSNAME"+String(v);X&&X.slice(0,6)=="_xlfn."&&!n.xlfn&&(X=X.slice(6)),c.push(X);break;case"PtgNameX":var I=_[1][1];v=_[1][2];var G;if(n.biff<=5)I<0&&(I=-I),a[I]&&(G=a[I][v]);else{var Y="";if(((a[I]||[])[0]||[])[0]==14849||(((a[I]||[])[0]||[])[0]==1025?a[I][v]&&a[I][v].itab>0&&(Y=a.SheetNames[a[I][v].itab-1]+"!"):Y=a.SheetNames[v-1]+"!"),a[I]&&a[I][v])Y+=a[I][v].Name;else if(a[0]&&a[0][v])Y+=a[0][v].Name;else{var P=(Ni(a,I,n)||"").split(";;");P[v-1]?Y=P[v-1]:Y+="SH33TJSERRX"}c.push(Y);break}G||(G={Name:"SH33TJSERRY"}),c.push(G.Name);break;case"PtgParen":var te="(",_e=")";if(d>=0){switch(p="",e[0][d][1][0]){case 2:te=rr(" ",e[0][d][1][1])+te;break;case 3:te=rr("\r",e[0][d][1][1])+te;break;case 4:_e=rr(" ",e[0][d][1][1])+_e;break;case 5:_e=rr("\r",e[0][d][1][1])+_e;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][d][1][0])}d=-1}c.push(te+c.pop()+_e);break;case"PtgRefErr":c.push("#REF!");break;case"PtgRefErr3d":c.push("#REF!");break;case"PtgExp":o={c:_[1][1],r:_[1][0]};var ie={c:r.c,r:r.r};if(a.sharedf[ye(o)]){var me=a.sharedf[ye(o)];c.push(kr(me,i,ie,a,n))}else{var pe=!1;for(l=0;l!=a.arrayf.length;++l)if(f=a.arrayf[l],!(o.c<f[0].s.c||o.c>f[0].e.c)&&!(o.r<f[0].s.r||o.r>f[0].e.r)){c.push(kr(f[1],i,ie,a,n)),pe=!0;break}pe||c.push(_[1])}break;case"PtgArray":c.push("{"+wh(_[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":d=A;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":c.push("");break;case"PtgAreaErr":c.push("#REF!");break;case"PtgAreaErr3d":c.push("#REF!");break;case"PtgList":c.push("Table"+_[1].idx+"[#"+_[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(_));default:throw new Error("Unrecognized Formula Token: "+String(_))}var Ge=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3&&d>=0&&Ge.indexOf(e[0][A][0])==-1){_=e[0][d];var K=!0;switch(_[1][0]){case 4:K=!1;case 0:p=rr(" ",_[1][1]);break;case 5:K=!1;case 1:p=rr("\r",_[1][1]);break;default:if(p="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+_[1][0])}c.push((K?p:"")+c.pop()+(K?"":p)),d=-1}}if(c.length>1&&n.WTF)throw new Error("bad formula stack");return c[0]}function Sh(e,t,r){var a=e.l+t,n=r.biff==2?1:2,s,i=e.read_shift(n);if(i==65535)return[[],Fr(e,t-2)];var c=ba(e,i,r);return t!==i+n&&(s=Na(e,t-i-n,c,r)),e.l=a,[c,s]}function yh(e,t,r){var a=e.l+t,n=r.biff==2?1:2,s,i=e.read_shift(n);if(i==65535)return[[],Fr(e,t-2)];var c=ba(e,i,r);return t!==i+n&&(s=Na(e,t-i-n,c,r)),e.l=a,[c,s]}function Ch(e,t,r,a){var n=e.l+t,s=ba(e,a,r),i;return n!==e.l&&(i=Na(e,n-e.l,s,r)),[s,i]}function Oh(e,t,r){var a=e.l+t,n,s=e.read_shift(2),i=ba(e,s,r);return s==65535?[[],Fr(e,t-2)]:(t!==s+2&&(n=Na(e,a-s-2,i,r)),[i,n])}function Dh(e){var t;if(vt(e,e.l+6)!==65535)return[Er(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function d0(e,t,r){var a=e.l+t,n=ht(e);r.biff==2&&++e.l;var s=Dh(e),i=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=yh(e,a-e.l,r);return{cell:n,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function a0(e,t,r){var a=e.read_shift(4),n=ba(e,a,r),s=e.read_shift(4),i=s>0?Na(e,s,n,r):null;return[n,i]}var Ih=a0,n0=a0,Rh=a0,Nh=a0,bh={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},bi={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Ph={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function ns(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function v0(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var ma={},Zt={};function _a(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Pi(e,t,r,a,n,s){try{a.cellNF&&(e.z=Ce[t])}catch(c){if(a.WTF)throw c}if(!(e.t==="z"&&!a.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=dr(e.v)),(!a||a.cellText!==!1)&&e.t!=="z")try{if(Ce[t]==null&&Pt(bc[t]||"General",t),e.t==="e")e.w=e.w||Gt[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ka(e.v);else if(e.t==="d"){var i=Ir(e.v);(i|0)===i?e.w=i.toString(10):e.w=ka(i)}else{if(e.v===void 0)return"";e.w=Bt(e.v,Zt)}else e.t==="d"?e.w=zr(t,Ir(e.v),Zt):e.w=zr(t,e.v,Zt)}catch(c){if(a.WTF)throw c}if(a.cellStyles&&r!=null)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=qa(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=qa(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(a.WTF&&s.Fills)throw c}}}function Lh(e,t){var r=tr(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=Le(r))}var Bh=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Mh=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,Uh=/<(?:\w:)?hyperlink [^>]*>/mg,Hh=/"(\w*:\w*)"/,Vh=/<(?:\w:)?col\b[^>]*[\/]?>/g,Wh=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,$h=/<(?:\w:)?pageMargins[^>]*\/>/g,Li=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Gh=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Xh=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function zh(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var c=t.dense?[]:{},l={s:{r:2e6,c:2e6},e:{r:0,c:0}},f="",o="",u=e.match(Mh);u?(f=e.slice(0,u.index),o=e.slice(u.index+u[0].length)):f=o=e;var v=f.match(Li);v?tn(v[0],c,n,r):(v=f.match(Gh))&&Kh(v[0],v[1]||"",c,n,r);var h=(f.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(h>0){var x=f.slice(h,h+50).match(Hh);x&&Lh(c,x[1])}var d=f.match(Xh);d&&d[1]&&Qh(d[1],n);var p=[];if(t.cellStyles){var A=f.match(Vh);A&&Jh(p,A)}u&&ex(u[1],c,t,l,s,i);var O=o.match(Wh);O&&(c["!autofilter"]=qh(O[0]));var _=[],b=o.match(Bh);if(b)for(h=0;h!=b.length;++h)_[h]=tr(b[h].slice(b[h].indexOf('"')+1));var U=o.match(Uh);U&&Yh(c,U,a);var R=o.match($h);if(R&&(c["!margins"]=jh(we(R[0]))),!c["!ref"]&&l.e.c>=l.s.c&&l.e.r>=l.s.r&&(c["!ref"]=Le(l)),t.sheetRows>0&&c["!ref"]){var F=tr(c["!ref"]);t.sheetRows<=+F.e.r&&(F.e.r=t.sheetRows-1,F.e.r>l.e.r&&(F.e.r=l.e.r),F.e.r<F.s.r&&(F.s.r=F.e.r),F.e.c>l.e.c&&(F.e.c=l.e.c),F.e.c<F.s.c&&(F.s.c=F.e.c),c["!fullref"]=c["!ref"],c["!ref"]=Le(F))}return p.length>0&&(c["!cols"]=p),_.length>0&&(c["!merges"]=_),c}function tn(e,t,r,a){var n=we(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=Me(Je(n.codeName)))}function Kh(e,t,r,a,n){tn(e.slice(0,e.indexOf(">")),r,a,n)}function Yh(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=we(Je(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Me(s.location))):(s.Target="#"+Me(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=tr(s.ref),l=c.s.r;l<=c.e.r;++l)for(var f=c.s.c;f<=c.e.c;++f){var o=ye({c:f,r:l});a?(e[l]||(e[l]=[]),e[l][f]||(e[l][f]={t:"z",v:void 0}),e[l][f].l=s):(e[o]||(e[o]={t:"z",v:void 0}),e[o].l=s)}}}function jh(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function Jh(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=we(t[a],!0);n.hidden&&(n.hidden=qe(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,en(n.width)),ea(n);s<=i;)e[s++]=gr(n)}}function qh(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}var Zh=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Qh(e,t){t.Views||(t.Views=[{}]),(e.match(Zh)||[]).forEach(function(r,a){var n=we(r);t.Views[a]||(t.Views[a]={}),+n.zoomScale&&(t.Views[a].zoom=+n.zoomScale),qe(n.rightToLeft)&&(t.Views[a].RTL=!0)})}var ex=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=Ea("v"),i=Ea("f");return function(l,f,o,u,v,h){for(var x=0,d="",p=[],A=[],O=0,_=0,b=0,U="",R,F,X=0,I=0,G,Y,P=0,te=0,_e=Array.isArray(h.CellXf),ie,me=[],pe=[],Ge=Array.isArray(f),K=[],ke={},Ae=!1,y=!!o.sheetStubs,V=l.split(t),N=0,D=V.length;N!=D;++N){d=V[N].trim();var J=d.length;if(J!==0){var oe=0;e:for(x=0;x<J;++x)switch(d[x]){case">":if(d[x-1]!="/"){++x;break e}if(o&&o.cellStyles){if(F=we(d.slice(oe,x),!0),X=F.r!=null?parseInt(F.r,10):X+1,I=-1,o.sheetRows&&o.sheetRows<X)continue;ke={},Ae=!1,F.ht&&(Ae=!0,ke.hpt=parseFloat(F.ht),ke.hpx=Sa(ke.hpt)),F.hidden=="1"&&(Ae=!0,ke.hidden=!0),F.outlineLevel!=null&&(Ae=!0,ke.level=+F.outlineLevel),Ae&&(K[X-1]=ke)}break;case"<":oe=x;break}if(oe>=x)break;if(F=we(d.slice(oe,x),!0),X=F.r!=null?parseInt(F.r,10):X+1,I=-1,!(o.sheetRows&&o.sheetRows<X)){u.s.r>X-1&&(u.s.r=X-1),u.e.r<X-1&&(u.e.r=X-1),o&&o.cellStyles&&(ke={},Ae=!1,F.ht&&(Ae=!0,ke.hpt=parseFloat(F.ht),ke.hpx=Sa(ke.hpt)),F.hidden=="1"&&(Ae=!0,ke.hidden=!0),F.outlineLevel!=null&&(Ae=!0,ke.level=+F.outlineLevel),Ae&&(K[X-1]=ke)),p=d.slice(x).split(e);for(var de=0;de!=p.length&&p[de].trim().charAt(0)=="<";++de);for(p=p.slice(de),x=0;x!=p.length;++x)if(d=p[x].trim(),d.length!==0){if(A=d.match(r),O=x,_=0,b=0,d="<c "+(d.slice(0,1)=="<"?">":"")+d,A!=null&&A.length===2){for(O=0,U=A[1],_=0;_!=U.length&&!((b=U.charCodeAt(_)-64)<1||b>26);++_)O=26*O+b;--O,I=O}else++I;for(_=0;_!=d.length&&d.charCodeAt(_)!==62;++_);if(++_,F=we(d.slice(0,_),!0),F.r||(F.r=ye({r:X-1,c:I})),U=d.slice(_),R={t:""},(A=U.match(s))!=null&&A[1]!==""&&(R.v=Me(A[1])),o.cellFormula){if((A=U.match(i))!=null&&A[1]!==""){if(R.f=Me(Je(A[1])).replace(/\r\n/g,`
`),o.xlfn||(R.f=es(R.f)),A[0].indexOf('t="array"')>-1)R.F=(U.match(n)||[])[1],R.F.indexOf(":")>-1&&me.push([tr(R.F),R.F]);else if(A[0].indexOf('t="shared"')>-1){Y=we(A[0]);var fe=Me(Je(A[1]));o.xlfn||(fe=es(fe)),pe[parseInt(Y.si,10)]=[Y,fe,F.r]}}else(A=U.match(/<f[^>]*\/>/))&&(Y=we(A[0]),pe[Y.si]&&(R.f=u1(pe[Y.si][1],pe[Y.si][2],F.r)));var ee=Or(F.r);for(_=0;_<me.length;++_)ee.r>=me[_][0].s.r&&ee.r<=me[_][0].e.r&&ee.c>=me[_][0].s.c&&ee.c<=me[_][0].e.c&&(R.F=me[_][1])}if(F.t==null&&R.v===void 0)if(R.f||R.F)R.v=0,R.t="n";else if(y)R.t="z";else continue;else R.t=F.t||"n";switch(u.s.c>I&&(u.s.c=I),u.e.c<I&&(u.e.c=I),R.t){case"n":if(R.v==""||R.v==null){if(!y)continue;R.t="z"}else R.v=parseFloat(R.v);break;case"s":if(typeof R.v>"u"){if(!y)continue;R.t="z"}else G=ma[parseInt(R.v,10)],R.v=G.t,R.r=G.r,o.cellHTML&&(R.h=G.h);break;case"str":R.t="s",R.v=R.v!=null?Je(R.v):"",o.cellHTML&&(R.h=$0(R.v));break;case"inlineStr":A=U.match(a),R.t="s",A!=null&&(G=Q0(A[1]))?(R.v=G.t,o.cellHTML&&(R.h=G.h)):R.v="";break;case"b":R.v=qe(R.v);break;case"d":o.cellDates?R.v=dr(R.v,1):(R.v=Ir(dr(R.v,1)),R.t="n");break;case"e":(!o||o.cellText!==!1)&&(R.w=R.v),R.v=ai[R.v];break}if(P=te=0,ie=null,_e&&F.s!==void 0&&(ie=h.CellXf[F.s],ie!=null&&(ie.numFmtId!=null&&(P=ie.numFmtId),o.cellStyles&&ie.fillId!=null&&(te=ie.fillId))),Pi(R,P,te,o,v,h),o.cellDates&&_e&&R.t=="n"&&ra(Ce[P])&&(R.t="d",R.v=r0(R.v)),F.cm&&o.xlmeta){var Re=(o.xlmeta.Cell||[])[+F.cm-1];Re&&Re.type=="XLDAPR"&&(R.D=!0)}if(Ge){var C=Or(F.r);f[C.r]||(f[C.r]=[]),f[C.r][C.c]=R}else f[F.r]=R}}}}K.length>0&&(f["!rows"]=K)}}();function rx(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=n/20),r}var tx=$t;function ax(){}function nx(e,t){var r={},a=e[e.l];return++e.l,r.above=!(a&64),r.left=!(a&128),e.l+=18,r.name=Ff(e),r}function sx(e){var t=Kr(e);return[t]}function ix(e){var t=Wt(e);return[t]}function cx(e){var t=Kr(e),r=e.read_shift(1);return[t,r,"b"]}function fx(e){var t=Wt(e),r=e.read_shift(1);return[t,r,"b"]}function ox(e){var t=Kr(e),r=e.read_shift(1);return[t,r,"e"]}function lx(e){var t=Wt(e),r=e.read_shift(1);return[t,r,"e"]}function ux(e){var t=Kr(e),r=e.read_shift(4);return[t,r,"s"]}function hx(e){var t=Wt(e),r=e.read_shift(4);return[t,r,"s"]}function xx(e){var t=Kr(e),r=Er(e);return[t,r,"n"]}function Bi(e){var t=Wt(e),r=Er(e);return[t,r,"n"]}function dx(e){var t=Kr(e),r=J0(e);return[t,r,"n"]}function vx(e){var t=Wt(e),r=J0(e);return[t,r,"n"]}function px(e){var t=Kr(e),r=Y0(e);return[t,r,"is"]}function gx(e){var t=Kr(e),r=Ar(e);return[t,r,"str"]}function mx(e){var t=Wt(e),r=Ar(e);return[t,r,"str"]}function _x(e,t,r){var a=e.l+t,n=Kr(e);n.r=r["!row"];var s=e.read_shift(1),i=[n,s,"b"];if(r.cellFormula){e.l+=2;var c=n0(e,a-e.l,r);i[3]=kr(c,null,n,r.supbooks,r)}else e.l=a;return i}function Tx(e,t,r){var a=e.l+t,n=Kr(e);n.r=r["!row"];var s=e.read_shift(1),i=[n,s,"e"];if(r.cellFormula){e.l+=2;var c=n0(e,a-e.l,r);i[3]=kr(c,null,n,r.supbooks,r)}else e.l=a;return i}function kx(e,t,r){var a=e.l+t,n=Kr(e);n.r=r["!row"];var s=Er(e),i=[n,s,"n"];if(r.cellFormula){e.l+=2;var c=n0(e,a-e.l,r);i[3]=kr(c,null,n,r.supbooks,r)}else e.l=a;return i}function Ex(e,t,r){var a=e.l+t,n=Kr(e);n.r=r["!row"];var s=Ar(e),i=[n,s,"str"];if(r.cellFormula){e.l+=2;var c=n0(e,a-e.l,r);i[3]=kr(c,null,n,r.supbooks,r)}else e.l=a;return i}var wx=$t;function Ax(e,t){var r=e.l+t,a=$t(e),n=j0(e),s=Ar(e),i=Ar(e),c=Ar(e);e.l=r;var l={rfx:a,relId:n,loc:s,display:c};return i&&(l.Tooltip=i),l}function Fx(){}function Sx(e,t,r){var a=e.l+t,n=ei(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var c=Ih(e,a-e.l,r);i[1]=c}else e.l=a;return i}function yx(e,t,r){var a=e.l+t,n=$t(e),s=[n];if(r.cellFormula){var i=Nh(e,a-e.l,r);s[1]=i,e.l=a}else e.l=a;return s}var Cx=["left","right","top","bottom","header","footer"];function Ox(e){var t={};return Cx.forEach(function(r){t[r]=Er(e)}),t}function Dx(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function Ix(){}function Rx(){}function Nx(e,t,r,a,n,s,i){if(!e)return e;var c=t||{};a||(a={"!id":{}});var l=c.dense?[]:{},f,o={s:{r:2e6,c:2e6},e:{r:0,c:0}},u=!1,v=!1,h,x,d,p,A,O,_,b,U,R=[];c.biff=12,c["!row"]=0;var F=0,X=!1,I=[],G={},Y=c.supbooks||n.supbooks||[[]];if(Y.sharedf=G,Y.arrayf=I,Y.SheetNames=n.SheetNames||n.Sheets.map(function(Ge){return Ge.name}),!c.supbooks&&(c.supbooks=Y,n.Names))for(var P=0;P<n.Names.length;++P)Y[0][P+1]=n.Names[P];var te=[],_e=[],ie=!1;e0[16]={n:"BrtShortReal",f:Bi};var me;if(_t(e,function(K,ke,Ae){if(!v)switch(Ae){case 148:f=K;break;case 0:h=K,c.sheetRows&&c.sheetRows<=h.r&&(v=!0),b=mr(p=h.r),c["!row"]=h.r,(K.hidden||K.hpt||K.level!=null)&&(K.hpt&&(K.hpx=Sa(K.hpt)),_e[K.r]=K);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(x={t:K[2]},K[2]){case"n":x.v=K[1];break;case"s":_=ma[K[1]],x.v=_.t,x.r=_.r;break;case"b":x.v=!!K[1];break;case"e":x.v=K[1],c.cellText!==!1&&(x.w=Gt[x.v]);break;case"str":x.t="s",x.v=K[1];break;case"is":x.t="s",x.v=K[1].t;break}if((d=i.CellXf[K[0].iStyleRef])&&Pi(x,d.numFmtId,null,c,s,i),A=K[0].c==-1?A+1:K[0].c,c.dense?(l[p]||(l[p]=[]),l[p][A]=x):l[lr(A)+b]=x,c.cellFormula){for(X=!1,F=0;F<I.length;++F){var y=I[F];h.r>=y[0].s.r&&h.r<=y[0].e.r&&A>=y[0].s.c&&A<=y[0].e.c&&(x.F=Le(y[0]),X=!0)}!X&&K.length>3&&(x.f=K[3])}if(o.s.r>h.r&&(o.s.r=h.r),o.s.c>A&&(o.s.c=A),o.e.r<h.r&&(o.e.r=h.r),o.e.c<A&&(o.e.c=A),c.cellDates&&d&&x.t=="n"&&ra(Ce[d.numFmtId])){var V=bt(x.v);V&&(x.t="d",x.v=new Date(V.y,V.m-1,V.d,V.H,V.M,V.S,V.u))}me&&(me.type=="XLDAPR"&&(x.D=!0),me=void 0);break;case 1:case 12:if(!c.sheetStubs||u)break;x={t:"z",v:void 0},A=K[0].c==-1?A+1:K[0].c,c.dense?(l[p]||(l[p]=[]),l[p][A]=x):l[lr(A)+b]=x,o.s.r>h.r&&(o.s.r=h.r),o.s.c>A&&(o.s.c=A),o.e.r<h.r&&(o.e.r=h.r),o.e.c<A&&(o.e.c=A),me&&(me.type=="XLDAPR"&&(x.D=!0),me=void 0);break;case 176:R.push(K);break;case 49:me=((c.xlmeta||{}).Cell||[])[K-1];break;case 494:var N=a["!id"][K.relId];for(N?(K.Target=N.Target,K.loc&&(K.Target+="#"+K.loc),K.Rel=N):K.relId==""&&(K.Target="#"+K.loc),p=K.rfx.s.r;p<=K.rfx.e.r;++p)for(A=K.rfx.s.c;A<=K.rfx.e.c;++A)c.dense?(l[p]||(l[p]=[]),l[p][A]||(l[p][A]={t:"z",v:void 0}),l[p][A].l=K):(O=ye({c:A,r:p}),l[O]||(l[O]={t:"z",v:void 0}),l[O].l=K);break;case 426:if(!c.cellFormula)break;I.push(K),U=c.dense?l[p][A]:l[lr(A)+b],U.f=kr(K[1],o,{r:h.r,c:A},Y,c),U.F=Le(K[0]);break;case 427:if(!c.cellFormula)break;G[ye(K[0].s)]=K[1],U=c.dense?l[p][A]:l[lr(A)+b],U.f=kr(K[1],o,{r:h.r,c:A},Y,c);break;case 60:if(!c.cellStyles)break;for(;K.e>=K.s;)te[K.e--]={width:K.w/256,hidden:!!(K.flags&1),level:K.level},ie||(ie=!0,en(K.w/256)),ea(te[K.e+1]);break;case 161:l["!autofilter"]={ref:Le(K)};break;case 476:l["!margins"]=K;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),K.name&&(n.Sheets[r].CodeName=K.name),(K.above||K.left)&&(l["!outline"]={above:K.above,left:K.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),K.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:u=!0;break;case 36:u=!1;break;case 37:u=!0;break;case 38:u=!1;break;default:if(!ke.T){if(!u||c.WTF)throw new Error("Unexpected record 0x"+Ae.toString(16))}}},c),delete c.supbooks,delete c["!row"],!l["!ref"]&&(o.s.r<2e6||f&&(f.e.r>0||f.e.c>0||f.s.r>0||f.s.c>0))&&(l["!ref"]=Le(f||o)),c.sheetRows&&l["!ref"]){var pe=tr(l["!ref"]);c.sheetRows<=+pe.e.r&&(pe.e.r=c.sheetRows-1,pe.e.r>o.e.r&&(pe.e.r=o.e.r),pe.e.r<pe.s.r&&(pe.s.r=pe.e.r),pe.e.c>o.e.c&&(pe.e.c=o.e.c),pe.e.c<pe.s.c&&(pe.s.c=pe.e.c),l["!fullref"]=l["!ref"],l["!ref"]=Le(pe))}return R.length>0&&(l["!merges"]=R),te.length>0&&(l["!cols"]=te),_e.length>0&&(l["!rows"]=_e),l}function bx(e){var t=[],r=e.match(/^<c:numCache>/),a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(s){var i=s.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);i&&(t[+i[1]]=r?+i[2]:i[2])});var n=Me((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(s){a=s.replace(/<.*?>/g,"")}),[t,n,a]}function Px(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,l=0,f="A",o={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(u){var v=bx(u);o.s.r=o.s.c=0,o.e.c=c,f=lr(c),v[0].forEach(function(h,x){i[f+mr(x)]={t:"n",v:h,z:v[1]},l=x}),o.e.r<l&&(o.e.r=l),++c}),c>0&&(i["!ref"]=Le(o)),i}function Lx(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i,c=e.match(Li);return c&&tn(c[0],s,n,r),(i=e.match(/drawing r:id="(.*?)"/))&&(s["!rel"]=i[1]),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}function Bx(e,t){e.l+=10;var r=Ar(e);return{name:r}}function Mx(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return _t(e,function(l,f,o){switch(o){case 550:s["!rel"]=l;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),l.name&&(n.Sheets[r].CodeName=l.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!(f.T>0)){if(!(f.T<0)){if(!i||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}}}},t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}var Mi=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Ux=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Hx=[],Vx=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function ss(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(a[s[0]]==null)a[s[0]]=s[1];else switch(s[2]){case"bool":typeof a[s[0]]=="string"&&(a[s[0]]=qe(a[s[0]]));break;case"int":typeof a[s[0]]=="string"&&(a[s[0]]=parseInt(a[s[0]],10));break}}}function is(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":typeof e[a[0]]=="string"&&(e[a[0]]=qe(e[a[0]]));break;case"int":typeof e[a[0]]=="string"&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function Ui(e){is(e.WBProps,Mi),is(e.CalcPr,Vx),ss(e.WBView,Ux),ss(e.Sheets,Hx),Zt.date1904=qe(e.WBProps.date1904)}var Wx="][*?/\\".split("");function $x(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return Wx.forEach(function(a){if(e.indexOf(a)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}var Gx=/<\w+:workbook/;function Xx(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(Sr,function(l,f){var o=we(l);switch(ut(o[0])){case"<?xml":break;case"<workbook":l.match(Gx)&&(n="xmlns"+l.match(/<(\w+):/)[1]),r.xmlns=o[n];break;case"</workbook>":break;case"<fileVersion":delete o[0],r.AppVersion=o;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Mi.forEach(function(u){if(o[u[0]]!=null)switch(u[2]){case"bool":r.WBProps[u[0]]=qe(o[u[0]]);break;case"int":r.WBProps[u[0]]=parseInt(o[u[0]],10);break;default:r.WBProps[u[0]]=o[u[0]]}}),o.codeName&&(r.WBProps.CodeName=Je(o.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete o[0],r.WBView.push(o);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(o.state){case"hidden":o.Hidden=1;break;case"veryHidden":o.Hidden=2;break;default:o.Hidden=0}delete o.state,o.name=Me(Je(o.name)),delete o[0],r.Sheets.push(o);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":s={},s.Name=Je(o.name),o.comment&&(s.Comment=o.comment),o.localSheetId&&(s.Sheet=+o.localSheetId),qe(o.hidden||"0")&&(s.Hidden=!0),i=f+l.length;break;case"</definedName>":s.Ref=Me(Je(e.slice(i,f))),r.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete o[0],r.CalcPr=o;break;case"<calcPr/>":delete o[0],r.CalcPr=o;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&t.WTF)throw new Error("unrecognized "+o[0]+" in workbook")}return l}),ff.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return Ui(r),r}function zx(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=E0(e),r.name=Ar(e),r}function Kx(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?Ar(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(a&65536),r.backupFile=!!(a&64),r.checkCompatibility=!!(a&4096),r.date1904=!!(a&1),r.filterPrivacy=!!(a&8),r.hidePivotFieldList=!!(a&1024),r.promptedSolutions=!!(a&16),r.publishItems=!!(a&2048),r.refreshAllConnections=!!(a&262144),r.saveExternalLinkValues=!!(a&128),r.showBorderUnselectedTables=!!(a&4),r.showInkAnnotation=!!(a&32),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(a&32768),r.updateLinks=["userSet","never","always"][a>>8&3],r}function Yx(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function jx(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=Sf(e),i=Rh(e,0,r),c=j0(e);e.l=a;var l={Name:s,Ptg:i};return n<268435455&&(l.Sheet=n),c&&(l.Comment=c),l}function Jx(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],e0[16]={n:"BrtFRTArchID$",f:Yx},_t(e,function(l,f,o){switch(o){case 156:i.SheetNames.push(l.name),r.Sheets.push(l);break;case 153:r.WBProps=l;break;case 39:l.Sheet!=null&&(t.SID=l.Sheet),l.Ref=kr(l.Ptg,null,null,i,t),delete t.SID,delete l.Ptg,s.push(l);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([o,l]):i[0]=[o,l],i[i.length-1].XTI=[];break;case 362:i.length===0&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(l),i.XTI=i.XTI.concat(l);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(o),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(o),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(!f.T){if(!n||t.WTF&&a[a.length-1]!=37&&a[a.length-1]!=35)throw new Error("Unexpected record 0x"+o.toString(16))}}},t),Ui(r),r.Names=s,r.supbooks=i,r}function qx(e,t,r){return t.slice(-4)===".bin"?Jx(e,r):Xx(e,r)}function Zx(e,t,r,a,n,s,i,c){return t.slice(-4)===".bin"?Nx(e,a,r,n,s,i,c):zh(e,a,r,n,s,i,c)}function Qx(e,t,r,a,n,s,i,c){return t.slice(-4)===".bin"?Mx(e,a,r,n,s):Lx(e,a,r,n,s)}function ed(e,t,r,a,n,s,i,c){return t.slice(-4)===".bin"?f1():o1()}function rd(e,t,r,a,n,s,i,c){return t.slice(-4)===".bin"?i1():c1()}function td(e,t,r,a){return t.slice(-4)===".bin"?wu(e,r,a):gu(e,r,a)}function ad(e,t,r){return Fi(e,r)}function nd(e,t,r){return t.slice(-4)===".bin"?Ul(e,r):Bl(e,r)}function sd(e,t,r){return t.slice(-4)===".bin"?a1(e,r):Zu(e,r)}function id(e,t,r){return t.slice(-4)===".bin"?ju(e):Ku(e)}function cd(e,t,r,a){return r.slice(-4)===".bin"?Ju(e,t,r,a):void 0}function fd(e,t,r){return t.slice(-4)===".bin"?Xu(e,t,r):zu(e,t,r)}var Hi=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Vi=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Qr(e,t){var r=e.split(/\s+/),a=[];if(a[0]=r[0],r.length===1)return a;var n=e.match(Hi),s,i,c,l;if(n)for(l=0;l!=n.length;++l)s=n[l].match(Vi),(i=s[1].indexOf(":"))===-1?a[s[1]]=s[2].slice(1,s[2].length-1):(s[1].slice(0,6)==="xmlns:"?c="xmlns"+s[1].slice(6):c=s[1].slice(i+1),a[c]=s[2].slice(1,s[2].length-1));return a}function od(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var a=e.match(Hi),n,s,i,c;if(a)for(c=0;c!=a.length;++c)n=a[c].match(Vi),(s=n[1].indexOf(":"))===-1?r[n[1]]=n[2].slice(1,n[2].length-1):(n[1].slice(0,6)==="xmlns:"?i="xmlns"+n[1].slice(6):i=n[1].slice(s+1),r[i]=n[2].slice(1,n[2].length-1));return r}var Ta;function ld(e,t){var r=Ta[e]||Me(e);return r==="General"?Bt(t):zr(r,t)}function ud(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=qe(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=dr(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Me(t)]=n}function hd(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||Gt[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ka(e.v):e.w=Bt(e.v):e.w=ld(t||"General",e.v)}catch(s){if(r.WTF)throw s}try{var a=Ta[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&e.t=="n"&&ra(a)){var n=bt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(s){if(r.WTF)throw s}}}function xd(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=uu[a.Pattern]||a.Pattern)}e[t.ID]=t}function dd(e,t,r,a,n,s,i,c,l,f){var o="General",u=a.StyleID,v={};f=f||{};var h=[],x=0;for(u===void 0&&c&&(u=c.StyleID),u===void 0&&i&&(u=i.StyleID);s[u]!==void 0&&(s[u].nf&&(o=s[u].nf),s[u].Interior&&h.push(s[u].Interior),!!s[u].Parent);)u=s[u].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=qe(e);break;case"String":a.t="s",a.r=Cn(Me(e)),a.v=e.indexOf("<")>-1?Me(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),a.v=(dr(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),a.v!==a.v?a.v=Me(e):a.v<60&&(a.v=a.v-1),(!o||o=="General")&&(o="yyyy-mm-dd");case"Number":a.v===void 0&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=ai[e],f.cellText!==!1&&(a.w=e);break;default:e==""&&t==""?a.t="z":(a.t="s",a.v=Cn(t||e));break}if(hd(a,o,f),f.cellFormula!==!1)if(a.Formula){var d=Me(a.Formula);d.charCodeAt(0)==61&&(d=d.slice(1)),a.f=qt(d,n),delete a.Formula,a.ArrayRange=="RC"?a.F=qt("RC:RC",n):a.ArrayRange&&(a.F=qt(a.ArrayRange,n),l.push([tr(a.F),a.F]))}else for(x=0;x<l.length;++x)n.r>=l[x][0].s.r&&n.r<=l[x][0].e.r&&n.c>=l[x][0].s.c&&n.c<=l[x][0].e.c&&(a.F=l[x][1]);f.cellStyles&&(h.forEach(function(p){!v.patternType&&p.patternType&&(v.patternType=p.patternType)}),a.s=v),a.StyleID!==void 0&&(a.ixfe=a.StyleID)}function vd(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function p0(e,t){var r=t||{};Os();var a=ca(G0(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(a=Je(a));var n=a.slice(0,1024).toLowerCase(),s=!1;if(n=n.replace(/".*?"/g,""),(n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var i=gr(r);return i.type="string",Aa.to_workbook(a,i)}if(n.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(Xe){n.indexOf("<"+Xe)>=0&&(s=!0)}),s)return Ad(a,r);Ta={"General Number":"General","General Date":Ce[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":Ce[15],"Short Date":Ce[14],"Long Time":Ce[19],"Medium Time":Ce[18],"Short Time":Ce[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:Ce[2],Standard:Ce[4],Percent:Ce[10],Scientific:Ce[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,l=[],f,o={},u=[],v=r.dense?[]:{},h="",x={},d={},p=Qr('<Data ss:Type="String">'),A=0,O=0,_=0,b={s:{r:2e6,c:2e6},e:{r:0,c:0}},U={},R={},F="",X=0,I=[],G={},Y={},P=0,te=[],_e=[],ie={},me=[],pe,Ge=!1,K=[],ke=[],Ae={},y=0,V=0,N={Sheets:[],WBProps:{date1904:!1}},D={};wa.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"");for(var J="";c=wa.exec(a);)switch(c[3]=(J=c[3]).toLowerCase()){case"data":if(J=="data"){if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&l.push([c[3],!0]);break}if(l[l.length-1][1])break;c[1]==="/"?dd(a.slice(A,c.index),F,p,l[l.length-1][0]=="comment"?ie:x,{c:O,r:_},U,me[O],d,K,r):(F="",p=Qr(c[0]),A=c.index+c[0].length);break;case"cell":if(c[1]==="/")if(_e.length>0&&(x.c=_e),(!r.sheetRows||r.sheetRows>_)&&x.v!==void 0&&(r.dense?(v[_]||(v[_]=[]),v[_][O]=x):v[lr(O)+mr(_)]=x),x.HRef&&(x.l={Target:Me(x.HRef)},x.HRefScreenTip&&(x.l.Tooltip=x.HRefScreenTip),delete x.HRef,delete x.HRefScreenTip),(x.MergeAcross||x.MergeDown)&&(y=O+(parseInt(x.MergeAcross,10)|0),V=_+(parseInt(x.MergeDown,10)|0),I.push({s:{c:O,r:_},e:{c:y,r:V}})),!r.sheetStubs)x.MergeAcross?O=y+1:++O;else if(x.MergeAcross||x.MergeDown){for(var oe=O;oe<=y;++oe)for(var de=_;de<=V;++de)(oe>O||de>_)&&(r.dense?(v[de]||(v[de]=[]),v[de][oe]={t:"z"}):v[lr(oe)+mr(de)]={t:"z"});O=y+1}else++O;else x=od(c[0]),x.Index&&(O=+x.Index-1),O<b.s.c&&(b.s.c=O),O>b.e.c&&(b.e.c=O),c[0].slice(-2)==="/>"&&++O,_e=[];break;case"row":c[1]==="/"||c[0].slice(-2)==="/>"?(_<b.s.r&&(b.s.r=_),_>b.e.r&&(b.e.r=_),c[0].slice(-2)==="/>"&&(d=Qr(c[0]),d.Index&&(_=+d.Index-1)),O=0,++_):(d=Qr(c[0]),d.Index&&(_=+d.Index-1),Ae={},(d.AutoFitHeight=="0"||d.Height)&&(Ae.hpx=parseInt(d.Height,10),Ae.hpt=Ai(Ae.hpx),ke[_]=Ae),d.Hidden=="1"&&(Ae.hidden=!0,ke[_]=Ae));break;case"worksheet":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"));u.push(h),b.s.r<=b.e.r&&b.s.c<=b.e.c&&(v["!ref"]=Le(b),r.sheetRows&&r.sheetRows<=b.e.r&&(v["!fullref"]=v["!ref"],b.e.r=r.sheetRows-1,v["!ref"]=Le(b))),I.length&&(v["!merges"]=I),me.length>0&&(v["!cols"]=me),ke.length>0&&(v["!rows"]=ke),o[h]=v}else b={s:{r:2e6,c:2e6},e:{r:0,c:0}},_=O=0,l.push([c[3],!1]),f=Qr(c[0]),h=Me(f.Name),v=r.dense?[]:{},I=[],K=[],ke=[],D={name:h,Hidden:0},N.Sheets.push(D);break;case"table":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else{if(c[0].slice(-2)=="/>")break;l.push([c[3],!1]),me=[],Ge=!1}break;case"style":c[1]==="/"?xd(U,R,r):R=Qr(c[0]);break;case"numberformat":R.nf=Me(Qr(c[0]).Format||"General"),Ta[R.nf]&&(R.nf=Ta[R.nf]);for(var fe=0;fe!=392&&Ce[fe]!=R.nf;++fe);if(fe==392){for(fe=57;fe!=392;++fe)if(Ce[fe]==null){Pt(R.nf,fe);break}}break;case"column":if(l[l.length-1][0]!=="table")break;if(pe=Qr(c[0]),pe.Hidden&&(pe.hidden=!0,delete pe.Hidden),pe.Width&&(pe.wpx=parseInt(pe.Width,10)),!Ge&&pe.wpx>10){Ge=!0,Cr=Ei;for(var ee=0;ee<me.length;++ee)me[ee]&&ea(me[ee])}Ge&&ea(pe),me[pe.Index-1||me.length]=pe;for(var Re=0;Re<+pe.Span;++Re)me[me.length]=gr(pe);break;case"namedrange":if(c[1]==="/")break;N.Names||(N.Names=[]);var C=we(c[0]),Ze={Name:C.Name,Ref:qt(C.RefersTo.slice(1),{r:0,c:0})};N.Sheets.length>0&&(Ze.Sheet=N.Sheets.length-1),N.Names.push(Ze);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(c[0].slice(-2)==="/>")break;c[1]==="/"?F+=a.slice(X,c.index):X=c.index+c[0].length;break;case"interior":if(!r.cellStyles)break;R.Interior=Qr(c[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(c[0].slice(-2)==="/>")break;c[1]==="/"?qf(G,J,a.slice(P,c.index)):P=c.index+c[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else l.push([c[3],!1]);break;case"comment":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"));vd(ie),_e.push(ie)}else l.push([c[3],!1]),f=Qr(c[0]),ie={a:f.Author};break;case"autofilter":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else if(c[0].charAt(c[0].length-2)!=="/"){var Ne=Qr(c[0]);v["!autofilter"]={ref:qt(Ne.Range).replace(/\$/g,"")},l.push([c[3],!0])}break;case"name":break;case"datavalidation":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&l.push([c[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(c[1]==="/"){if((f=l.pop())[0]!==c[3])throw new Error("Bad state: "+f.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&l.push([c[3],!0]);break;case"null":break;default:if(l.length==0&&c[3]=="document"||l.length==0&&c[3]=="uof")return hs(a,r);var Be=!0;switch(l[l.length-1][0]){case"officedocumentsettings":switch(c[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:Be=!1}break;case"componentoptions":switch(c[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:Be=!1}break;case"excelworkbook":switch(c[3]){case"date1904":N.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:Be=!1}break;case"workbookoptions":switch(c[3]){case"owcversion":break;case"height":break;case"width":break;default:Be=!1}break;case"worksheetoptions":switch(c[3]){case"visible":if(c[0].slice(-2)!=="/>")if(c[1]==="/")switch(a.slice(P,c.index)){case"SheetHidden":D.Hidden=1;break;case"SheetVeryHidden":D.Hidden=2;break}else P=c.index+c[0].length;break;case"header":v["!margins"]||_a(v["!margins"]={},"xlml"),isNaN(+we(c[0]).Margin)||(v["!margins"].header=+we(c[0]).Margin);break;case"footer":v["!margins"]||_a(v["!margins"]={},"xlml"),isNaN(+we(c[0]).Margin)||(v["!margins"].footer=+we(c[0]).Margin);break;case"pagemargins":var Te=we(c[0]);v["!margins"]||_a(v["!margins"]={},"xlml"),isNaN(+Te.Top)||(v["!margins"].top=+Te.Top),isNaN(+Te.Left)||(v["!margins"].left=+Te.Left),isNaN(+Te.Right)||(v["!margins"].right=+Te.Right),isNaN(+Te.Bottom)||(v["!margins"].bottom=+Te.Bottom);break;case"displayrighttoleft":N.Views||(N.Views=[]),N.Views[0]||(N.Views[0]={}),N.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":v["!outline"]||(v["!outline"]={}),v["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":v["!outline"]||(v["!outline"]={}),v["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:Be=!1}break;case"pivottable":case"pivotcache":switch(c[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:Be=!1}break;case"pagebreaks":switch(c[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:Be=!1}break;case"autofilter":switch(c[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:Be=!1}break;case"querytable":switch(c[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:Be=!1}break;case"datavalidation":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:Be=!1}break;case"sorting":case"conditionalformatting":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:Be=!1}break;case"mapinfo":case"schema":case"data":switch(c[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:Be=!1}break;case"smarttags":break;default:Be=!1;break}if(Be||c[3].match(/!\[CDATA/))break;if(!l[l.length-1][1])throw"Unrecognized tag: "+c[3]+"|"+l.join("|");if(l[l.length-1][0]==="customdocumentproperties"){if(c[0].slice(-2)==="/>")break;c[1]==="/"?ud(Y,J,te,a.slice(P,c.index)):(te=c,P=c.index+c[0].length);break}if(r.WTF)throw"Unrecognized tag: "+c[3]+"|"+l.join("|")}var le={};return!r.bookSheets&&!r.bookProps&&(le.Sheets=o),le.SheetNames=u,le.Workbook=N,le.SSF=gr(Ce),le.Props=G,le.Custprops=Y,le}function S0(e,t){switch(sn(t=t||{}),t.type||"base64"){case"base64":return p0(Mr(e),t);case"binary":case"buffer":case"file":return p0(e,t);case"array":return p0(Vt(e),t)}}function pd(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=Of(r),r.length-r.l<=4)return t;var a=r.read_shift(4);if(a==0||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(a=r.read_shift(4),a!==1907505652)||(t.UnicodeClipboardFormat=Df(r),a=r.read_shift(4),a==0||a>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var gd=[60,1084,2066,2165,2175];function md(e,t,r,a,n){var s=a,i=[],c=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(c)}i.push(c),r.l+=s;for(var l=vt(r,r.l),f=y0[l],o=0;f!=null&&gd.indexOf(l)>-1;)s=vt(r,r.l+2),o=r.l+4,l==2066?o+=4:(l==2165||l==2175)&&(o+=12),c=r.slice(o,r.l+4+s),i.push(c),r.l+=4+s,f=y0[l=vt(r,r.l)];var u=Et(i);pr(u,0);var v=0;u.lens=[];for(var h=0;h<i.length;++h)u.lens.push(v),v+=i[h].length;if(u.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+u.length+" < "+a;return t.f(u,u.length,n)}function ft(e,t,r){if(e.t!=="z"&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=Ce[a])}catch(s){if(t.WTF)throw s}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||Gt[e.v]:a===0||a=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=ka(e.v):e.w=Bt(e.v):e.w=zr(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&a&&e.t=="n"&&ra(Ce[a]||String(a))){var n=bt(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function $a(e,t,r){return{v:e,ixfe:t,t:r}}function _d(e,t){var r={opts:{}},a={},n=t.dense?[]:{},s={},i={},c=null,l=[],f="",o={},u,v="",h,x,d,p,A={},O=[],_,b,U=[],R=[],F={Sheets:[],WBProps:{date1904:!1},Views:[{}]},X={},I=function(Oe){return Oe<8?Lt[Oe]:Oe<64&&R[Oe-8]||Lt[Oe]},G=function(Oe,er,Rr){var ze=er.XF.data;if(!(!ze||!ze.patternType||!Rr||!Rr.cellStyles)){er.s={},er.s.patternType=ze.patternType;var Hr;(Hr=Fa(I(ze.icvFore)))&&(er.s.fgColor={rgb:Hr}),(Hr=Fa(I(ze.icvBack)))&&(er.s.bgColor={rgb:Hr})}},Y=function(Oe,er,Rr){if(!(Ae>1)&&!(Rr.sheetRows&&Oe.r>=Rr.sheetRows)){if(Rr.cellStyles&&er.XF&&er.XF.data&&G(Oe,er,Rr),delete er.ixfe,delete er.XF,u=Oe,v=ye(Oe),(!i||!i.s||!i.e)&&(i={s:{r:0,c:0},e:{r:0,c:0}}),Oe.r<i.s.r&&(i.s.r=Oe.r),Oe.c<i.s.c&&(i.s.c=Oe.c),Oe.r+1>i.e.r&&(i.e.r=Oe.r+1),Oe.c+1>i.e.c&&(i.e.c=Oe.c+1),Rr.cellFormula&&er.f){for(var ze=0;ze<O.length;++ze)if(!(O[ze][0].s.c>Oe.c||O[ze][0].s.r>Oe.r)&&!(O[ze][0].e.c<Oe.c||O[ze][0].e.r<Oe.r)){er.F=Le(O[ze][0]),(O[ze][0].s.c!=Oe.c||O[ze][0].s.r!=Oe.r)&&delete er.f,er.f&&(er.f=""+kr(O[ze][1],i,Oe,K,P));break}}Rr.dense?(n[Oe.r]||(n[Oe.r]=[]),n[Oe.r][Oe.c]=er):n[v]=er}},P={enc:!1,sbcch:0,snames:[],sharedf:A,arrayf:O,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(P.password=t.password);var te,_e=[],ie=[],me=[],pe=[],Ge=!1,K=[];K.SheetNames=P.snames,K.sharedf=P.sharedf,K.arrayf=P.arrayf,K.names=[],K.XTI=[];var ke=0,Ae=0,y=0,V=[],N=[],D;P.codepage=1200,at(1200);for(var J=!1;e.l<e.length-1;){var oe=e.l,de=e.read_shift(2);if(de===0&&ke===10)break;var fe=e.l===e.length?0:e.read_shift(2),ee=y0[de];if(ee&&ee.f){if(t.bookSheets&&ke===133&&de!==133)break;if(ke=de,ee.r===2||ee.r==12){var Re=e.read_shift(2);if(fe-=2,!P.enc&&Re!==de&&((Re&255)<<8|Re>>8)!==de)throw new Error("rt mismatch: "+Re+"!="+de);ee.r==12&&(e.l+=10,fe-=10)}var C={};if(de===10?C=ee.f(e,fe,P):C=md(de,ee,e,fe,P),Ae==0&&[9,521,1033,2057].indexOf(ke)===-1)continue;switch(de){case 34:r.opts.Date1904=F.WBProps.date1904=C;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(P.enc||(e.l=0),P.enc=C,!t.password)throw new Error("File is password-protected");if(C.valid==null)throw new Error("Encryption scheme unsupported");if(!C.valid)throw new Error("Password is incorrect");break;case 92:P.lastuser=C;break;case 66:var Ze=Number(C);switch(Ze){case 21010:Ze=1200;break;case 32768:Ze=1e4;break;case 32769:Ze=1252;break}at(P.codepage=Ze),J=!0;break;case 317:P.rrtabid=C;break;case 25:P.winlocked=C;break;case 439:r.opts.RefreshAll=C;break;case 12:r.opts.CalcCount=C;break;case 16:r.opts.CalcDelta=C;break;case 17:r.opts.CalcIter=C;break;case 13:r.opts.CalcMode=C;break;case 14:r.opts.CalcPrecision=C;break;case 95:r.opts.CalcSaveRecalc=C;break;case 15:P.CalcRefMode=C;break;case 2211:r.opts.FullCalc=C;break;case 129:C.fDialog&&(n["!type"]="dialog"),C.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),C.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:U.push(C);break;case 430:K.push([C]),K[K.length-1].XTI=[];break;case 35:case 547:K[K.length-1].push(C);break;case 24:case 536:D={Name:C.Name,Ref:kr(C.rgce,i,null,K,P)},C.itab>0&&(D.Sheet=C.itab-1),K.names.push(D),K[0]||(K[0]=[],K[0].XTI=[]),K[K.length-1].push(C),C.Name=="_xlnm._FilterDatabase"&&C.itab>0&&C.rgce&&C.rgce[0]&&C.rgce[0][0]&&C.rgce[0][0][0]=="PtgArea3d"&&(N[C.itab-1]={ref:Le(C.rgce[0][0][1][2])});break;case 22:P.ExternCount=C;break;case 23:K.length==0&&(K[0]=[],K[0].XTI=[]),K[K.length-1].XTI=K[K.length-1].XTI.concat(C),K.XTI=K.XTI.concat(C);break;case 2196:if(P.biff<8)break;D!=null&&(D.Comment=C[1]);break;case 18:n["!protect"]=C;break;case 19:C!==0&&P.WTF&&console.error("Password verifier: "+C);break;case 133:s[C.pos]=C,P.snames.push(C.name);break;case 10:{if(--Ae)break;if(i.e){if(i.e.r>0&&i.e.c>0){if(i.e.r--,i.e.c--,n["!ref"]=Le(i),t.sheetRows&&t.sheetRows<=i.e.r){var Ne=i.e.r;i.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=Le(i),i.e.r=Ne}i.e.r++,i.e.c++}_e.length>0&&(n["!merges"]=_e),ie.length>0&&(n["!objects"]=ie),me.length>0&&(n["!cols"]=me),pe.length>0&&(n["!rows"]=pe),F.Sheets.push(X)}f===""?o=n:a[f]=n,n=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(P.biff===8&&(P.biff={9:2,521:3,1033:4}[de]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[C.BIFFVer]||8),P.biffguess=C.BIFFVer==0,C.BIFFVer==0&&C.dt==4096&&(P.biff=5,J=!0,at(P.codepage=28591)),P.biff==8&&C.BIFFVer==0&&C.dt==16&&(P.biff=2),Ae++)break;if(n=t.dense?[]:{},P.biff<8&&!J&&(J=!0,at(P.codepage=t.codepage||1252)),P.biff<5||C.BIFFVer==0&&C.dt==4096){f===""&&(f="Sheet1"),i={s:{r:0,c:0},e:{r:0,c:0}};var Be={pos:e.l-fe,name:f};s[Be.pos]=Be,P.snames.push(f)}else f=(s[oe]||{name:""}).name;C.dt==32&&(n["!type"]="chart"),C.dt==64&&(n["!type"]="macro"),_e=[],ie=[],P.arrayf=O=[],me=[],pe=[],Ge=!1,X={Hidden:(s[oe]||{hs:0}).hs,name:f}}break;case 515:case 3:case 2:n["!type"]=="chart"&&(t.dense?(n[C.r]||[])[C.c]:n[ye({c:C.c,r:C.r})])&&++C.c,_={ixfe:C.ixfe,XF:U[C.ixfe]||{},v:C.val,t:"n"},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t);break;case 5:case 517:_={ixfe:C.ixfe,XF:U[C.ixfe],v:C.val,t:C.t},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t);break;case 638:_={ixfe:C.ixfe,XF:U[C.ixfe],v:C.rknum,t:"n"},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t);break;case 189:for(var Te=C.c;Te<=C.C;++Te){var le=C.rkrec[Te-C.c][0];_={ixfe:le,XF:U[le],v:C.rkrec[Te-C.c][1],t:"n"},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:Te,r:C.r},_,t)}break;case 6:case 518:case 1030:{if(C.val=="String"){c=C;break}if(_=$a(C.val,C.cell.ixfe,C.tt),_.XF=U[_.ixfe],t.cellFormula){var Xe=C.formula;if(Xe&&Xe[0]&&Xe[0][0]&&Xe[0][0][0]=="PtgExp"){var Ye=Xe[0][0][1][0],Ur=Xe[0][0][1][1],Yr=ye({r:Ye,c:Ur});A[Yr]?_.f=""+kr(C.formula,i,C.cell,K,P):_.F=((t.dense?(n[Ye]||[])[Ur]:n[Yr])||{}).F}else _.f=""+kr(C.formula,i,C.cell,K,P)}y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y(C.cell,_,t),c=C}break;case 7:case 519:if(c)c.val=C,_=$a(C,c.cell.ixfe,"s"),_.XF=U[_.ixfe],t.cellFormula&&(_.f=""+kr(c.formula,i,c.cell,K,P)),y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y(c.cell,_,t),c=null;else throw new Error("String record expects Formula");break;case 33:case 545:{O.push(C);var Dt=ye(C[0].s);if(h=t.dense?(n[C[0].s.r]||[])[C[0].s.c]:n[Dt],t.cellFormula&&h){if(!c||!Dt||!h)break;h.f=""+kr(C[1],i,C[0],K,P),h.F=Le(C[0])}}break;case 1212:{if(!t.cellFormula)break;if(v){if(!c)break;A[ye(c.cell)]=C[0],h=t.dense?(n[c.cell.r]||[])[c.cell.c]:n[ye(c.cell)],(h||{}).f=""+kr(C[0],i,u,K,P)}}break;case 253:_=$a(l[C.isst].t,C.ixfe,"s"),l[C.isst].h&&(_.h=l[C.isst].h),_.XF=U[_.ixfe],y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t);break;case 513:t.sheetStubs&&(_={ixfe:C.ixfe,XF:U[C.ixfe],t:"z"},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t));break;case 190:if(t.sheetStubs)for(var it=C.c;it<=C.C;++it){var hr=C.ixfe[it-C.c];_={ixfe:hr,XF:U[hr],t:"z"},y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:it,r:C.r},_,t)}break;case 214:case 516:case 4:_=$a(C.val,C.ixfe,"s"),_.XF=U[_.ixfe],y>0&&(_.z=V[_.ixfe>>8&63]),ft(_,t,r.opts.Date1904),Y({c:C.c,r:C.r},_,t);break;case 0:case 512:Ae===1&&(i=C);break;case 252:l=C;break;case 1054:if(P.biff==4){V[y++]=C[1];for(var jr=0;jr<y+163&&Ce[jr]!=C[1];++jr);jr>=163&&Pt(C[1],y+163)}else Pt(C[1],C[0]);break;case 30:{V[y++]=C;for(var Jr=0;Jr<y+163&&Ce[Jr]!=C;++Jr);Jr>=163&&Pt(C,y+163)}break;case 229:_e=_e.concat(C);break;case 93:ie[C.cmo[0]]=P.lastobj=C;break;case 438:P.lastobj.TxO=C;break;case 127:P.lastobj.ImData=C;break;case 440:for(p=C[0].s.r;p<=C[0].e.r;++p)for(d=C[0].s.c;d<=C[0].e.c;++d)h=t.dense?(n[p]||[])[d]:n[ye({c:d,r:p})],h&&(h.l=C[1]);break;case 2048:for(p=C[0].s.r;p<=C[0].e.r;++p)for(d=C[0].s.c;d<=C[0].e.c;++d)h=t.dense?(n[p]||[])[d]:n[ye({c:d,r:p})],h&&h.l&&(h.l.Tooltip=C[1]);break;case 28:{if(P.biff<=5&&P.biff>=2)break;h=t.dense?(n[C[0].r]||[])[C[0].c]:n[ye(C[0])];var ct=ie[C[2]];h||(t.dense?(n[C[0].r]||(n[C[0].r]=[]),h=n[C[0].r][C[0].c]={t:"z"}):h=n[ye(C[0])]={t:"z"},i.e.r=Math.max(i.e.r,C[0].r),i.s.r=Math.min(i.s.r,C[0].r),i.e.c=Math.max(i.e.c,C[0].c),i.s.c=Math.min(i.s.c,C[0].c)),h.c||(h.c=[]),x={a:C[1],t:ct.TxO.t},h.c.push(x)}break;case 2173:Vu(U[C.ixfe],C.ext);break;case 125:{if(!P.cellStyles)break;for(;C.e>=C.s;)me[C.e--]={width:C.w/256,level:C.level||0,hidden:!!(C.flags&1)},Ge||(Ge=!0,en(C.w/256)),ea(me[C.e+1])}break;case 520:{var Qe={};C.level!=null&&(pe[C.r]=Qe,Qe.level=C.level),C.hidden&&(pe[C.r]=Qe,Qe.hidden=!0),C.hpt&&(pe[C.r]=Qe,Qe.hpt=C.hpt,Qe.hpx=Sa(C.hpt))}break;case 38:case 39:case 40:case 41:n["!margins"]||_a(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[de]]=C;break;case 161:n["!margins"]||_a(n["!margins"]={}),n["!margins"].header=C.header,n["!margins"].footer=C.footer;break;case 574:C.RTL&&(F.Views[0].RTL=!0);break;case 146:R=C;break;case 2198:te=C;break;case 140:b=C;break;case 442:f?X.CodeName=C||X.name:F.WBProps.CodeName=C||"ThisWorkbook";break}}else ee||console.error("Missing Info for XLS Record 0x"+de.toString(16)),e.l+=fe}return r.SheetNames=lt(s).sort(function(Pr,Oe){return Number(Pr)-Number(Oe)}).map(function(Pr){return s[Pr].name}),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&o["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=o)):r.Preamble=o,r.Sheets&&N.forEach(function(Pr,Oe){r.Sheets[r.SheetNames[Oe]]["!autofilter"]=Pr}),r.Strings=l,r.SSF=gr(Ce),P.enc&&(r.Encryption=P.enc),te&&(r.Themes=te),r.Metadata={},b!==void 0&&(r.Metadata.Country=b),K.names.length>0&&(F.Names=K.names),r.Workbook=F,r}var cs={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function Td(e,t,r){var a=Pe.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Gn(a,Lf,cs.DSI);for(var s in n)t[s]=n[s]}catch(f){if(r.WTF)throw f}var i=Pe.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=Gn(i,Bf,cs.SI);for(var l in c)t[l]==null&&(t[l]=c[l])}catch(f){if(r.WTF)throw f}t.HeadingPairs&&t.TitlesOfParts&&(si(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function Wi(e,t){t||(t={}),sn(t),gs(),t.codepage&&B0(t.codepage);var r,a;if(e.FullPaths){if(Pe.find(e,"/encryption"))throw new Error("File is password-protected");r=Pe.find(e,"!CompObj"),a=Pe.find(e,"/Workbook")||Pe.find(e,"/Book")}else{switch(t.type){case"base64":e=tt(Mr(e));break;case"binary":e=tt(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}pr(e,0),a={content:e}}var n,s;if(r&&pd(r),t.bookProps&&!t.bookSheets)n={};else{var i=be?"buffer":"array";if(a&&a.content)n=_d(a.content,t);else if((s=Pe.find(e,"PerfectOffice_MAIN"))&&s.content)n=ga.to_workbook(s.content,(t.type=i,t));else if((s=Pe.find(e,"NativeContent_MAIN"))&&s.content)n=ga.to_workbook(s.content,(t.type=i,t));else throw(s=Pe.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&Pe.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=s1(e))}var c={};return e.FullPaths&&Td(e,c,t),n.Props=n.Custprops=c,t.bookFiles&&(n.cfb=e),n}var e0={0:{f:rx},1:{f:sx},2:{f:dx},3:{f:ox},4:{f:cx},5:{f:xx},6:{f:gx},7:{f:ux},8:{f:Ex},9:{f:kx},10:{f:_x},11:{f:Tx},12:{f:ix},13:{f:vx},14:{f:lx},15:{f:fx},16:{f:Bi},17:{f:mx},18:{f:hx},19:{f:Y0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:jx},40:{},42:{},43:{f:_u},44:{f:mu},45:{f:Tu},46:{f:Eu},47:{f:ku},48:{},49:{f:Ef},50:{},51:{f:$u},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:gi},62:{f:px},63:{f:Yu},64:{f:Ix},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Fr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Dx},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:nx},148:{f:tx,p:16},151:{f:Fx},152:{},153:{f:Kx},154:{},155:{},156:{f:zx},157:{},158:{},159:{T:1,f:Ml},160:{T:-1},161:{T:1,f:$t},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:wx},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Wu},336:{T:-1},337:{f:Gu,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:E0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:pi},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Sx},427:{f:yx},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Ox},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:ax},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Ax},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:E0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:t1},633:{T:1},634:{T:-1},635:{T:1,f:r1},636:{T:-1},637:{f:Af},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Bx},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Rx},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},y0={6:{f:d0},10:{f:kt},12:{f:fr},13:{f:fr},14:{f:nr},15:{f:nr},16:{f:Er},17:{f:nr},18:{f:nr},19:{f:fr},20:{f:Yn},21:{f:Yn},23:{f:pi},24:{f:Jn},25:{f:nr},26:{},27:{},28:{f:Qo},29:{},34:{f:nr},35:{f:jn},38:{f:Er},39:{f:Er},40:{f:Er},41:{f:Er},42:{f:nr},43:{f:nr},47:{f:au},49:{f:bo},51:{f:fr},60:{},61:{f:Io},64:{f:nr},65:{f:No},66:{f:fr},77:{},80:{},81:{},82:{},85:{f:fr},89:{},90:{},91:{},92:{f:wo},93:{f:rl},94:{},95:{f:nr},96:{},97:{},99:{f:nr},125:{f:gi},128:{f:Go},129:{f:Ao},130:{f:fr},131:{f:nr},132:{f:nr},133:{f:Fo},134:{},140:{f:cl},141:{f:fr},144:{},146:{f:ol},151:{},152:{},153:{},154:{},155:{},156:{f:fr},157:{},158:{},160:{f:dl},161:{f:ul},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Ho},190:{f:Vo},193:{f:kt},197:{},198:{},199:{},200:{},201:{},202:{f:nr},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:fr},220:{},221:{f:nr},222:{},224:{f:$o},225:{f:Eo},226:{f:kt},227:{},229:{f:el},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:So},253:{f:Po},255:{f:yo},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:li},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:nr},353:{f:kt},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:zo},431:{f:nr},432:{},433:{},434:{},437:{},438:{f:nl},439:{f:nr},440:{f:sl},441:{},442:{f:Ia},443:{},444:{f:fr},445:{},446:{},448:{f:kt},449:{f:Do,r:2},450:{f:kt},512:{f:zn},513:{f:xl},515:{f:Xo},516:{f:Lo},517:{f:Kn},519:{f:vl},520:{f:Co},523:{},545:{f:qn},549:{f:Xn},566:{},574:{f:Ro},638:{f:Uo},659:{},1048:{},1054:{f:Bo},1084:{},1212:{f:Jo},2048:{f:il},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:Ha},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:kt},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:ll,r:12},2173:{f:Hu,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:nr,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:jo,r:12},2197:{},2198:{f:bu,r:12},2199:{},2200:{},2201:{},2202:{f:qo,r:12},2203:{f:kt},2204:{},2205:{},2206:{},2207:{},2211:{f:Oo},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:fr},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:hl},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:fl},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:zn},1:{},2:{f:_l},3:{f:ml},4:{f:gl},5:{f:Kn},7:{f:Tl},8:{},9:{f:Ha},11:{},22:{f:fr},30:{f:Mo},31:{},32:{},33:{f:qn},36:{},37:{f:Xn},50:{f:kl},62:{},52:{},67:{},68:{f:fr},69:{},86:{},126:{},127:{f:pl},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:El},223:{},234:{},354:{},421:{},518:{f:d0},521:{f:Ha},536:{f:Jn},547:{f:jn},561:{},579:{},1030:{f:d0},1033:{f:Ha},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function et(e,t,r,a){var n=t;if(!isNaN(n)){var s=(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&qs(r)&&e.push(r)}}function fs(e,t){var r=t||{},a=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,c=s&&s.index||e.length,l=$c(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),f=-1,o=0,u=0,v=0,h={s:{r:1e7,c:1e7},e:{r:0,c:0}},x=[];for(i=0;i<l.length;++i){var d=l[i].trim(),p=d.slice(0,3).toLowerCase();if(p=="<tr"){if(++f,r.sheetRows&&r.sheetRows<=f){--f;break}o=0;continue}if(!(p!="<td"&&p!="<th")){var A=d.split(/<\/t[dh]>/i);for(c=0;c<A.length;++c){var O=A[c].trim();if(O.match(/<t[dh]/i)){for(var _=O,b=0;_.charAt(0)=="<"&&(b=_.indexOf(">"))>-1;)_=_.slice(b+1);for(var U=0;U<x.length;++U){var R=x[U];R.s.c==o&&R.s.r<f&&f<=R.e.r&&(o=R.e.c+1,U=-1)}var F=we(O.slice(0,O.indexOf(">")));v=F.colspan?+F.colspan:1,((u=+F.rowspan)>1||v>1)&&x.push({s:{r:f,c:o},e:{r:f+(u||1)-1,c:o+v-1}});var X=F.t||F["data-t"]||"";if(!_.length){o+=v;continue}if(_=Ms(_),h.s.r>f&&(h.s.r=f),h.e.r<f&&(h.e.r=f),h.s.c>o&&(h.s.c=o),h.e.c<o&&(h.e.c=o),!_.length){o+=v;continue}var I={t:"s",v:_};r.raw||!_.trim().length||X=="s"||(_==="TRUE"?I={t:"b",v:!0}:_==="FALSE"?I={t:"b",v:!1}:isNaN(st(_))?isNaN(Qt(_).getDate())||(I={t:"d",v:dr(_)},r.cellDates||(I={t:"n",v:Ir(I.v)}),I.z=r.dateNF||Ce[14]):I={t:"n",v:st(_)}),r.dense?(a[f]||(a[f]=[]),a[f][o]=I):a[ye({r:f,c:o})]=I,o+=v}}}}return a["!ref"]=Le(h),x.length&&(a["!merges"]=x),a}function kd(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var c=0,l=0,f=0;f<n.length;++f)if(!(n[f].s.r>r||n[f].s.c>i)&&!(n[f].e.r<r||n[f].e.c<i)){if(n[f].s.r<r||n[f].s.c<i){c=-1;break}c=n[f].e.r-n[f].s.r+1,l=n[f].e.c-n[f].s.c+1;break}if(!(c<0)){var o=ye({r,c:i}),u=a.dense?(e[r]||[])[i]:e[o],v=u&&u.v!=null&&(u.h||$0(u.w||(mt(u),u.w)||""))||"",h={};c>1&&(h.rowspan=c),l>1&&(h.colspan=l),a.editable?v='<span contenteditable="true">'+v+"</span>":u&&(h["data-t"]=u&&u.t||"z",u.v!=null&&(h["data-v"]=u.v),u.z!=null&&(h["data-z"]=u.z),u.l&&(u.l.Target||"#").charAt(0)!="#"&&(v='<a href="'+u.l.Target+'">'+v+"</a>")),h.id=(a.id||"sjs")+"-"+o,s.push(sf("td",v,h))}}var x="<tr>";return x+s.join("")+"</tr>"}var Ed='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',wd="</body></html>";function Ad(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return Ot(fs(r[0],t),t);var a=fn();return r.forEach(function(n,s){on(a,fs(n,t),"Sheet"+(s+1))}),a}function Fd(e,t,r){var a=[];return a.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Sd(e,t){var r=t||{},a=r.header!=null?r.header:Ed,n=r.footer!=null?r.footer:wd,s=[a],i=ta(e["!ref"]);r.dense=Array.isArray(e),s.push(Fd(e,i,r));for(var c=i.s.r;c<=i.e.r;++c)s.push(kd(e,i,c,r));return s.push("</table>"+n),s.join("")}function $i(e,t,r){var a=r||{},n=0,s=0;if(a.origin!=null)if(typeof a.origin=="number")n=a.origin;else{var i=typeof a.origin=="string"?Or(a.origin):a.origin;n=i.r,s=i.c}var c=t.getElementsByTagName("tr"),l=Math.min(a.sheetRows||1e7,c.length),f={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var o=ta(e["!ref"]);f.s.r=Math.min(f.s.r,o.s.r),f.s.c=Math.min(f.s.c,o.s.c),f.e.r=Math.max(f.e.r,o.e.r),f.e.c=Math.max(f.e.c,o.e.c),n==-1&&(f.e.r=n=o.e.r+1)}var u=[],v=0,h=e["!rows"]||(e["!rows"]=[]),x=0,d=0,p=0,A=0,O=0,_=0;for(e["!cols"]||(e["!cols"]=[]);x<c.length&&d<l;++x){var b=c[x];if(os(b)){if(a.display)continue;h[d]={hidden:!0}}var U=b.children;for(p=A=0;p<U.length;++p){var R=U[p];if(!(a.display&&os(R))){var F=R.hasAttribute("data-v")?R.getAttribute("data-v"):R.hasAttribute("v")?R.getAttribute("v"):Ms(R.innerHTML),X=R.getAttribute("data-z")||R.getAttribute("z");for(v=0;v<u.length;++v){var I=u[v];I.s.c==A+s&&I.s.r<d+n&&d+n<=I.e.r&&(A=I.e.c+1-s,v=-1)}_=+R.getAttribute("colspan")||1,((O=+R.getAttribute("rowspan")||1)>1||_>1)&&u.push({s:{r:d+n,c:A+s},e:{r:d+n+(O||1)-1,c:A+s+(_||1)-1}});var G={t:"s",v:F},Y=R.getAttribute("data-t")||R.getAttribute("t")||"";F!=null&&(F.length==0?G.t=Y||"z":a.raw||F.trim().length==0||Y=="s"||(F==="TRUE"?G={t:"b",v:!0}:F==="FALSE"?G={t:"b",v:!1}:isNaN(st(F))?isNaN(Qt(F).getDate())||(G={t:"d",v:dr(F)},a.cellDates||(G={t:"n",v:Ir(G.v)}),G.z=a.dateNF||Ce[14]):G={t:"n",v:st(F)})),G.z===void 0&&X!=null&&(G.z=X);var P="",te=R.getElementsByTagName("A");if(te&&te.length)for(var _e=0;_e<te.length&&!(te[_e].hasAttribute("href")&&(P=te[_e].getAttribute("href"),P.charAt(0)!="#"));++_e);P&&P.charAt(0)!="#"&&(G.l={Target:P}),a.dense?(e[d+n]||(e[d+n]=[]),e[d+n][A+s]=G):e[ye({c:A+s,r:d+n})]=G,f.e.c<A+s&&(f.e.c=A+s),A+=_}}++d}return u.length&&(e["!merges"]=(e["!merges"]||[]).concat(u)),f.e.r=Math.max(f.e.r,d-1+n),e["!ref"]=Le(f),d>=l&&(e["!fullref"]=Le((f.e.r=c.length-x+d-1+n,f))),e}function Gi(e,t){var r=t||{},a=r.dense?[]:{};return $i(a,e,t)}function yd(e,t){return Ot(Gi(e,t),t)}function os(e){var t="",r=Cd(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Cd(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function Od(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,n){return Array(parseInt(n,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Me(t.replace(/<[^>]*>/g,""));return[r]}var ls={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Xi(e,t){var r=t||{},a=G0(e),n=[],s,i,c={name:""},l="",f=0,o,u,v={},h=[],x=r.dense?[]:{},d,p,A={value:""},O="",_=0,b=[],U=-1,R=-1,F={s:{r:1e6,c:1e7},e:{r:0,c:0}},X=0,I={},G=[],Y={},P=0,te=0,_e=[],ie=1,me=1,pe=[],Ge={Names:[]},K={},ke=["",""],Ae=[],y={},V="",N=0,D=!1,J=!1,oe=0;for(wa.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");d=wa.exec(a);)switch(d[3]=d[3].replace(/_.*$/,"")){case"table":case"工作表":d[1]==="/"?(F.e.c>=F.s.c&&F.e.r>=F.s.r?x["!ref"]=Le(F):x["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=F.e.r&&(x["!fullref"]=x["!ref"],F.e.r=r.sheetRows-1,x["!ref"]=Le(F)),G.length&&(x["!merges"]=G),_e.length&&(x["!rows"]=_e),o.name=o.名称||o.name,typeof JSON<"u"&&JSON.stringify(o),h.push(o.name),v[o.name]=x,J=!1):d[0].charAt(d[0].length-2)!=="/"&&(o=we(d[0],!1),U=R=-1,F.s.r=F.s.c=1e7,F.e.r=F.e.c=0,x=r.dense?[]:{},G=[],_e=[],J=!0);break;case"table-row-group":d[1]==="/"?--X:++X;break;case"table-row":case"行":if(d[1]==="/"){U+=ie,ie=1;break}if(u=we(d[0],!1),u.行号?U=u.行号-1:U==-1&&(U=0),ie=+u["number-rows-repeated"]||1,ie<10)for(oe=0;oe<ie;++oe)X>0&&(_e[U+oe]={level:X});R=-1;break;case"covered-table-cell":d[1]!=="/"&&++R,r.sheetStubs&&(r.dense?(x[U]||(x[U]=[]),x[U][R]={t:"z"}):x[ye({r:U,c:R})]={t:"z"}),O="",b=[];break;case"table-cell":case"数据":if(d[0].charAt(d[0].length-2)==="/")++R,A=we(d[0],!1),me=parseInt(A["number-columns-repeated"]||"1",10),p={t:"z",v:null},A.formula&&r.cellFormula!=!1&&(p.f=ns(Me(A.formula))),(A.数据类型||A["value-type"])=="string"&&(p.t="s",p.v=Me(A["string-value"]||""),r.dense?(x[U]||(x[U]=[]),x[U][R]=p):x[ye({r:U,c:R})]=p),R+=me-1;else if(d[1]!=="/"){++R,O="",_=0,b=[],me=1;var de=ie?U+ie-1:U;if(R>F.e.c&&(F.e.c=R),R<F.s.c&&(F.s.c=R),U<F.s.r&&(F.s.r=U),de>F.e.r&&(F.e.r=de),A=we(d[0],!1),Ae=[],y={},p={t:A.数据类型||A["value-type"],v:null},r.cellFormula)if(A.formula&&(A.formula=Me(A.formula)),A["number-matrix-columns-spanned"]&&A["number-matrix-rows-spanned"]&&(P=parseInt(A["number-matrix-rows-spanned"],10)||0,te=parseInt(A["number-matrix-columns-spanned"],10)||0,Y={s:{r:U,c:R},e:{r:U+P-1,c:R+te-1}},p.F=Le(Y),pe.push([Y,p.F])),A.formula)p.f=ns(A.formula);else for(oe=0;oe<pe.length;++oe)U>=pe[oe][0].s.r&&U<=pe[oe][0].e.r&&R>=pe[oe][0].s.c&&R<=pe[oe][0].e.c&&(p.F=pe[oe][1]);switch((A["number-columns-spanned"]||A["number-rows-spanned"])&&(P=parseInt(A["number-rows-spanned"],10)||0,te=parseInt(A["number-columns-spanned"],10)||0,Y={s:{r:U,c:R},e:{r:U+P-1,c:R+te-1}},G.push(Y)),A["number-columns-repeated"]&&(me=parseInt(A["number-columns-repeated"],10)),p.t){case"boolean":p.t="b",p.v=qe(A["boolean-value"]);break;case"float":p.t="n",p.v=parseFloat(A.value);break;case"percentage":p.t="n",p.v=parseFloat(A.value);break;case"currency":p.t="n",p.v=parseFloat(A.value);break;case"date":p.t="d",p.v=dr(A["date-value"]),r.cellDates||(p.t="n",p.v=Ir(p.v)),p.z="m/d/yy";break;case"time":p.t="n",p.v=Hc(A["time-value"])/86400,r.cellDates&&(p.t="d",p.v=r0(p.v)),p.z="HH:MM:SS";break;case"number":p.t="n",p.v=parseFloat(A.数据数值);break;default:if(p.t==="string"||p.t==="text"||!p.t)p.t="s",A["string-value"]!=null&&(O=Me(A["string-value"]),b=[]);else throw new Error("Unsupported value type "+p.t)}}else{if(D=!1,p.t==="s"&&(p.v=O||"",b.length&&(p.R=b),D=_==0),K.Target&&(p.l=K),Ae.length>0&&(p.c=Ae,Ae=[]),O&&r.cellText!==!1&&(p.w=O),D&&(p.t="z",delete p.v),(!D||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=U))for(var fe=0;fe<ie;++fe){if(me=parseInt(A["number-columns-repeated"]||"1",10),r.dense)for(x[U+fe]||(x[U+fe]=[]),x[U+fe][R]=fe==0?p:gr(p);--me>0;)x[U+fe][R+me]=gr(p);else for(x[ye({r:U+fe,c:R})]=p;--me>0;)x[ye({r:U+fe,c:R+me})]=gr(p);F.e.c<=R&&(F.e.c=R)}me=parseInt(A["number-columns-repeated"]||"1",10),R+=me-1,me=0,p={},O="",b=[]}K={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(d[1]==="/"){if((s=n.pop())[0]!==d[3])throw"Bad state: "+s}else d[0].charAt(d[0].length-2)!=="/"&&n.push([d[3],!0]);break;case"annotation":if(d[1]==="/"){if((s=n.pop())[0]!==d[3])throw"Bad state: "+s;y.t=O,b.length&&(y.R=b),y.a=V,Ae.push(y)}else d[0].charAt(d[0].length-2)!=="/"&&n.push([d[3],!1]);V="",N=0,O="",_=0,b=[];break;case"creator":d[1]==="/"?V=a.slice(N,d.index):N=d.index+d[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(d[1]==="/"){if((s=n.pop())[0]!==d[3])throw"Bad state: "+s}else d[0].charAt(d[0].length-2)!=="/"&&n.push([d[3],!1]);O="",_=0,b=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(d[1]==="/"){if(I[c.name]=l,(s=n.pop())[0]!==d[3])throw"Bad state: "+s}else d[0].charAt(d[0].length-2)!=="/"&&(l="",c=we(d[0],!1),n.push([d[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":case"date-style":i=we(d[0],!1),l+=ls[d[3]][i.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(n[n.length-1][0]){case"time-style":case"date-style":i=we(d[0],!1),l+=ls[d[3]][i.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(d[0].slice(-2)==="/>")break;if(d[1]==="/")switch(n[n.length-1][0]){case"number-style":case"date-style":case"time-style":l+=a.slice(f,d.index);break}else f=d.index+d[0].length;break;case"named-range":i=we(d[0],!1),ke=v0(i["cell-range-address"]);var ee={Name:i.name,Ref:ke[0]+"!"+ke[1]};J&&(ee.Sheet=h.length),Ge.Names.push(ee);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(d[1]==="/"&&(!A||!A["string-value"])){var Re=Od(a.slice(_,d.index));O=(O.length>0?O+`
`:"")+Re[0]}else we(d[0],!1),_=d.index+d[0].length;break;case"s":break;case"database-range":if(d[1]==="/")break;try{ke=v0(we(d[0])["target-range-address"]),v[ke[0]]["!autofilter"]={ref:ke[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(d[1]!=="/"){if(K=we(d[0],!1),!K.href)break;K.Target=Me(K.href),delete K.href,K.Target.charAt(0)=="#"&&K.Target.indexOf(".")>-1?(ke=v0(K.Target.slice(1)),K.Target="#"+ke[0]+"!"+ke[1]):K.Target.match(/^\.\.[\\\/]/)&&(K.Target=K.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(d[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(d)}}var C={Sheets:v,SheetNames:h,Workbook:Ge};return r.bookSheets&&delete C.Sheets,C}function us(e,t){t=t||{},Xr(e,"META-INF/manifest.xml")&&Gf(cr(e,"META-INF/manifest.xml"),t);var r=Br(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=Xi(Je(r),t);return Xr(e,"meta.xml")&&(a.Props=ni(cr(e,"meta.xml"))),a}function hs(e,t){return Xi(e,t)}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function an(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function C0(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):Je(Vt(e))}function O0(e){var t=e.reduce(function(n,s){return n+s.length},0),r=new Uint8Array(t),a=0;return e.forEach(function(n){r.set(n,a),a+=n.length}),r}function xs(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Dd(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,a=e[t+14]&1,n=t+13;n>=t;--n)a=a*256+e[n];return(e[t+15]&128?-a:a)*Math.pow(10,r-6176)}function ya(e,t){var r=t?t[0]:0,a=e[r]&127;e:if(e[r++]>=128&&(a|=(e[r]&127)<<7,e[r++]<128||(a|=(e[r]&127)<<14,e[r++]<128)||(a|=(e[r]&127)<<21,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),a}function ur(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function wr(e){for(var t=[],r=[0];r[0]<e.length;){var a=r[0],n=ya(e,r),s=n&7;n=Math.floor(n/8);var i=0,c;if(n==0)break;switch(s){case 0:{for(var l=r[0];e[r[0]++]>=128;);c=e.slice(l,r[0])}break;case 5:i=4,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 1:i=8,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 2:i=ya(e,r),c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(n," at offset ").concat(a))}var f={data:c,type:s};t[n]==null?t[n]=[f]:t[n].push(f)}return t}function nn(e,t){return(e==null?void 0:e.map(function(r){return t(r.data)}))||[]}function Id(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=ya(e,a),s=wr(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:ur(s[1][0].data),messages:[]};s[2].forEach(function(c){var l=wr(c.data),f=ur(l[3][0].data);i.messages.push({meta:l,data:e.slice(a[0],a[0]+f)}),a[0]+=f}),(t=s[3])!=null&&t[0]&&(i.merge=ur(s[3][0].data)>>>0>0),r.push(i)}return r}function Rd(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=ya(t,r),n=[];r[0]<t.length;){var s=t[r[0]]&3;if(s==0){var i=t[r[0]++]>>2;if(i<60)++i;else{var c=i-59;i=t[r[0]],c>1&&(i|=t[r[0]+1]<<8),c>2&&(i|=t[r[0]+2]<<16),c>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=c}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}else{var l=0,f=0;if(s==1?(f=(t[r[0]]>>2&7)+4,l=(t[r[0]++]&224)<<3,l|=t[r[0]++]):(f=(t[r[0]++]>>2)+1,s==2?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[O0(n)],l==0)throw new Error("Invalid offset 0");if(l>n[0].length)throw new Error("Invalid offset beyond length");if(f>=l)for(n.push(n[0].slice(-l)),f-=l;f>=n[n.length-1].length;)n.push(n[n.length-1]),f-=n[n.length-1].length;n.push(n[0].slice(-l,-l+f))}}var o=O0(n);if(o.length!=a)throw new Error("Unexpected length: ".concat(o.length," != ").concat(a));return o}function Nd(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Rd(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return O0(t)}function bd(e,t,r,a){var n=an(e),s=n.getUint32(4,!0),i=(a>1?12:8)+xs(s&(a>1?3470:398))*4,c=-1,l=-1,f=NaN,o=new Date(2001,0,1);s&512&&(c=n.getUint32(i,!0),i+=4),i+=xs(s&(a>1?12288:4096))*4,s&16&&(l=n.getUint32(i,!0),i+=4),s&32&&(f=n.getFloat64(i,!0),i+=8),s&64&&(o.setTime(o.getTime()+n.getFloat64(i,!0)*1e3),i+=8);var u;switch(e[2]){case 0:break;case 2:u={t:"n",v:f};break;case 3:u={t:"s",v:t[l]};break;case 5:u={t:"d",v:o};break;case 6:u={t:"b",v:f>0};break;case 7:u={t:"n",v:f/86400};break;case 8:u={t:"e",v:0};break;case 9:if(c>-1)u={t:"s",v:r[c]};else if(l>-1)u={t:"s",v:t[l]};else if(!isNaN(f))u={t:"n",v:f};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return u}function Pd(e,t,r){var a=an(e),n=a.getUint32(8,!0),s=12,i=-1,c=-1,l=NaN,f=NaN,o=new Date(2001,0,1);n&1&&(l=Dd(e,s),s+=16),n&2&&(f=a.getFloat64(s,!0),s+=8),n&4&&(o.setTime(o.getTime()+a.getFloat64(s,!0)*1e3),s+=8),n&8&&(c=a.getUint32(s,!0),s+=4),n&16&&(i=a.getUint32(s,!0),s+=4);var u;switch(e[1]){case 0:break;case 2:u={t:"n",v:l};break;case 3:u={t:"s",v:t[c]};break;case 5:u={t:"d",v:o};break;case 6:u={t:"b",v:f>0};break;case 7:u={t:"n",v:f/86400};break;case 8:u={t:"e",v:0};break;case 9:if(i>-1)u={t:"s",v:r[i]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)));break;case 10:u={t:"n",v:l};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)))}return u}function Ld(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return bd(e,t,r,e[0]);case 5:return Pd(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function St(e){var t=wr(e);return ya(t[1][0].data)}function ds(e,t){var r=wr(t.data),a=ur(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(i){var c=wr(i.data),l=ur(c[1][0].data)>>>0;switch(a){case 1:s[l]=C0(c[3][0].data);break;case 8:{var f=e[St(c[9][0].data)][0],o=wr(f.data),u=e[St(o[1][0].data)][0],v=ur(u.meta[1][0].data);if(v!=2001)throw new Error("2000 unexpected reference to ".concat(v));var h=wr(u.data);s[l]=h[3].map(function(x){return C0(x.data)}).join("")}break}}),s}function Bd(e,t){var r,a,n,s,i,c,l,f,o,u,v,h,x,d,p=wr(e),A=ur(p[1][0].data)>>>0,O=ur(p[2][0].data)>>>0,_=((a=(r=p[8])==null?void 0:r[0])==null?void 0:a.data)&&ur(p[8][0].data)>0||!1,b,U;if((s=(n=p[7])==null?void 0:n[0])!=null&&s.data&&t!=0)b=(c=(i=p[7])==null?void 0:i[0])==null?void 0:c.data,U=(f=(l=p[6])==null?void 0:l[0])==null?void 0:f.data;else if((u=(o=p[4])==null?void 0:o[0])!=null&&u.data&&t!=1)b=(h=(v=p[4])==null?void 0:v[0])==null?void 0:h.data,U=(d=(x=p[3])==null?void 0:x[0])==null?void 0:d.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var R=_?4:1,F=an(b),X=[],I=0;I<b.length/2;++I){var G=F.getUint16(I*2,!0);G<65535&&X.push([I,G])}if(X.length!=O)throw"Expected ".concat(O," cells, found ").concat(X.length);var Y=[];for(I=0;I<X.length-1;++I)Y[X[I][0]]=U.subarray(X[I][1]*R,X[I+1][1]*R);return X.length>=1&&(Y[X[X.length-1][0]]=U.subarray(X[X.length-1][1]*R)),{R:A,cells:Y}}function Md(e,t){var r,a=wr(t.data),n=(r=a==null?void 0:a[7])!=null&&r[0]?ur(a[7][0].data)>>>0>0?1:0:-1,s=nn(a[5],function(i){return Bd(i,n)});return{nrows:ur(a[4][0].data)>>>0,data:s.reduce(function(i,c){return i[c.R]||(i[c.R]=[]),c.cells.forEach(function(l,f){if(i[c.R][f])throw new Error("Duplicate cell r=".concat(c.R," c=").concat(f));i[c.R][f]=l}),i},[])}}function Ud(e,t,r){var a,n=wr(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(ur(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(ur(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=Le(s);var i=wr(n[4][0].data),c=ds(e,e[St(i[4][0].data)][0]),l=(a=i[17])!=null&&a[0]?ds(e,e[St(i[17][0].data)][0]):[],f=wr(i[3][0].data),o=0;f[1].forEach(function(u){var v=wr(u.data),h=e[St(v[2][0].data)][0],x=ur(h.meta[1][0].data);if(x!=6002)throw new Error("6001 unexpected reference to ".concat(x));var d=Md(e,h);d.data.forEach(function(p,A){p.forEach(function(O,_){var b=ye({r:o+A,c:_}),U=Ld(O,c,l);U&&(r[b]=U)})}),o+=d.nrows})}function Hd(e,t){var r=wr(t.data),a={"!ref":"A1"},n=e[St(r[2][0].data)],s=ur(n[0].meta[1][0].data);if(s!=6001)throw new Error("6000 unexpected reference to ".concat(s));return Ud(e,n[0],a),a}function Vd(e,t){var r,a=wr(t.data),n={name:(r=a[1])!=null&&r[0]?C0(a[1][0].data):"",sheets:[]},s=nn(a[2],St);return s.forEach(function(i){e[i].forEach(function(c){var l=ur(c.meta[1][0].data);l==6e3&&n.sheets.push(Hd(e,c))})}),n}function Wd(e,t){var r=fn(),a=wr(t.data),n=nn(a[1],St);if(n.forEach(function(s){e[s].forEach(function(i){var c=ur(i.meta[1][0].data);if(c==2){var l=Vd(e,i);l.sheets.forEach(function(f,o){on(r,f,o==0?l.name:l.name+"_"+o,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function g0(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach(function(l){if(l.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(l){if(l.name.match(/\.iwa$/)){var f;try{f=Nd(l.content)}catch(u){return console.log("?? "+l.content.length+" "+(u.message||u))}var o;try{o=Id(f)}catch(u){return console.log("## "+(u.message||u))}o.forEach(function(u){s[u.id]=u.messages,i.push(u.id)})}}),!i.length)throw new Error("File has no messages");var c=((n=(a=(r=(t=s==null?void 0:s[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:a[1])==null?void 0:n[0].data)&&ur(s[1][0].meta[1][0].data)==1&&s[1][0];if(c||i.forEach(function(l){s[l].forEach(function(f){var o=ur(f.meta[1][0].data)>>>0;if(o==1)if(!c)c=f;else throw new Error("Document has multiple roots")})}),!c)throw new Error("Cannot find Document root");return Wd(s,c)}function $d(e){return function(r){for(var a=0;a!=e.length;++a){var n=e[a];r[n[0]]===void 0&&(r[n[0]]=n[1]),n[2]==="n"&&(r[n[0]]=Number(r[n[0]]))}}}function sn(e){$d([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Gd(e){return jt.WS.indexOf(e)>-1?"sheet":e==jt.CS?"chart":e==jt.DS?"dialog":e==jt.MS?"macro":e&&e.length?e:"sheet"}function Xd(e,t){if(!e)return 0;try{e=t.map(function(a){return a.id||(a.id=a.strRelID),[a.name,e["!id"][a.id].Target,Gd(e["!id"][a.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function zd(e,t,r,a,n,s,i,c,l,f,o,u){try{s[a]=va(Br(e,r,!0),t);var v=cr(e,t),h;switch(c){case"sheet":h=Zx(v,t,n,l,s[a],f,o,u);break;case"chart":if(h=Qx(v,t,n,l,s[a],f,o,u),!h||!h["!drawel"])break;var x=oa(h["!drawel"].Target,t),d=w0(x),p=qu(Br(e,x,!0),va(Br(e,d,!0),x)),A=oa(p,x),O=w0(A);h=Px(Br(e,A,!0),A,l,va(Br(e,O,!0),A),f,h);break;case"macro":h=ed(v,t,n,l,s[a],f,o,u);break;case"dialog":h=rd(v,t,n,l,s[a],f,o,u);break;default:throw new Error("Unrecognized sheet type "+c)}i[a]=h;var _=[];s&&s[a]&&lt(s[a]).forEach(function(b){var U="";if(s[a][b].Type==jt.CMNT){U=oa(s[a][b].Target,t);var R=sd(cr(e,U,!0),U,l);if(!R||!R.length)return;Qn(h,R,!1)}s[a][b].Type==jt.TCMNT&&(U=oa(s[a][b].Target,t),_=_.concat(Qu(cr(e,U,!0),l)))}),_&&_.length&&Qn(h,_,!0,l.people||[])}catch(b){if(l.WTF)throw b}}function $r(e){return e.charAt(0)=="/"?e.slice(1):e}function Kd(e,t){if(Os(),t=t||{},sn(t),Xr(e,"META-INF/manifest.xml")||Xr(e,"objectdata.xml"))return us(e,t);if(Xr(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof g0<"u"){if(e.FileIndex)return g0(e);var r=Pe.utils.cfb_new();return Sn(e).forEach(function(_e){zc(r,_e,Xc(e,_e))}),g0(r)}throw new Error("Unsupported NUMBERS file")}if(!Xr(e,"[Content_Types].xml"))throw Xr(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):Xr(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var a=Sn(e),n=Wf(Br(e,"[Content_Types].xml")),s=!1,i,c;if(n.workbooks.length===0&&(c="xl/workbook.xml",cr(e,c,!0)&&n.workbooks.push(c)),n.workbooks.length===0){if(c="xl/workbook.bin",!cr(e,c,!0))throw new Error("Could not find workbook");n.workbooks.push(c),s=!0}n.workbooks[0].slice(-3)=="bin"&&(s=!0);var l={},f={};if(!t.bookSheets&&!t.bookProps){if(ma=[],n.sst)try{ma=nd(cr(e,$r(n.sst)),n.sst,t)}catch(_e){if(t.WTF)throw _e}t.cellStyles&&n.themes.length&&(l=ad(Br(e,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],t)),n.style&&(f=td(cr(e,$r(n.style)),n.style,l,t))}n.links.map(function(_e){try{var ie=va(Br(e,w0($r(_e))),_e);return cd(cr(e,$r(_e)),ie,_e,t)}catch{}});var o=qx(cr(e,$r(n.workbooks[0])),n.workbooks[0],t),u={},v="";n.coreprops.length&&(v=cr(e,$r(n.coreprops[0]),!0),v&&(u=ni(v)),n.extprops.length!==0&&(v=cr(e,$r(n.extprops[0]),!0),v&&Kf(v,u,t)));var h={};(!t.bookSheets||t.bookProps)&&n.custprops.length!==0&&(v=Br(e,$r(n.custprops[0]),!0),v&&(h=jf(v,t)));var x={};if((t.bookSheets||t.bookProps)&&(o.Sheets?i=o.Sheets.map(function(ie){return ie.name}):u.Worksheets&&u.SheetNames.length>0&&(i=u.SheetNames),t.bookProps&&(x.Props=u,x.Custprops=h),t.bookSheets&&typeof i<"u"&&(x.SheetNames=i),t.bookSheets?x.SheetNames:t.bookProps))return x;i={};var d={};t.bookDeps&&n.calcchain&&(d=id(cr(e,$r(n.calcchain)),n.calcchain));var p=0,A={},O,_;{var b=o.Sheets;u.Worksheets=b.length,u.SheetNames=[];for(var U=0;U!=b.length;++U)u.SheetNames[U]=b[U].name}var R=s?"bin":"xml",F=n.workbooks[0].lastIndexOf("/"),X=(n.workbooks[0].slice(0,F+1)+"_rels/"+n.workbooks[0].slice(F+1)+".rels").replace(/^\//,"");Xr(e,X)||(X="xl/_rels/workbook."+R+".rels");var I=va(Br(e,X,!0),X.replace(/_rels.*/,"s5s"));(n.metadata||[]).length>=1&&(t.xlmeta=fd(cr(e,$r(n.metadata[0])),n.metadata[0],t)),(n.people||[]).length>=1&&(t.people=e1(cr(e,$r(n.people[0])),t)),I&&(I=Xd(I,o.Sheets));var G=cr(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(p=0;p!=u.Worksheets;++p){var Y="sheet";if(I&&I[p]?(O="xl/"+I[p][1].replace(/[\/]?xl\//,""),Xr(e,O)||(O=I[p][1]),Xr(e,O)||(O=X.replace(/_rels\/.*$/,"")+I[p][1]),Y=I[p][2]):(O="xl/worksheets/sheet"+(p+1-G)+"."+R,O=O.replace(/sheet0\./,"sheet.")),_=O.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(p!=t.sheets)continue e;break;case"string":if(u.SheetNames[p].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var P=!1,te=0;te!=t.sheets.length;++te)typeof t.sheets[te]=="number"&&t.sheets[te]==p&&(P=1),typeof t.sheets[te]=="string"&&t.sheets[te].toLowerCase()==u.SheetNames[p].toLowerCase()&&(P=1);if(!P)continue e}}zd(e,O,_,u.SheetNames[p],p,A,i,Y,t,o,l,f)}return x={Directory:n,Workbook:o,Props:u,Custprops:h,Deps:d,Sheets:i,SheetNames:u.SheetNames,Strings:ma,Styles:f,Themes:l,SSF:gr(Ce)},t&&t.bookFiles&&(e.files?(x.keys=a,x.files=e.files):(x.keys=[],x.files={},e.FullPaths.forEach(function(_e,ie){_e=_e.replace(/^Root Entry[\/]/,""),x.keys.push(_e),x.files[_e]=e.FileIndex[ie]}))),t&&t.bookVBA&&(n.vba.length>0?x.vbaraw=cr(e,$r(n.vba[0]),!0):n.defaults&&n.defaults.bin===n1&&(x.vbaraw=cr(e,"xl/vbaProject.bin",!0))),x}function Yd(e,t){var r=t||{},a="Workbook",n=Pe.find(e,a);try{if(a="/!DataSpaces/Version",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(Hl(n.content),a="/!DataSpaces/DataSpaceMap",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=Wl(n.content);if(s.length!==1||s[0].comps.length!==1||s[0].comps[0].t!==0||s[0].name!=="StrongEncryptionDataSpace"||s[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=$l(n.content);if(i.length!=1||i[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);Xl(n.content)}catch{}if(a="/EncryptionInfo",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var c=zl(n.content);if(a="/EncryptedPackage",n=Pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(c[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(c[1],n.content,r.password||"",r);if(c[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(c[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function cn(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Mr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function jd(e,t){return Pe.find(e,"EncryptedPackage")?Yd(e,t):Wi(e,t)}function Jd(e,t){var r,a=e,n=t||{};return n.type||(n.type=be&&Buffer.isBuffer(e)?"buffer":"base64"),r=Ps(a,n),Kd(r,n)}function zi(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return S0(e.slice(r),t);default:break e}return Aa.to_workbook(e,t)}function qd(e,t){var r="",a=cn(e,t);switch(t.type){case"base64":r=Mr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=Mt(e);break;default:throw new Error("Unrecognized type "+t.type)}return a[0]==239&&a[1]==187&&a[2]==191&&(r=Je(r)),t.type="binary",zi(r,t)}function Zd(e,t){var r=e;return t.type=="base64"&&(r=Mr(r)),r=T0.utils.decode(1200,r.slice(2),"str"),t.type="binary",zi(r,t)}function Qd(e){return e.match(/[^\x00-\x7F]/)?la(e):e}function m0(e,t,r,a){return a?(r.type="string",Aa.to_workbook(e,r)):Aa.to_workbook(t,r)}function D0(e,t){gs();var r=t||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return D0(new Uint8Array(e),(r=gr(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var a=e,n=[0,0,0,0],s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),Zt={},r.dateNF&&(Zt.dateNF=r.dateNF),r.type||(r.type=be&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=be?"buffer":"binary",a=Mc(e),typeof Uint8Array<"u"&&!be&&(r.type="array")),r.type=="string"&&(s=!0,r.type="binary",r.codepage=65001,a=Qd(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return r=gr(r),r.type="array",D0(M0(a),r)}switch((n=cn(a,r))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return jd(Pe.read(a,r),r);break;case 9:if(n[1]<=8)return Wi(a,r);break;case 60:return S0(a,r);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return yl(a,r);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return Fl.to_workbook(a,r);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?Jd(a,r):m0(e,a,r,s);case 239:return n[3]===60?S0(a,r):m0(e,a,r,s);case 255:if(n[1]===254)return Zd(a,r);if(n[1]===0&&n[2]===2&&n[3]===0)return ga.to_workbook(a,r);break;case 0:if(n[1]===0&&(n[2]>=2&&n[3]===0||n[2]===0&&(n[3]===8||n[3]===9)))return ga.to_workbook(a,r);break;case 3:case 131:case 139:case 140:return Zn.to_workbook(a,r);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return nu.to_workbook(a,r);break;case 10:case 13:case 32:return qd(a,r);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return wl.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?Zn.to_workbook(a,r):m0(e,a,r,s)}function ev(e,t,r,a,n,s,i,c){var l=mr(r),f=c.defval,o=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),u=!0,v=n===1?[]:{};if(n!==1)if(Object.defineProperty)try{Object.defineProperty(v,"__rowNum__",{value:r,enumerable:!1})}catch{v.__rowNum__=r}else v.__rowNum__=r;if(!i||e[r])for(var h=t.s.c;h<=t.e.c;++h){var x=i?e[r][h]:e[a[h]+l];if(x===void 0||x.t===void 0){if(f===void 0)continue;s[h]!=null&&(v[s[h]]=f);continue}var d=x.v;switch(x.t){case"z":if(d==null)break;continue;case"e":d=d==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+x.t)}if(s[h]!=null){if(d==null)if(x.t=="e"&&d===null)v[s[h]]=null;else if(f!==void 0)v[s[h]]=f;else if(o&&d===null)v[s[h]]=null;else continue;else v[s[h]]=o&&(x.t!=="n"||x.t==="n"&&c.rawNumbers!==!1)?d:mt(x,d,c);d!=null&&(u=!1)}}return{row:v,isempty:u}}function I0(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,c="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},o=f.range!=null?f.range:e["!ref"];switch(f.header===1?a=1:f.header==="A"?a=2:Array.isArray(f.header)?a=3:f.header==null&&(a=0),typeof o){case"string":l=tr(o);break;case"number":l=tr(e["!ref"]),l.s.r=o;break;default:l=o}a>0&&(n=0);var u=mr(l.s.r),v=[],h=[],x=0,d=0,p=Array.isArray(e),A=l.s.r,O=0,_={};p&&!e[A]&&(e[A]=[]);var b=f.skipHidden&&e["!cols"]||[],U=f.skipHidden&&e["!rows"]||[];for(O=l.s.c;O<=l.e.c;++O)if(!(b[O]||{}).hidden)switch(v[O]=lr(O),r=p?e[A][O]:e[v[O]+u],a){case 1:s[O]=O-l.s.c;break;case 2:s[O]=v[O];break;case 3:s[O]=f.header[O-l.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),c=i=mt(r,null,f),d=_[i]||0,!d)_[i]=1;else{do c=i+"_"+d++;while(_[c]);_[i]=d,_[c]=1}s[O]=c}for(A=l.s.r+n;A<=l.e.r;++A)if(!(U[A]||{}).hidden){var R=ev(e,l,A,v,a,s,p,f);(R.isempty===!1||(a===1?f.blankrows!==!1:f.blankrows))&&(h[x++]=R.row)}return h.length=x,h}var vs=/"/g;function rv(e,t,r,a,n,s,i,c){for(var l=!0,f=[],o="",u=mr(r),v=t.s.c;v<=t.e.c;++v)if(a[v]){var h=c.dense?(e[r]||[])[v]:e[a[v]+u];if(h==null)o="";else if(h.v!=null){l=!1,o=""+(c.rawNumbers&&h.t=="n"?h.v:mt(h,null,c));for(var x=0,d=0;x!==o.length;++x)if((d=o.charCodeAt(x))===n||d===s||d===34||c.forceQuotes){o='"'+o.replace(vs,'""')+'"';break}o=="ID"&&(o='"ID"')}else h.f!=null&&!h.F?(l=!1,o="="+h.f,o.indexOf(",")>=0&&(o='"'+o.replace(vs,'""')+'"')):o="";f.push(o)}return c.blankrows===!1&&l?null:f.join(i)}function Ki(e,t){var r=[],a=t??{};if(e==null||e["!ref"]==null)return"";var n=tr(e["!ref"]),s=a.FS!==void 0?a.FS:",",i=s.charCodeAt(0),c=a.RS!==void 0?a.RS:`
`,l=c.charCodeAt(0),f=new RegExp((s=="|"?"\\|":s)+"+$"),o="",u=[];a.dense=Array.isArray(e);for(var v=a.skipHidden&&e["!cols"]||[],h=a.skipHidden&&e["!rows"]||[],x=n.s.c;x<=n.e.c;++x)(v[x]||{}).hidden||(u[x]=lr(x));for(var d=0,p=n.s.r;p<=n.e.r;++p)(h[p]||{}).hidden||(o=rv(e,n,p,u,i,l,s,a),o!=null&&(a.strip&&(o=o.replace(f,"")),(o||a.blankrows!==!1)&&r.push((d++?c:"")+o)));return delete a.dense,r.join("")}function tv(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=Ki(e,t);return r}function av(e){var t="",r,a="";if(e==null||e["!ref"]==null)return[];var n=tr(e["!ref"]),s="",i=[],c,l=[],f=Array.isArray(e);for(c=n.s.c;c<=n.e.c;++c)i[c]=lr(c);for(var o=n.s.r;o<=n.e.r;++o)for(s=mr(o),c=n.s.c;c<=n.e.c;++c)if(t=i[c]+s,r=f?(e[o]||[])[c]:e[t],a="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;a=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)a=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)a=""+r.v;else if(r.t=="b")a=r.v?"TRUE":"FALSE";else if(r.w!==void 0)a="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?a="'"+r.v:a=""+r.v}}l[l.length]=t+"="+a}return l}function Yi(e,t,r){var a=r||{},n=+!a.skipHeader,s=e||{},i=0,c=0;if(s&&a.origin!=null)if(typeof a.origin=="number")i=a.origin;else{var l=typeof a.origin=="string"?Or(a.origin):a.origin;i=l.r,c=l.c}var f,o={s:{c:0,r:0},e:{c,r:i+t.length-1+n}};if(s["!ref"]){var u=tr(s["!ref"]);o.e.c=Math.max(o.e.c,u.e.c),o.e.r=Math.max(o.e.r,u.e.r),i==-1&&(i=u.e.r+1,o.e.r=i+t.length-1+n)}else i==-1&&(i=0,o.e.r=t.length-1+n);var v=a.header||[],h=0;t.forEach(function(d,p){lt(d).forEach(function(A){(h=v.indexOf(A))==-1&&(v[h=v.length]=A);var O=d[A],_="z",b="",U=ye({c:c+h,r:i+p+n});f=Ca(s,U),O&&typeof O=="object"&&!(O instanceof Date)?s[U]=O:(typeof O=="number"?_="n":typeof O=="boolean"?_="b":typeof O=="string"?_="s":O instanceof Date?(_="d",a.cellDates||(_="n",O=Ir(O)),b=a.dateNF||Ce[14]):O===null&&a.nullError&&(_="e",O=0),f?(f.t=_,f.v=O,delete f.w,delete f.R,b&&(f.z=b)):s[U]=f={t:_,v:O},b&&(f.z=b))})}),o.e.c=Math.max(o.e.c,c+v.length-1);var x=mr(i);if(n)for(h=0;h<v.length;++h)s[lr(h+c)+x]={t:"s",v:v[h]};return s["!ref"]=Le(o),s}function nv(e,t){return Yi(null,e,t)}function Ca(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var a=Or(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Ca(e,ye(t)):Ca(e,ye({r:t,c:r||0}))}function sv(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function fn(){return{SheetNames:[],Sheets:{}}}function on(e,t,r,a){var n=1;if(!r)for(;n<=65535&&e.SheetNames.indexOf(r="Sheet"+n)!=-1;++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&e.SheetNames.indexOf(r=i+n)!=-1;++n);}if($x(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function iv(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=sv(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r}function cv(e,t){return e.z=t,e}function ji(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function fv(e,t,r){return ji(e,"#"+t,r)}function ov(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function lv(e,t,r,a){for(var n=typeof t!="string"?t:tr(t),s=typeof t=="string"?t:Le(t),i=n.s.r;i<=n.e.r;++i)for(var c=n.s.c;c<=n.e.c;++c){var l=Ca(e,i,c);l.t="n",l.F=s,delete l.v,i==n.s.r&&c==n.s.c&&(l.f=r,a&&(l.D=!0))}return e}var uv={encode_col:lr,encode_row:mr,encode_cell:ye,encode_range:Le,decode_col:K0,decode_row:z0,split_cell:kf,decode_cell:Or,decode_range:ta,format_cell:mt,sheet_add_aoa:Qs,sheet_add_json:Yi,sheet_add_dom:$i,aoa_to_sheet:aa,json_to_sheet:nv,table_to_sheet:Gi,table_to_book:yd,sheet_to_csv:Ki,sheet_to_txt:tv,sheet_to_json:I0,sheet_to_html:Sd,sheet_to_formulae:av,sheet_to_row_object_array:I0,sheet_get_cell:Ca,book_new:fn,book_append_sheet:on,book_set_sheet_visibility:iv,cell_set_number_format:cv,cell_set_hyperlink:ji,cell_set_internal_link:fv,cell_add_comment:ov,sheet_set_array_formula:lv,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};function hv(e,t){if(!t||!Array.isArray(t))return e;const r=new Map;return t.forEach(a=>{a&&a.id&&r.set(a.id,a)}),e.map(a=>{const n=r.get(a.uid);return n?{...a,status:n.status}:a})}async function xv(e){return new Promise((t,r)=>{const a=new FileReader,n=e.name.toLowerCase();n.endsWith(".xlsx")||n.endsWith(".xls")?(a.onload=s=>{try{const i=new Uint8Array(s.target.result),c=D0(i,{type:"array"}),l=c.SheetNames[0],f=c.Sheets[l],o=uv.sheet_to_json(f);if(o.length>0){const u=dv(o);t(u)}else r(new Error("Excel文件中没有数据"))}catch(i){console.error("解析Excel文件失败:",i),r(new Error("解析Excel文件失败"))}},a.readAsArrayBuffer(e)):n.endsWith(".txt")?(a.onload=s=>{try{const i=s.target.result,c=pv(i);t(c)}catch(i){console.error("解析TXT文件失败:",i),r(new Error("解析TXT文件失败"))}},a.readAsText(e)):r(new Error("不支持的文件格式"))})}function dv(e){const t=[],r=e[0],a=Object.keys(r);let n=a.find(c=>c.toLowerCase().includes("uid")||c.toLowerCase()==="id"||c.toLowerCase().includes("用户id")),s=a.find(c=>c.toLowerCase().includes("token")||c.toLowerCase().includes("令牌")),i=a.find(c=>c.toLowerCase().includes("user")||c.toLowerCase().includes("username")||c.toLowerCase().includes("用户")||c.toLowerCase().includes("用户名"));if(!n||!s||!i)if(a.length>=3)n=a[0],s=a[1],i=a[2];else if(a.length===2)n=a[0],s=a[1],i=null;else{if(a.length===1)return vv(e,a[0]);throw new Error("Excel文件格式不正确，无法识别列")}return e.forEach(c=>{c[n]&&c[s]&&t.push({uid:String(c[n]),token:String(c[s]),user:i&&c[i]?String(c[i]):"未知用户"})}),t}function vv(e,t){const r=[];return e.forEach(a=>{const s=String(a[t]).split(/[,;:|\/\\\s]+/).filter(Boolean);s.length>=2&&r.push({uid:s[0],token:s[1],user:s[2]||"未知用户"})}),r}function pv(e){const t=[];return e.split(/\r?\n/).filter(a=>a.trim()).forEach(a=>{if(a.includes("----")){const s=a.split("----");if(s.length>=2){t.push({uid:s[0].trim(),token:s[1].trim(),user:s.length>=3?s[2].trim():"未知用户"});return}}const n=["	",",",";","|"," "];for(const s of n)if(a.includes(s)){const i=a.split(s).filter(Boolean);if(i.length>=2){t.push({uid:i[0].trim(),token:i[1].trim(),user:i.length>=3?i[2].trim():"未知用户"});return}}if(a.length>20){const s=a.substring(0,10).trim(),i=a.substring(10).trim();s&&i&&t.push({uid:s,token:i,user:"未知用户"})}}),t}function gv(e,t="YYYY-MM-DD HH:mm:ss"){if(!e)return"";const r=new Date(e);if(isNaN(r.getTime()))return"";const a=r.getFullYear(),n=String(r.getMonth()+1).padStart(2,"0"),s=String(r.getDate()).padStart(2,"0"),i=String(r.getHours()).padStart(2,"0"),c=String(r.getMinutes()).padStart(2,"0"),l=String(r.getSeconds()).padStart(2,"0");return t.replace("YYYY",a).replace("MM",n).replace("DD",s).replace("HH",i).replace("mm",c).replace("ss",l)}const mv=async e=>{if(!e||!e.uid||!e.token)return{success:!1,message:"Token信息不完整",results:[]};try{const t=await b0([{uid:e.uid,token:e.token}]);if(!t.success)return{success:!1,message:`查询订单失败: ${t.message||"未知错误"}`,results:[]};const r=t.results.find(c=>c.uid===e.uid);if(!r||!r.success||!r.data)return{success:!1,message:"未能获取订单信息",results:[]};const n=(r.data.orders||[]).filter(c=>c.status&&c.status.includes("已评价"));if(n.length===0)return{success:!1,message:"没有可删除的订单，只有已评价的订单可以删除",results:[]};const s=n.map(c=>({uid:e.uid,token:e.token,orderSn:c.orderId}));return await P0(s)}catch(t){return console.error("自动查询和删除订单出错:",t),{success:!1,message:t.message||"操作异常",results:[]}}},Ji=e=>!e||!e.orderInfo||!e.orderInfo.orders?0:e.orderInfo.orders.filter(t=>t.status&&t.status.includes("已评价")).length,_v=e=>{if(!e||!e.orderInfo)return"未查询";if(e.orderInfo.status==="掉线")return"掉线";if(e.orderInfo.status==="在线"&&e.orderInfo.orderCount===0)return"无订单";if(e.orderInfo.status==="在线"&&e.orderInfo.orderCount>0){const t=Ji(e);return`${e.orderInfo.orderCount}个订单 (${t}个可删)`}return e.orderInfo.status||"查询失败"},R0=async(e,t={})=>{if(!e||!e.length)return{success:!0,message:"没有订单需要删除",results:[]};const r=t.batchSize||20,a=t.concurrency||5,n=t.orderProgressCallback,s=[];for(let f=0;f<e.length;f+=r)s.push(e.slice(f,f+r));console.log(`订单已分成${s.length}批，每批${r}个，并发数${a}`);let i=[],c=0;const l=(f,o)=>{if(n)try{n(f,o)}catch(u){console.error("调用进度回调时发生错误:",u,"订单项:",f)}};try{const f=[];for(let o=0;o<s.length;o++){for(;f.filter(v=>v.status==="pending").length>=a;)await new Promise(v=>setTimeout(v,100));const u={status:"pending",promise:null};u.promise=(async()=>{try{const v=await P0(s[o]);return c++,v&&v.success&&v.results&&v.results.forEach(h=>{i.push(h),l({uid:h.uid,orderSn:h.orderSn},h.success)}),u.status="fulfilled",v}catch(v){throw u.status="rejected",console.error(`批次${o+1}删除失败:`,v),s[o].forEach(h=>{const x={uid:h.uid,token:h.token,orderSn:h.orderSn,success:!1,message:v.message||"批量删除请求失败"};i.push(x),l({uid:h.uid,orderSn:h.orderSn},!1)}),v}})(),f.push(u)}return await Promise.all(f.map(o=>o.promise.catch(u=>u))),{success:!0,message:`成功删除${i.filter(o=>o.success).length}个订单`,results:i}}catch(f){return console.error("并发删除订单出错:",f),{success:!1,message:f.message||"并发删除订单出错",results:i}}},qi=async(e,t={})=>{if(!e||!e.length)return{success:!0,message:"没有Token需要处理",results:[]};console.log("开始执行并发查询和删除订单，tokens数量:",e.length);const r=t.progressCallback;let a=[],n=[];try{const s=e.filter(f=>f.orderInfo&&f.orderInfo.orders&&f.orderInfo.orders.length>0),i=e.filter(f=>!f.orderInfo||!f.orderInfo.orders);if(console.log(`已有订单信息的tokens: ${s.length}, 未查询的tokens: ${i.length}`),s.length>0){let f=[];const o={};if(s.forEach(u=>{const v=u.orderInfo.orders.filter(h=>h.status&&h.status.includes("已评价"));console.log(`Token ${u.uid}: 总订单数=${u.orderInfo.orders.length}, 可删除订单数=${v.length}`),n.push({uid:u.uid,token:u.token,isOnline:!0,status:"在线",orders:u.orderInfo.orders||[]}),o[u.uid]={totalOrders:v.length,completedOrders:0,successCount:0,failCount:0},r&&u.uid&&r(u.uid,{totalOrders:v.length,completedOrders:0,successCount:0,failCount:0}),v.forEach(h=>{f.push({uid:u.uid,token:u.token,orderSn:h.orderId})})}),f.length>0){console.log(`准备删除已知订单，总数: ${f.length}`);const v=await R0(f,{...t,orderProgressCallback:(h,x)=>{if(r)try{const d=h==null?void 0:h.uid;if(!d){console.warn("无法更新进度，无效的token UID:",h);return}if(!o[d]){console.warn("无法更新进度，未找到对应的token:",d,h);return}o[d].completedOrders++,x?o[d].successCount++:o[d].failCount++;try{r(d,{...o[d]})}catch(p){console.error("调用进度回调时出错:",p)}}catch(d){if(console.error("处理订单进度回调时出错:",d,h),h!=null&&h.uid)try{r(h.uid,{error:"进度更新错误: "+(d.message||"未知错误")})}catch(p){console.error("尝试发送错误通知也失败了:",p)}}}});if(v.success){const h=v.results.filter(x=>x.success).length;console.log(`已知订单删除结果: 成功=${h}/${v.results.length}`),a=a.concat(v.results)}else console.error("删除已知订单失败:",v.message)}else console.log("没有找到可删除的已知订单")}if(i.length>0){i.forEach(x=>{r&&x.uid&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!0})});const f=i.map(x=>({uid:x.uid,token:x.token}));console.log(`开始查询未知订单信息，tokens数量: ${f.length}`);const o=await b0(f);if(!o||!o.success)return console.error("查询订单信息失败:",(o==null?void 0:o.message)||"未知错误"),i.forEach(x=>{r&&x.uid&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!1,queryError:(o==null?void 0:o.message)||"查询订单信息失败"})}),{success:!1,message:(o==null?void 0:o.message)||"查询订单信息失败",results:a};const u=o.results||[];if(!Array.isArray(u))return console.error("查询结果不是数组:",u),i.forEach(x=>{r&&x.uid&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!1,queryError:"查询结果格式错误"})}),{success:!1,message:"查询结果格式错误",results:a};console.log(`订单查询完成，获取到${u.length}个结果`);let v=[];const h={};if(u.forEach(x=>{if(x.success&&x.data)if(n.push({uid:x.uid,token:x.token,isOnline:x.isOnline!==void 0?x.isOnline:!0,status:x.status||"在线",orders:x.data.orders||[]}),x.data.orders){const d=x.data.orders,p=d.filter(A=>A.status&&A.status.includes("已评价"));console.log(`Token ${x.uid}: 查询到${d.length}个订单，其中${p.length}个可删除`),h[x.uid]={totalOrders:p.length,completedOrders:0,successCount:0,failCount:0,isQuerying:!1},r&&x.uid&&r(x.uid,{totalOrders:p.length,completedOrders:0,successCount:0,failCount:0,isQuerying:!1}),p.forEach(A=>{v.push({uid:x.uid,token:x.token,orderSn:A.orderId})})}else r&&x.uid&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!1,noOrders:!0});else console.warn(`Token ${x.uid} 查询失败或无订单:`,x),r&&x.uid&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!1,queryError:x.message||"查询订单失败"})}),v.length>0){console.log(`准备删除新查询到的订单，总数: ${v.length}`);const d=await R0(v,{...t,orderProgressCallback:(p,A)=>{if(r)try{const O=p==null?void 0:p.uid;if(!O){console.warn("无法更新进度，无效的token UID:",p);return}if(!h[O]){console.warn("无法更新进度，未找到对应的token:",O,p);return}h[O].completedOrders++,A?h[O].successCount++:h[O].failCount++;try{r(O,{...h[O]})}catch(_){console.error("调用进度回调时出错:",_)}}catch(O){if(console.error("处理订单进度回调时出错:",O,p),p!=null&&p.uid)try{r(p.uid,{error:"进度更新错误: "+(O.message||"未知错误")})}catch(_){console.error("尝试发送错误通知也失败了:",_)}}}});if(d.success){const p=d.results.filter(A=>A.success).length;console.log(`新查询订单删除结果: 成功=${p}/${d.results.length}`),a=a.concat(d.results)}else console.error("删除新查询订单失败:",d.message)}else console.log("没有找到可删除的新查询订单"),u.forEach(x=>{x.success&&x.uid&&!h[x.uid]&&r&&r(x.uid,{totalOrders:0,completedOrders:0,successCount:0,failCount:0,isQuerying:!1,noDeleteableOrders:!0})})}const c=a.filter(f=>f.success).length,l=a.length-c;return console.log(`订单处理总结果: 成功=${c}, 失败=${l}, 总数=${a.length}`),console.log(`已查询Token数量: ${n.length}`),{success:!0,message:a.length>0?`处理了${a.length}个订单，成功删除${c}个`:"没有找到可删除的订单，只有已评价的订单可以删除",results:a,queriedTokens:n}}catch(s){return console.error("并发查询和删除订单出错:",s),e.forEach(i=>{r&&i.uid&&r(i.uid,{error:s.message||"删除订单失败"})}),{success:!1,message:s.message||"并发查询和删除订单出错",results:a}}},Tv=Object.freeze(Object.defineProperty({__proto__:null,autoQueryAndDeleteOrders:mv,concurrentDeleteOrders:R0,concurrentQueryAndDeleteOrders:qi,countDeletableOrders:Ji,formatOrderStatusText:_v},Symbol.toStringTag,{value:"Module"})),kv=e=>e&&e.includes("已评价"),ln=e=>!e||!e.orderInfo||!e.orderInfo.orders||!Array.isArray(e.orderInfo.orders)?0:e.orderInfo.orders.filter(t=>kv(t.status)).length,Ev=async(e,t={},r)=>{if(!e||!e.uid||!e.token)return{success:!1,message:"Token信息不完整"};try{r&&r({uid:e.uid,inProgress:!0,totalOrders:0,completedOrders:0,successCount:0,failCount:0});const n={...t,progressCallback:i=>{r&&r({uid:e.uid,inProgress:!0,...i})}},s=await Qi(()=>Promise.resolve().then(()=>Tv),void 0).then(i=>i.concurrentQueryAndDeleteOrders([e],n));if(r){const i=s.results?s.results.filter(l=>l.success).length:0,c=s.results?s.results.length-i:0;r({uid:e.uid,inProgress:!1,totalOrders:s.results?s.results.length:0,completedOrders:s.results?s.results.length:0,successCount:i,failCount:c,results:s.results||[]})}return Zi(s),s}catch(a){return console.error("删除Token订单出错:",a),r&&r({uid:e.uid,inProgress:!1,error:a.message||"删除订单时发生错误"}),{success:!1,message:a.message||"删除Token订单时发生错误",results:[]}}},Zi=e=>{if(!e)return;const t=e.results?e.results.filter(a=>a.success).length:0,r=e.results?e.results.length-t:0;t>0?ua({title:"删除订单完成",message:`成功删除 ${t} 个订单${r>0?`，${r} 个删除失败`:""}`,type:"success",duration:5e3}):e.results&&e.results.length>0?ua({title:"删除订单未成功",message:`未能成功删除任何订单，${r} 个删除失败`,type:"warning",duration:5e3}):ua({title:"无可删除订单",message:"查询完成，但没有找到可删除的订单（只有已评价的订单可以删除）",type:"info",duration:5e3})},wv={class:"order-status-component"},Av={key:0,class:"order-info"},Fv={key:1,class:"order-info"},Sv={key:2,class:"order-info"},yv={key:0,class:"order-status-container"},Cv={class:"order-number"},Ov={class:"order-deletion-status"},Dv={key:1,class:"order-status-text"},Iv={class:"deletable-count"},Rv={class:"order-number"},Nv={key:3,class:"order-info"},bv={class:"status-badge status-error"},Pv={__name:"OrderStatus",props:{orderInfo:{type:Object,default:()=>({status:"未查询",orderCount:0,orders:[]})},orderDeletionStatus:{type:Object,default:()=>null}},emits:["view-orders"],setup(e){const t=e,r=rt(()=>{const i={orderInfo:t.orderInfo};return ln(i)}),a=rt(()=>{if(!t.orderDeletionStatus)return 0;const{totalOrders:i,completedOrders:c}=t.orderDeletionStatus;return!i||i===0?0:Math.floor(c/i*100)}),n=rt(()=>t.orderDeletionStatus?t.orderDeletionStatus.error?"exception":a.value===100?"success":"":""),s=rt(()=>{if(!t.orderDeletionStatus)return"";const{totalOrders:i,completedOrders:c,successCount:l,failCount:f}=t.orderDeletionStatus;return t.orderDeletionStatus.error?`删除失败: ${t.orderDeletionStatus.error}`:i===0?"正在查询订单...":`删除中: ${c}/${i} (成功:${l}, 失败:${f})`});return(i,c)=>{const l=Ie("el-progress"),f=Ie("el-tag");return $e(),sr("div",wv,[e.orderInfo.status==="掉线"?($e(),sr("div",Av,c[2]||(c[2]=[se("div",{class:"status-badge status-offline"},[se("i",{class:"el-icon-warning-outline"}),se("span",null,"掉线")],-1)]))):e.orderInfo.status==="在线"&&e.orderInfo.orderCount===0?($e(),sr("div",Fv,c[3]||(c[3]=[se("div",{class:"status-badge status-no-order"},[se("i",{class:"el-icon-shopping-bag-1"}),se("span",null,"无订单")],-1)]))):e.orderInfo.status==="在线"&&e.orderInfo.orderCount>0?($e(),sr("div",Sv,[e.orderDeletionStatus&&e.orderDeletionStatus.totalOrders>0?($e(),sr("div",yv,[se("div",{class:"order-count-badge",onClick:c[0]||(c[0]=o=>i.$emit("view-orders"))},[c[4]||(c[4]=se("i",{class:"el-icon-goods"},null,-1)),se("span",Cv,Ue(e.orderInfo.orderCount),1),c[5]||(c[5]=se("span",{class:"order-text"},"订单",-1))]),se("div",Ov,[e.orderDeletionStatus.inProgress?($e(),wt(l,{key:0,percentage:a.value,status:n.value,"stroke-width":12,class:"deletion-progress"},{default:q(()=>[se("span",null,Ue(s.value),1)]),_:1},8,["percentage","status"])):($e(),sr("div",Dv,[e.orderDeletionStatus.successCount>0?($e(),wt(f,{key:0,type:"success",size:"small",effect:"plain",class:"status-tag"},{default:q(()=>[c[6]||(c[6]=se("i",{class:"el-icon-check"},null,-1)),se("span",null,"已删除 "+Ue(e.orderDeletionStatus.successCount)+" 个",1)]),_:1})):dt("",!0),e.orderDeletionStatus.failCount>0?($e(),wt(f,{key:1,type:"danger",size:"small",effect:"plain",class:"status-tag"},{default:q(()=>[c[7]||(c[7]=se("i",{class:"el-icon-close"},null,-1)),se("span",null,"失败 "+Ue(e.orderDeletionStatus.failCount)+" 个",1)]),_:1})):dt("",!0),j(f,{type:"info",size:"small",effect:"plain",class:"status-tag"},{default:q(()=>[c[8]||(c[8]=se("i",{class:"el-icon-goods"},null,-1)),se("span",null,"剩余 "+Ue(e.orderInfo.orderCount)+" 个",1),se("span",Iv,"("+Ue(r.value)+"个可删)",1)]),_:1})]))])])):($e(),sr("div",{key:1,class:"order-count-badge",onClick:c[1]||(c[1]=o=>i.$emit("view-orders"))},[c[9]||(c[9]=se("i",{class:"el-icon-goods"},null,-1)),se("span",Rv,Ue(e.orderInfo.orderCount),1),c[10]||(c[10]=se("span",{class:"order-text"},"订单",-1)),r.value>0?($e(),wt(f,{key:0,size:"small",type:"success",effect:"light",class:"deletable-tag"},{default:q(()=>[Ee(Ue(r.value)+"个可删 ",1)]),_:1})):dt("",!0)]))])):($e(),sr("div",Nv,[se("div",bv,[c[11]||(c[11]=se("i",{class:"el-icon-circle-close"},null,-1)),se("span",null,Ue(e.orderInfo.status||"查询失败"),1)])]))])}}},Lv=N0(Pv,[["__scopeId","data-v-c49435fb"]]),Bv={class:"order-actions"},Mv={__name:"OrderActions",props:{token:{type:Object,required:!0}},emits:["delete-start","delete-progress","delete-complete"],setup(e,{emit:t}){const r=e,a=t,n=He(!1),s=rt(()=>ln(r.token)),i=async()=>{if(!n.value){if(s.value===0){Tr.alert("当前无可删除的订单（只有已评价的订单可以删除）","提示",{type:"info"});return}try{await Tr.confirm("确定要删除此Token的订单吗？将会自动查询并使用并发模式删除所有可删除的订单。","删除订单确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}),n.value=!0,a("delete-start",r.token.uid);const c=f=>{a("delete-progress",{uid:r.token.uid,status:f})},l=await Ev(r.token,{batchSize:20,concurrency:5},c);a("delete-complete",{uid:r.token.uid,result:l})}catch(c){c!=="cancel"&&(console.error("删除订单错误:",c),a("delete-complete",{uid:r.token.uid,result:{success:!1,message:c.message||"删除订单时发生错误"}}))}finally{n.value=!1}}};return(c,l)=>{const f=Ie("el-button");return $e(),sr("div",Bv,[j(f,{type:"warning",size:"small",text:"",onClick:i,loading:n.value,disabled:s.value===0},{default:q(()=>[l[0]||(l[0]=Ee(" 删除订单 ")),s.value>0?($e(),sr(_0,{key:0},[Ee("("+Ue(s.value)+")",1)],64)):dt("",!0)]),_:1},8,["loading","disabled"])])}}},Uv=N0(Mv,[["__scopeId","data-v-c3ee4367"]]),Hv={class:"token-reset-container"},Vv={class:"card-header"},Wv={key:0,class:"token-count-indicator"},$v={class:"header-actions"},Gv={class:"filter-container"},Xv={class:"search-container"},zv={class:"search-actions"},Kv={class:"data-stats"},Yv={class:"stats-card info-card"},jv={class:"stats-value"},Jv={class:"stats-card primary-card"},qv={class:"stats-value"},Zv={key:0,class:"stats-card warning-card"},Qv={class:"stats-value"},e2={class:"function-buttons"},r2={class:"table-actions"},t2={key:0,class:"selection-info"},a2={key:0},n2={key:1,class:"order-info"},s2={class:"table-action-buttons"},i2={class:"pagination-container"},c2={class:"operation-dialog-content"},f2={class:"dialog-footer"},o2={class:"order-details"},l2={class:"orders-summary-bar"},u2={class:"orders-stats"},h2={class:"batch-actions"},x2={class:"order-action-buttons"},d2={class:"dialog-footer"},v2={key:0,class:"order-detail-content"},p2={key:0,class:"order-detail-actions"},g2={key:1,class:"order-detail-actions"},m2={class:"order-detail-item"},_2={class:"order-detail-value"},T2={class:"order-detail-item"},k2={class:"order-detail-value"},E2={class:"order-detail-item"},w2={class:"order-detail-value"},A2={class:"order-detail-item"},F2={class:"order-detail-value"},S2={class:"order-detail-item"},y2={class:"order-detail-value"},C2={class:"order-detail-item"},O2={class:"order-detail-value"},D2={class:"order-detail-item"},I2={class:"order-detail-value"},R2={class:"order-extra-info"},N2={class:"dialog-footer"},b2={__name:"TokenReset",setup(e){const t=He(!1),r=He(""),a=He("");He("");const n=He(null),s=He(null),i=()=>{n.value.click()},c=L=>{const S=L.target.files[0];S&&(s.value=S,l(S),L.target.value="")},l=async L=>{const S=L.name;if(S.endsWith(".xlsx")||S.endsWith(".xls")||S.endsWith(".txt")){const Q=zt.service({fullscreen:!0,text:"正在导入数据..."});try{const re=await xv(L);if(re.length===0){De.warning("文件中没有有效数据"),Q.close();return}const{newTokens:ue,duplicateCount:ve}=N(re),Fe=h.value;vr.value=!1,f.value=ue,d.value=ue.length;let We=`成功导入 ${ue.length} 个Token`;ve>0&&(We+=`，已自动去除 ${ve} 个重复Token`),De.success(We),Q.close(),Tt(()=>{h.value=Fe})}catch(re){console.error("导入失败:",re),De.error("导入失败: "+(re.message||"无效的数据格式")),Q.close()}}else De.error("不支持的文件格式，仅支持Excel(.xlsx, .xls)和文本文件(.txt)")},f=He([]),o=He([]),u=He(!1);He(!0);const v=He("500px"),h=He(1),x=He(100),d=He(0),p=He([]),A=He(!1),O=()=>{A.value||(A.value=!0,u.value=!0,setTimeout(()=>{try{let L=[...f.value];if(R.value&&(L=L.filter(S=>S.status===R.value)),F.value&&(L=L.filter(S=>S.user===F.value)),U.value){const S=U.value.toLowerCase();L=L.filter(Q=>Q.uid&&Q.uid.toLowerCase().includes(S)||Q.user&&Q.user.toLowerCase().includes(S)||Q.nickname&&Q.nickname&&Q.nickname.toLowerCase().includes(S)||Q.token&&Q.token.toLowerCase().includes(S)||Q.status&&Q.status.toLowerCase().includes(S))}p.value=L,d.value=f.value.length,Tt(()=>{const S=Math.ceil(p.value.length/x.value)||1;h.value>S&&(h.value=1),V()})}catch(L){console.error("数据筛选错误:",L)}finally{A.value=!1,u.value=!1}},0))},_=rt(()=>{const L=(h.value-1)*x.value,S=Math.min(L+x.value,p.value.length);return p.value.slice(L,S)}),b=rt(()=>p.value.length),U=He(""),R=He(""),F=He(""),X=[{value:"未知",label:"未知"},{value:"在线",label:"在线"},{value:"掉线",label:"掉线"}],I=rt(()=>{const L=new Set;return f.value.forEach(S=>{S.user&&L.add(S.user)}),Array.from(L).sort()}),G=He(null),Y=()=>{vr.value=!0,O()},P=()=>{vr.value=!0,O()},te=L=>{F.value===L?F.value="":F.value=L,vr.value=!0,O()},_e=()=>{R.value="",F.value="",U.value="",vr.value=!0,O()},ie=()=>{u.value||(u.value=!0,O(),setTimeout(()=>{V(),u.value=!1},50))};ec(f,L=>{L.length>1e3&&x.value<50&&(x.value=50,De.info("数据量较大，已自动调整为每页显示50条")),O()},{deep:!1});const me=async L=>{Tr.confirm("确认要重置此Token吗？","操作确认",{confirmButtonText:"重置",cancelButtonText:"取消",type:"warning"}).then(()=>{const S=f.value.findIndex(Q=>Q.uid===L.uid);if(S!==-1){const Q={...L};Q.token="NEW_"+Math.random().toString(36).substring(2,10),Q.status="已重置",Q.resetTime=gv(new Date),f.value[S]=Q,De.success("Token已重置")}}).catch(()=>{})},pe=async L=>{De.info("本地模式：备份功能仅在服务器模式下可用")},Ge=L=>{De.info("编辑功能尚未实现")},K=async L=>{Tr.confirm("确认要删除此Token吗？","操作确认",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{f.value=f.value.filter(S=>S.uid!==L.uid),d.value=f.value.length,De.success("Token已删除")}).catch(()=>{})},ke=L=>{try{const S=new Set(_.value.map(re=>re.uid)),Q=[...o.value.filter(re=>!S.has(re.uid)),...L];o.value=Q}catch(S){console.error("选择变化处理错误:",S)}},Ae=L=>{[...o.value],x.value=L,setTimeout(()=>{try{G.value&&G.value.clearSelection();const S=Math.ceil(p.value.length/L)||1;h.value>S&&(h.value=1),o.value.length>0&&setTimeout(()=>{_.value.forEach(Q=>{o.value.some(ue=>ue.uid===Q.uid)&&G.value&&G.value.toggleRowSelection(Q,!0)})},50)}catch(S){console.error("页面大小变化错误:",S)}},50)},y=L=>{[...o.value],h.value=L,setTimeout(()=>{try{G.value&&G.value.clearSelection(),o.value.length>0&&setTimeout(()=>{_.value.forEach(S=>{o.value.some(re=>re.uid===S.uid)&&G.value&&G.value.toggleRowSelection(S,!0)})},50)}catch(S){console.error("页面切换错误:",S)}},50)},V=()=>{if(G.value)try{G.value.clearSelection(),Tt(()=>{_.value.forEach(L=>{var Q;o.value.some(re=>re.uid===L.uid)&&((Q=G.value)==null||Q.toggleRowSelection(L,!0))})})}catch(L){console.error("应用选择状态错误:",L)}};rt(()=>f.value);const N=L=>{const S=L.map(Fe=>({...Fe,status:Fe.status||"未知"}));if(f.value.length===0)return{newTokens:S,duplicateCount:0};const Q=new Set(f.value.map(Fe=>Fe.uid)),re=new Set(f.value.map(Fe=>Fe.token)),ue=[];let ve=0;for(const Fe of S)!Q.has(Fe.uid)&&!re.has(Fe.token)?(ue.push(Fe),Q.add(Fe.uid),re.add(Fe.token)):ve++;return{newTokens:[...f.value,...ue],duplicateCount:ve}},D=L=>({在线:"success",掉线:"danger",未知:"info",正常:"success",过期:"danger",待激活:"warning",已重置:"info"})[L]||"info";rc(()=>{fe(),window.addEventListener("resize",fe),O(),window.addEventListener("unhandledrejection",L=>{console.error("未捕获的Promise异常:",L.reason),L.preventDefault(),De.error("操作过程中出现异常，请刷新页面后重试")})});const J=()=>{var L;try{u.value=!0,(L=G.value)==null||L.clearSelection(),o.value=JSON.parse(JSON.stringify(p.value)),setTimeout(()=>{G.value&&_.value.forEach(S=>{G.value.toggleRowSelection(S,!0)}),De.success(`已选中全部 ${o.value.length} 条数据`),u.value=!1},100)}catch(S){console.error("全选操作错误:",S),u.value=!1,De.error("全选操作失败，请重试")}},oe=()=>{var L;try{const S=h.value;(L=G.value)==null||L.clearSelection(),o.value=[],G.value&&G.value.clearSelection(),vr.value=!1,Tt(()=>{h.value=S,V()}),De.success("已清除所有选择")}catch(S){console.error("清除选择错误:",S),De.error("清除选择失败，请重试")}},de=rt(()=>o.value.length>_.value.length),fe=()=>{const L=window.innerHeight;v.value=`${L-280}px`},ee=(L,S)=>{if(!(S.type==="selection"||S.label==="操作")&&G.value){const Q=o.value.some(re=>re.uid===L.uid);G.value.toggleRowSelection(L,!Q)}},Re=async()=>{if(o.value.length===0){De.warning("请先选择要查询的Token");return}try{await Tr.confirm(`确定要查询选中的 ${o.value.length} 个Token的在线状态吗？此操作将通过后端并发处理，速度更快。`,"操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const L=zt.service({fullscreen:!0,text:"正在查询在线状态...",background:"rgba(0, 0, 0, 0.7)"}),S=h.value;vr.value=!1;const Q=JSON.parse(JSON.stringify(f.value));try{const re=o.value.map(ve=>({uid:ve.uid,token:ve.token})),ue=await ic(re);if(ue.success){const ve=hv(f.value,ue.results);vr.value=!1,f.value=ve;const Fe=ue.results.filter(ge=>ge.status==="在线").length,We=ue.results.filter(ge=>ge.status==="掉线").length,ar=`
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${ue.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span> 
                <span style="font-size: 18px;">${Fe}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span> 
                <span style="font-size: 18px;">${We}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">状态已更新到表格中</div>
          </div>
        `;Tr.alert(ar,"查询结果",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",callback:()=>{vr.value=!1,ie(),Tt(()=>{h.value=S})}}),De.success("查询完成，状态已更新")}else f.value=Q,De.error(`查询失败: ${ue.error||"未知错误"}`)}catch(re){console.error("查询在线状态异常:",re),f.value=Q,De.error(`查询异常: ${re.message||"未知错误"}`)}finally{L.close(),Tt(()=>{h.value=S})}}catch(L){L!=="cancel"&&console.error("确认对话框异常:",L)}},C=async()=>{if(o.value.length===0){De.warning("请先选择要查询的Token");return}try{await Tr.confirm(`确定要查询选中的 ${o.value.length} 个Token的头像和昵称吗？此操作将通过后端并发处理，速度更快。`,"操作确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const L=zt.service({fullscreen:!0,text:"正在查询头像和昵称...",background:"rgba(0, 0, 0, 0.7)"}),S=JSON.parse(JSON.stringify(f.value));try{const Q=o.value.map(ue=>({uid:ue.uid,token:ue.token})),re=await sc(Q);if(re.success){const ue=f.value.map(ar=>{const ge=re.results.find(Ke=>Ke.uid===ar.uid);return ge&&ge.success&&ge.data?{...ar,avatar:ge.data.avatar||ar.avatar,nickname:ge.data.nickname||ar.nickname,status:ge.data.isOnline?"在线":ar.status}:ar});f.value=ue;const ve=re.results.filter(ar=>ar.success).length,Fe=re.results.length-ve,We=`
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${re.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">成功</span> 
                <span style="font-size: 18px;">${ve}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">失败</span> 
                <span style="font-size: 18px;">${Fe}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">头像和昵称信息已更新到表格中</div>
          </div>
        `;Tr.alert(We,"查询结果",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",callback:()=>{ie()}}),De.success("查询完成，头像和昵称已更新")}else f.value=S,De.error(`查询失败: ${re.error||"未知错误"}`)}catch(Q){console.error("查询头像昵称异常:",Q),f.value=S,De.error(`查询异常: ${Q.message||"未知错误"}`)}finally{L.close()}}catch(L){L!=="cancel"&&console.error("确认对话框异常:",L)}},Ze=async()=>{if(o.value.length===0){De.warning("请先选择要查询的Token");return}try{await Tr.confirm(`确定要查询 ${o.value.length} 个选中Token的订单信息吗？`,"查询订单信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const L=zt.service({fullscreen:!0,text:"正在查询订单信息...",background:"rgba(0, 0, 0, 0.7)"}),S=JSON.parse(JSON.stringify(f.value));try{const Q=o.value.map(ue=>({uid:ue.uid,token:ue.token})),re=await b0(Q);if(re.success){const ue=f.value.map(ge=>{var qr,Wr;const Ke=re.results.find(Nr=>Nr.uid===ge.uid);return Ke&&Ke.success?{...ge,status:Ke.status||ge.status,orderInfo:{isOnline:Ke.isOnline,status:Ke.status||"未知",orderCount:((qr=Ke.data)==null?void 0:qr.orderCount)||0,orders:((Wr=Ke.data)==null?void 0:Wr.orders)||[],message:Ke.message}}:{...ge,status:(Ke==null?void 0:Ke.status)||ge.status,orderInfo:Ke?{isOnline:Ke.isOnline||!1,status:Ke.status||"未知",orderCount:0,orders:[],message:Ke.message||"查询失败"}:null}});vr.value=!1,f.value=ue;const ve=re.results.filter(ge=>ge.status==="在线").length,Fe=re.results.filter(ge=>ge.status==="掉线").length,We=re.results.filter(ge=>ge.success&&ge.status==="在线"&&ge.data&&ge.data.orderCount>0).length;ua({title:"查询成功",message:`共处理 ${re.stats.total} 个Token，${ve} 个在线，${We} 个有订单`,type:"success",duration:5e3});const ar=`
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${re.stats.total} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span> 
                <span style="font-size: 18px;">${ve}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span> 
                <span style="font-size: 18px;">${Fe}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #e6f2ff; color: #409EFF; font-weight: bold;">
                <span style="margin-right: 5px;">有订单</span> 
                <span style="font-size: 18px;">${We}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">订单信息已更新到表格中，点击"订单数量"可查看详情</div>
          </div>
        `;Tr.alert(ar,"查询结果",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定",callback:()=>{ie()}})}else f.value=S,De.error(`查询失败: ${re.message||"未知错误"}`)}catch(Q){console.error("查询订单信息异常:",Q),f.value=S,De.error(`查询异常: ${Q.message||"未知错误"}`)}finally{L.close()}}catch(L){L!=="cancel"&&console.error("确认对话框异常:",L)}f.value.forEach(L=>{if(L.orderInfo&&L.orderInfo.orders){const S=ln(L);Vr(L.uid,{totalOrders:0,deletableCount:S,inProgress:!1})}})},Ne=L=>L?L.includes("待收货")||L.includes("待发货")?"warning":L.includes("已收货")||L.includes("已完成")?"success":L.includes("已取消")||L.includes("已关闭")?"danger":"info":"info",Be=He(!1),Te=He([]),le=He(""),Xe=He(!1),Ye=He(null),Ur=L=>{L.orderInfo&&L.orderInfo.orders&&L.orderInfo.orders.length>0?(Te.value=L.orderInfo.orders.map(S=>({...S,uid:L.uid,tokenValue:L.token})),le.value=L.uid,Be.value=!0):De.warning("没有找到订单信息")},Yr=L=>{if(L){const S=L.tokenInfo||{};Ye.value={...L,uid:L.uid||S.uid,tokenValue:L.tokenValue||S.token},Xe.value=!0}},Dt=async()=>{if(o.value.length===0){De.warning("请先选择要删除订单的Token");return}try{await Tr.confirm("确定要删除选中Token的订单吗？系统将自动查询并并发删除所有可删除的订单。","批量删除订单确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"})}catch{return}o.value.forEach(S=>{Vr(S.uid,{inProgress:!0,totalOrders:0,completedOrders:0,successCount:0,failCount:0}),S.orderDeleteLoading=!0});const L=h.value;vr.value=!1;try{const S=(re,ue)=>{Vr(re,ue)},Q=await qi(o.value,{batchSize:20,concurrency:5,progressCallback:S});if(Q.success){const re=Q.results.filter(ve=>ve.success).length,ue=Q.results.length-re;hr(Q.results),Q.queriedTokens&&Q.queriedTokens.length>0&&it(Q.queriedTokens),o.value.forEach(ve=>{const Fe=Q.results.filter(ge=>ge.uid===ve.uid),We=Fe.filter(ge=>ge.success).length,ar=Fe.length-We;Vr(ve.uid,{inProgress:!1,totalOrders:Fe.length,completedOrders:Fe.length,successCount:We,failCount:ar,results:Fe}),ve.orderDeleteLoading=!1}),Zi(Q),vr.value=!1,ie(),Tt(()=>{h.value=L})}else De.error(Q.message||"操作失败"),o.value.forEach(re=>{Vr(re.uid,{inProgress:!1,error:Q.message||"操作失败"}),re.orderDeleteLoading=!1})}catch(S){console.error("批量删除订单错误:",S),De.error(`操作异常: ${S.message||"未知错误"}`),o.value.forEach(Q=>{Vr(Q.uid,{inProgress:!1,error:S.message||"操作异常"}),Q.orderDeleteLoading=!1})}},it=L=>{!L||L.length===0||(console.log("更新已查询Token状态:",L),vr.value=!1,f.value=f.value.map(S=>{var re;const Q=L.find(ue=>ue.uid===S.uid);return Q?{...S,orderInfo:{isOnline:Q.isOnline!==void 0?Q.isOnline:!0,status:Q.status||"在线",orderCount:((re=Q.orders)==null?void 0:re.length)||0,orders:Q.orders||[],message:"查询成功"}}:S}))},hr=L=>{if(!L||L.length===0){console.warn("无效的删除结果，没有订单信息被更新");return}vr.value=!1;const S=h.value,Q=L.filter(ue=>ue.success).map(ue=>ue.orderSn),re=L.filter(ue=>!ue.success);if(re.length>0){console.warn("删除失败的订单:",re);const ue={};re.forEach(Fe=>{const We=Fe.message||"未知错误";ue[We]=(ue[We]||0)+1});let ve=`
      <div style="margin-top: 15px;">
        <div style="font-weight: bold; color: #f56c6c; margin-bottom: 10px;">删除失败原因:</div>
        <ul style="text-align: left; margin: 0; padding-left: 20px;">
    `;for(const[Fe,We]of Object.entries(ue))ve+=`<li>${Fe} (${We}个订单)</li>`;ve+=`
        </ul>
        <div style="margin-top: 10px; font-size: 12px; color: #909399;">
          常见原因：订单未评价、Token已失效或者无权限操作
        </div>
      </div>
    `,re.length>0&&Tr.alert(ve,"订单删除失败详情",{dangerouslyUseHTMLString:!0,confirmButtonText:"我知道了",type:"warning"}).catch(Fe=>{console.log("用户关闭了错误详情弹窗",Fe)})}if(Q.length===0){L.length>0&&De.warning("没有成功删除任何订单，请查看失败详情");return}console.log("开始更新本地订单数据，成功删除的订单数量:",Q.length);try{const ue={};L.forEach(ve=>{if(!ve.uid){console.warn("缺少uid的结果项:",ve);return}ue[ve.uid]||(ue[ve.uid]={success:[],fail:[]}),ve.success?ue[ve.uid].success.push(ve):ue[ve.uid].fail.push(ve)}),f.value=f.value.map(ve=>{if(!ve.uid||!ve.orderInfo||!ve.orderInfo.orders||!ue[ve.uid])return ve;try{const Fe=ve.orderInfo.orders,We=Fe.filter(ge=>!Q.includes(ge.orderId)),ar=Fe.length-We.length;if(console.log(`Token ${ve.uid}: 原始订单数量=${Fe.length}, 更新后订单数量=${We.length}, 删除数量=${ar}`),ar>0){const ge={...ve.orderInfo,orders:We,orderCount:We.length,isOnline:ve.orderInfo.isOnline,status:ve.orderInfo.status},Ke=ue[ve.uid]||{success:[],fail:[]},qr=Ke.success.length,Wr=Ke.fail.length;try{Vr(ve.uid,{inProgress:!1,totalOrders:qr+Wr,completedOrders:qr+Wr,successCount:qr,failCount:Wr,results:[...Ke.success,...Ke.fail]})}catch(Nr){console.error(`更新Token(${ve.uid})的删除状态时出错:`,Nr)}return{...ve,orderInfo:ge,currentDisplayOrders:We,deletableOrders:We.filter(Nr=>ze(Nr.status))}}}catch(Fe){console.error(`处理Token(${ve.uid})的订单更新时出错:`,Fe)}return ve}),Te.value&&Te.value.length>0&&(Te.value=Te.value.filter(ve=>!Q.includes(ve.orderId))),Hr.value&&Hr.value.length>0&&(Hr.value=Hr.value.filter(ve=>!Q.includes(ve.orderId)))}catch(ue){console.error("处理删除结果更新时出错:",ue),De.error("更新订单状态时出错，请刷新页面")}Tt(()=>{try{h.value=S,G.value&&G.value.$forceUpdate(),V()}catch(ue){console.error("刷新UI时出错:",ue)}})},jr=()=>{Tr.confirm("确定要清空当前表格中的所有数据吗？此操作不可恢复！","清空表格确认",{confirmButtonText:"确定清空",cancelButtonText:"取消",type:"warning",distinguishCancelAndClose:!0,customClass:"warning-confirm-dialog"}).then(()=>{f.value=[],o.value=[],d.value=0,p.value=[],h.value=1,G.value&&G.value.clearSelection(),O(),De.success("表格数据已全部清空")}).catch(()=>{})},Jr=async L=>{if(!L||!L.uid||!L.tokenValue||!L.orderId){De.warning("订单信息不完整，无法删除");return}if(!ze(L.status)){De.warning(`只能删除已评价的订单，当前订单状态为"${L.status}"`);return}try{await Tr.confirm(`确定要删除订单 ${L.orderId} 吗？此操作不可恢复！`,"删除订单确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const S=zt.service({fullscreen:!0,text:"正在删除订单...",background:"rgba(0, 0, 0, 0.7)"});try{const Q=await cc(L.uid,L.tokenValue,L.orderId);if(Q.success)Xe.value=!1,hr([{success:!0,orderSn:L.orderId}]),De.success("订单删除成功"),Be.value&&Te.value.length>0&&(Te.value=Te.value.filter(re=>re.orderId!==L.orderId));else{const re=Q.message||"删除失败";De.error(re);let ue=`
          <div style="text-align: left; margin: 15px 0;">
            <h3 style="margin-bottom: 10px;">删除失败详情</h3>
            <div style="color: #f56c6c;">
              <p>${re}${Q.errorCode?` (错误码: ${Q.errorCode})`:""}</p>
            </div>
            
            <div style="margin-top: 15px; font-size: 14px;">
              <p>可能的原因：</p>
              <ul style="padding-left: 20px; margin-top: 5px;">
                <li>订单状态不是"已评价"(拼多多要求必须是严格的"已评价")</li>
                <li>Token已失效或没有足够权限</li>
                <li>网络问题或API限流</li>
              </ul>
            </div>
        `;Q.responseData&&(ue+=`
            <div style="margin-top: 15px;">
              <details>
                <summary>API响应详情</summary>
                <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(Q.responseData,null,2)}</pre>
              </details>
            </div>
          `),ue+="</div>",Tr.alert(ue,"删除失败",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})}}finally{S.close()}}catch(S){S!=="cancel"&&(console.error("删除订单错误:",S),De.error(`操作异常: ${S.message||"未知错误"}`))}},ct=He(null),Qe=He([]),Pr=rt(()=>Te.value.filter(L=>ze(L.status)).length),Oe=L=>{Qe.value=L},er=()=>{var L;(L=ct.value)==null||L.clearSelection(),Te.value.forEach(S=>{var Q;ze(S.status)&&((Q=ct.value)==null||Q.toggleRowSelection(S,!0))})},Rr=async()=>{var L;if(Qe.value.length===0){De.warning("请先选择要删除的订单");return}try{await Tr.confirm(`确定要删除选中的 ${Qe.value.length} 个订单吗？`,"删除订单确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const S=zt.service({fullscreen:!0,text:"正在删除订单...",background:"rgba(0, 0, 0, 0.7)"});try{const Q=Qe.value.map(ue=>({uid:ue.uid,token:ue.tokenValue,orderSn:ue.orderId})),re=await P0(Q);if(re.success){const ue=re.results.filter(Fe=>Fe.success).length,ve=re.results.length-ue;hr(re.results),Qe.value=[],(L=ct.value)==null||L.clearSelection(),ua({title:"删除订单完成",message:`成功删除 ${ue} 个订单${ve>0?`，${ve} 个删除失败`:""}`,type:"success",duration:5e3}),ie()}else De.error(`批量删除失败: ${re.message||"未知错误"}`)}finally{S.close()}}catch(S){S!=="cancel"&&(console.error("批量删除订单错误:",S),De.error(`操作异常: ${S.message||"未知错误"}`))}},ze=L=>L?(console.log("检查订单是否可删除，状态:",L),!!(L==="已评价"||L.includes("已评价"))):!1,Hr=He([]),vr=He(!0),It=He({}),Vr=(L,S)=>{L&&(It.value[L]||(It.value[L]={inProgress:!1,totalOrders:0,completedOrders:0,successCount:0,failCount:0,results:[]}),It.value[L]={...It.value[L],...S})};return(L,S)=>{const Q=Ie("el-icon-document"),re=Ie("el-icon"),ue=Ie("el-tag"),ve=Ie("el-option"),Fe=Ie("el-select"),We=Ie("el-icon-search"),ar=Ie("el-input"),ge=Ie("el-button"),Ke=Ie("el-icon-refresh"),qr=Ie("el-icon-upload"),Wr=Ie("el-icon-delete"),Nr=Ie("el-button-group"),na=Ie("el-icon-user"),s0=Ie("el-icon-connection"),i0=Ie("el-icon-goods"),c0=Ie("el-icon-picture"),g=Ie("el-icon-edit"),k=Ie("el-icon-document-copy"),m=Ie("el-icon-refresh-right"),T=Ie("el-icon-shopping-cart"),E=Ie("el-icon-user-filled"),w=Ie("el-icon-select"),M=Ie("el-icon-close"),z=Ie("el-table-column"),B=Ie("el-avatar"),H=Ie("el-table"),W=Ie("el-pagination"),Z=Ie("el-card"),ce=Ie("el-dialog"),xe=Ie("el-tooltip"),ae=Ie("el-divider"),he=Ie("el-collapse-item"),Se=Ie("el-collapse"),je=tc("loading");return $e(),sr("div",Hv,[j(Z,{class:"token-list-card",shadow:"hover"},{header:q(()=>[se("div",Vv,[S[20]||(S[20]=se("span",null,"Token列表",-1)),d.value>0?($e(),sr("div",Wv,[j(ue,{type:"success",effect:"plain"},{default:q(()=>[j(re,null,{default:q(()=>[j(Q)]),_:1}),Ee(" 共 "+Ue(d.value)+" 个Token ",1)]),_:1})])):dt("",!0),se("div",$v,[se("div",Gv,[j(Fe,{modelValue:R.value,"onUpdate:modelValue":S[0]||(S[0]=ne=>R.value=ne),placeholder:"状态筛选",clearable:"",class:"filter-select",onChange:Y},{default:q(()=>[($e(),sr(_0,null,hn(X,ne=>j(ve,{key:ne.value,label:ne.label,value:ne.value},{default:q(()=>[j(ue,{type:D(ne.value),effect:"light",size:"small"},{default:q(()=>[Ee(Ue(ne.label),1)]),_:2},1032,["type"])]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"]),j(Fe,{modelValue:F.value,"onUpdate:modelValue":S[1]||(S[1]=ne=>F.value=ne),placeholder:"用户筛选",clearable:"",class:"filter-select",filterable:"",onChange:Y},{default:q(()=>[($e(!0),sr(_0,null,hn(I.value,ne=>($e(),wt(ve,{key:ne,label:ne,value:ne},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),se("div",Xv,[j(ar,{modelValue:U.value,"onUpdate:modelValue":S[2]||(S[2]=ne=>U.value=ne),placeholder:"搜索UID、TOKEN/用户",class:"search-input",clearable:"",onClear:P,onInput:P,onKeyup:nc(P,["enter"])},{prefix:q(()=>[j(re,null,{default:q(()=>[j(We)]),_:1})]),_:1},8,["modelValue"]),se("div",zv,[j(ge,{type:"primary",onClick:P},{default:q(()=>[j(re,null,{default:q(()=>[j(We)]),_:1}),S[15]||(S[15]=Ee("搜索 "))]),_:1}),j(ge,{onClick:_e,class:"reset-button"},{default:q(()=>[j(re,null,{default:q(()=>[j(Ke)]),_:1}),S[16]||(S[16]=Ee("重置筛选 "))]),_:1})])]),se("div",Kv,[se("div",Yv,[se("span",jv,Ue(d.value),1),S[17]||(S[17]=se("span",{class:"stats-label"},"总数据",-1))]),se("div",Jv,[se("span",qv,Ue(b.value),1),S[18]||(S[18]=se("span",{class:"stats-label"},"筛选结果",-1))]),o.value.length>0?($e(),sr("div",Zv,[se("span",Qv,Ue(o.value.length),1),S[19]||(S[19]=se("span",{class:"stats-label"},"已选择",-1))])):dt("",!0)])])])]),default:q(()=>[se("div",e2,[j(Nr,null,{default:q(()=>[j(ge,{type:"primary",onClick:i},{default:q(()=>[j(re,null,{default:q(()=>[j(qr)]),_:1}),S[21]||(S[21]=Ee("导入Token "))]),_:1}),j(ge,{type:"danger",onClick:jr},{default:q(()=>[j(re,null,{default:q(()=>[j(Wr)]),_:1}),S[22]||(S[22]=Ee("清空表格 "))]),_:1}),j(ge,{type:"warning",onClick:L.handleLoadUnsold},{default:q(()=>[j(re,null,{default:q(()=>[j(Ke)]),_:1}),S[23]||(S[23]=Ee("加载未卖 "))]),_:1},8,["onClick"])]),_:1}),j(Nr,null,{default:q(()=>[j(ge,{type:"info",onClick:C},{default:q(()=>[j(re,null,{default:q(()=>[j(na)]),_:1}),S[24]||(S[24]=Ee("查询头像昵称 "))]),_:1}),j(ge,{type:"success",onClick:Re},{default:q(()=>[j(re,null,{default:q(()=>[j(s0)]),_:1}),S[25]||(S[25]=Ee("查询在线 "))]),_:1}),j(ge,{type:"warning",onClick:Ze},{default:q(()=>[j(re,null,{default:q(()=>[j(i0)]),_:1}),S[26]||(S[26]=Ee("查询订单 "))]),_:1})]),_:1}),j(Nr,null,{default:q(()=>[j(ge,{type:"primary",onClick:L.handleChangeAvatar},{default:q(()=>[j(re,null,{default:q(()=>[j(c0)]),_:1}),S[27]||(S[27]=Ee("修改头像 "))]),_:1},8,["onClick"]),j(ge,{type:"primary",onClick:L.handleChangeNickname},{default:q(()=>[j(re,null,{default:q(()=>[j(g)]),_:1}),S[28]||(S[28]=Ee("修改昵称 "))]),_:1},8,["onClick"])]),_:1}),j(Nr,null,{default:q(()=>[j(ge,{type:"success",onClick:L.handleCreateBackup},{default:q(()=>[j(re,null,{default:q(()=>[j(k)]),_:1}),S[29]||(S[29]=Ee("创建备份 "))]),_:1},8,["onClick"]),j(ge,{type:"warning",onClick:L.handleResetSelected},{default:q(()=>[j(re,null,{default:q(()=>[j(m)]),_:1}),S[30]||(S[30]=Ee("重置选中 "))]),_:1},8,["onClick"])]),_:1}),j(Nr,null,{default:q(()=>[j(ge,{type:"primary",onClick:L.handleUploadSelected},{default:q(()=>[j(re,null,{default:q(()=>[j(qr)]),_:1}),S[31]||(S[31]=Ee("选中上传后台 "))]),_:1},8,["onClick"]),j(ge,{type:"warning",onClick:L.handleAddToOrder},{default:q(()=>[j(re,null,{default:q(()=>[j(T)]),_:1}),S[32]||(S[32]=Ee("新Token置入账号内订单 "))]),_:1},8,["onClick"])]),_:1}),j(Nr,null,{default:q(()=>[j(ge,{type:"danger",onClick:Dt},{default:q(()=>[j(re,null,{default:q(()=>[j(Wr)]),_:1}),S[33]||(S[33]=Ee("删除订单 "))]),_:1}),j(ge,{type:"success",onClick:L.handleAssignToAccount},{default:q(()=>[j(re,null,{default:q(()=>[j(E)]),_:1}),S[34]||(S[34]=Ee("选中给指定账号 "))]),_:1},8,["onClick"])]),_:1})]),se("div",r2,[j(ge,{type:"primary",size:"small",onClick:J,disabled:f.value.length===0},{default:q(()=>[j(re,null,{default:q(()=>[j(w)]),_:1}),S[35]||(S[35]=Ee("全选所有数据 "))]),_:1},8,["disabled"]),j(ge,{type:"info",size:"small",onClick:oe,disabled:o.value.length===0},{default:q(()=>[j(re,null,{default:q(()=>[j(M)]),_:1}),S[36]||(S[36]=Ee("清除选择 "))]),_:1},8,["disabled"]),de.value?($e(),sr("div",t2,[j(ue,{type:"warning",effect:"plain"},{default:q(()=>[S[37]||(S[37]=Ee(" 已选择 ")),se("strong",null,Ue(o.value.length),1),S[38]||(S[38]=Ee(" 条数据（包含跨页选择） "))]),_:1})])):dt("",!0)]),un(($e(),wt(H,{ref_key:"tokenTableRef",ref:G,data:_.value,style:{width:"100%"},border:"",height:v.value,onSelectionChange:ke,onRowClick:ee,"row-key":"uid",stripe:"","highlight-current-row":"",class:"token-table"},{default:q(()=>[j(z,{type:"selection",width:"55","reserve-selection":!0}),j(z,{prop:"uid",label:"UID",width:"150",sortable:""}),j(z,{prop:"token",label:"Token","min-width":"230","show-overflow-tooltip":""}),j(z,{prop:"user",label:"用户",width:"120",sortable:""},{default:q(ne=>[j(ge,{type:"text",onClick:Ve=>te(ne.row.user),style:ac({color:F.value===ne.row.user?"#409EFF":""}),class:"user-filter-btn"},{default:q(()=>[Ee(Ue(ne.row.user),1)]),_:2},1032,["onClick","style"])]),_:1}),j(z,{prop:"avatar",label:"头像",width:"80"},{default:q(ne=>[j(B,{size:40,src:ne.row.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",class:"token-avatar"},null,8,["src"])]),_:1}),j(z,{prop:"nickname",label:"昵称",width:"120","show-overflow-tooltip":""}),j(z,{prop:"purchaseTime",label:"购买时间",width:"180",sortable:""}),j(z,{prop:"expiryDate",label:"到期时间",width:"180",sortable:""}),j(z,{prop:"status",label:"状态",width:"100"},{default:q(ne=>[j(ue,{type:D(ne.row.status),effect:"light",class:"status-tag"},{default:q(()=>[Ee(Ue(ne.row.status),1)]),_:2},1032,["type"])]),_:1}),j(z,{prop:"orderInfo",label:"订单",width:"280"},{default:q(ne=>[ne.row.orderInfo?($e(),sr("div",a2,[j(Lv,{orderInfo:ne.row.orderInfo,orderDeletionStatus:It.value[ne.row.uid],onViewOrders:Ve=>Ur(ne.row)},null,8,["orderInfo","orderDeletionStatus","onViewOrders"])])):($e(),sr("div",n2,S[39]||(S[39]=[se("div",{class:"status-badge status-not-queried"},[se("i",{class:"el-icon-question"}),se("span",null,"未查询")],-1)])))]),_:1}),j(z,{prop:"createTime",label:"创建时间",width:"180",sortable:""}),j(z,{label:"操作",width:"280",fixed:"right"},{default:q(ne=>[se("div",s2,[j(ge,{type:"primary",size:"small",text:"",onClick:Ve=>me(ne.row),class:"action-btn"},{default:q(()=>S[40]||(S[40]=[Ee("重置")])),_:2},1032,["onClick"]),j(ge,{type:"success",size:"small",text:"",onClick:Ve=>pe(ne.row),class:"action-btn"},{default:q(()=>S[41]||(S[41]=[Ee("备份")])),_:2},1032,["onClick"]),j(ge,{type:"info",size:"small",text:"",onClick:Ve=>Ge(ne.row),class:"action-btn"},{default:q(()=>S[42]||(S[42]=[Ee("编辑")])),_:2},1032,["onClick"]),j(ge,{type:"danger",size:"small",text:"",onClick:Ve=>K(ne.row),class:"action-btn"},{default:q(()=>S[43]||(S[43]=[Ee("删除")])),_:2},1032,["onClick"]),j(Uv,{token:ne.row,onDeleteStart:S[3]||(S[3]=Ve=>Vr(Ve,{inProgress:!0})),onDeleteProgress:S[4]||(S[4]=({uid:Ve,status:Zr})=>Vr(Ve,Zr)),onDeleteComplete:S[5]||(S[5]=({uid:Ve,result:Zr})=>{Vr(Ve,{inProgress:!1}),Zr&&Zr.success&&(hr(Zr.results||[]),ie())})},null,8,["token"])])]),_:1})]),_:1},8,["data","height"])),[[je,u.value]]),se("div",i2,[j(W,{"current-page":h.value,"onUpdate:currentPage":S[6]||(S[6]=ne=>h.value=ne),"page-size":x.value,"onUpdate:pageSize":S[7]||(S[7]=ne=>x.value=ne),"page-sizes":[10,20,50,100,200],total:b.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ae,onCurrentChange:y},null,8,["current-page","page-size","total"])])]),_:1}),se("input",{type:"file",ref_key:"fileInputRef",ref:n,style:{display:"none"},onChange:c,accept:".xlsx,.xls,.txt"},null,544),j(ce,{modelValue:t.value,"onUpdate:modelValue":S[9]||(S[9]=ne=>t.value=ne),title:r.value,width:"40%"},{footer:q(()=>[se("span",f2,[j(ge,{onClick:S[8]||(S[8]=ne=>t.value=!1)},{default:q(()=>S[44]||(S[44]=[Ee("取消")])),_:1}),j(ge,{type:"primary",onClick:L.confirmOperation},{default:q(()=>S[45]||(S[45]=[Ee("确认")])),_:1},8,["onClick"])])]),default:q(()=>[se("div",c2,Ue(a.value),1)]),_:1},8,["modelValue","title"]),j(ce,{modelValue:Be.value,"onUpdate:modelValue":S[11]||(S[11]=ne=>Be.value=ne),title:"订单详情",width:"70%"},{footer:q(()=>[se("span",d2,[j(ge,{onClick:S[10]||(S[10]=ne=>Be.value=!1)},{default:q(()=>S[53]||(S[53]=[Ee("关闭")])),_:1})])]),default:q(()=>[se("div",o2,[se("div",l2,[se("div",u2,[j(ue,{type:"info",effect:"plain"},{default:q(()=>[S[46]||(S[46]=Ee(" 总计 ")),se("strong",null,Ue(Te.value.length),1),S[47]||(S[47]=Ee(" 个订单 "))]),_:1}),j(ue,{type:"success",effect:"plain",class:"ml-10"},{default:q(()=>[S[48]||(S[48]=Ee(" 可删除 ")),se("strong",null,Ue(Pr.value),1),S[49]||(S[49]=Ee(" 个订单 "))]),_:1})]),se("div",h2,[j(ge,{type:"primary",size:"small",onClick:er,disabled:Pr.value===0},{default:q(()=>[j(re,null,{default:q(()=>[j(w)]),_:1}),S[50]||(S[50]=Ee("全选可删除订单 "))]),_:1},8,["disabled"]),j(ge,{type:"danger",size:"small",onClick:Rr,disabled:Qe.value.length===0},{default:q(()=>[j(re,null,{default:q(()=>[j(Wr)]),_:1}),Ee("批量删除 ("+Ue(Qe.value.length)+") ",1)]),_:1},8,["disabled"])])]),un(($e(),wt(H,{data:Te.value,style:{width:"100%"},border:"","row-key":"orderId",stripe:"","highlight-current-row":"",class:"order-table",onSelectionChange:Oe,ref_key:"orderTableRef",ref:ct},{default:q(()=>[j(z,{type:"selection",width:"55",selectable:ne=>ze(ne.status)},null,8,["selectable"]),j(z,{prop:"orderId",label:"订单号",width:"180","show-overflow-tooltip":""}),j(z,{prop:"status",label:"状态",width:"120"},{default:q(ne=>[j(ue,{type:Ne(ne.row.status),effect:"light"},{default:q(()=>[Ee(Ue(ne.row.status),1)]),_:2},1032,["type"])]),_:1}),j(z,{prop:"amount",label:"金额",width:"80"},{default:q(ne=>[Ee(" ¥"+Ue(ne.row.amount),1)]),_:1}),j(z,{prop:"goodsInfo",label:"商品信息","min-width":"200","show-overflow-tooltip":""}),j(z,{prop:"orderTime",label:"订单时间",width:"180",sortable:""}),j(z,{prop:"mallName",label:"商家名称",width:"120","show-overflow-tooltip":""}),j(z,{label:"操作",width:"120",fixed:"right"},{default:q(ne=>[se("div",x2,[j(ge,{type:"primary",size:"small",onClick:Ve=>Yr(ne.row),text:""},{default:q(()=>S[51]||(S[51]=[Ee(" 查看详情 ")])),_:2},1032,["onClick"]),ze(ne.row.status)?($e(),wt(ge,{key:0,type:"danger",size:"small",onClick:Ve=>Jr(ne.row),text:""},{default:q(()=>S[52]||(S[52]=[Ee(" 删除 ")])),_:2},1032,["onClick"])):dt("",!0)])]),_:1})]),_:1},8,["data"])),[[je,u.value]])])]),_:1},8,["modelValue"]),j(ce,{modelValue:Xe.value,"onUpdate:modelValue":S[14]||(S[14]=ne=>Xe.value=ne),title:"订单详细信息",width:"50%"},{footer:q(()=>[se("span",N2,[j(ge,{onClick:S[13]||(S[13]=ne=>Xe.value=!1)},{default:q(()=>S[65]||(S[65]=[Ee("关闭")])),_:1})])]),default:q(()=>[Ye.value?($e(),sr("div",v2,[ze(Ye.value.status)?($e(),sr("div",p2,[j(ge,{type:"danger",size:"small",onClick:S[12]||(S[12]=ne=>Jr(Ye.value))},{default:q(()=>S[54]||(S[54]=[Ee(" 删除订单 ")])),_:1}),j(xe,{content:"仅已评价的订单可删除",placement:"top"},{default:q(()=>[j(ue,{type:"success",size:"small",style:{"margin-left":"10px"}},{default:q(()=>S[55]||(S[55]=[Ee("可删除")])),_:1})]),_:1})])):($e(),sr("div",g2,[j(xe,{content:"该订单状态不允许删除",placement:"top"},{default:q(()=>[j(ue,{type:"danger",size:"small"},{default:q(()=>S[56]||(S[56]=[Ee("不可删除")])),_:1})]),_:1}),S[57]||(S[57]=se("span",{class:"deletion-notice"},"（只有已评价的订单可删除）",-1))])),se("div",m2,[S[58]||(S[58]=se("div",{class:"order-detail-label"},"订单号:",-1)),se("div",_2,Ue(Ye.value.orderId),1)]),se("div",T2,[S[59]||(S[59]=se("div",{class:"order-detail-label"},"状态:",-1)),se("div",k2,[j(ue,{type:Ne(Ye.value.status)},{default:q(()=>[Ee(Ue(Ye.value.status),1)]),_:1},8,["type"])])]),se("div",E2,[S[60]||(S[60]=se("div",{class:"order-detail-label"},"金额:",-1)),se("div",w2,"¥"+Ue(Ye.value.amount),1)]),se("div",A2,[S[61]||(S[61]=se("div",{class:"order-detail-label"},"下单时间:",-1)),se("div",F2,Ue(Ye.value.orderTime),1)]),se("div",S2,[S[62]||(S[62]=se("div",{class:"order-detail-label"},"商品:",-1)),se("div",y2,Ue(Ye.value.goodsInfo),1)]),se("div",C2,[S[63]||(S[63]=se("div",{class:"order-detail-label"},"物流单号:",-1)),se("div",O2,Ue(Ye.value.trackingNumber||"暂无"),1)]),se("div",D2,[S[64]||(S[64]=se("div",{class:"order-detail-label"},"商家:",-1)),se("div",I2,Ue(Ye.value.mallName),1)]),j(ae),se("div",R2,[j(Se,null,{default:q(()=>[j(he,{title:"更多订单信息",name:"1"},{default:q(()=>[se("pre",null,Ue(JSON.stringify(Ye.value.extraInfo,null,2)),1)]),_:1})]),_:1})])])):dt("",!0)]),_:1},8,["modelValue"])])}}},B2=N0(b2,[["__scopeId","data-v-f089e9a4"]]);export{B2 as default};
