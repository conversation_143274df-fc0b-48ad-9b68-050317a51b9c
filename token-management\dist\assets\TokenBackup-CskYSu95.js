import{_ as xe,r as g,c as N,w as Ae,D as Qe,o as Ue,A as Ke,E as c,G as $e,a as D,b as J,e as n,k as ne,p as z,f as s,g as w,u as je,d as u,F as We,j as Ee,B as x,h as d,t as k,H as oe,i as Ge,I as Xe,q as W}from"./index-CckIkgn1.js";import{u as Ye}from"./token-Bnd6eYkJ.js";const Pe={class:"token-backup-container"},Ze={class:"card-header"},Le={class:"header-actions"},He={key:0,class:"relation-filter-panel"},qe={class:"filter-content"},et={key:0,class:"relation-tags"},tt={class:"tags-content"},at={class:"tag-count"},nt={class:"filter-footer"},ot={class:"backup-id-cell"},st={key:0,class:"el-icon-link"},it={class:"operation-buttons"},lt={class:"table-footer"},ct={class:"card-header"},rt={class:"header-title"},ut={class:"backup-info"},dt={class:"backup-id-display"},pt={class:"backup-id-text"},ft={class:"header-actions"},kt={key:0,class:"backup-type-selector"},mt={class:"token-relation-id"},It={class:"token-value"},wt={class:"token-actions"},bt={key:0,class:"action-toolbar"},Tt={__name:"TokenBackup",setup(yt){Ye();const R=Ke(),se=je(),T=g(null),M=g([]),B=g(""),O=g("all"),S=g(""),V=g(!1),v=g(""),ie=N(()=>v.value!==""),h=g("before"),I=g([{id:1,name:"重置前备份",backupId:"QBF20240329_167112345670001",backupType:"重置前",createTime:"2024-03-29 03:00:15",backupTime:"2024-03-29 03:00:20",tokenCount:120,size:"1.2MB",type:"自动",description:"系统重置前自动备份",tokenData:[{uid:"100001",accountId:"ACC001",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"正常",platform:"Amazon",username:"user123",createTime:"2024-03-17 08:15:22",lastUsedTime:"2024-03-28 16:45:12",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100002",accountId:"ACC002",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"已重置",platform:"Walmart",username:"seller456",createTime:"2024-03-17 10:22:15",lastUsedTime:"2024-03-27 14:30:18",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"},{uid:"100003",accountId:"ACC003",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"失效",platform:"Shopify",username:"store789",createTime:"2024-03-17 14:05:33",lastUsedTime:"2024-03-25 09:15:40",avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}]},{id:2,name:"重置后备份",backupId:"HBF20240329_167112345670001",backupType:"重置后",createTime:"2024-03-29 03:15:32",backupTime:"2024-03-29 03:15:40",tokenCount:120,size:"1.2MB",type:"自动",description:"系统重置后自动备份",tokenData:[{uid:"100001",accountId:"ACC001",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"正常",platform:"Amazon",username:"user123",createTime:"2024-03-29 03:10:22",lastUsedTime:"2024-03-29 03:10:22",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100002",accountId:"ACC002",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"正常",platform:"Walmart",username:"seller456",createTime:"2024-03-29 03:10:45",lastUsedTime:"2024-03-29 03:10:45",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"},{uid:"100003",accountId:"ACC003",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-17",status:"失败",platform:"Shopify",username:"store789",createTime:"2024-03-29 03:11:02",lastUsedTime:"2024-03-29 03:11:02",avatar:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}]},{id:3,name:"重置前备份",backupId:"QBF20240328_167109876540001",backupType:"重置前",createTime:"2024-03-28 15:20:15",backupTime:"2024-03-28 15:20:30",tokenCount:85,size:"1.1MB",type:"手动",description:"系统更新前手动备份",tokenData:[{uid:"100004",accountId:"ACC004",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-16",status:"正常",platform:"eBay",username:"trader123",createTime:"2024-03-16 09:30:45",lastUsedTime:"2024-03-27 11:22:36",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100005",accountId:"ACC005",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-16",status:"正常",platform:"Etsy",username:"craft456",createTime:"2024-03-16 13:15:28",lastUsedTime:"2024-03-26 15:45:10",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}]},{id:4,name:"每日自动备份",createTime:"2024-03-28 03:00:00",backupTime:"2024-03-28 03:01:10",tokenCount:118,size:"1.1MB",type:"自动",description:"系统每日自动备份"},{id:5,name:"手动备份-紧急",createTime:"2024-03-27 10:15:00",backupTime:"2024-03-27 10:16:25",tokenCount:115,size:"1.1MB",type:"手动",description:"紧急问题修复前备份"},{id:6,name:"重置前备份",backupId:"QBF20240330_987654321001",backupType:"重置前",createTime:"2024-03-30 09:15:20",backupTime:"2024-03-30 09:15:30",tokenCount:98,size:"0.9MB",type:"自动",description:"系统重置前自动备份",tokenData:[{uid:"100006",accountId:"ACC006",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-25",status:"正常",platform:"Amazon",username:"alice123",createTime:"2024-03-25 11:22:35",lastUsedTime:"2024-03-30 08:45:10",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100007",accountId:"ACC007",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-25",status:"正常",platform:"Ebay",username:"bob456",createTime:"2024-03-25 14:35:42",lastUsedTime:"2024-03-29 17:25:30",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}]},{id:7,name:"重置后备份",backupId:"HBF20240330_987654321001",backupType:"重置后",createTime:"2024-03-30 09:30:45",backupTime:"2024-03-30 09:31:00",tokenCount:98,size:"0.9MB",type:"自动",description:"系统重置后自动备份",tokenData:[{uid:"100006",accountId:"ACC006",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-25",status:"正常",platform:"Amazon",username:"alice123",createTime:"2024-03-30 09:25:15",lastUsedTime:"2024-03-30 09:25:15",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100007",accountId:"ACC007",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-25",status:"正常",platform:"Ebay",username:"bob456",createTime:"2024-03-30 09:25:22",lastUsedTime:"2024-03-30 09:25:22",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}]},{id:8,name:"重置前备份",backupId:"QBF20240331_876543210002",backupType:"重置前",createTime:"2024-03-31 14:20:10",backupTime:"2024-03-31 14:20:25",tokenCount:112,size:"1.0MB",type:"手动",description:"手动触发备份",tokenData:[{uid:"100008",accountId:"ACC008",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-28",status:"正常",platform:"Shopify",username:"charlie123",createTime:"2024-03-28 10:15:22",lastUsedTime:"2024-03-31 13:45:10",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100009",accountId:"ACC009",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-28",status:"正常",platform:"Etsy",username:"david456",createTime:"2024-03-28 11:30:45",lastUsedTime:"2024-03-31 12:30:20",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}]},{id:9,name:"重置后备份",backupId:"HBF20240331_876543210002",backupType:"重置后",createTime:"2024-03-31 14:35:05",backupTime:"2024-03-31 14:35:20",tokenCount:112,size:"1.0MB",type:"手动",description:"手动触发备份",tokenData:[{uid:"100008",accountId:"ACC008",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-28",status:"正常",platform:"Shopify",username:"charlie123",createTime:"2024-03-31 14:30:15",lastUsedTime:"2024-03-31 14:30:15",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{uid:"100009",accountId:"ACC009",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-28",status:"正常",platform:"Etsy",username:"david456",createTime:"2024-03-31 14:30:22",lastUsedTime:"2024-03-31 14:30:22",avatar:"https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"}]},{id:10,name:"仅重置前备份",backupId:"QBF20240401_765432100003",backupType:"重置前",createTime:"2024-04-01 10:05:10",backupTime:"2024-04-01 10:05:25",tokenCount:75,size:"0.8MB",type:"自动",description:"仅有重置前备份（测试用）",tokenData:[{uid:"100010",accountId:"ACC010",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-30",status:"正常",platform:"Amazon",username:"emily123",createTime:"2024-03-30 09:20:15",lastUsedTime:"2024-04-01 09:45:10",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}]},{id:11,name:"仅重置后备份",backupId:"HBF20240401_654321000004",backupType:"重置后",createTime:"2024-04-01 15:35:05",backupTime:"2024-04-01 15:35:20",tokenCount:82,size:"0.85MB",type:"自动",description:"仅有重置后备份（测试用）",tokenData:[{uid:"100011",accountId:"ACC011",tokenValue:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",loadDate:"2024-03-30",status:"正常",platform:"Shopify",username:"frank456",createTime:"2024-04-01 15:30:15",lastUsedTime:"2024-04-01 15:30:15",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}]}]),A=g(1),Q=g(10),le=N(()=>E.value.length),E=N(()=>{const e=I.value.filter(a=>{var r,i,f,b;if(O.value!=="all"){if(O.value==="双备份"){const l=m(a.backupId);if(!l)return!1;const F=I.value.some(C=>m(C.backupId)===l&&C.backupType==="重置前"),p=I.value.some(C=>m(C.backupId)===l&&C.backupType==="重置后");return F&&p}else if(a.backupType!==O.value)return!1}if(S.value){const l=S.value.toLowerCase();if(!((r=a.name)!=null&&r.toLowerCase().includes(l)||(i=a.backupId)!=null&&i.toLowerCase().includes(l)||(f=a.backupType)!=null&&f.toLowerCase().includes(l)||(b=a.type)!=null&&b.toLowerCase().includes(l)))return!1}return!(v.value&&m(a.backupId)!==v.value)}),t=new Map;return e.forEach(a=>{const r=m(a.backupId);if(!r){t.has(`single_${a.id}`)||t.set(`single_${a.id}`,{...a,relatedBackups:[]});return}if(t.has(r)){const i=t.get(r);i.relatedBackups.push(a),a.backupType==="重置前"?(i.hasBeforeBackup=!0,i.beforeBackupId=a.backupId,i.beforeBackupData=a.tokenData):a.backupType==="重置后"&&(i.hasAfterBackup=!0,i.afterBackupId=a.backupId,i.afterBackupData=a.tokenData)}else{const i={...a,taskId:r,relatedBackups:[],hasBeforeBackup:a.backupType==="重置前",hasAfterBackup:a.backupType==="重置后",beforeBackupId:a.backupType==="重置前"?a.backupId:null,afterBackupId:a.backupType==="重置后"?a.backupId:null,beforeBackupData:a.backupType==="重置前"?a.tokenData:null,afterBackupData:a.backupType==="重置后"?a.tokenData:null,combinedBackup:!0};t.set(r,i)}}),Array.from(t.values()).sort((a,r)=>{const i=new Date(a.createTime||0);return new Date(r.createTime||0)-i})}),U=N(()=>{const e=new Map;return I.value.forEach(t=>{const a=m(t.backupId);a&&(e.has(a)?e.set(a,e.get(a)+1):e.set(a,1))}),Array.from(e.entries()).filter(([t,a])=>a>1).map(([t,a])=>({id:t,count:a})).sort((t,a)=>a.count-t.count)}),G=()=>{V.value=!1},K=()=>{v.value=""},ce=e=>{v.value=e},re=e=>{Q.value=e},ue=e=>{A.value=e},de=e=>{T.value=e},X=e=>{if(!e){c.warning("请先选择备份");return}if(T.value=e,e.combinedBackup)e.hasBeforeBackup?h.value="before":e.hasAfterBackup&&(h.value="after"),Y();else{if(!e.tokenData){c.warning("此备份没有关联的Token数据");return}c.success(`正在加载备份数据: ${e.name}`),setTimeout(()=>{M.value=[...e.tokenData],B.value=e.backupId||`备份ID-${e.id}`,c({message:`成功加载备份数据，共${e.tokenCount}个Token`,type:"success",duration:3e3})},500)}},Y=()=>{if(!T.value)return;const e=T.value;if(h.value==="before"&&e.hasBeforeBackup){const t=e.beforeBackupData;if(!t){c.warning("重置前备份数据不可用");return}c.success("正在加载重置前备份数据"),setTimeout(()=>{M.value=[...t],B.value=e.beforeBackupId||`备份ID-${e.id}`,c({message:`成功加载重置前备份数据，共${t.length}个Token`,type:"success",duration:3e3})},500)}else if(h.value==="after"&&e.hasAfterBackup){const t=e.afterBackupData;if(!t){c.warning("重置后备份数据不可用");return}c.success("正在加载重置后备份数据"),setTimeout(()=>{M.value=[...t],B.value=e.afterBackupId||`备份ID-${e.id}`,c({message:`成功加载重置后备份数据，共${t.length}个Token`,type:"success",duration:3e3})},500)}else c.warning("所选备份类型不可用")};Ae(h,()=>{T.value&&T.value.combinedBackup&&Y()});const $=()=>{M.value=[],B.value="",c({message:"已清除备份数据显示",type:"info",duration:2e3})},pe=e=>{switch(e){case"正常":return"success";case"已重置":return"info";case"失效":return"danger";case"失败":return"danger";default:return"info"}},fe=()=>{window.tokenResetFunctions||(c.info("正在初始化Token操作功能..."),localStorage.setItem("backupPageReturn",JSON.stringify({path:R.path,query:R.query,params:R.params})),se.push({path:"/token-auto-reset",query:{initFunctions:"true"}}))};Qe(()=>{fe()}),Ue(()=>{const e=R.params.id||R.query.id;if(e){const r=I.value.find(i=>i.backupId===e);r?X(r):c.warning(`未找到ID为${e}的备份`)}localStorage.getItem("backupPageReturn")&&(localStorage.removeItem("backupPageReturn"),window.tokenResetFunctions?c.success("Token操作功能已成功初始化"):c.warning("Token操作功能初始化失败，部分功能可能无法正常使用"));const a=setInterval(()=>{const r=new Date;I.value.forEach(i=>{if(i.deleteStatus&&i.scheduledDeleteTime){const f=new Date(i.scheduledDeleteTime);r>=f&&(Z(i),c.info(`备份"${i.name||i.backupId}"已自动删除`))}})},6e4);$e(()=>{clearInterval(a)})});const ke=e=>{c.info(`查询"${e.username}"的头像昵称`),window.tokenResetFunctions&&window.tokenResetFunctions.checkAvatarNickname&&window.tokenResetFunctions.checkAvatarNickname(e)},me=e=>{c.info(`查询"${e.username}"的在线状态`),window.tokenResetFunctions&&window.tokenResetFunctions.checkOnline&&window.tokenResetFunctions.checkOnline(e)},Ie=e=>{c.info(`修改"${e.username}"的头像`),window.tokenResetFunctions&&window.tokenResetFunctions.modifyAvatar&&window.tokenResetFunctions.modifyAvatar(e)},we=e=>{c.info(`修改"${e.username}"的昵称`),window.tokenResetFunctions&&window.tokenResetFunctions.modifyNickname&&window.tokenResetFunctions.modifyNickname(e)},be=e=>{c.info(`将Token置入订单: ${e.tokenValue.substring(0,10)}...`),window.tokenResetFunctions&&window.tokenResetFunctions.addToOrder&&window.tokenResetFunctions.addToOrder(e)},Te=e=>{c.info(`将Token指定给账号: ${e.tokenValue.substring(0,10)}...`),window.tokenResetFunctions&&window.tokenResetFunctions.assignToAccount&&window.tokenResetFunctions.assignToAccount(e)},ye=e=>{c.info(`删除"${e.username}"的订单`),window.tokenResetFunctions&&window.tokenResetFunctions.deleteOrder&&window.tokenResetFunctions.deleteOrder(e)},ge=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量查询${e.length}个Token的头像昵称`),window.tokenResetFunctions&&window.tokenResetFunctions.batchCheckAvatarNickname&&window.tokenResetFunctions.batchCheckAvatarNickname(e)},he=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量查询${e.length}个Token的在线状态`),window.tokenResetFunctions&&window.tokenResetFunctions.batchCheckOnline&&window.tokenResetFunctions.batchCheckOnline(e)},ve=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量修改${e.length}个Token的头像`),window.tokenResetFunctions&&window.tokenResetFunctions.batchModifyAvatar&&window.tokenResetFunctions.batchModifyAvatar(e)},Ce=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量修改${e.length}个Token的昵称`),window.tokenResetFunctions&&window.tokenResetFunctions.batchModifyNickname&&window.tokenResetFunctions.batchModifyNickname(e)},Je=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量将${e.length}个Token置入订单`),window.tokenResetFunctions&&window.tokenResetFunctions.batchAddToOrder&&window.tokenResetFunctions.batchAddToOrder(e)},Me=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量将${e.length}个Token指定给账号`),window.tokenResetFunctions&&window.tokenResetFunctions.batchAssignToAccount&&window.tokenResetFunctions.batchAssignToAccount(e)},_e=()=>{const e=_();if(e.length===0){c.warning("请选择要操作的Token");return}c.info(`批量删除${e.length}个Token的订单`),window.tokenResetFunctions&&window.tokenResetFunctions.batchDeleteOrder&&window.tokenResetFunctions.batchDeleteOrder(e)},_=()=>{var a,r,i,f;const e=document.querySelector(".backup-data-card .el-table");return e?((f=(i=(r=(a=e.__vue__)==null?void 0:a.store)==null?void 0:r.states)==null?void 0:i.selection)==null?void 0:f.value)||[]:[]},De=e=>{if(!e)return"-";const t=e.createTime?new Date(e.createTime):new Date,a=t.getMonth()+1,r=t.getDate(),i=e.tokenCount||0;return`${a}月${r}日${i}个token`},P=e=>{if(!e)return"-";const t=new Date(e),r=new Date-t,i=Math.floor(r/(1e3*60*60*24)),f=Math.floor(r%(1e3*60*60*24)/(1e3*60*60)),b=Math.floor(r%(1e3*60*60)/(1e3*60));return i>0?`${i}天${f}小时`:f>0?`${f}小时${b}分钟`:`${b}分钟`},Be=e=>{const t=m(e.backupId),a=t&&U.value.some(i=>i.id===t),r=[{label:"立即删除",value:"delete_now"},{label:"标记为即将删除",value:"mark_for_deletion"}];e.deleteStatus&&r.push({label:"取消删除标记",value:"cancel_deletion"}),a?W.confirm("此备份与其他备份有关联，请选择操作方式","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",showCancelButton:!0,distinguishCancelAndClose:!0,showClose:!0,closeOnClickModal:!1}).then(()=>{W.confirm("请选择删除操作方式","选择操作",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,closeOnClickModal:!1,showInput:!0,inputType:"select",inputValue:"delete_now",inputPlaceholder:"请选择操作方式",inputOptions:r}).then(({value:i})=>{switch(i){case"delete_now":Fe(t);break;case"mark_for_deletion":I.value.filter(l=>m(l.backupId)===t).forEach(l=>L(l));break;case"cancel_deletion":I.value.filter(l=>m(l.backupId)===t).forEach(l=>H(l));break}}).catch(()=>{})}).catch(()=>{}):W.confirm("请选择删除操作方式","选择操作",{confirmButtonText:"确定",cancelButtonText:"取消",showCancelButton:!0,closeOnClickModal:!1,showInput:!0,inputType:"select",inputValue:"delete_now",inputPlaceholder:"请选择操作方式",inputOptions:r}).then(({value:i})=>{switch(i){case"delete_now":Z(e);break;case"mark_for_deletion":L(e);break;case"cancel_deletion":H(e);break}}).catch(()=>{})},Fe=e=>{const t=I.value.filter(a=>m(a.backupId)===e);t.forEach(a=>{const r=I.value.findIndex(i=>i.id===a.id);r!==-1&&I.value.splice(r,1),B.value===(a.backupId||`备份ID-${a.id}`)&&$()}),c.success(`已删除${t.length}个关联备份`),v.value===e&&K()},Z=e=>{const t=I.value.findIndex(a=>a.id===e.id);t!==-1&&(I.value.splice(t,1),B.value===(e.backupId||`备份ID-${e.id}`)&&$(),c.success(`已删除备份"${e.name||e.backupId}"`))},m=e=>{if(!e)return null;const t=e.split("_");return t.length===2?t[1]:null},ze=e=>{var r,i,f,b;const t=m(e.backupId);if(!t)return"无关联ID";(r=e.backupId)==null||r.startsWith("QBF"),(i=e.backupId)==null||i.startsWith("HBF");const a=I.value.filter(l=>l.id!==e.id&&m(l.backupId)===t);if(a.length>0){const l=(f=a[0].backupId)!=null&&f.startsWith("QBF")?"重置前备份":(b=a[0].backupId)!=null&&b.startsWith("HBF")?"重置后备份":"其他备份";return`关联ID: ${t.substring(0,8)}... (关联${l})`}return`关联ID: ${t.substring(0,8)}...`},Re=e=>e.combinedBackup&&e.hasBeforeBackup&&e.hasAfterBackup?"双备份":e.backupType?e.backupType:e.type||"未知",Oe=e=>e.combinedBackup&&e.hasBeforeBackup&&e.hasAfterBackup?"primary":e.backupType==="重置前"?"success":e.backupType==="重置后"?"warning":e.type==="手动"?"primary":"success",j=e=>{if(e.deleteStatus)return"即将删除";const t=new Date(e.backupTime||e.createTime);return(new Date-t)/(1e3*60*60*24)<=1?"冷却中":"备份中"},Se=e=>{switch(j(e)){case"冷却中":return"warning";case"备份中":return"success";case"即将删除":return"danger";default:return"info"}},Ve=e=>{const t=j(e);if(t==="冷却中"){const a=new Date(e.backupTime||e.createTime);return`备份冷却中，${24-Math.round((new Date-a)/(1e3*60*60))}小时后进入备份中状态`}else if(t==="即将删除"){if(e.scheduledDeleteTime){const a=new Date(e.scheduledDeleteTime);return`将在${Math.round((a-new Date)/(1e3*60*60))}小时后删除 (${e.scheduledDeleteTime})`}return"将在2天后自动删除"}else return`备份已保存${P(e.backupTime||e.createTime)}`},L=e=>{const t=new Date,a=new Date(t.getTime()+2*24*60*60*1e3);e.deleteStatus=!0,e.scheduledDeleteTime=a.toLocaleString(),c.success("备份已标记为即将删除，将在2天后自动删除")},H=e=>{e.deleteStatus=!1,e.scheduledDeleteTime=null,c.success("已取消删除标记")};return(e,t)=>{const a=w("el-radio-button"),r=w("el-radio-group"),i=w("el-icon-search"),f=w("el-icon"),b=w("el-input"),l=w("el-button"),F=w("el-tag"),p=w("el-table-column"),C=w("el-tooltip"),q=w("el-table"),Ne=w("el-pagination"),ee=w("el-card"),te=w("el-col"),ae=w("el-row");return J(),D("div",Pe,[n(ae,{gutter:20},{default:s(()=>[n(te,{span:24},{default:s(()=>[n(ee,{class:"backup-list-card"},{header:s(()=>[u("div",Ze,[t[12]||(t[12]=u("span",{class:"header-title"},"Token备份管理",-1)),u("div",Le,[n(r,{modelValue:O.value,"onUpdate:modelValue":t[0]||(t[0]=o=>O.value=o),size:"small",class:"backup-type-filter"},{default:s(()=>[n(a,{label:"all"},{default:s(()=>t[7]||(t[7]=[d("全部")])),_:1}),n(a,{label:"重置前"},{default:s(()=>t[8]||(t[8]=[d("重置前")])),_:1}),n(a,{label:"重置后"},{default:s(()=>t[9]||(t[9]=[d("重置后")])),_:1}),n(a,{label:"双备份"},{default:s(()=>t[10]||(t[10]=[d("双备份")])),_:1})]),_:1},8,["modelValue"]),n(b,{modelValue:S.value,"onUpdate:modelValue":t[1]||(t[1]=o=>S.value=o),placeholder:"搜索备份",class:"search-input",clearable:""},{prefix:s(()=>[n(f,null,{default:s(()=>[n(i)]),_:1})]),_:1},8,["modelValue"]),n(l,{type:"primary",size:"small",onClick:t[2]||(t[2]=o=>V.value=!V.value),class:x(["relation-filter-btn",{"is-active":ie.value}])},{default:s(()=>t[11]||(t[11]=[u("i",{class:"el-icon-connection",style:{"margin-right":"5px"}},null,-1),d(" 关联筛选 ")])),_:1},8,["class"])])])]),default:s(()=>[V.value?(J(),D("div",He,[t[17]||(t[17]=u("div",{class:"filter-title"},"按关联ID筛选",-1)),u("div",qe,[n(b,{modelValue:v.value,"onUpdate:modelValue":t[3]||(t[3]=o=>v.value=o),placeholder:"输入关联ID",clearable:"",onClear:K},{append:s(()=>[n(l,{onClick:G},{default:s(()=>t[13]||(t[13]=[u("i",{class:"el-icon-search"},null,-1)])),_:1})]),_:1},8,["modelValue"]),U.value.length>0?(J(),D("div",et,[t[14]||(t[14]=u("div",{class:"tags-title"},"可选关联ID:",-1)),u("div",tt,[(J(!0),D(We,null,Ee(U.value,o=>(J(),ne(F,{key:o.id,effect:"plain",class:x(["relation-tag",{"is-active":o.id===v.value}]),onClick:y=>ce(o.id)},{default:s(()=>[d(k(o.id.substring(0,8))+"... ",1),u("span",at,"("+k(o.count)+")",1)]),_:2},1032,["onClick","class"]))),128))])])):z("",!0)]),u("div",nt,[n(l,{size:"small",onClick:K},{default:s(()=>t[15]||(t[15]=[d("清除筛选")])),_:1}),n(l,{size:"small",type:"primary",onClick:G},{default:s(()=>t[16]||(t[16]=[d("应用筛选")])),_:1})])])):z("",!0),n(q,{data:E.value,style:{width:"100%"},border:"",onRowClick:de,"highlight-current-row":"","header-cell-style":{background:"#f5f7fa",color:"#303133",fontSize:"15px",height:"56px"},"cell-style":{fontSize:"14px",padding:"12px 0"}},{default:s(()=>[n(p,{prop:"id",label:"ID",width:"80"}),n(p,{prop:"name",label:"备份名称",width:"180"},{default:s(o=>[u("span",null,k(De(o.row)),1)]),_:1}),n(p,{prop:"backupTime",label:"备份时间",width:"180",sortable:""},{default:s(o=>[u("span",null,k(o.row.backupTime||o.row.createTime),1)]),_:1}),n(p,{label:"已备份时间",width:"120",sortable:""},{default:s(o=>[u("span",null,k(P(o.row.backupTime||o.row.createTime)),1)]),_:1}),n(p,{prop:"tokenCount",label:"Token数量",width:"120",sortable:""}),n(p,{prop:"createTime",label:"创建时间",width:"180",sortable:""}),n(p,{label:"状态",width:"120"},{default:s(o=>[n(C,{effect:"dark",content:Ve(o.row),placement:"top"},{default:s(()=>[n(F,{type:Se(o.row),effect:"plain",class:"backup-status-tag"},{default:s(()=>[d(k(j(o.row)),1)]),_:2},1032,["type"])]),_:2},1032,["content"])]),_:1}),n(p,{prop:"backupId",label:"备份ID",width:"220"},{default:s(o=>[u("div",ot,k(o.row.backupId||"-"),1)]),_:1}),n(p,{label:"备份关联ID",width:"240"},{default:s(o=>[n(C,{effect:"dark",content:m(o.row.backupId)?`关联ID: ${m(o.row.backupId)}`:"无关联ID",placement:"top"},{default:s(()=>[u("div",{class:x(["backup-link-cell",{"has-relation":m(o.row.backupId)}])},[m(o.row.backupId)?(J(),D("i",st)):z("",!0),u("span",null,k(ze(o.row)),1)],2)]),_:2},1032,["content"])]),_:1}),n(p,{prop:"type",label:"备份类型",width:"120"},{default:s(o=>[n(F,{type:Oe(o.row),effect:"plain"},{default:s(()=>[d(k(Re(o.row)),1)]),_:2},1032,["type"])]),_:1}),n(p,{prop:"size",label:"文件大小",width:"100"}),n(p,{label:"操作",width:"220",fixed:"right"},{default:s(o=>[u("div",it,[n(l,{type:"primary",size:"small",class:"restore-btn",onClick:oe(y=>X(o.row),["stop"])},{default:s(()=>t[18]||(t[18]=[d("查看备份数据")])),_:2},1032,["onClick"]),n(l,{type:"danger",size:"small",class:"delete-btn",onClick:oe(y=>Be(o.row),["stop"])},{default:s(()=>t[19]||(t[19]=[d("删除备份")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),u("div",lt,[n(Ne,{"current-page":A.value,"onUpdate:currentPage":t[4]||(t[4]=o=>A.value=o),"page-size":Q.value,"onUpdate:pageSize":t[5]||(t[5]=o=>Q.value=o),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:le.value,onSizeChange:re,onCurrentChange:ue},null,8,["current-page","page-size","total"])])]),_:1})]),_:1})]),_:1}),M.value.length>0?(J(),ne(ae,{key:0,gutter:20,class:"backup-data-row"},{default:s(()=>[n(te,{span:24},{default:s(()=>[n(ee,{class:"backup-data-card"},{header:s(()=>{var o;return[u("div",ct,[u("div",rt,[t[21]||(t[21]=u("i",{class:"el-icon-document-checked header-icon"},null,-1)),t[22]||(t[22]=u("span",null,"备份数据",-1)),u("div",ut,[u("div",{class:x(["backup-type-tag",{before:h.value==="before",after:h.value==="after"}])},k(h.value==="before"?"重置前备份":"重置后备份"),3),u("div",dt,[t[20]||(t[20]=d(" 关联ID: ")),u("span",pt,k(((o=T.value)==null?void 0:o.taskId)||"-"),1)])])]),u("div",ft,[T.value&&T.value.combinedBackup?(J(),D("div",kt,[n(r,{modelValue:h.value,"onUpdate:modelValue":t[6]||(t[6]=y=>h.value=y),size:"small"},{default:s(()=>[n(a,{label:"before",disabled:!T.value.hasBeforeBackup},{default:s(()=>t[23]||(t[23]=[d("重置前")])),_:1},8,["disabled"]),n(a,{label:"after",disabled:!T.value.hasAfterBackup},{default:s(()=>t[24]||(t[24]=[d("重置后")])),_:1},8,["disabled"])]),_:1},8,["modelValue"])])):z("",!0),n(l,{onClick:$,type:"danger",size:"small"},{default:s(()=>t[25]||(t[25]=[d(" 清除数据 ")])),_:1})])])]}),default:s(()=>[n(q,{data:M.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"",size:"medium","header-cell-style":{background:"#f5f7fa",color:"#303133",fontSize:"15px",height:"56px"},"cell-style":{fontSize:"14px",padding:"12px 0"}},{default:s(()=>[n(p,{type:"selection",width:"55",align:"center"}),n(p,{prop:"uid",label:"UID",width:"150",sortable:""},{default:s(o=>[n(C,{effect:"dark",content:o.row.uid||"-",placement:"top"},{default:s(()=>[u("div",mt,k(o.row.uid||"-"),1)]),_:2},1032,["content"])]),_:1}),n(p,{prop:"tokenValue",label:"TOKEN"},{default:s(o=>[u("div",It,k(o.row.tokenValue),1)]),_:1}),n(p,{prop:"username",label:"用户",width:"120"}),n(p,{label:"头像",width:"70",align:"center"},{default:s(o=>[n(Ge(Xe),{size:40,src:o.row.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"},null,8,["src"])]),_:1}),n(p,{prop:"nickname",label:"昵称",width:"120"},{default:s(o=>[u("span",null,k(o.row.nickname||o.row.username||"-"),1)]),_:1}),n(p,{prop:"loadDate",label:"购买时间",width:"180",sortable:""},{default:s(o=>[u("span",null,k(o.row.loadDate),1)]),_:1}),n(p,{prop:"expireDate",label:"到期时间",width:"180",sortable:""},{default:s(o=>[u("span",null,k(o.row.expireDate||"-"),1)]),_:1}),n(p,{prop:"status",label:"状态",width:"100"},{default:s(o=>[n(F,{type:pe(o.row.status),size:"medium"},{default:s(()=>[d(k(o.row.status),1)]),_:2},1032,["type"])]),_:1}),n(p,{prop:"createTime",label:"创建时间",width:"180",sortable:""}),n(p,{label:"操作",width:"360",fixed:"right"},{default:s(o=>[u("div",wt,[n(l,{type:"primary",size:"small",onClick:y=>ke(o.row),class:"action-btn"},{default:s(()=>t[26]||(t[26]=[d("查询头像昵称")])),_:2},1032,["onClick"]),n(l,{type:"success",size:"small",onClick:y=>me(o.row),class:"action-btn"},{default:s(()=>t[27]||(t[27]=[d("查询在线")])),_:2},1032,["onClick"]),n(l,{type:"info",size:"small",onClick:y=>Ie(o.row),class:"action-btn"},{default:s(()=>t[28]||(t[28]=[d("修改头像")])),_:2},1032,["onClick"]),n(l,{type:"warning",size:"small",onClick:y=>we(o.row),class:"action-btn"},{default:s(()=>t[29]||(t[29]=[d("修改昵称")])),_:2},1032,["onClick"]),n(l,{type:"success",size:"small",onClick:y=>be(o.row),class:"action-btn"},{default:s(()=>t[30]||(t[30]=[d("置入订单")])),_:2},1032,["onClick"]),n(l,{type:"primary",size:"small",onClick:y=>Te(o.row),class:"action-btn"},{default:s(()=>t[31]||(t[31]=[d("指定账号")])),_:2},1032,["onClick"]),n(l,{type:"danger",size:"small",onClick:y=>ye(o.row),class:"action-btn"},{default:s(()=>t[32]||(t[32]=[d("删除订单")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),M.value.length>0?(J(),D("div",bt,[n(l,{type:"primary",onClick:ge},{default:s(()=>t[33]||(t[33]=[d("查询头像昵称")])),_:1}),n(l,{type:"success",onClick:he},{default:s(()=>t[34]||(t[34]=[d("查询在线")])),_:1}),n(l,{type:"info",onClick:ve},{default:s(()=>t[35]||(t[35]=[d("修改头像")])),_:1}),n(l,{type:"warning",onClick:Ce},{default:s(()=>t[36]||(t[36]=[d("修改昵称")])),_:1}),n(l,{type:"success",onClick:Je},{default:s(()=>t[37]||(t[37]=[d("新Token置入账号内订单")])),_:1}),n(l,{type:"primary",onClick:Me},{default:s(()=>t[38]||(t[38]=[d("选中给指定账号")])),_:1}),n(l,{type:"danger",onClick:_e},{default:s(()=>t[39]||(t[39]=[d("删除订单")])),_:1})])):z("",!0)]),_:1})]),_:1})]),_:1})):z("",!0)])}}},Ct=xe(Tt,[["__scopeId","data-v-113c908d"]]);export{Ct as default};
