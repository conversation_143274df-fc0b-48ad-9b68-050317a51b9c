<template>
  <div class="login-page">
    <div class="login-background">
      <div class="login-shape shape-1"></div>
      <div class="login-shape shape-2"></div>
      <div class="login-shape shape-3"></div>
    </div>
    
    <div class="login-container">
      <div class="login-content">
        <div class="login-header">
          <div class="login-logo">
            <el-icon class="logo-icon"><el-icon-key /></el-icon>
          </div>
          <h1 class="login-title">Token 管理系统</h1>
          <p class="login-subtitle">安全、高效的Token资产管理平台</p>
        </div>
        
        <el-card class="login-card" shadow="always">
          <el-form 
            :model="loginForm" 
            :rules="loginRules" 
            ref="loginFormRef"
            class="login-form"
            size="large"
          >
            <el-form-item prop="username">
              <el-input 
                v-model="loginForm.username" 
                placeholder="请输入用户名"
                prefix-icon="User"
                size="large"
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input 
                v-model="loginForm.password" 
                type="password" 
                show-password 
                placeholder="请输入密码"
                prefix-icon="Lock"
                size="large"
              />
            </el-form-item>
            
            <div class="login-options">
              <el-checkbox v-model="rememberMe">记住我</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码?</el-link>
            </div>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="handleLogin" 
                :loading="loading" 
                class="login-button"
              >
                登录系统
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
        
        <div class="login-footer">
          <p class="copyright">© {{ currentYear }} Token 管理系统 - 版权所有</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../store/user';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';

const router = useRouter();
const userStore = useUserStore();
const loginFormRef = ref(null);
const loading = ref(false);
const rememberMe = ref(false);

// 获取当前年份用于版权信息
const currentYear = computed(() => new Date().getFullYear());

const loginForm = reactive({
  username: '',
  password: ''
});

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度应在6-30个字符之间', trigger: 'blur' }
  ]
};

const handleLogin = async () => {
  if (!loginFormRef.value) return;
  
  try {
    await loginFormRef.value.validate();
    loading.value = true;
    
    // 记住用户选择
    if (rememberMe.value) {
      localStorage.setItem('rememberUsername', loginForm.username);
    } else {
      localStorage.removeItem('rememberUsername');
    }
    
    // 调用后端API进行用户验证
    const success = await userStore.login(loginForm);
    if (success) {
      ElMessage({
        message: '登录成功，正在进入系统...',
        type: 'success',
        duration: 2000
      });
      setTimeout(() => {
        router.push('/');
      }, 300);
    } else {
      ElMessage({
        message: '用户名或密码错误，请重试',
        type: 'error'
      });
    }
  } catch (error) {
    console.error('登录失败:', error);
    ElMessage({
      message: error.message || '登录失败，请检查用户名和密码',
      type: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// 检查是否需要自动填充用户名
const checkRememberedUser = () => {
  const rememberedUsername = localStorage.getItem('rememberUsername');
  if (rememberedUsername) {
    loginForm.username = rememberedUsername;
    rememberMe.value = true;
  }
};

// 组件挂载时执行
checkRememberedUser();
</script>

<style scoped>
.login-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(140deg, #f0f2f5 0%, #e6e9ef 100%);
}

.login-background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.login-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.5;
  filter: blur(50px);
}

.shape-1 {
  top: -180px;
  right: -180px;
  width: 600px;
  height: 600px;
  background: linear-gradient(140deg, #1890ff 0%, #096dd9 100%);
  animation: float 15s ease-in-out infinite;
}

.shape-2 {
  bottom: -200px;
  left: -200px;
  width: 700px;
  height: 700px;
  background: linear-gradient(140deg, #722ed1 0%, #531dab 100%);
  animation: float 18s ease-in-out infinite reverse;
}

.shape-3 {
  top: 40%;
  left: 60%;
  width: 400px;
  height: 400px;
  background: linear-gradient(140deg, #52c41a 0%, #389e0d 100%);
  animation: float 12s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(30px, 20px) rotate(5deg);
  }
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
}

.login-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  max-width: 1200px;
  padding: 0 20px;
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 420px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo {
  margin: 0 auto 20px;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(140deg, #1890ff 0%, #096dd9 100%);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.35);
}

.logo-icon {
  font-size: 46px;
  color: white;
}

.login-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px;
  letter-spacing: 1px;
}

.login-subtitle {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.login-card {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.login-form {
  padding: 20px 30px;
}

/* 输入框样式 */
.login-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 50px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset;
}

.login-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #1890ff inset !important;
}

.login-form :deep(.el-input__prefix) {
  display: flex;
  align-items: center;
  padding-left: 5px;
}

.login-form :deep(.el-input__prefix-inner .el-icon) {
  font-size: 18px;
  color: #909399;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 20px;
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  border-radius: 8px;
  background: linear-gradient(140deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
  background: linear-gradient(140deg, #40a9ff 0%, #1890ff 100%);
}

.login-footer {
  margin-top: 30px;
  text-align: center;
}

.copyright {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .login-title {
    font-size: 28px;
  }
  
  .login-logo {
    width: 80px;
    height: 80px;
  }
  
  .logo-icon {
    font-size: 40px;
  }
  
  .login-form {
    padding: 20px 20px;
  }
}
</style> 