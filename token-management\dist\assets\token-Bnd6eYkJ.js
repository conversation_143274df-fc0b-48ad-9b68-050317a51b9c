import{J as e}from"./index-CckIkgn1.js";const a=e("token",{state:()=>({tokens:[],backups:[],lastExpiryDate:"2024-06-30",totalTokens:0,totalBackups:0,loading:!1}),getters:{getTokenCount:t=>t.totalTokens,getBackupCount:t=>t.totalBackups,getLastExpiryDate:t=>t.lastExpiryDate,getTokens:t=>t.tokens,getBackups:t=>t.backups},actions:{setTokens(t){this.tokens=t,this.totalTokens=t.length},setBackups(t){this.backups=t,this.totalBackups=t.length},setLastExpiryDate(t){this.lastExpiryDate=t}}});export{a as u};
