import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../store/user'
// 预先导入Login组件，避免动态导入可能出现的问题
import Login from '../views/Login.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', public: true }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/token-reset',
    name: 'TokenReset',
    component: () => import('@/views/TokenReset.vue'),
    meta: { title: 'Token重置' }
  },
  {
    path: '/token-auto-reset',
    name: 'TokenAutoReset',
    component: () => import('@/views/TokenAutoReset.vue'),
    meta: { title: 'Token自动重置' }
  },
  {
    path: '/token-backup',
    name: 'TokenBackup',
    component: () => import('@/views/TokenBackup.vue'),
    meta: { title: 'Token备份' }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: { title: '设置' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '404' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - Token管理系统` : 'Token管理系统'
  
  // 检查是否需要登录（如果不是公开页面且没有token则重定向到登录页）
  const token = localStorage.getItem('token')
  const isPublicPage = to.meta.public === true
  
  if (!isPublicPage && !token) {
    // 如果要访问非公开页面但没有token，重定向到登录页
    next('/login')
  } else {
    // 其他情况正常放行
    next()
  }
})

export default router 