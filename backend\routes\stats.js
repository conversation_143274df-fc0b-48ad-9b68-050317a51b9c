const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const StatsController = require('../controllers/stats');

// 获取系统概览统计
router.get('/dashboard', authenticate, StatsController.getDashboardStats);

// 获取token相关统计
router.get('/tokens', authenticate, StatsController.getTokenStats);

// 获取备份相关统计
router.get('/backups', authenticate, StatsController.getBackupStats);

// 获取重置相关统计
router.get('/resets', authenticate, StatsController.getResetStats);

// 获取用户相关统计（仅管理员）
router.get('/users', authenticate, isAdmin, StatsController.getUserStats);

// 获取系统活动日志统计（仅管理员）
router.get('/activity', authenticate, isAdmin, StatsController.getActivityStats);

// 获取过去N天的活动趋势
router.get('/trends/:days', authenticate, StatsController.getTrends);

// 导出统计数据（CSV/Excel）
router.get('/export/:type/:format', authenticate, StatsController.exportStats);

module.exports = router; 