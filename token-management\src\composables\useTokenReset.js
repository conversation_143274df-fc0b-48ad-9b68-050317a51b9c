import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tokenApi } from '../api/token'

/**
 * Token重置、激活和续期相关的组合式函数
 */
export default function useTokenReset(options = {}) {
  // 从选项中获取需要的数据和函数
  const {
    tokens = ref([]),
    selectedRows = ref([]),
    refreshTokens = () => {}
  } = options
  
  // 加载状态
  const resetLoading = ref(false)
  const activateLoading = ref(false)
  const renewLoading = ref(false)
  
  /**
   * 重置单个Token
   * @param {Object} row Token行数据
   */
  const handleResetToken = async (row) => {
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要重置此Token吗？重置后Token将变为新的Token值。`,
        '重置Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      resetLoading.value = true
      
      // 调用API重置Token
      const response = await tokenApi.resetToken(row.uid)
      
      if (response.code === 200) {
        ElMessage.success(`Token重置成功: ${row.username}`)
        
        // 更新本地数据
        const index = tokens.value.findIndex(t => t.uid === row.uid)
        if (index !== -1) {
          tokens.value[index] = { ...tokens.value[index], ...response.data }
        }
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '重置失败')
      }
    } catch (error) {
      console.error('重置Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`重置Token失败: ${error.message || '未知错误'}`)
    } finally {
      resetLoading.value = false
    }
  }
  
  /**
   * 批量重置选中的Token
   */
  const handleBatchResetTokens = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要重置的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要重置选中的 ${selectedRows.value.length} 个Token吗？此操作将生成新的Token值。`,
        '批量重置Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      resetLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量重置Token
      const response = await tokenApi.batchResetTokens(uids)
      
      if (response.code === 200) {
        ElMessage.success(`成功重置 ${response.data.successCount || 0} 个Token`)
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量重置失败')
      }
    } catch (error) {
      console.error('批量重置Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量重置Token失败: ${error.message || '未知错误'}`)
    } finally {
      resetLoading.value = false
    }
  }
  
  /**
   * 激活单个Token
   * @param {Object} row Token行数据
   */
  const handleActivateToken = async (row) => {
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要激活此Token吗？激活后Token状态将变为"正常"。`,
        '激活Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      activateLoading.value = true
      
      // 调用API激活Token
      const response = await tokenApi.activateToken(row.uid)
      
      if (response.code === 200) {
        ElMessage.success(`Token激活成功: ${row.username}`)
        
        // 更新本地数据
        const index = tokens.value.findIndex(t => t.uid === row.uid)
        if (index !== -1) {
          tokens.value[index] = { ...tokens.value[index], status: 'normal' }
        }
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '激活失败')
      }
    } catch (error) {
      console.error('激活Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`激活Token失败: ${error.message || '未知错误'}`)
    } finally {
      activateLoading.value = false
    }
  }
  
  /**
   * 批量激活选中的Token
   */
  const handleBatchActivateTokens = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要激活的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要激活选中的 ${selectedRows.value.length} 个Token吗？此操作将使这些Token状态变为"正常"。`,
        '批量激活Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      activateLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量激活Token
      const response = await tokenApi.batchActivateTokens(uids)
      
      if (response.code === 200) {
        ElMessage.success(`成功激活 ${response.data.successCount || 0} 个Token`)
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量激活失败')
      }
    } catch (error) {
      console.error('批量激活Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量激活Token失败: ${error.message || '未知错误'}`)
    } finally {
      activateLoading.value = false
    }
  }
  
  /**
   * 续期单个Token
   * @param {Object} row Token行数据
   * @param {number} days 续期天数
   */
  const handleRenewToken = async (row, days = 30) => {
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要为此Token续期 ${days} 天吗？`,
        '续期Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      renewLoading.value = true
      
      // 调用API续期Token
      const response = await tokenApi.renewToken(row.uid, { days })
      
      if (response.code === 200) {
        ElMessage.success(`Token续期成功: ${row.username}，已延长 ${days} 天`)
        
        // 更新本地数据
        const index = tokens.value.findIndex(t => t.uid === row.uid)
        if (index !== -1) {
          tokens.value[index] = { 
            ...tokens.value[index], 
            expireTime: response.data.expireTime 
          }
        }
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '续期失败')
      }
    } catch (error) {
      console.error('续期Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`续期Token失败: ${error.message || '未知错误'}`)
    } finally {
      renewLoading.value = false
    }
  }
  
  /**
   * 批量续期选中的Token
   * @param {number} days 续期天数
   */
  const handleBatchRenewTokens = async (days = 30) => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要续期的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要为选中的 ${selectedRows.value.length} 个Token续期 ${days} 天吗？`,
        '批量续期Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      renewLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量续期Token
      const response = await tokenApi.batchRenewTokens(uids, { days })
      
      if (response.code === 200) {
        ElMessage.success(`成功为 ${response.data.successCount || 0} 个Token续期 ${days} 天`)
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量续期失败')
      }
    } catch (error) {
      console.error('批量续期Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量续期Token失败: ${error.message || '未知错误'}`)
    } finally {
      renewLoading.value = false
    }
  }
  
  /**
   * 自定义续期天数
   * @param {Object} row Token行数据，如果为null则为批量续期
   */
  const handleCustomRenewDays = async (row = null) => {
    try {
      // 获取用户输入的续期天数
      const { value: days } = await ElMessageBox.prompt(
        '请输入要续期的天数',
        '自定义续期',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: '请输入正整数',
          inputValue: '30'
        }
      )
      
      // 转换为数字
      const daysNum = parseInt(days, 10)
      
      // 根据是否有行数据决定是单个续期还是批量续期
      if (row) {
        await handleRenewToken(row, daysNum)
      } else {
        await handleBatchRenewTokens(daysNum)
      }
    } catch (error) {
      if (error === 'cancel') return
      console.error('自定义续期出错:', error)
      ElMessage.error(`自定义续期失败: ${error.message || '未知错误'}`)
    }
  }

  return {
    // 加载状态
    resetLoading,
    activateLoading,
    renewLoading,
    
    // 方法
    handleResetToken,
    handleBatchResetTokens,
    handleActivateToken,
    handleBatchActivateTokens,
    handleRenewToken,
    handleBatchRenewTokens,
    handleCustomRenewDays
  }
} 