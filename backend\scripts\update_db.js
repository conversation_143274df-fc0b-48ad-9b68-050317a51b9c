/**
 * 数据库结构更新脚本
 * 
 * 用于解决"Unknown column 'system_name' in 'field list"错误
 * 
 * 使用方法：
 * 1. 在backend目录下运行: node scripts/update_db.js
 * 2. 脚本将自动添加缺失的system_name字段
 */

const db = require('../database/db');
const { logger } = require('../utils/logger');

async function updateUserPreferencesTable() {
  try {
    logger.info('开始更新user_preferences表结构...');

    // 检查字段是否存在
    const [columns] = await db.query(`
      SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'user_preferences' 
      AND COLUMN_NAME = 'system_name'
    `);

    if (columns.length === 0) {
      // 字段不存在，添加它
      logger.info('system_name字段不存在，正在添加...');
      await db.query(`
        ALTER TABLE user_preferences 
        ADD COLUMN system_name VARCHAR(100) DEFAULT 'Token 管理系统' 
        AFTER items_per_page
      `);
      logger.info('system_name字段已成功添加。');
    } else {
      logger.info('system_name字段已存在，无需修改。');
    }

    // 验证表结构
    const [updatedColumns] = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'user_preferences'
      ORDER BY ORDINAL_POSITION
    `);

    logger.info('user_preferences表结构更新后：');
    console.table(updatedColumns);

    logger.info('数据库更新完成.');
    process.exit(0);
  } catch (error) {
    logger.error(`数据库更新失败: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// 执行更新
updateUserPreferencesTable(); 