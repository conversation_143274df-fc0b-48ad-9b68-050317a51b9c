{"name": "token-management-api", "version": "1.0.0", "description": "Token管理系统API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "express": "^4.18.2", "helmet": "^6.0.1", "https": "^1.0.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.0", "mysql2": "^3.2.0", "winston": "^3.8.2"}, "devDependencies": {"nodemon": "^2.0.22"}}