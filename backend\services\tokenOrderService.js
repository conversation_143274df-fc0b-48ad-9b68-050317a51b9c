const axios = require('axios');
const { logger } = require('../utils/logger');
const { Worker } = require('worker_threads');
const path = require('path');
const os = require('os');
const crypto = require('crypto-js');
const apiConfig = require('../config/externalApiConfig');

/**
 * AES加密函数 - 只用于加密token
 * @param {string} text - 要加密的文本
 * @param {string} key - 加密密钥
 * @returns {string} - 加密后的Base64字符串
 */
const encryptAES = (text, key = 'jijiji1560440222') => {
  // 将key转换为crypto-js可用的格式
  const keyHex = crypto.enc.Utf8.parse(key);

  // 配置ECB模式和PKCS7填充
  const encrypted = crypto.AES.encrypt(text, keyHex, {
    mode: crypto.mode.ECB,
    padding: crypto.pad.Pkcs7
  });

  // 返回Base64格式的加密结果
  return encrypted.toString();
};

/**
 * 删除指定订单
 * @param {Object} orderData 包含uid, token, orderSn的对象
 * @param {number} timeout 超时时间（毫秒）
 * @param {number} retryCount 重试次数
 * @returns {Promise<Object>} 删除结果
 */
exports.deleteOrder = async (orderData, timeout = 10000, retryCount = 1) => {
  try {
    const { uid, token, orderSn } = orderData;

    if (!uid || !token || !orderSn) {
      return {
        success: false,
        orderSn: orderSn || '未知',
        message: '参数不完整，需要提供uid、token和orderSn'
      };
    }

    logger.info(`尝试删除订单: ${orderSn}`);

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'AccessToken': token,
      'Cookie': `pdd_user_id=${uid};PDDAccessToken=${token}`,
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    };

    // 调整API URL格式，尝试修复删除失败问题
    const apiUrl = `https://mobile.yangkeduo.com/proxy/api/order/${orderSn}/delete`;
    logger.info(`尝试删除订单，调用API: ${apiUrl}`);

    // 调用拼多多删除订单API - 从接口文档可以看出请求体应该是空的
    const response = await axios.post(
      apiUrl,
      {},  // 空请求体，无需传递order_sn
      {
        headers,
        timeout
      }
    );

    // 记录完整响应，便于调试
    logger.info(`API响应状态码: ${response.status}`);

    if (response.data && (response.data.success === true || response.data.result === true)) {
      logger.info(`成功删除订单: ${orderSn}`);
      return {
        success: true,
        orderSn
      };
    } else {
      const errorMsg = response.data?.error_msg || '删除失败';
      const errorCode = response.data?.error_code;

      // 记录完整响应信息，帮助诊断
      logger.warn(`删除订单API响应: ${JSON.stringify(response.data || {})}`);

      // 针对特定错误码提供友好提示
      let userFriendlyMsg = errorMsg;
      if (errorCode === 45526) {
        userFriendlyMsg = '该订单暂未完结，无法删除订单';
      } else if (errorCode === 45001) {
        userFriendlyMsg = '当前订单不可删除，请确认订单是否已评价完成';
      } else if (errorCode === 40001 || errorCode === 40002) {
        userFriendlyMsg = '身份验证失败，请重新登录后再试';
      } else if (errorCode === 50000) {
        userFriendlyMsg = '服务器繁忙，请稍后再试';
      }

      // 查看error_payload中是否有更详细的错误信息
      if (response.data?.error_payload?.view_object?.title) {
        userFriendlyMsg = response.data.error_payload.view_object.title;
      }

      // 修改日志记录方式，确保完整记录错误信息
      logger.warn(`删除订单失败 ${orderSn}: ${userFriendlyMsg} (错误码: ${errorCode})`);

      return {
        success: false,
        orderSn,
        message: userFriendlyMsg,
        errorCode,
        responseData: response.data || {} // 返回完整的响应数据以便前端查看
      };
    }
  } catch (error) {
    // 增强错误处理，记录更多错误信息
    logger.error(`删除订单请求异常 ${orderSn}:`, error.message);
    if (error.response) {
      logger.error(`API错误状态码: ${error.response.status}`);
      logger.error(`API错误响应数据: ${JSON.stringify(error.response.data || {})}`);
    }

    // 重试机制
    if (retryCount > 0) {
      logger.info(`重试删除订单，剩余重试次数: ${retryCount-1}`);
      return exports.deleteOrder(orderData, timeout, retryCount - 1);
    }

    return {
      success: false,
      orderSn: orderData.orderSn || '未知',
      message: error.response?.data?.error_msg || `删除失败: ${error.message}`,
      errorCode: error.response?.data?.error_code,
      responseData: error.response?.data || {}
    };
  }
};

/**
 * 批量删除订单
 * @param {Array<Object>} orderItems 订单数据数组，每项包含uid, token, orderSn
 * @returns {Promise<Object>} 包含删除结果的对象
 */
exports.batchDeleteOrders = async (orderItems) => {
  try {
    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return {
        success: false,
        message: '参数不完整，需要提供订单数据数组',
        results: []
      };
    }

    logger.info(`开始批量删除 ${orderItems.length} 个订单`);

    // 并发请求数量控制 - 增加并发数
    const CONCURRENCY = 10;  // 从5增加到10
    const TIMEOUT = 15000;
    const RETRY_COUNT = 2;

    // 存储所有删除请求的Promise
    const deletePromises = [];

    // 控制并发数
    for (let i = 0; i < orderItems.length; i += CONCURRENCY) {
      const batch = orderItems.slice(i, i + CONCURRENCY);
      const batchPromises = batch.map(item => exports.deleteOrder(item, TIMEOUT, RETRY_COUNT));

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);
      deletePromises.push(...batchResults);

      // 每个批次之间稍微延迟，避免请求过于密集 - 减少延迟
      if (i + CONCURRENCY < orderItems.length) {
        await new Promise(resolve => setTimeout(resolve, 200)); // 从500ms减少到200ms
      }
    }

    // 统计结果
    const successCount = deletePromises.filter(r => r.success).length;

    logger.info(`批量删除订单完成: 成功 ${successCount}/${deletePromises.length} 个`);

    return {
      success: true,
      message: `成功删除 ${successCount}/${deletePromises.length} 个订单`,
      results: deletePromises
    };
  } catch (error) {
    logger.error('批量删除订单错误:', error);
    return {
      success: false,
      message: '批量删除失败: ' + error.message,
      results: []
    };
  }
};

/**
 * 将Token置入账号内订单
 * @param {Array} tokens - Token数据数组，每项包含UID、token和username
 * @returns {Promise<Object>} - 操作结果
 */
exports.placeOrder = async (tokens) => {
  try {
    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return {
        success: false,
        message: '请提供有效的Token数据'
      };
    }

    // 记录操作日志
    logger.info(`尝试为用户提交Token置入订单，共${tokens.length}个Token`);

    // 预设的管理员凭证
    const ADMIN_CREDENTIALS = {
      admin: "a15604402",
      password: "lijinrong11"
    };

    // 准备请求数据 - 使用第一个Token的用户名
    // 注意：只对token字段进行加密，其他保持明文
    const requestData = {
      username: tokens[0].username || '',  // 使用第一个token的用户名
      admin: ADMIN_CREDENTIALS.admin,      // 管理员凭证明文
      password: ADMIN_CREDENTIALS.password,// 密码明文
      data: tokens.map(item => ({
        UID: item.UID,                     // UID保持明文
        token: encryptAES(item.token)      // 只对token值进行加密
      }))
    };

    logger.info('准备发送请求数据到外部API');

    // 使用配置的API URL替代硬编码URL
    const API_URL = apiConfig.endpoints.TOKEN.BULK_UPDATE;

    logger.info('正在发送请求到: ' + API_URL);

    const response = await axios.post(API_URL, requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });

    logger.info('收到API响应');

    // 处理响应结果
    if (response.data && response.data.success) {
      logger.info('Token置入订单成功');
      return {
        success: true,
        message: response.data.success || '操作成功',
        data: response.data
      };
    } else if (response.data && response.data.error) {
      // 处理错误响应
      const errorMessages = Array.isArray(response.data.error)
        ? response.data.error.join('\n')
        : response.data.error;

      logger.error('外部API返回错误: ' + errorMessages);

      // 检查是否是认证错误
      if (errorMessages.includes('用户名或密码不正确')) {
        logger.error('API认证失败，请更新管理员凭证');
      }

      return {
        success: false,
        message: errorMessages,
        data: response.data
      };
    } else {
      // 其他情况，默认视为成功
      logger.info('操作完成，但响应格式不完全符合预期');
      return {
        success: true,
        message: '操作已完成',
        data: response.data
      };
    }
  } catch (error) {
    logger.error('Token置入订单失败:', error);
    return {
      success: false,
      message: '服务器处理请求失败: ' + error.message,
      error: error.message
    };
  }
};