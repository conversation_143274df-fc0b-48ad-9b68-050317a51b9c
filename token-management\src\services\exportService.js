// 导出服务，处理Token数据的导出
import { exportToExcel } from '../utils/exportUtils'

const exportService = {
  /**
   * 导出选中的Token数据
   * @param {Array} selectedTokens - 选中的Token数据
   * @returns {Promise<boolean>} - 导出结果
   */
  exportSelectedTokens(selectedTokens) {
    if (!selectedTokens || selectedTokens.length === 0) {
      throw new Error('未选择任何数据')
    }
    
    // 处理要导出的数据，只保留uid, token, 用户字段
    const exportData = selectedTokens.map(token => ({
      uid: token.uid,
      token: token.token,
      用户: token.user
    }))
    
    // 生成日期格式：月月日日数量个
    const today = new Date()
    const month = today.getMonth() + 1 // 月份从0开始，所以需要+1
    const day = today.getDate()
    const fileName = `${month}月${day}日${selectedTokens.length}个`
    
    // 导出为Excel
    return exportToExcel(exportData, fileName, 'Token数据')
  },
  
  /**
   * 导出所有Token数据
   * @param {Array} allTokens - 所有Token数据
   * @returns {Promise<boolean>} - 导出结果
   */
  exportAllTokens(allTokens) {
    if (!allTokens || allTokens.length === 0) {
      throw new Error('没有可导出的数据')
    }
    
    // 处理要导出的数据，只保留uid, token, 用户字段
    const exportData = allTokens.map(token => ({
      uid: token.uid,
      token: token.token,
      用户: token.user
    }))
    
    // 生成日期格式：月月日日数量个
    const today = new Date()
    const month = today.getMonth() + 1 // 月份从0开始，所以需要+1
    const day = today.getDate()
    const fileName = `${month}月${day}日${allTokens.length}个`
    
    // 导出为Excel
    return exportToExcel(exportData, fileName, 'Token数据')
  }
}

export default exportService 