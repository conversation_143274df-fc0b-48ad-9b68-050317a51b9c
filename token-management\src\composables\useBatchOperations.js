import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tokenApi } from '../api/token'
import { exportToCsv, exportToExcel } from '../utils/export'

/**
 * Token批量操作的组合式函数
 * 提供批量删除、激活、禁用、续期和标签管理等功能
 */
export default function useBatchOperations(options = {}) {
  // 从选项中获取需要的数据和函数
  const {
    tokens = ref([]),
    selectedRows = ref([]),
    refreshTokens = () => {}
  } = options
  
  // 加载状态
  const deleteLoading = ref(false)
  const activateLoading = ref(false)
  const disableLoading = ref(false)
  const renewLoading = ref(false)
  const tagLoading = ref(false)
  const exportLoading = ref(false)
  
  // 计算属性
  const hasSelected = computed(() => selectedRows.value.length > 0)
  const selectedCount = computed(() => selectedRows.value.length)
  
  /**
   * 删除单个Token
   * @param {Object} row Token行数据
   */
  const handleDeleteToken = async (row) => {
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要删除此Token吗？此操作不可逆。`,
        '删除Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      deleteLoading.value = true
      
      // 调用API删除Token
      const response = await tokenApi.deleteToken(row.uid)
      
      if (response.code === 200) {
        ElMessage.success(`Token删除成功: ${row.username || row.uid}`)
        
        // 从本地数据中移除
        const index = tokens.value.findIndex(t => t.uid === row.uid)
        if (index !== -1) {
          tokens.value.splice(index, 1)
        }
        
        // 从选中行中移除
        const selectedIndex = selectedRows.value.findIndex(t => t.uid === row.uid)
        if (selectedIndex !== -1) {
          selectedRows.value.splice(selectedIndex, 1)
        }
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`删除Token失败: ${error.message || '未知错误'}`)
    } finally {
      deleteLoading.value = false
    }
  }
  
  /**
   * 批量删除选中的Token
   */
  const handleBatchDeleteTokens = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要删除的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 个Token吗？此操作不可逆。`,
        '批量删除Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      deleteLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量删除Token
      const response = await tokenApi.batchDeleteTokens(uids)
      
      if (response.code === 200) {
        ElMessage.success(`成功删除 ${response.data?.successCount || 0} 个Token`)
        
        // 从本地数据中移除已删除的Token
        const deletedUids = new Set(uids)
        tokens.value = tokens.value.filter(t => !deletedUids.has(t.uid))
        
        // 清空选中的行
        selectedRows.value = []
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量删除Token失败: ${error.message || '未知错误'}`)
    } finally {
      deleteLoading.value = false
    }
  }

  /**
   * 批量激活选中的Token
   */
  const handleBatchActivateTokens = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要激活的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要激活选中的 ${selectedRows.value.length} 个Token吗？此操作将使这些Token状态变为"正常"。`,
        '批量激活Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      activateLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量激活Token
      const response = await tokenApi.batchActivateTokens(uids)
      
      if (response.code === 200) {
        ElMessage.success(`成功激活 ${response.data?.successCount || 0} 个Token`)
        
        // 更新本地数据状态
        uids.forEach(uid => {
          const index = tokens.value.findIndex(t => t.uid === uid)
          if (index !== -1) {
            tokens.value[index] = { ...tokens.value[index], status: 'normal' }
          }
        })
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量激活失败')
      }
    } catch (error) {
      console.error('批量激活Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量激活Token失败: ${error.message || '未知错误'}`)
    } finally {
      activateLoading.value = false
    }
  }

  /**
   * 批量禁用选中的Token
   */
  const handleBatchDisableTokens = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要禁用的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要禁用选中的 ${selectedRows.value.length} 个Token吗？此操作将使这些Token状态变为"禁用"。`,
        '批量禁用Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      disableLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量禁用Token
      const response = await tokenApi.batchDisableTokens(uids)
      
      if (response.code === 200) {
        ElMessage.success(`成功禁用 ${response.data?.successCount || 0} 个Token`)
        
        // 更新本地数据状态
        uids.forEach(uid => {
          const index = tokens.value.findIndex(t => t.uid === uid)
          if (index !== -1) {
            tokens.value[index] = { ...tokens.value[index], status: 'disabled' }
          }
        })
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量禁用失败')
      }
    } catch (error) {
      console.error('批量禁用Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量禁用Token失败: ${error.message || '未知错误'}`)
    } finally {
      disableLoading.value = false
    }
  }

  /**
   * 批量续期选中的Token
   * @param {number} days 续期天数
   */
  const handleBatchRenewTokens = async (days = 30) => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要续期的Token')
      return
    }
    
    try {
      // 确认对话框
      await ElMessageBox.confirm(
        `确定要为选中的 ${selectedRows.value.length} 个Token续期 ${days} 天吗？`,
        '批量续期Token',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      
      renewLoading.value = true
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量续期Token
      const response = await tokenApi.batchRenewTokens(uids, { days })
      
      if (response.code === 200) {
        ElMessage.success(`成功为 ${response.data?.successCount || 0} 个Token续期 ${days} 天`)
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量续期失败')
      }
    } catch (error) {
      console.error('批量续期Token时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量续期Token失败: ${error.message || '未知错误'}`)
    } finally {
      renewLoading.value = false
    }
  }

  /**
   * 自定义续期天数对话框
   */
  const handleCustomRenewDays = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要续期的Token')
      return
    }
    
    try {
      // 获取用户输入的续期天数
      const { value: days } = await ElMessageBox.prompt(
        '请输入要续期的天数',
        '自定义续期',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: '请输入正整数',
          inputValue: '30'
        }
      )
      
      // 转换为数字
      const daysNum = parseInt(days, 10)
      
      // 执行批量续期
      await handleBatchRenewTokens(daysNum)
    } catch (error) {
      if (error === 'cancel') return
      console.error('自定义续期出错:', error)
      ElMessage.error(`自定义续期失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 批量设置标签
   */
  const handleBatchSetTags = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要设置标签的Token')
      return
    }
    
    try {
      // 获取用户输入的标签
      const { value: tags } = await ElMessageBox.prompt(
        '请输入要设置的标签，多个标签用逗号分隔',
        '设置标签',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: value => value.trim() !== '',
          inputErrorMessage: '标签不能为空'
        }
      )
      
      tagLoading.value = true
      
      // 处理标签，转换为数组
      const tagArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量设置标签
      const response = await tokenApi.batchSetTags(uids, { tags: tagArray })
      
      if (response.code === 200) {
        ElMessage.success(`成功为 ${response.data?.successCount || 0} 个Token设置标签`)
        
        // 更新本地数据
        uids.forEach(uid => {
          const index = tokens.value.findIndex(t => t.uid === uid)
          if (index !== -1) {
            tokens.value[index] = { 
              ...tokens.value[index], 
              tags: [...(tokens.value[index].tags || []), ...tagArray]
            }
          }
        })
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量设置标签失败')
      }
    } catch (error) {
      console.error('批量设置标签时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量设置标签失败: ${error.message || '未知错误'}`)
    } finally {
      tagLoading.value = false
    }
  }

  /**
   * 批量移除标签
   */
  const handleBatchRemoveTags = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要移除标签的Token')
      return
    }
    
    try {
      // 获取用户输入的标签
      const { value: tags } = await ElMessageBox.prompt(
        '请输入要移除的标签，多个标签用逗号分隔',
        '移除标签',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: value => value.trim() !== '',
          inputErrorMessage: '标签不能为空'
        }
      )
      
      tagLoading.value = true
      
      // 处理标签，转换为数组
      const tagArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
      
      // 收集所有UID
      const uids = selectedRows.value.map(row => row.uid)
      
      // 调用API批量移除标签
      const response = await tokenApi.batchRemoveTags(uids, { tags: tagArray })
      
      if (response.code === 200) {
        ElMessage.success(`成功从 ${response.data?.successCount || 0} 个Token移除标签`)
        
        // 更新本地数据
        uids.forEach(uid => {
          const index = tokens.value.findIndex(t => t.uid === uid)
          if (index !== -1 && tokens.value[index].tags) {
            tokens.value[index] = { 
              ...tokens.value[index], 
              tags: tokens.value[index].tags.filter(tag => !tagArray.includes(tag))
            }
          }
        })
        
        // 刷新数据
        await refreshTokens()
      } else {
        throw new Error(response.message || '批量移除标签失败')
      }
    } catch (error) {
      console.error('批量移除标签时出错:', error)
      if (error === 'cancel') return
      
      ElMessage.error(`批量移除标签失败: ${error.message || '未知错误'}`)
    } finally {
      tagLoading.value = false
    }
  }

  /**
   * 复制选中Token
   */
  const handleCopySelectedTokens = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要复制的Token')
      return
    }
    
    try {
      // 提取token值
      const tokenValues = selectedRows.value.map(row => row.token).join('\n')
      
      // 创建临时文本区域
      const textArea = document.createElement('textarea')
      textArea.value = tokenValues
      document.body.appendChild(textArea)
      textArea.select()
      
      // 执行复制命令
      document.execCommand('copy')
      
      // 移除临时元素
      document.body.removeChild(textArea)
      
      ElMessage.success(`已复制 ${selectedRows.value.length} 个Token到剪贴板`)
    } catch (error) {
      console.error('复制Token时出错:', error)
      ElMessage.error(`复制失败: ${error.message || '未知错误'}`)
    }
  }
  
  /**
   * 导出选中的Token到CSV文件
   */
  const handleExportSelectedToCsv = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要导出的Token')
      return
    }
    
    try {
      exportLoading.value = true
      
      // 准备导出数据
      const dataToExport = selectedRows.value.map(token => ({
        用户名: token.username || token.user || '',
        Token: token.token,
        状态: token.status === 'normal' ? '正常' : token.status === 'expired' ? '已过期' : '已禁用',
        创建时间: token.createTime,
        过期时间: token.expireTime || token.expiryDate,
        最后使用时间: token.lastUseTime || '-',
        描述: token.description || '-'
      }))
      
      // 导出文件名
      const fileName = `token_export_selected_${new Date().toISOString().slice(0, 10)}`
      
      // 调用导出工具
      exportToCsv(dataToExport, fileName)
      
      ElMessage.success(`成功导出 ${selectedRows.value.length} 个选中的Token到CSV文件`)
    } catch (error) {
      console.error('导出选中Token到CSV时出错:', error)
      ElMessage.error(`导出失败: ${error.message || '未知错误'}`)
    } finally {
      exportLoading.value = false
    }
  }
  
  /**
   * 导出选中的Token到Excel文件
   */
  const handleExportSelectedToExcel = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要导出的Token')
      return
    }
    
    try {
      exportLoading.value = true
      
      // 准备导出数据
      const dataToExport = selectedRows.value.map(token => ({
        用户名: token.username || token.user || '',
        Token: token.token,
        状态: token.status === 'normal' ? '正常' : token.status === 'expired' ? '已过期' : '已禁用',
        创建时间: token.createTime,
        过期时间: token.expireTime || token.expiryDate,
        最后使用时间: token.lastUseTime || '-',
        描述: token.description || '-'
      }))
      
      // 导出文件名
      const fileName = `token_export_selected_${new Date().toISOString().slice(0, 10)}`
      
      // 表头和列宽
      const headers = ['用户名', 'Token', '状态', '创建时间', '过期时间', '最后使用时间', '描述']
      const colWidths = [15, 40, 10, 20, 20, 20, 30]
      
      // 调用导出工具
      exportToExcel(dataToExport, fileName, headers, colWidths)
      
      ElMessage.success(`成功导出 ${selectedRows.value.length} 个选中的Token到Excel文件`)
    } catch (error) {
      console.error('导出选中Token到Excel时出错:', error)
      ElMessage.error(`导出失败: ${error.message || '未知错误'}`)
    } finally {
      exportLoading.value = false
    }
  }
  
  return {
    // 状态
    deleteLoading,
    activateLoading,
    disableLoading,
    renewLoading,
    tagLoading,
    exportLoading,
    
    // 计算属性
    hasSelected,
    selectedCount,
    
    // 方法
    handleDeleteToken,
    handleBatchDeleteTokens,
    handleBatchActivateTokens,
    handleBatchDisableTokens,
    handleBatchRenewTokens,
    handleCustomRenewDays,
    handleBatchSetTags,
    handleBatchRemoveTags,
    handleCopySelectedTokens,
    handleExportSelectedToCsv,
    handleExportSelectedToExcel
  }
} 