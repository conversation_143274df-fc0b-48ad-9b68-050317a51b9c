<template>
  <div class="nickname-changer">
    <!-- 修改昵称按钮 -->
    <el-button
      type="primary"
      :icon="Edit"
      :loading="loading"
      @click="openNicknameDialog"
    >
      修改昵称
    </el-button>

    <!-- 修改昵称对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="修改昵称"
      width="500px"
    >
      <div class="nickname-dialog-content">
        <!-- 昵称输入区域，只在自定义昵称时显示 -->
        <div class="nickname-input-section" v-if="showCustomInput">
          <h4>自定义昵称</h4>
          <el-input
            v-model="customNickname"
            placeholder="请输入昵称"
            maxlength="20"
            show-word-limit
          ></el-input>
        </div>

        <!-- 昵称选择选项 -->
        <div class="nickname-options">
          <el-button type="primary" @click="randomModifyNickname" :loading="randomLoading">
            <el-icon><el-icon-refresh /></el-icon>随机修改
          </el-button>
          <el-button type="success" @click="toggleCustomInput">
            <el-icon><el-icon-edit /></el-icon>自定义昵称
          </el-button>
        </div>

        <!-- 提示信息 -->
        <div class="info-section" v-if="selectedTokens.length > 0">
          <el-alert
            title="操作提示"
            type="info"
            :closable="false"
          >
            <p>已选择 {{ selectedTokens.length }} 个Token，点击"随机修改"将为每个Token分配随机昵称，点击"自定义昵称"可设置统一昵称。</p>
          </el-alert>
        </div>
        <div class="no-tokens-selected" v-else>
          <el-alert
            title="未选择Token"
            type="warning"
            :closable="false"
          >
            请先在Token列表中选择要修改昵称的Token
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="changeNickname(false)"
            :disabled="!canChangeNickname || (showCustomInput && !customNickname)"
            :loading="changing"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import api from '../services/api'

// 定义props
const props = defineProps({
  selectedTokens: {
    type: Array,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['update:tokens', 'success', 'error'])

// 状态变量
const dialogVisible = ref(false)
const loading = ref(false)
const randomLoading = ref(false)
const changing = ref(false)
const customNickname = ref('')
const showCustomInput = ref(false)
// 跟踪是否使用自定义昵称
const useCustomNickname = ref(false)

// 计算属性
const canChangeNickname = computed(() => {
  return props.selectedTokens.length > 0
})

// 打开昵称对话框
const openNicknameDialog = () => {
  dialogVisible.value = true
  // 重置状态
  customNickname.value = ''
  showCustomInput.value = false
  useCustomNickname.value = false
  console.log('打开昵称对话框，重置状态')
}

// 切换自定义昵称输入
const toggleCustomInput = () => {
  showCustomInput.value = !showCustomInput.value
  if (showCustomInput.value) {
    // 切换到自定义昵称模式
    useCustomNickname.value = true
    console.log('切换到自定义昵称模式，useCustomNickname=', useCustomNickname.value)
  } else {
    // 关闭自定义昵称模式
    customNickname.value = ''
    useCustomNickname.value = false
    console.log('关闭自定义昵称模式，useCustomNickname=', useCustomNickname.value)
  }
}

// 随机修改昵称
const randomModifyNickname = async () => {
  if (!canChangeNickname.value) {
    ElMessage.warning('请先选择Token')
    return
  }

  try {
    randomLoading.value = true
    // 直接调用修改昵称函数，不显示自定义输入
    showCustomInput.value = false
    // 清除自定义昵称，确保使用随机昵称
    customNickname.value = ''
    // 设置不使用自定义昵称
    useCustomNickname.value = false
    console.log('随机修改模式：已清除自定义昵称，useCustomNickname=', useCustomNickname.value)
    await changeNickname(true) // 传入true表示随机修改
  } catch (error) {
    console.error('随机修改昵称失败:', error)
    ElMessage.error(`随机修改昵称失败: ${error.message}`)
  } finally {
    randomLoading.value = false
  }
}

// 获取随机昵称
const getRandomNickname = async () => {
  try {
    // 调用后端API获取随机昵称
    const response = await api.get('/nickname/random')

    if (response && response.success && response.nickname) {
      return {
        success: true,
        nickname: response.nickname
      }
    } else {
      throw new Error('获取随机昵称失败: 响应数据不完整')
    }
  } catch (error) {
    console.error('获取随机昵称失败:', error)
    // 如果后端服务不可用，使用模拟数据
    console.log('使用模拟随机昵称')
    return simulateRandomNickname()
  }
}

// 模拟随机昵称（仅用于演示）
const simulateRandomNickname = () => {
  // 这里应该是从服务器获取随机昵称
  // 由于无法直接访问本地文件系统，我们使用一个模拟的随机昵称
  const randomNicknames = [
    '快乐的舞步', '真心的爱', '翩若惊鸿', '运气多多', '封笔',
    '灵魂的港湾', '醉笙', '夏至春掩', '色舞天涯', '无道纣王'
  ]

  const randomIndex = Math.floor(Math.random() * randomNicknames.length)
  const randomNickname = randomNicknames[randomIndex]

  console.log('使用模拟随机昵称:', randomNickname)

  // 返回模拟的成功结果
  return {
    success: true,
    nickname: randomNickname
  }
}

// 修改昵称
const changeNickname = async (isRandom = false) => {
  // 打印参数类型，便于调试
  console.log('调用changeNickname函数，isRandom=', isRandom, '类型:', typeof isRandom)
  console.log('当前状态: useCustomNickname=', useCustomNickname.value, 'showCustomInput=', showCustomInput.value, 'customNickname=', customNickname.value)

  // 如果是自定义昵称模式，需要检查是否输入了昵称
  if (!isRandom && showCustomInput.value && !customNickname.value) {
    ElMessage.warning('请先输入昵称')
    return
  }

  // 检查是否选择了Token
  if (!canChangeNickname.value) {
    ElMessage.warning('请先选择Token')
    return
  }

  try {
    changing.value = true

    // 显示加载提示
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在修改昵称...'
    })

    // 获取选中的Token
    const tokens = props.selectedTokens
    if (tokens.length === 0) {
      throw new Error('未选择Token')
    }

    // 初始化结果对象
    const results = {
      success: 0,
      failed: 0,
      tokens: [],
      total: tokens.length,
      processed: 0
    }

    // 并发控制参数
    const BATCH_SIZE = 10 // 每批处理的Token数量
    const MAX_CONCURRENT = 5 // 最大并发数
    const MAX_RETRIES = 3 // 最大重试次数

    // 将Token分批
    const batches = []
    for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
      batches.push(tokens.slice(i, i + BATCH_SIZE))
    }

    // 并发控制函数
    const concurrentControl = async (tasks, maxConcurrent) => {
      const results = []
      const running = new Set()

      for (const task of tasks) {
        if (running.size >= maxConcurrent) {
          // 等待任意一个任务完成
          await Promise.race([...running])
        }

        // 创建新任务
        const promise = (async () => {
          try {
            const result = await task()
            results.push(result)
          } catch (error) {
            results.push({ error })
          } finally {
            running.delete(promise)
          }
        })()

        running.add(promise)
      }

      // 等待所有任务完成
      await Promise.all([...running])

      return results
    }

    // 处理单个Token的函数（带重试）
    const processToken = async (token, retries = 0) => {
      try {
        // 检查Token是否有效
        if (!token.token) {
          throw new Error('Token无效')
        }

        let nicknameToUse = ''

        // 打印当前状态，便于调试
        console.log(`当前状态: isRandom=${isRandom}, useCustomNickname=${useCustomNickname.value}, showCustomInput=${showCustomInput.value}, customNickname=${customNickname.value}`)

        // 根据模式决定使用的昵称
        if (isRandom) {
          // 随机修改模式，获取随机昵称
          const randomResult = await getRandomNickname()
          nicknameToUse = randomResult.nickname
          console.log(`Token ${token.uid} 使用随机昵称: ${nicknameToUse}`)
        } else if (useCustomNickname.value && customNickname.value) {
          // 自定义昵称模式，使用用户输入的昵称
          nicknameToUse = customNickname.value
          console.log(`Token ${token.uid} 使用自定义昵称: ${nicknameToUse}`)
        } else {
          // 如果没有选择自定义昵称模式或没有输入昵称，使用随机昵称
          const randomResult = await getRandomNickname()
          nicknameToUse = randomResult.nickname
          console.log(`Token ${token.uid} 未指定昵称模式，使用随机昵称: ${nicknameToUse}`)
        }

        // 修改昵称
        await changeNicknameForToken(token.token, nicknameToUse)

        // 更新Token信息
        const updatedToken = { ...token, nickname: nicknameToUse }

        // 更新进度
        results.processed++
        loadingInstance.setText(`正在修改昵称... (${results.processed}/${results.total})`)

        return { success: true, token: updatedToken }
      } catch (error) {
        console.error(`修改Token ${token.uid} 的昵称失败:`, error)

        // 重试机制
        if (retries < MAX_RETRIES) {
          console.log(`重试修改Token ${token.uid} 的昵称，第 ${retries + 1} 次重试`)
          return processToken(token, retries + 1)
        }

        // 更新进度
        results.processed++
        loadingInstance.setText(`正在修改昵称... (${results.processed}/${results.total})`)

        return { success: false, error, token }
      }
    }

    // 处理所有批次
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      console.log(`开始处理第 ${i + 1}/${batches.length} 批，共 ${batch.length} 个Token`)

      // 创建任务列表
      const tasks = batch.map(token => () => processToken(token))

      // 并发执行任务
      const batchResults = await concurrentControl(tasks, MAX_CONCURRENT)

      // 处理结果
      for (const result of batchResults) {
        if (result.success) {
          results.tokens.push(result.token)
          results.success++
        } else {
          results.failed++
        }
      }

      console.log(`完成第 ${i + 1}/${batches.length} 批处理，成功: ${results.success}, 失败: ${results.failed}`)
    }

    // 更新Token列表
    if (results.tokens.length > 0) {
      emit('update:tokens', results.tokens)
    }

    // 显示结果
    if (results.success > 0) {
      ElMessage.success(`成功修改 ${results.success} 个Token的昵称`)
      emit('success', results)
    }

    if (results.failed > 0) {
      ElMessage.warning(`${results.failed} 个Token的昵称修改失败`)
    }

    // 关闭对话框
    dialogVisible.value = false
  } catch (error) {
    console.error('修改昵称失败:', error)
    ElMessage.error(`修改昵称失败: ${error.message}`)
    emit('error', error)
  } finally {
    changing.value = false
    ElLoading.service().close()
  }
}

// 为单个Token修改昵称
const changeNicknameForToken = async (token, nickname) => {
  try {
    console.log(`开始修改昵称，通过后端API请求，昵称值: "${nickname}"`);

    // 使用后端API一次完成昵称修改
    const response = await api.post('/nickname/change', {
      token,
      nickname
    });

    console.log('修改昵称响应:', response);
    console.log(`昵称修改成功，昵称值: "${nickname}"`);

    if (response && response.success) {
      return true;
    } else {
      throw new Error(response?.message || '修改昵称失败');
    }
  } catch (error) {
    console.error(`修改昵称API请求失败，昵称值: "${nickname}"`, error);

    // 如果是在开发环境或者后端服务不可用，使用模拟数据
    if (process.env.NODE_ENV === 'development' || error.message?.includes('Network Error') || error.message?.includes('404') || error.message?.includes('401')) {
      console.log(`使用模拟数据修改昵称，昵称值: "${nickname}"`);
      ElMessage.warning('后端服务不可用，使用模拟数据修改昵称');
      // 模拟成功响应
      return true;
    }

    throw error;
  }
}

// 监听自定义昵称的变化
watch(customNickname, (newValue) => {
  if (newValue && showCustomInput.value) {
    // 当用户输入自定义昵称时，设置useCustomNickname为true
    useCustomNickname.value = true
    console.log('用户输入了自定义昵称，useCustomNickname=', useCustomNickname.value)
  }
})

// 在组件挂载时初始化
onMounted(() => {
  // 可以在这里进行初始化操作
})
</script>

<style scoped>
.nickname-changer {
  display: inline-block;
}

.nickname-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.nickname-input-section {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
}

.nickname-options {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.info-section,
.no-tokens-selected {
  width: 100%;
  margin-top: 20px;
}

.token-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
</style>
