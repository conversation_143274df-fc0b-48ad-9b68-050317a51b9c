const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const logger = require('./utils/logger');
const path = require('path');
const helmet = require('helmet');
const dotenv = require('dotenv');

// 导入路由模块
const tokenRoutes = require('./routes/tokenRoutes');
const backupRoutes = require('./routes/backupRoutes');
const avatarRoutes = require('./routes/avatarRoutes');
const nicknameRoutes = require('./routes/nicknameRoutes');

// 加载环境变量
dotenv.config();

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 配置中间件
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json({ limit: '10mb' })); // 处理JSON请求体，增加限制
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' })); // 处理URL编码的请求体

// 简单的请求日志
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url}`);
  next();
});

// 注册路由
app.use('/api/tokens', tokenRoutes);
app.use('/api/backups', backupRoutes);
app.use('/api', avatarRoutes);
app.use('/api/nickname', nicknameRoutes);

// 基础API状态路由
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString()
  });
});

// 默认路由 - API文档
app.get('/', (req, res) => {
  res.send({
    message: 'Token管理系统API服务正常运行',
    status: 'ok',
    time: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `路径 ${req.url} 不存在`
  });
});

// 错误处理
app.use((err, req, res, next) => {
  logger.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 启动服务器
app.listen(PORT, () => {
  logger.info(`服务器运行在端口 ${PORT}`);
});

module.exports = app;