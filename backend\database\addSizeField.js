// 用于添加缺失的size字段的一次性脚本
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function updateDatabase() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'qq@666666',
    database: process.env.DB_NAME || 'token_management'
  });

  try {
    console.log('连接到数据库成功，开始执行更新...');

    // 检查字段是否存在
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
      AND TABLE_NAME = 'backups' 
      AND COLUMN_NAME = 'size'
    `);
    
    if (columns.length === 0) {
      // 如果字段不存在，添加它
      await connection.query(`
        ALTER TABLE backups 
        ADD COLUMN size VARCHAR(20) AFTER token_count
      `);
      console.log('成功添加 size 字段');
    } else {
      console.log('size 字段已存在，无需添加');
    }

    // 添加其他可能缺失的字段
    try {
      // 检查type字段
      const [typeColumns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
        AND TABLE_NAME = 'backups' 
        AND COLUMN_NAME = 'type'
      `);
      
      if (typeColumns.length === 0) {
        await connection.query(`
          ALTER TABLE backups 
          ADD COLUMN type VARCHAR(20) AFTER size
        `);
        console.log('成功添加 type 字段');
      } else {
        console.log('type 字段已存在，无需添加');
      }

      // 检查remark字段
      const [remarkColumns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
        AND TABLE_NAME = 'backups' 
        AND COLUMN_NAME = 'remark'
      `);
      
      if (remarkColumns.length === 0) {
        await connection.query(`
          ALTER TABLE backups 
          ADD COLUMN remark TEXT AFTER description
        `);
        console.log('成功添加 remark 字段');
      } else {
        console.log('remark 字段已存在，无需添加');
      }

      // 检查delete_status字段
      const [deleteStatusColumns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
        AND TABLE_NAME = 'backups' 
        AND COLUMN_NAME = 'delete_status'
      `);
      
      if (deleteStatusColumns.length === 0) {
        await connection.query(`
          ALTER TABLE backups 
          ADD COLUMN delete_status BOOLEAN DEFAULT FALSE AFTER status
        `);
        console.log('成功添加 delete_status 字段');
      } else {
        console.log('delete_status 字段已存在，无需添加');
      }

      // 检查scheduled_delete_time字段
      const [scheduleDeleteTimeColumns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
        AND TABLE_NAME = 'backups' 
        AND COLUMN_NAME = 'scheduled_delete_time'
      `);
      
      if (scheduleDeleteTimeColumns.length === 0) {
        await connection.query(`
          ALTER TABLE backups 
          ADD COLUMN scheduled_delete_time DATETIME AFTER delete_status
        `);
        console.log('成功添加 scheduled_delete_time 字段');
      } else {
        console.log('scheduled_delete_time 字段已存在，无需添加');
      }

      // 检查backup_time字段
      const [backupTimeColumns] = await connection.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
        AND TABLE_NAME = 'backups' 
        AND COLUMN_NAME = 'backup_time'
      `);
      
      if (backupTimeColumns.length === 0) {
        await connection.query(`
          ALTER TABLE backups 
          ADD COLUMN backup_time DATETIME AFTER scheduled_delete_time
        `);
        console.log('成功添加 backup_time 字段');
      } else {
        console.log('backup_time 字段已存在，无需添加');
      }

    } catch (error) {
      console.error('添加其他字段时出错:', error.message);
    }

    console.log('数据库更新完成！');
  } catch (error) {
    console.error('执行数据库更新时出错:', error.message);
  } finally {
    // 关闭连接
    await connection.end();
    console.log('数据库连接已关闭');
  }
}

// 执行更新
updateDatabase().catch(err => {
  console.error('更新脚本执行失败:', err);
  process.exit(1);
}); 