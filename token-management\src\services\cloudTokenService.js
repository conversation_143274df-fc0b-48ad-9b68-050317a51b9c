import axios from 'axios';
import { API_ENDPOINTS } from '../config/apiConfig';

// 使用集中配置的API URL
const LOCAL_BACKEND_API_URL = '/api/cloud/truncate-unsold'; // 假设前端代理已配置
// 如果没有代理，可能需要写完整 URL，例如: 'http://localhost:3000/api/cloud/truncate-unsold'

/**
 * Calls the local backend API to trigger the truncation of unsold tokens in the cloud.
 * @returns {Promise<{success: boolean, message: string}>}
 */
export const truncateCloudUnsoldTokens = async () => {
  try {
    console.log('Sending request to local backend to truncate cloud unsold tokens...');
    // 向本地后端发送 POST 请求
    const response = await axios.post(LOCAL_BACKEND_API_URL, {}, { // 发送空对象作为请求体，因为后端不需要参数
      // 本地后端调用通常不需要特殊 header，除非后端有特定要求 (如 CSRF token)
      timeout: 60000 // 增加超时时间，因为后端需要调用外部 API
    });

    console.log('Local Backend Response Status:', response.status);
    console.log('Local Backend Response Data:', response.data);

    // 直接返回后端的结果 { success, message }
    if (response.data && typeof response.data.success === 'boolean') {
      return response.data;
    } else {
      // 如果后端响应格式不符合预期
      const errorMessage = `本地后端响应格式无效: ${JSON.stringify(response.data)}`;
      console.error(errorMessage);
      return { success: false, message: errorMessage };
    }

  } catch (error) {
    console.error('Error calling local backend API:', error);
    let message = '请求本地后端接口失败。';
    if (error.response) {
      // 后端返回了错误状态码
      message += ` 服务器错误: ${error.response.status} - ${error.response.data?.message || JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      // 请求已发出但未收到响应
      message += ' 未收到本地后端响应，请检查后端服务是否运行及前端代理配置。';
    } else {
      // 请求设置错误
      message += ` 请求设置错误: ${error.message}`;
    }
    return { success: false, message: message };
  }
}; 