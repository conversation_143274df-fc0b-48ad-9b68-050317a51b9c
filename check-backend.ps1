Write-Host "正在检查后端API状态..." -ForegroundColor Yellow

try {
    # 尝试连接后端API
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -Method GET -TimeoutSec 5
    
    if ($response.StatusCode -eq 200) {
        Write-Host "后端API连接成功！" -ForegroundColor Green
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "响应内容: $($response.Content)" -ForegroundColor Green
    }
    else {
        Write-Host "后端API返回非成功状态码: $($response.StatusCode)" -ForegroundColor Red
    }
}
catch {
    Write-Host "后端API连接失败: $_" -ForegroundColor Red
    Write-Host "" -ForegroundColor Red
    Write-Host "请确保后端服务已正确启动!" -ForegroundColor Yellow
    Write-Host "可以通过以下步骤手动启动后端服务:" -ForegroundColor Yellow
    Write-Host "1. 打开一个新的PowerShell窗口" -ForegroundColor White
    Write-Host "2. 运行: cd 'E:\1备份\PY文件\新版tk网站\backend'" -ForegroundColor White
    Write-Host "3. 运行: node server.js" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 