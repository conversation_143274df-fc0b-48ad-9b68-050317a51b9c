const fs = require('fs');
const path = require('path');
const { Parser } = require('json2csv');
const ExcelJS = require('exceljs');
const { logger } = require('./logger');

/**
 * 将数据导出为CSV文件
 * @param {Array} data - 要导出的数据数组
 * @param {Array} fields - 要包含的字段
 * @param {string} filename - 文件名
 * @param {string} outputDir - 输出目录
 * @returns {string} 生成的文件路径
 */
const exportToCSV = async (data, fields, filename, outputDir = 'exports') => {
  try {
    // 确保输出目录存在
    const dir = path.resolve(process.cwd(), outputDir);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 创建CSV解析器
    const opts = { fields };
    const parser = new Parser(opts);
    const csv = parser.parse(data);
    
    // 生成文件名和路径
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filepath = path.join(dir, `${filename}-${timestamp}.csv`);
    
    // 写入文件
    fs.writeFileSync(filepath, csv);
    logger.info(`CSV导出成功: ${filepath}`);
    
    return filepath;
  } catch (error) {
    logger.error(`导出CSV失败: ${error.message}`);
    throw new Error(`导出CSV失败: ${error.message}`);
  }
};

/**
 * 将数据导出为Excel文件
 * @param {Array} data - 要导出的数据数组
 * @param {Array} columns - 列配置 [{header: '标题', key: '字段名', width: 20}]
 * @param {string} filename - 文件名
 * @param {string} outputDir - 输出目录
 * @returns {string} 生成的文件路径
 */
const exportToExcel = async (data, columns, filename, outputDir = 'exports') => {
  try {
    // 确保输出目录存在
    const dir = path.resolve(process.cwd(), outputDir);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 创建工作簿和工作表
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('数据');
    
    // 设置列
    worksheet.columns = columns;
    
    // 添加行
    worksheet.addRows(data);
    
    // 设置标题行样式
    worksheet.getRow(1).font = { bold: true };
    
    // 生成文件名和路径
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filepath = path.join(dir, `${filename}-${timestamp}.xlsx`);
    
    // 写入文件
    await workbook.xlsx.writeFile(filepath);
    logger.info(`Excel导出成功: ${filepath}`);
    
    return filepath;
  } catch (error) {
    logger.error(`导出Excel失败: ${error.message}`);
    throw new Error(`导出Excel失败: ${error.message}`);
  }
};

module.exports = {
  exportToCSV,
  exportToExcel
}; 