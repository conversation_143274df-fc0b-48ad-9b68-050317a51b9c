import axios from 'axios';
import { ElMessage } from 'element-plus';

// API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// Token导入相关功能
export async function importTokensAsJson(parsedData, localModeOnly = false) {
  // 本地模式直接返回成功，不发送API请求
  if (localModeOnly) {
    return {
      success: true,
      count: parsedData.length,
      message: `成功导入${parsedData.length}个Token（本地模式）`,
      tokens: parsedData // 返回解析后的数据供本地使用
    };
  }

  try {
    const response = await axios.post(`${apiBaseUrl}/tokens/import-json`, {
      tokens: parsedData
    });

    if (response.data.success) {
      return {
        success: true,
        count: response.data.data?.count || parsedData.length,
        message: `成功导入${response.data.data?.count || parsedData.length}个Token`
      };
    }
    return { success: false, message: response.data.message || '导入失败' };
  } catch (error) {
    console.log('JSON方式导入失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

export async function importTokensAsFormData(file, parsedData, localModeOnly = false) {
  // 本地模式直接返回成功，不发送API请求
  if (localModeOnly) {
    return {
      success: true,
      count: Array.isArray(parsedData) ? parsedData.length : 0,
      message: `成功导入Token数据（本地模式）`,
      tokens: Array.isArray(parsedData) ? parsedData : [] // 返回解析后的数据供本地使用
    };
  }

  try {
    const formData = new FormData();
    if (file) {
      formData.append('file', file);
    } else if (typeof parsedData === 'string') {
      // 如果是文本导入，创建文本文件
      const textBlob = new Blob([parsedData], { type: 'text/plain' });
      const textFile = new File([textBlob], 'import.txt', { type: 'text/plain' });
      formData.append('file', textFile);
    }

    formData.append('parsedData', JSON.stringify(parsedData));

    const response = await axios.post(`${apiBaseUrl}/tokens/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.data.success) {
      return {
        success: true,
        count: response.data.data?.count || parsedData.length,
        message: `成功导入${response.data.data?.count || parsedData.length}个Token`
      };
    }
    return { success: false, message: response.data.message || '导入失败' };
  } catch (error) {
    console.error('FormData方式导入失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 获取Token列表
export async function fetchTokenList(params = {}) {
  try {
    const response = await axios.get(`${apiBaseUrl}/tokens`, { params });

    if (response.data.success) {
      return {
        success: true,
        tokens: response.data.data?.tokens || [],
        total: response.data.data?.total || 0
      };
    }
    return { success: false, message: response.data.message || '获取Token列表失败' };
  } catch (error) {
    console.error('获取Token列表失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 重置单个Token
export async function resetToken(uid) {
  try {
    const response = await axios.post(`${apiBaseUrl}/tokens/${uid}/reset`);

    if (response.data.success) {
      return { success: true, message: 'Token重置成功' };
    }
    return { success: false, message: response.data.message || 'Token重置失败' };
  } catch (error) {
    console.error('Token重置失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 批量重置Token
export async function batchResetTokens(tokenIds) {
  try {
    const response = await axios.post(`${apiBaseUrl}/tokens/batch-reset`, {
      token_ids: tokenIds
    });

    if (response.data.success) {
      return {
        success: true,
        count: response.data.data?.count || 0,
        message: `成功重置${response.data.data?.count || 0}个Token`
      };
    }
    return { success: false, message: response.data.message || '重置Token失败' };
  } catch (error) {
    console.error('重置Token失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 查询头像昵称
export async function queryTokenInfo(tokenIds) {
  try {
    const ids = Array.isArray(tokenIds) ? tokenIds.join(',') : tokenIds;
    const response = await axios.get(`${apiBaseUrl}/tokens/query/info?ids=${ids}`);

    if (response.data.success) {
      return {
        success: true,
        tokens: response.data.data?.tokens || [],
        message: '查询成功'
      };
    }
    return { success: false, message: response.data.message || '查询失败' };
  } catch (error) {
    console.error('查询失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 查询头像昵称（优化版，通过后端API）
export async function queryTokenUserInfo(tokenData) {
  try {
    console.log('发送查询用户信息请求到后端，数据:', tokenData);

    // 获取存储的认证令牌
    const token = localStorage.getItem('token');

    // 使用后端API进行查询
    const response = await axios.post(`${apiBaseUrl}/tokens/query/userinfo`, {
      token_data: tokenData
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        results: response.data.data?.results || [],
        stats: response.data.data?.stats || {},
        message: '查询用户信息成功'
      };
    }
    return { success: false, message: response.data.message || '查询用户信息失败' };
  } catch (error) {
    console.error('查询用户信息失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 通过后端API查询在线状态（优化版）
export async function queryOnlineStatusOptimized(tokenData) {
  try {
    console.log('发送查询在线状态请求到后端，数据:', tokenData);

    // 获取存储的认证令牌
    const token = localStorage.getItem('token');

    // 使用后端API进行查询
    const response = await axios.post(`${apiBaseUrl}/tokens/query/online-optimized`, {
      token_data: tokenData
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        results: response.data.data?.results || [],
        message: '查询在线状态成功'
      };
    }
    return { success: false, message: response.data.message || '查询在线状态失败' };
  } catch (error) {
    console.error('查询在线状态失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 查询订单信息
export async function queryTokenOrderInfo(tokenData) {
  try {
    console.log('发送查询订单请求到后端，数据:', tokenData);

    // 获取存储的认证令牌
    const token = localStorage.getItem('token');

    // 使用后端API进行查询
    const response = await axios.post(`${apiBaseUrl}/tokens/query/order`, {
      token_data: tokenData
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        results: response.data.data?.results || [],
        stats: response.data.data?.stats || {},
        message: '查询订单信息成功'
      };
    }
    return { success: false, message: response.data.message || '查询订单信息失败' };
  } catch (error) {
    console.error('查询订单信息失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

// 创建备份
export async function createBackup(tokenIds, name, description) {
  try {
    // 记录调试信息
    console.log('创建备份API请求URL:', `${apiBaseUrl}/backups`);
    console.log('发送的Token IDs:', tokenIds);
    console.log('备份名称:', name);
    console.log('备份描述:', description);

    const response = await axios.post(`${apiBaseUrl}/backups`, {
      name: name || `手动备份 ${new Date().toLocaleString()}`,
      description: description || '手动创建的备份',
      backup_type: 'manual',
      token_ids: tokenIds,
      type: 'manual'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        backupId: response.data.data?.id || '',
        message: response.data.message || '备份创建成功'
      };
    }
    return { success: false, message: response.data.message || '创建备份失败' };
  } catch (error) {
    console.error('创建备份失败:', error);
    let errorMsg = '创建备份失败';

    if (error.response) {
      console.log('API响应状态:', error.response.status);
      console.log('API响应数据:', error.response.data);

      if (error.response.data?.message) {
        errorMsg = error.response.data.message;
      }
    }

    return {
      success: false,
      error,
      message: errorMsg
    };
  }
}

// 通过UID创建备份（专为Token重置页面设计）
export async function createBackupByUid(tokenUids, name, description) {
  try {
    // 记录调试信息
    console.log('创建备份API请求URL:', `${apiBaseUrl}/tokens/create-backup-by-uid`);
    console.log('发送的Token UIDs:', tokenUids);
    console.log('备份名称:', name);
    console.log('备份描述:', description);

    const response = await axios.post(`${apiBaseUrl}/tokens/create-backup-by-uid`, {
      name: name || `手动备份 ${new Date().toLocaleString()}`,
      description: description || '从Token重置页面创建的手动备份',
      backup_type: 'manual',
      token_uids: tokenUids // 使用token_uids代替token_ids
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        backupId: response.data.data?.backup?.id || '',
        message: `成功创建备份: ${response.data.data?.backup?.name || ''}`
      };
    }
    return { success: false, message: response.data.message || '创建备份失败' };
  } catch (error) {
    console.error('创建备份失败:', error);
    let errorMsg = '创建备份失败';

    if (error.response) {
      console.log('API响应状态:', error.response.status);
      console.log('API响应数据:', error.response.data);

      if (error.response.status === 404) {
        errorMsg = 'API路径不存在，请检查后端是否正确配置';
      } else if (error.response.data?.message) {
        errorMsg = error.response.data.message;
      }
    }

    return {
      success: false,
      error,
      message: errorMsg
    };
  }
}

// 通过UID创建备份的替代方案 - 使用现有API
export async function createBackupWithUids(tokenUids, name, description) {
  try {
    // 记录调试信息
    console.log('创建备份API请求URL:', `${apiBaseUrl}/backups`);
    console.log('发送的Token UIDs:', tokenUids);
    console.log('备份名称:', name);
    console.log('备份描述:', description);

    // 使用标准备份API，但传递UID作为token_ids，同时指定跳过验证
    const response = await axios.post(`${apiBaseUrl}/backups`, {
      name: name || `手动备份 ${new Date().toLocaleString()}`,
      description: description || '从Token重置页面创建的手动备份',
      backup_type: 'manual',
      token_ids: tokenUids, // 直接传递UID而不是ID
      type: 'manual',
      skip_validation: true // 添加跳过验证参数
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
      }
    });

    if (response.data.success) {
      return {
        success: true,
        backupId: response.data.data?.id || '',
        message: `成功创建备份: ${response.data.message || ''}`
      };
    }
    return { success: false, message: response.data.message || '创建备份失败' };
  } catch (error) {
    console.error('创建备份失败:', error);
    let errorMsg = '创建备份失败';

    if (error.response) {
      console.log('API响应状态:', error.response.status);
      console.log('API响应数据:', error.response.data);

      if (error.response.data?.message) {
        errorMsg = error.response.data.message;
      }
    }

    return {
      success: false,
      error,
      message: errorMsg
    };
  }
}

// 删除Token
export async function deleteToken(uid) {
  try {
    const response = await axios.delete(`${apiBaseUrl}/tokens/${uid}`);

    if (response.data.success) {
      return { success: true, message: 'Token删除成功' };
    }
    return { success: false, message: response.data.message || 'Token删除失败' };
  } catch (error) {
    console.error('Token删除失败:', error);
    return {
      success: false,
      error,
      message: getErrorMessage(error)
    };
  }
}

/**
 * 删除指定订单
 * @param {string} uid Token的UID
 * @param {string} tokenValue Token值
 * @param {string} orderSn 订单编号
 * @returns {Promise<Object>} 删除结果
 */
export const deleteOrder = async (uid, tokenValue, orderSn) => {
  try {
    const response = await axios.post(`${apiBaseUrl}/tokens/delete-order`, {
      uid,
      token: tokenValue,
      orderSn
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
      }
    });

    return response.data;
  } catch (error) {
    console.error('删除订单出错:', error);
    return { success: false, message: getErrorMessage(error) };
  }
};

/**
 * 批量删除订单
 * @param {Array} orderItems 订单信息数组，每项包含uid, token和orderSn
 * @returns {Promise<Object>} 批量删除结果
 */
export const batchDeleteOrders = async (orderItems) => {
  try {
    // 添加详细调试日志，检查传入的orderItems结构
    console.log('批量删除订单 - 数据准备:', {
      itemsCount: orderItems?.length || 0,
      firstItem: orderItems?.length > 0 ? orderItems[0] : null
    });

    // 检查orderItems中是否包含必要的参数
    if (orderItems?.length > 0) {
      const missingParams = orderItems.filter(item => !item.uid || !item.token || !item.orderSn);
      if (missingParams.length > 0) {
        console.error(`发现${missingParams.length}个缺少参数的订单项`);

        // 记录详细的错误信息
        missingParams.forEach((item, index) => {
          console.error(`缺少参数的订单项 #${index + 1}:`, {
            uid: item.uid || '缺失',
            token: item.token ? '存在' : '缺失',
            orderSn: item.orderSn || '缺失',
            allFields: Object.keys(item)
          });
        });

        // 记录有效订单项的示例，用于比较
        const validItems = orderItems.filter(item => item.uid && item.token && item.orderSn);
        if (validItems.length > 0) {
          console.log('有效订单项示例:', validItems[0]);
        }
      }

      // 记录所有订单项的字段名分布情况
      const fieldStats = {
        totalItems: orderItems.length,
        withUid: orderItems.filter(item => item.uid).length,
        withToken: orderItems.filter(item => item.token).length,
        withOrderSn: orderItems.filter(item => item.orderSn).length,
        withOrderId: orderItems.filter(item => item.orderId).length,
        withId: orderItems.filter(item => item.id).length
      };
      console.log('订单项字段统计:', fieldStats);
    }

    // 再次检查orderItems中是否有无效项
    const finalCheck = orderItems.filter(item => !item.uid || !item.token || !item.orderSn);
    if (finalCheck.length > 0) {
      console.error(`最终检查：仍有${finalCheck.length}个无效订单项将被发送到API`);
      console.error('无效项示例:', finalCheck[0]);
    }

    // 记录请求详情
    console.log(`发送批量删除请求到 ${apiBaseUrl}/tokens/batch-delete-orders`);
    console.log(`请求包含 ${orderItems.length} 个订单项`);

    try {
      const response = await axios.post(`${apiBaseUrl}/tokens/batch-delete-orders`, {
        orderItems  // 确保传递的是整个orderItems数组
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
        }
      });

      // 添加API响应调试信息
      console.log('批量删除订单API响应:', {
        success: response.data?.success,
        resultsCount: response.data?.results?.length || 0,
        firstResult: response.data?.results?.length > 0 ? response.data.results[0] : null
      });

      return response.data;
    } catch (error) {
      // 增强错误处理
      console.error('批量删除订单API调用失败:', error.message);

      if (error.response) {
        console.error('API错误状态码:', error.response.status);
        console.error('API错误响应数据:', error.response.data);

        // 检查是否是系统繁忙错误（错误码46047）
        if (error.response.data && error.response.data.error_code === 46047) {
          console.warn('检测到系统繁忙错误，将跳过当前token');

          // 返回特定的错误信息，而不是抛出错误
          return {
            success: false,
            message: '系统繁忙，请稍后重试！',
            error_code: 46047,
            systemBusy: true, // 添加标志，表示这是系统繁忙错误
            results: []
          };
        }
      }

      // 如果是orderSn相关的错误，记录更详细的信息
      if (error.message && error.message.includes('orderSn')) {
        console.error('orderSn相关错误，请求数据:', {
          itemsCount: orderItems.length,
          sampleItems: orderItems.slice(0, 3)
        });
      }

      throw error; // 重新抛出错误，让外层catch捕获
    }

    // 这部分代码已经移到try块内部
  } catch (error) {
    console.error('批量删除订单出错:', error);
    return {
      success: false,
      message: getErrorMessage(error),
      results: []
    };
  }
};

// 错误信息处理函数
function getErrorMessage(error) {
  if (error.isAxiosError) {
    console.log('详细API错误:', error.response?.data);

    if (error.response?.data?.message) {
      return `操作失败: ${error.response.data.message}`;
    } else if (error.response?.status === 500) {
      return '服务器内部错误，请检查数据格式是否正确';
    } else if (error.response?.status === 404) {
      return 'API不存在，请联系管理员配置正确的API';
    } else if (error.response?.status === 401) {
      return '未授权访问，请先登录系统';
    }
  }

  return error.message || '操作失败，请稍后再试';
}