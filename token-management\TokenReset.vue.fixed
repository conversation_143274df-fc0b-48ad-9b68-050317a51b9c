<!-- 
This is a fixed version of TokenReset.vue, with the following changes:
1. Remove CSS code from script section (after shouldResetPage = ref(true))
2. Remove the duplicate .pagination-container at the end of the file
-->

<template>
  <!-- Your template section here, no changes needed -->
</template>

<script>
// Your script code here, no changes needed

// 添加一个标志来控制是否需要重置页码
const shouldResetPage = ref(true);
</script>

<style scoped>
// Your style section here, with only one .pagination-container definition
</style> 