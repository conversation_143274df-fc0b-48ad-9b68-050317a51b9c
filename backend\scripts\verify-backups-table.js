/**
 * 验证备份表结构脚本
 * 确保所有必要的列都存在
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function verifyBackupsTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'token_management'
    });
    
    console.log('数据库连接成功');
    
    // 获取表结构
    const [columns] = await connection.query('SHOW COLUMNS FROM backups');
    const columnNames = columns.map(col => col.Field);
    
    console.log('当前备份表列:', columnNames.join(', '));
    
    // 定义应该存在的列
    const requiredColumns = [
      'id', 'backup_id', 'name', 'backup_type', 'task_id', 
      'token_count', 'size', 'type', 'description', 'remark', 
      'status', 'delete_status', 'scheduled_delete_time', 'backup_time', 
      'created_at', 'updated_at', 'created_by'
    ];
    
    // 检查缺失的列
    const missingColumns = requiredColumns.filter(col => !columnNames.includes(col));
    
    if (missingColumns.length === 0) {
      console.log('验证成功: 所有必要的列都存在');
    } else {
      console.log('发现缺失的列:', missingColumns.join(', '));
      
      // 添加缺失的列
      for (const column of missingColumns) {
        try {
          let dataType = '';
          let defaultValue = '';
          let afterColumn = '';
          
          // 根据列名设置数据类型和默认值
          switch (column) {
            case 'token_count':
              dataType = 'INT';
              defaultValue = 'DEFAULT 0';
              afterColumn = 'AFTER task_id';
              break;
            case 'size':
              dataType = 'VARCHAR(20)';
              afterColumn = 'AFTER token_count';
              break;
            case 'type':
              dataType = 'VARCHAR(20)';
              afterColumn = 'AFTER size';
              break;
            case 'description':
              dataType = 'TEXT';
              afterColumn = 'AFTER type';
              break;
            case 'remark':
              dataType = 'TEXT';
              afterColumn = 'AFTER description';
              break;
            case 'status':
              dataType = 'VARCHAR(20)';
              defaultValue = "DEFAULT '备份中'";
              afterColumn = 'AFTER remark';
              break;
            case 'delete_status':
              dataType = 'BOOLEAN';
              defaultValue = 'DEFAULT FALSE';
              afterColumn = 'AFTER status';
              break;
            case 'scheduled_delete_time':
              dataType = 'DATETIME';
              afterColumn = 'AFTER delete_status';
              break;
            case 'backup_time':
              dataType = 'DATETIME';
              afterColumn = 'AFTER scheduled_delete_time';
              break;
            default:
              console.log(`跳过未知列: ${column}`);
              continue;
          }
          
          const query = `ALTER TABLE backups ADD COLUMN ${column} ${dataType} ${defaultValue} ${afterColumn}`;
          console.log(`执行SQL: ${query}`);
          await connection.query(query);
          console.log(`成功添加列: ${column}`);
        } catch (error) {
          console.error(`添加列 ${column} 失败:`, error.message);
        }
      }
    }
    
    // 再次验证
    const [updatedColumns] = await connection.query('SHOW COLUMNS FROM backups');
    const updatedColumnNames = updatedColumns.map(col => col.Field);
    const stillMissingColumns = requiredColumns.filter(col => !updatedColumnNames.includes(col));
    
    if (stillMissingColumns.length === 0) {
      console.log('最终验证成功: 所有必要的列都已存在');
    } else {
      console.error('最终验证失败: 仍有缺失的列:', stillMissingColumns.join(', '));
    }
    
  } catch (error) {
    console.error('验证备份表失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行验证
verifyBackupsTable();
