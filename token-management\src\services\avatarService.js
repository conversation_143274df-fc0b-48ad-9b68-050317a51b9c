import axios from 'axios'
import { ElMessage } from 'element-plus'

/**
 * 头像服务 - 处理头像相关的功能
 */

// 获取随机头像
export const getRandomAvatar = async () => {
  try {
    // 调用后端API获取随机头像
    // 注意：这里假设有一个后端API可以提供随机头像
    // 在实际环境中，需要实现这个API
    const response = await axios.get('/api/random-avatar')
    
    if (response.data && response.data.imageBase64) {
      return {
        success: true,
        imageBase64: response.data.imageBase64,
        message: '获取随机头像成功'
      }
    } else {
      throw new Error('获取随机头像失败')
    }
  } catch (error) {
    console.error('获取随机头像失败:', error)
    
    // 模拟随机头像（仅用于演示）
    // 在实际环境中，应该从服务器获取随机头像
    return simulateRandomAvatar()
  }
}

// 模拟随机头像（仅用于演示）
const simulateRandomAvatar = () => {
  // 这里应该是从服务器获取随机头像
  // 由于无法直接访问本地文件系统，我们使用一个模拟的随机头像
  
  // 模拟一个base64编码的图片
  // 在实际环境中，这应该是从服务器获取的真实图片
  const dummyBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAAnElEQVR42u3RAQ0AAAgDIN8/9K3hHFQg03Y4IYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhLwbOyYBBUSk/0AAAAASUVORK5CYII='
  
  return {
    success: true,
    imageBase64: dummyBase64,
    message: '获取随机头像成功（模拟）'
  }
}

// 修改头像
export const changeAvatar = async (token, imageBase64) => {
  try {
    // 1. 获取签名
    const signResponse = await axios.post('https://apiv3.yangkeduo.com/image/signature',
      {
        "bucket_tag": "pddavatar"
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://apiv3.yangkeduo.com/image/signature?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 3000
      }
    );

    // 2. 上传头像
    const uploadResponse = await axios.post('https://apiv3.yangkeduo.com/store_avatar_image',
      {
        "upload_sign": signResponse.data.signature,
        "image": `data:image/jpeg;base64,${imageBase64}`
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://file.yangkeduo.com/store_avatar_image?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 3000
      }
    );

    // 3. 更新头像
    await axios.post('https://apiv3.yangkeduo.com/user/profile/update/avatar',
      {
        "avatar": uploadResponse.data.url
      },
      {
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-cn',
          'AccessToken': token,
          'Connection': 'Keep-Alive',
          'Content-Type': 'application/json; charset=utf-8',
          'Cookie': 'api_uid=rBUp72AvOnVPSWC963UbAg==',
          'Host': 'apiv3.yangkeduo.com',
          'Referer': 'https://api.pinduoduo.com/user/profile/update/avatar?pdduid=',
          'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-HK) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5  phh_android_version/3.32.0 phh_android_build/228842 phh_android_channel/pp'
        },
        timeout: 3000
      }
    );

    return {
      success: true,
      message: '头像修改成功',
      avatarUrl: uploadResponse.data.url
    }
  } catch (error) {
    console.error('修改头像失败:', error)
    return {
      success: false,
      message: `修改头像失败: ${error.message}`,
      error
    }
  }
}

// 批量修改头像
export const batchChangeAvatar = async (tokens, imageBase64) => {
  if (!tokens || tokens.length === 0) {
    return {
      success: false,
      message: '未提供Token',
      results: {
        success: 0,
        failed: 0,
        tokens: []
      }
    }
  }

  const results = {
    success: 0,
    failed: 0,
    tokens: []
  }

  for (const token of tokens) {
    try {
      // 检查Token是否有效
      if (!token.token) {
        throw new Error('Token无效')
      }

      // 修改头像
      const result = await changeAvatar(token.token, imageBase64)

      if (result.success) {
        // 更新Token信息
        const updatedToken = { 
          ...token, 
          avatar: result.avatarUrl || `data:image/jpeg;base64,${imageBase64}`
        }
        results.tokens.push(updatedToken)
        results.success++
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error(`修改Token ${token.uid} 的头像失败:`, error)
      results.failed++
    }
  }

  return {
    success: results.success > 0,
    message: `成功修改 ${results.success} 个Token的头像，失败 ${results.failed} 个`,
    results
  }
}

// 从本地头像库读取随机头像
export const getRandomAvatarFromLocalLibrary = async () => {
  try {
    // 注意：这个函数需要后端支持
    // 在前端JavaScript中，无法直接访问本地文件系统
    // 这里我们假设有一个后端API可以提供这个功能
    
    const response = await axios.get('/api/random-avatar-from-library')
    
    if (response.data && response.data.imageBase64) {
      return {
        success: true,
        imageBase64: response.data.imageBase64,
        message: '从本地头像库获取随机头像成功'
      }
    } else {
      throw new Error('从本地头像库获取随机头像失败')
    }
  } catch (error) {
    console.error('从本地头像库获取随机头像失败:', error)
    ElMessage.error(`从本地头像库获取随机头像失败: ${error.message}`)
    
    // 返回模拟的随机头像
    return simulateRandomAvatar()
  }
}
