import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

/**
 * Token工具栏相关功能的组合式函数
 * @param {Object} options - 配置选项
 * @returns {Object} 工具栏相关方法和状态
 */
export default function useTokenToolbar(options = {}) {
  const router = useRouter()
  
  // 从选项中获取需要的数据
  const { 
    selectedTokens = ref([]), 
    searchForm = ref({}),
    loading = ref(false),
    fetchTokenList = () => {}
  } = options
  
  // 工具栏相关状态
  const toolbarCollapsed = ref(false)
  const showAdvancedSearch = ref(false)
  const showStatistics = ref(false)
  
  // 高级搜索表单
  const advancedSearchForm = ref({
    tokenType: '',
    status: '',
    createTimeRange: [],
    expireTimeRange: [],
    usageMin: null,
    usageMax: null,
    tags: [],
    projectId: '',
    createdBy: ''
  })
  
  // 搜索表单原始备份，用于重置
  const originalSearchForm = { ...searchForm.value }
  const originalAdvancedSearchForm = { ...advancedSearchForm.value }
  
  /**
   * 切换工具栏折叠状态
   */
  const toggleToolbar = () => {
    toolbarCollapsed.value = !toolbarCollapsed.value
  }
  
  /**
   * 切换高级搜索面板
   */
  const toggleAdvancedSearch = () => {
    showAdvancedSearch.value = !showAdvancedSearch.value
    
    // 如果关闭高级搜索，重置高级搜索表单
    if (!showAdvancedSearch.value) {
      resetAdvancedSearchForm()
    }
  }
  
  /**
   * 切换统计视图
   */
  const toggleStatistics = () => {
    showStatistics.value = !showStatistics.value
  }
  
  /**
   * 处理新建Token
   */
  const handleCreateToken = () => {
    router.push('/token/create')
  }
  
  /**
   * 重置基本搜索表单
   */
  const resetSearchForm = () => {
    // 重置为原始值
    Object.keys(searchForm.value).forEach(key => {
      searchForm.value[key] = originalSearchForm[key] || ''
    })
    
    // 立即执行搜索
    fetchTokenList()
  }
  
  /**
   * 重置高级搜索表单
   */
  const resetAdvancedSearchForm = () => {
    // 重置为原始值
    Object.keys(advancedSearchForm.value).forEach(key => {
      // 如果是数组，重置为空数组
      if (Array.isArray(advancedSearchForm.value[key])) {
        advancedSearchForm.value[key] = []
      } else {
        advancedSearchForm.value[key] = originalAdvancedSearchForm[key] || ''
      }
    })
  }
  
  /**
   * 提交高级搜索
   */
  const submitAdvancedSearch = () => {
    // 合并基本搜索和高级搜索条件
    const params = {
      ...searchForm.value,
      ...advancedSearchForm.value
    }
    
    // 处理时间范围
    if (params.createTimeRange && params.createTimeRange.length === 2) {
      params.createTimeStart = params.createTimeRange[0]
      params.createTimeEnd = params.createTimeRange[1]
    }
    
    if (params.expireTimeRange && params.expireTimeRange.length === 2) {
      params.expireTimeStart = params.expireTimeRange[0]
      params.expireTimeEnd = params.expireTimeRange[1]
    }
    
    // 删除不需要的字段
    delete params.createTimeRange
    delete params.expireTimeRange
    
    // 执行搜索
    fetchTokenList(params)
    
    // 关闭高级搜索面板
    showAdvancedSearch.value = false
  }
  
  /**
   * 刷新Token列表
   */
  const refreshTokenList = () => {
    // 保存当前的搜索条件
    const currentParams = {
      ...searchForm.value,
      ...advancedSearchForm.value
    }
    
    // 处理时间范围
    if (currentParams.createTimeRange && currentParams.createTimeRange.length === 2) {
      currentParams.createTimeStart = currentParams.createTimeRange[0]
      currentParams.createTimeEnd = currentParams.createTimeRange[1]
      delete currentParams.createTimeRange
    }
    
    if (currentParams.expireTimeRange && currentParams.expireTimeRange.length === 2) {
      currentParams.expireTimeStart = currentParams.expireTimeRange[0]
      currentParams.expireTimeEnd = currentParams.expireTimeRange[1]
      delete currentParams.expireTimeRange
    }
    
    // 刷新列表
    fetchTokenList(currentParams)
  }
  
  /**
   * 处理快速筛选
   * @param {string} filterType - 筛选类型
   * @param {string|number} value - 筛选值
   */
  const handleQuickFilter = (filterType, value) => {
    // 重置搜索表单
    resetSearchForm()
    resetAdvancedSearchForm()
    
    // 根据筛选类型设置相应的筛选条件
    switch (filterType) {
      case 'status':
        advancedSearchForm.value.status = value
        break
      case 'tokenType':
        advancedSearchForm.value.tokenType = value
        break
      case 'project':
        advancedSearchForm.value.projectId = value
        break
      case 'tag':
        advancedSearchForm.value.tags = [value]
        break
      case 'recent':
        // 最近7天创建的Token
        const now = new Date()
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(now.getDate() - 7)
        advancedSearchForm.value.createTimeRange = [sevenDaysAgo, now]
        break
      case 'expiringSoon':
        // 即将过期的Token（7天内）
        const today = new Date()
        const nextWeek = new Date()
        nextWeek.setDate(today.getDate() + 7)
        advancedSearchForm.value.expireTimeRange = [today, nextWeek]
        break
      default:
        break
    }
    
    // 提交高级搜索
    submitAdvancedSearch()
  }
  
  // 计算属性：是否有选中的Token
  const hasSelectedTokens = computed(() => {
    return selectedTokens.value && selectedTokens.value.length > 0
  })
  
  return {
    // 状态
    toolbarCollapsed,
    showAdvancedSearch,
    showStatistics,
    advancedSearchForm,
    
    // 计算属性
    hasSelectedTokens,
    
    // 方法
    toggleToolbar,
    toggleAdvancedSearch,
    toggleStatistics,
    handleCreateToken,
    resetSearchForm,
    resetAdvancedSearchForm,
    submitAdvancedSearch,
    refreshTokenList,
    handleQuickFilter
  }
} 