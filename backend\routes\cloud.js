const express = require('express');
const axios = require('axios');
const router = express.Router();
const { logger } = require('../utils/logger');
const apiConfig = require('../config/externalApiConfig');

// 使用集中配置替代硬编码的URL和凭证
const EXTERNAL_API_URL = apiConfig.endpoints.TOKEN.TRUNCATE_UNSOLD;
const EXTERNAL_USERNAME = apiConfig.credentials.USERNAME;
const EXTERNAL_PASSWORD = apiConfig.credentials.PASSWORD;
const API_TIMEOUT = apiConfig.timeout;

// POST /api/cloud/truncate-unsold
router.post('/truncate-unsold', async (req, res, next) => {
  logger.info('Received request to truncate cloud unsold tokens');

  const requestData = new URLSearchParams();
  requestData.append('username', EXTERNAL_USERNAME);
  requestData.append('password', EXTERNAL_PASSWORD);

  try {
    logger.info(`Sending POST request to external API: ${EXTERNAL_API_URL}`);
    const response = await axios.post(EXTERNAL_API_URL, requestData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: API_TIMEOUT // 使用配置的超时时间
    });

    logger.info(`External API Response Status: ${response.status}`);
    logger.debug('External API Response Data:', response.data);

    // Check if the request was successful and the response body matches expectation
    if (response.status === 200 && response.data && typeof response.data === 'string' && response.data.includes('Token table truncated successfully.')) {
      logger.info('External API call successful.');
      res.json({ success: true, message: '云端未卖Token已成功清空！' });
    } else {
      const errorMessage = `外部API调用成功但响应内容无效或非预期: ${JSON.stringify(response.data)}`;
      logger.warn(errorMessage);
      // Send a success=false but maybe with a 200 status if the call itself didn't fail
      res.status(200).json({ success: false, message: `操作可能未完全成功: ${errorMessage}` });
    }

  } catch (error) {
    logger.error('Error calling external API to truncate cloud unsold tokens:', error);
    let message = '请求外部云端接口失败。';
    let statusCode = 500;

    if (error.response) {
      message += ` 外部服务器错误: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
      statusCode = error.response.status >= 500 ? 502 : 500; // Bad Gateway or Internal Server Error
    } else if (error.request) {
      message += ' 未收到外部服务器响应，请检查网络或API地址。';
      statusCode = 504; // Gateway Timeout
    } else {
      message += ` 请求设置错误: ${error.message}`;
    }
    // Pass error to the global error handler or send response directly
    // Sending directly for now
    res.status(statusCode).json({ success: false, message: message });
    // Alternatively: next(error); // If you have a sophisticated error handler
  }
});

module.exports = router; 