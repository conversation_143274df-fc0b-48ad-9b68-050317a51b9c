-- 创建Token管理系统数据库
CREATE DATABASE IF NOT EXISTS token_management;

USE token_management;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    avatar_url VARCHAR(255),
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQ<PERSON> KEY (username),
    UNIQUE KEY (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Token表
CREATE TABLE IF NOT EXISTS tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(50) NOT NULL,
    token_value TEXT NOT NULL,
    username VA<PERSON>HA<PERSON>(100),
    nickname <PERSON><PERSON><PERSON><PERSON>(100),
    avatar_url TEXT,
    avatar_query_id VARCHAR(100),
    platform VARCHAR(50),
    purchase_time DATETIME,
    expiry_date DATETIME,
    status VARCHAR(20) DEFAULT '正常',
    account_id VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_used_time DATETIME,
    deleted_at DATETIME,
    created_by INT,
    updated_by INT,
    remarks TEXT,
    INDEX (uid),
    INDEX (status),
    INDEX (purchase_time),
    INDEX (expiry_date),
    INDEX (platform, status),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 备份表
CREATE TABLE IF NOT EXISTS backups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    backup_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    backup_type VARCHAR(20), -- 重置前，重置后
    task_id VARCHAR(50),     -- 关联ID
    token_count INT DEFAULT 0,
    size VARCHAR(20),
    type VARCHAR(20),        -- 自动，手动
    description TEXT,
    remark TEXT,             -- 备注
    status VARCHAR(20) DEFAULT '备份中',
    delete_status BOOLEAN DEFAULT FALSE,
    scheduled_delete_time DATETIME,
    backup_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    INDEX (backup_id),
    INDEX (backup_type),
    INDEX (task_id),
    INDEX (status),
    INDEX (delete_status),
    INDEX (scheduled_delete_time),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 备份-Token关联表
CREATE TABLE IF NOT EXISTS backup_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    backup_id VARCHAR(50) NOT NULL,
    token_id BIGINT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX (backup_id),
    INDEX (token_id),
    FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 活动日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action_type VARCHAR(50) NOT NULL, -- Token重置, Token备份, Token导入, Token删除
    content TEXT,
    status VARCHAR(20) DEFAULT '成功',
    ip_address VARCHAR(45),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (action_type),
    INDEX (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 通知设置表
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_enabled BOOLEAN DEFAULT TRUE,
    expiry_notification JSON, -- 存储过期提醒天数，例如: ["7", "1"]
    backup_notification BOOLEAN DEFAULT TRUE,
    system_events JSON, -- 存储系统事件类型，例如: ["token_reset", "backup_created"]
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 自动重置任务表
CREATE TABLE IF NOT EXISTS auto_reset_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    name VARCHAR(100),
    status VARCHAR(20) DEFAULT '待执行',
    schedule_type VARCHAR(20), -- 单次, 每日, 每周, 每月
    scheduled_time DATETIME,
    load_days INT, -- 加载天数设置
    platform VARCHAR(50),
    token_count INT DEFAULT 0,
    before_backup_id VARCHAR(50),
    after_backup_id VARCHAR(50),
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at DATETIME,
    remarks TEXT,
    INDEX (task_id),
    INDEX (status),
    INDEX (scheduled_time),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 初始化管理员用户
INSERT INTO users (username, password, email, role) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin');

-- 初始化系统设置
INSERT INTO settings (setting_key, setting_value, description) VALUES
('system_name', 'Token 管理系统', '系统名称'),
('theme', 'default', '系统主题'),
('language', 'zh-CN', '系统语言'),
('page_size', '20', '默认分页大小'),
('backup_retention_days', '30', '备份保留天数'),
('token_cleanup_days', '60', '过期Token清理天数'); 