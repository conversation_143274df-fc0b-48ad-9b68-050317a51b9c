/**
 * 外部API配置文件
 * 集中管理所有需要调用的外部API URL和凭证
 */

// 从环境变量获取API基础URL，如果不存在则使用默认值
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://110.42.32.166:3080';

// WordPress REST API前缀
const WP_API_PREFIX = '/wp-json';

// 完整的WordPress API基础URL
const WP_API_BASE_URL = `${EXTERNAL_API_BASE_URL}${WP_API_PREFIX}`;

// 外部API凭证
const API_CREDENTIALS = {
  USERNAME: process.env.EXTERNAL_API_USERNAME || 'a15604402',
  PASSWORD: process.env.EXTERNAL_API_PASSWORD || 'lijinrong11',
};

// API超时设置（毫秒）
const API_TIMEOUT = 60000; // 60秒

// API重试次数
const API_RETRY_COUNT = 3;

// 外部API端点
const API_ENDPOINTS = {
  // Token相关
  TOKEN: {
    // 批量更新数据API（Token置入账号内订单）
    BULK_UPDATE: `${WP_API_BASE_URL}/custom-jwt-plugin/v1/bulk-update-data`,
    
    // 加载未卖Token
    GET_UNSOLD: `${WP_API_BASE_URL}/myplugin/v1/get-delete-tokens`,
    
    // 加载已卖Token
    GET_SOLD: `${WP_API_BASE_URL}/myplugin/v1/get-time-tokens/`,
    
    // 导入Token
    IMPORT: `${WP_API_BASE_URL}/custom-token-plugin/v1/import`,
    
    // 清空未卖Token
    TRUNCATE_UNSOLD: `${WP_API_BASE_URL}/custom/v1/truncate-token`,
  },
};

module.exports = {
  baseUrl: EXTERNAL_API_BASE_URL,
  wpApiBaseUrl: WP_API_BASE_URL,
  endpoints: API_ENDPOINTS,
  credentials: API_CREDENTIALS,
  timeout: API_TIMEOUT,
  retryCount: API_RETRY_COUNT
}; 