import api from './api';

/**
 * 用户服务
 */
export default {
  /**
   * 测试API连接
   * @returns {Promise} API状态
   */
  testApiConnection() {
    return api.get('/health');
  },

  /**
   * 登录
   * @param {Object} credentials - 登录凭证
   * @returns {Promise} 登录结果
   */
  login(credentials) {
    return api.post('/auth/login', credentials);
  },

  /**
   * 注册
   * @param {Object} userData - 用户数据
   * @returns {Promise} 注册结果
   */
  register(userData) {
    return api.post('/auth/register', userData);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息
   */
  getCurrentUser() {
    return api.get('/auth/me');
  },

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @returns {Promise} 操作结果
   */
  changePassword(passwordData) {
    return api.post('/auth/change-password', passwordData);
  },

  /**
   * 更新用户资料
   * @param {Object} profileData - 用户资料数据
   * @returns {Promise} 操作结果
   */
  updateProfile(profileData) {
    return api.put('/auth/profile', profileData);
  },

  /**
   * 获取用户设置
   * @returns {Promise} 用户设置
   */
  getUserPreferences() {
    return api.get('/settings/user/preferences');
  },

  /**
   * 更新用户设置
   * @param {Object} preferences - 用户设置
   * @returns {Promise} 操作结果
   */
  updateUserPreferences(preferences) {
    // 确保我们发送的是纯净的JSON对象，没有循环引用或特殊对象
    try {
      // 先序列化再解析，确保数据是纯JSON
      const cleanPreferences = JSON.parse(JSON.stringify(preferences));
      return api.put('/settings/user/preferences', { preferences: cleanPreferences });
    } catch (error) {
      console.error('序列化偏好设置失败:', error);
      // 如果JSON处理失败，尝试发送原始对象
      return api.put('/settings/user/preferences', { preferences });
    }
  }
}; 