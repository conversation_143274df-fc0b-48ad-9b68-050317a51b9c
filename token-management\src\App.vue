<template>
  <div class="app-container">
    <!-- 登录页面使用不同的布局 -->
    <template v-if="route.path === '/login'">
      <router-view />
    </template>
    
    <!-- 主应用布局 -->
    <el-container v-else class="layout-container">
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo" :class="{ 'logo-collapsed': isCollapse }">
          <h2 v-if="!isCollapse">Token 管理系统</h2>
          <h2 v-else>TMS</h2>
        </div>
        <el-menu
          :router="true"
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#001529"
          text-color="#a6adb8"
          active-text-color="#409EFF"
          :collapse="isCollapse"
        >
          <el-menu-item index="/dashboard">
            <el-icon><el-icon-monitor /></el-icon>
            <template #title>首页</template>
          </el-menu-item>
          <el-menu-item index="/token-reset">
            <el-icon><el-icon-refresh /></el-icon>
            <template #title>Token重置</template>
          </el-menu-item>
          <el-menu-item index="/token-auto-reset">
            <el-icon><el-icon-timer /></el-icon>
            <template #title>Token自动重置</template>
          </el-menu-item>
          <el-menu-item index="/token-backup">
            <el-icon><el-icon-document-copy /></el-icon>
            <template #title>Token备份</template>
          </el-menu-item>
          <el-menu-item index="/settings">
            <el-icon><el-icon-setting /></el-icon>
            <template #title>设置</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-container class="main-container">
        <el-header class="header">
          <div class="header-left">
            <el-icon class="toggle-sidebar" @click="toggleSidebar">
              <el-icon-menu />
            </el-icon>
            <div class="breadcrumb">
              <span>首页</span>
              <template v-if="currentRoute !== '首页'">
                <span class="breadcrumb-separator">/</span>
                <span>{{ currentRoute }}</span>
              </template>
            </div>
          </div>
          <div class="header-right">
            <div class="header-actions">
              <el-tooltip content="全屏" placement="bottom">
                <el-icon class="header-icon" @click="toggleFullScreen">
                  <el-icon-full-screen />
                </el-icon>
              </el-tooltip>
              <el-tooltip content="消息" placement="bottom">
                <el-badge :value="3" class="badge-item">
                  <el-icon class="header-icon">
                    <el-icon-bell />
                  </el-icon>
                </el-badge>
              </el-tooltip>
            </div>
            <el-dropdown trigger="click" @command="handleCommand">
              <div class="user-dropdown">
                <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                <span class="username">{{ userStore.username }}</span>
                <el-icon><el-icon-arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><el-icon-user /></el-icon>个人信息
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><el-icon-setting /></el-icon>账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><el-icon-switch-button /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        <el-main class="main">
          <transition name="fade-transform" mode="out-in">
            <router-view />
          </transition>
        </el-main>
        <el-footer class="footer">
          Token 管理系统 &copy; {{ new Date().getFullYear() }}
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from './store/user'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const activeMenu = computed(() => route.path)
const isCollapse = ref(false)

// 面包屑当前路由
const currentRoute = computed(() => {
  return route.meta.title || '首页'
})

// 切换侧边栏收起/展开
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 切换全屏
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else if (document.exitFullscreen) {
    document.exitFullscreen()
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/settings')
      break
    case 'settings':
      router.push({ 
        path: '/settings', 
        query: { tab: 'user' } 
      })
      break
    case 'logout':
      ElMessage({
        message: '正在退出登录...',
        type: 'info',
        duration: 1000
      })
      userStore.logout()
      router.push('/login')
      break
  }
}

// 组件挂载时获取用户信息，带超时控制
onMounted(async () => {
  // 只在非登录页面获取用户信息
  if (route.path !== '/login') {
    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('获取用户信息超时')), 5000)
    })
    
    try {
      // 添加超时控制，避免无限等待
      await Promise.race([
        userStore.fetchCurrentUser(),
        timeoutPromise
      ])
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 如果获取用户信息失败，且有token，则清除token并重定向到登录页
      if (localStorage.getItem('token')) {
        localStorage.removeItem('token')
        router.push('/login')
      }
    }
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --sidebar-bg: #001529;
  --sidebar-dark-bg: #000c17;
  --header-bg: #fff;
  --main-bg: #f5f7fa;
  --footer-bg: #f2f6fc;
  --border-color: #e4e7ed;
  --text-color: #303133;
  --text-color-light: #909399;
}

/* 全局过渡效果 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.28s;
}

.fade-transform-enter-from,
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
}

.app-container {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: var(--sidebar-bg);
  transition: width 0.3s;
  position: relative;
  z-index: 10;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  overflow-x: hidden;
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  background-color: var(--sidebar-dark-bg);
  color: #fff;
  overflow: hidden;
  transition: all 0.3s;
}

.logo-collapsed {
  padding: 0;
}

.logo h2 {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.header {
  background-color: var(--header-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 9;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.header-icon {
  font-size: 18px;
  padding: 0 10px;
  cursor: pointer;
  color: var(--text-color-light);
  transition: color 0.3s;
}

.header-icon:hover {
  color: var(--primary-color);
}

.toggle-sidebar {
  font-size: 20px;
  cursor: pointer;
  margin-right: 16px;
  color: var(--text-color-light);
  transition: color 0.3s;
}

.toggle-sidebar:hover {
  color: var(--primary-color);
}

.user-dropdown {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}

.breadcrumb {
  font-size: 14px;
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: var(--text-color-light);
}

.badge-item {
  margin-top: 8px;
}

.main {
  background-color: var(--main-bg);
  min-height: calc(100vh - 110px);
  padding: 20px;
  overflow-y: auto;
}

.footer {
  padding: 12px 20px;
  text-align: center;
  font-size: 12px;
  color: var(--text-color-light);
  background-color: var(--footer-bg);
  border-top: 1px solid var(--border-color);
}

/* 卡片样式优化 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: none;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.el-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.el-card__header {
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
  font-weight: 600;
}
</style> 