/**
 * 更新备份表结构脚本
 * 添加缺失的size列
 */
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function updateBackupsTable() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'token_management'
    });
    
    console.log('数据库连接成功');
    
    // 检查列是否存在
    const [columns] = await connection.query('SHOW COLUMNS FROM backups LIKE "size"');
    
    if (columns.length === 0) {
      // 列不存在，添加列
      await connection.query('ALTER TABLE backups ADD COLUMN size VARCHAR(20) AFTER token_count');
      console.log('备份表更新成功，已添加size列');
    } else {
      console.log('列已存在，无需更新');
    }
    
    // 再次验证列是否存在
    const [verifyColumns] = await connection.query('SHOW COLUMNS FROM backups LIKE "size"');
    if (verifyColumns.length > 0) {
      console.log('验证成功: size列已存在');
    } else {
      console.error('验证失败: size列未添加');
    }
    
  } catch (error) {
    console.error('更新备份表失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行更新
updateBackupsTable();
