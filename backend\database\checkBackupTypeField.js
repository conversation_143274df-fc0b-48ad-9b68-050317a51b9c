// 用于检查backup_type字段定义的脚本
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

async function checkDatabaseField() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'qq@666666',
    database: process.env.DB_NAME || 'token_management'
  });

  try {
    console.log('连接到数据库成功，开始检查字段...');

    // 获取字段信息
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = '${process.env.DB_NAME || 'token_management'}' 
      AND TABLE_NAME = 'backups' 
      AND COLUMN_NAME = 'backup_type'
    `);
    
    if (columns.length > 0) {
      console.log('backup_type字段信息:', columns[0]);
      
      // 如果是ENUM类型，提取允许的值
      const columnType = columns[0].COLUMN_TYPE;
      if (columnType.startsWith('enum')) {
        const enumValues = columnType.match(/'([^']*)'/g);
        if (enumValues) {
          console.log('允许的枚举值:', enumValues.map(v => v.replace(/'/g, '')));
        }
      }
      
      // 检查backup_type字段是否为ENUM，如果是，修改为VARCHAR
      if (columnType.startsWith('enum')) {
        console.log('backup_type是ENUM类型，正在修改为VARCHAR(20)...');
        await connection.query(`
          ALTER TABLE backups 
          MODIFY COLUMN backup_type VARCHAR(20)
        `);
        console.log('成功将backup_type从ENUM修改为VARCHAR(20)');
      } else {
        console.log('backup_type已经是非ENUM类型，无需修改');
      }
    } else {
      console.log('未找到backup_type字段');
    }
    
    console.log('字段检查完成！');
  } catch (error) {
    console.error('执行字段检查时出错:', error.message);
  } finally {
    // 关闭连接
    await connection.end();
    console.log('数据库连接已关闭');
  }
}

// 执行检查
checkDatabaseField().catch(err => {
  console.error('检查脚本执行失败:', err);
  process.exit(1);
}); 