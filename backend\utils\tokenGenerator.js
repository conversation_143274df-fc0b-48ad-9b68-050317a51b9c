const crypto = require('crypto');
const { logger } = require('./logger');

/**
 * 生成随机Token
 * @param {number} length - Token长度，默认32
 * @returns {string} 生成的Token
 */
const generateToken = (length = 32) => {
  try {
    return crypto.randomBytes(Math.ceil(length / 2))
      .toString('hex')
      .slice(0, length);
  } catch (error) {
    logger.error(`生成Token失败: ${error.message}`);
    throw error;
  }
};

/**
 * 生成带前缀的UID
 * @param {string} prefix - UID前缀，默认为'TK'
 * @returns {string} 生成的UID
 */
const generateUID = (prefix = 'TK') => {
  try {
    // 格式: 前缀 + 时间戳 + 随机数
    const timestamp = new Date().getTime().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  } catch (error) {
    logger.error(`生成UID失败: ${error.message}`);
    throw error;
  }
};

/**
 * 验证Token格式是否有效
 * @param {string} token - 待验证的Token
 * @returns {boolean} 是否有效
 */
const validateTokenFormat = (token) => {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // 添加基本验证规则，可根据需求调整
  // 例如: 长度检查、格式检查等
  return token.length >= 16;
};

/**
 * 为平台生成特定格式的Token
 * @param {string} platform - 平台名称
 * @returns {string} 生成的Token
 */
const generatePlatformToken = (platform) => {
  try {
    // 根据不同平台生成不同格式的Token
    switch (platform.toLowerCase()) {
      case 'amazon':
        return `amzn.${generateToken(20)}`;
      case 'shopify':
        return `shpat_${generateToken(28)}`;
      case 'facebook':
        return `fb.${generateToken(24)}`;
      case 'twitter':
        return `twt_${generateToken(22)}`;
      default:
        return generateToken();
    }
  } catch (error) {
    logger.error(`为平台生成Token失败: ${error.message}`);
    throw error;
  }
};

module.exports = {
  generateToken,
  generateUID,
  validateTokenFormat,
  generatePlatformToken
}; 