const jwt = require('jsonwebtoken');
const { logger } = require('../utils/logger');

// 验证JWT令牌中间件
const authenticate = (req, res, next) => {
  // 从请求头获取token
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false, 
      message: '未授权访问，请登录' 
    });
  }
  
  // 提取token
  const token = authHeader.split(' ')[1];
  
  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 将用户信息添加到请求对象
    req.user = decoded;
    
    next();
  } catch (error) {
    logger.error(`Token验证失败: ${error.message}`);
    
    return res.status(401).json({ 
      success: false, 
      message: '身份验证失败，请重新登录'
    });
  }
};

// 验证管理员权限中间件
const isAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ 
      success: false, 
      message: '需要管理员权限' 
    });
  }
  
  next();
};

module.exports = {
  authenticate,
  isAdmin
}; 