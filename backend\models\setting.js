const db = require('../database/db');
const { logger } = require('../utils/logger');

class Setting {
  /**
   * 获取所有系统设置
   * @returns {Promise<Array>} 所有设置项
   */
  static async getAll() {
    try {
      const query = `
        SELECT s.*, u.username as updated_by_username
        FROM settings s
        LEFT JOIN users u ON s.updated_by = u.id
        ORDER BY s.category, s.key
      `;
      
      const [rows] = await db.query(query);
      return rows;
    } catch (error) {
      logger.error(`获取所有系统设置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据键获取设置
   * @param {string} key - 设置键
   * @returns {Promise<object|null>} 设置对象或null
   */
  static async getByKey(key) {
    try {
      const query = `
        SELECT s.*, u.username as updated_by_username
        FROM settings s
        LEFT JOIN users u ON s.updated_by = u.id
        WHERE s.key = ?
      `;
      
      const [rows] = await db.query(query, [key]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      logger.error(`获取设置(Key: ${key})失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据类别获取设置
   * @param {string} category - 设置类别
   * @returns {Promise<Array>} 设置对象数组
   */
  static async getByCategory(category) {
    try {
      const query = `
        SELECT s.*, u.username as updated_by_username
        FROM settings s
        LEFT JOIN users u ON s.updated_by = u.id
        WHERE s.category = ?
        ORDER BY s.key
      `;
      
      const [rows] = await db.query(query, [category]);
      return rows;
    } catch (error) {
      logger.error(`获取类别设置(Category: ${category})失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建新设置
   * @param {object} settingData - 设置数据
   * @returns {Promise<number>} 新创建的设置ID
   */
  static async create(settingData) {
    try {
      const { key, value, description, category, is_sensitive = 0, created_by } = settingData;
      
      const query = `
        INSERT INTO settings (key, value, description, category, is_sensitive, created_by, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      const [result] = await db.query(query, [
        key, 
        value, 
        description || null, 
        category || 'general', 
        is_sensitive ? 1 : 0, 
        created_by || null, 
        new Date()
      ]);
      
      return result.insertId;
    } catch (error) {
      logger.error(`创建设置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新设置
   * @param {string} key - 设置键
   * @param {object} settingData - 设置数据
   * @returns {Promise<boolean>} 更新是否成功
   */
  static async update(key, settingData) {
    try {
      const { value, description, updated_by } = settingData;
      
      let query = 'UPDATE settings SET ';
      const params = [];
      const updates = [];
      
      if (value !== undefined) {
        updates.push('value = ?');
        params.push(value);
      }
      
      if (description !== undefined) {
        updates.push('description = ?');
        params.push(description);
      }
      
      updates.push('updated_at = ?');
      params.push(new Date());
      
      if (updated_by) {
        updates.push('updated_by = ?');
        params.push(updated_by);
      }
      
      query += updates.join(', ') + ' WHERE `key` = ?';
      params.push(key);
      
      const [result] = await db.query(query, params);
      
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新设置(Key: ${key})失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除设置
   * @param {string} key - 设置键
   * @returns {Promise<boolean>} 删除是否成功
   */
  static async delete(key) {
    try {
      const query = 'DELETE FROM settings WHERE `key` = ?';
      
      const [result] = await db.query(query, [key]);
      
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`删除设置(Key: ${key})失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 初始化默认设置
   * @returns {Promise<boolean>} 初始化是否成功
   */
  static async initializeDefaultSettings() {
    try {
      // 检查数据库中是否已有设置
      const [existingSettings] = await db.query('SELECT COUNT(*) as count FROM settings');
      
      // 如果已经有设置，则不再初始化
      if (existingSettings[0].count > 0) {
        logger.info('系统设置已存在，跳过初始化');
        return true;
      }
      
      // 默认设置
      const defaultSettings = [
        {
          key: 'system.name',
          value: 'Token管理系统',
          description: '系统名称',
          category: 'system',
          is_sensitive: 0
        },
        {
          key: 'system.version',
          value: '1.0.0',
          description: '系统版本',
          category: 'system',
          is_sensitive: 0
        },
        {
          key: 'system.maintenance_mode',
          value: 'false',
          description: '系统维护模式，设置为true时系统将暂停服务',
          category: 'system',
          is_sensitive: 0
        },
        {
          key: 'token.default_expiry_days',
          value: '365',
          description: '令牌默认有效期（天）',
          category: 'token',
          is_sensitive: 0
        },
        {
          key: 'token.length',
          value: '32',
          description: '生成令牌的长度',
          category: 'token',
          is_sensitive: 0
        },
        {
          key: 'security.password_reset_expiry',
          value: '24',
          description: '密码重置链接有效期（小时）',
          category: 'security',
          is_sensitive: 0
        },
        {
          key: 'security.jwt_expiry',
          value: '24',
          description: 'JWT令牌有效期（小时）',
          category: 'security',
          is_sensitive: 0
        },
        {
          key: 'notification.admin_email',
          value: '<EMAIL>',
          description: '系统管理员邮箱',
          category: 'notification',
          is_sensitive: 0
        }
      ];
      
      // 批量插入默认设置
      for (const setting of defaultSettings) {
        await this.create({
          ...setting,
          created_at: new Date()
        });
      }
      
      logger.info('默认系统设置初始化完成');
      return true;
    } catch (error) {
      logger.error(`初始化默认设置失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = Setting; 