const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const dotenv = require('dotenv');
const routes = require('./routes');
const errorHandler = require('./middleware/errorHandler');
const { logger } = require('./utils/logger');
const db = require('./database/db');
const Setting = require('./models/setting');
const UserPreference = require('./models/userPreference');
const ActivityLog = require('./models/activityLog');

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// 初始化数据库
const initializeApp = async () => {
  try {
    // 测试数据库连接
    const dbConnected = await db.testConnection();
    if (!dbConnected) {
      logger.error('无法连接到数据库，应用将退出');
      process.exit(1);
    }

    // 初始化数据库表结构
    await db.initializeDatabase();

    // 初始化默认设置
    await Setting.initializeDefaultSettings();

    // 初始化用户偏好设置表
    await UserPreference.initializeTable();

    // 初始化活动日志表
    await ActivityLog.initializeTable();

    logger.info('应用初始化完成');
  } catch (error) {
    logger.error(`应用初始化失败: ${error.message}`);
    process.exit(1);
  }
};

// 配置中间件
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
// 增加请求体大小限制为50MB
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 注册路由
app.use('/api', routes);

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    await initializeApp();
    app.listen(PORT, () => {
      logger.info(`服务器运行在端口 ${PORT}`);
    });
  } catch (error) {
    logger.error(`服务器启动失败: ${error.message}`);
    process.exit(1);
  }
};

startServer();