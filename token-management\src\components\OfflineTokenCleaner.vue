<template>
  <div class="offline-token-cleaner">
    <el-button
      type="danger"
      :icon="Delete"
      :loading="loading"
      @click="showConfirmDialog"
    >
      清除掉线
    </el-button>

    <!-- 确认对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="确认清除掉线Token"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="confirm-content">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <div class="confirm-message">
          <p><strong>此操作将清除所有掉线状态的Token，不可撤销！</strong></p>
          <p>确定要继续吗？</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="danger" @click="clearOfflineTokens" :loading="loading">
            确定清除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, Warning } from '@element-plus/icons-vue'

// 定义props
const props = defineProps({
  tokens: {
    type: Array,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['update:tokens'])

// 状态变量
const loading = ref(false)
const dialogVisible = ref(false)

// 显示确认对话框
const showConfirmDialog = () => {
  // 检查是否有掉线的Token
  const offlineTokens = props.tokens.filter(token => token.status === '掉线')

  if (offlineTokens.length === 0) {
    ElMessage.info('当前没有掉线状态的Token')
    return
  }

  dialogVisible.value = true
}

// 清除掉线Token
const clearOfflineTokens = () => {
  try {
    // 设置加载状态
    loading.value = true

    // 过滤掉掉线状态的Token
    const onlineTokens = props.tokens.filter(token => token.status !== '掉线')

    // 计算删除的数量
    const removedCount = props.tokens.length - onlineTokens.length

    // 更新Token列表
    emit('update:tokens', onlineTokens)

    // 关闭对话框
    dialogVisible.value = false

    // 显示成功消息
    ElMessage.success(`成功清除 ${removedCount} 个掉线状态的Token`)
  } catch (error) {
    console.error('清除掉线Token失败:', error)
    ElMessage.error(`清除掉线Token失败: ${error.message}`)
  } finally {
    // 重置加载状态
    loading.value = false
  }
}
</script>

<style scoped>
.offline-token-cleaner {
  display: inline-block;
  margin-right: 10px;
}

.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 10px 0;
}

.warning-icon {
  font-size: 24px;
  color: #E6A23C;
}

.confirm-message {
  flex: 1;
}

.confirm-message p {
  margin: 5px 0;
}
</style>
