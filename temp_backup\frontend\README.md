# Token 管理系统 - 前端应用

基于 Vue 3 + Element Plus 开发的 Token 管理系统前端应用。

## 功能概述

本系统是一个完整的 Token 管理解决方案，提供以下主要功能：

- Token 的创建、编辑、删除和查询
- Token 的状态管理和批量操作
- Token 备份和还原
- Token 重置记录管理
- 数据统计和可视化展示
- 系统设置和用户偏好管理
- 多用户权限管理

## 技术栈

- Vue 3 (Composition API)
- Vue Router 4
- Pinia (状态管理)
- Element Plus (UI 组件库)
- Axios (HTTP 客户端)
- ECharts (数据可视化)
- Vite (构建工具)
- TypeScript

## 项目结构

```
frontend/
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/             # API 请求
│   │   ├── auth.js      # 认证相关
│   │   ├── token.js     # Token 相关
│   │   ├── backup.js    # 备份相关
│   │   ├── reset.js     # 重置相关
│   │   ├── stats.js     # 统计相关
│   │   └── settings.js  # 设置相关
│   ├── assets/          # 资源文件
│   │   ├── images/      # 图片
│   │   └── styles/      # 样式
│   ├── components/      # 通用组件
│   │   ├── layout/      # 布局组件
│   │   ├── common/      # 通用组件
│   │   ├── token/       # Token 相关组件
│   │   ├── backup/      # 备份相关组件
│   │   └── charts/      # 图表组件
│   ├── composables/     # 组合式函数
│   │   ├── useAuth.js   # 认证相关
│   │   ├── useToken.js  # Token 相关
│   │   └── useStats.js  # 统计相关
│   ├── router/          # 路由
│   │   └── index.js     # 路由配置
│   ├── stores/          # Pinia 状态仓库
│   │   ├── auth.js      # 认证状态
│   │   ├── token.js     # Token 状态
│   │   ├── backup.js    # 备份状态
│   │   └── settings.js  # 设置状态
│   ├── utils/           # 工具函数
│   │   ├── request.js   # 请求封装
│   │   ├── validator.js # 验证工具
│   │   └── formatter.js # 格式化工具
│   ├── views/           # 页面组件
│   │   ├── auth/        # 认证相关页面
│   │   ├── dashboard/   # 仪表盘页面
│   │   ├── token/       # Token 管理页面
│   │   ├── backup/      # 备份管理页面
│   │   ├── reset/       # 重置记录页面
│   │   ├── settings/    # 设置页面
│   │   └── error/       # 错误页面
│   ├── App.vue          # 根组件
│   ├── main.js          # 入口文件
│   └── env.d.ts         # 环境变量类型定义
├── .env.development     # 开发环境变量
├── .env.production      # 生产环境变量
├── index.html           # HTML 模板
├── package.json         # 项目依赖
├── vite.config.js       # Vite 配置
├── tsconfig.json        # TypeScript 配置
└── README.md            # 项目说明
```

## 页面和功能

### 1. 认证相关
- 登录页面
- 注册页面
- 忘记密码
- 用户信息设置

### 2. 仪表盘
- 概览统计
- 活动记录
- 数据趋势图表
- 快速操作入口

### 3. Token 管理
- Token 列表 (分页、筛选、排序)
- Token 创建与编辑
- Token 详情查看
- Token 状态管理
- Token 批量操作

### 4. 备份管理
- 备份列表 (分页、筛选、排序)
- 创建备份
- 备份详情查看
- 从备份恢复 Token
- 备份删除与标记

### 5. 重置记录
- 重置历史列表
- 重置详情查看
- 按 Token 筛选重置记录

### 6. 系统设置
- 用户偏好设置
- 系统参数配置 (管理员)
- 用户管理 (管理员)

## 安装与运行

### 前置条件
- Node.js (v16 或更高版本)
- npm 或 yarn

### 安装步骤

1. 克隆仓库并进入前端目录
```bash
cd frontend
```

2. 安装依赖
```bash
npm install
# 或
yarn install
```

3. 创建并配置环境变量文件 `.env.development`
```
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=Token管理系统
```

### 开发运行
```bash
npm run dev
# 或
yarn dev
```

应用将在 http://localhost:5173 (默认) 运行。

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

生成的文件将位于 `dist` 目录中。

## 组件库使用指南

### Token 相关组件

#### TokenTable
用于展示 Token 列表的表格组件，支持分页、筛选和排序。

```vue
<TokenTable
  :loading="loading"
  :data="tokens"
  :total="total"
  :page-size="pageSize"
  :current-page="currentPage"
  @page-change="handlePageChange"
  @filter-change="handleFilterChange"
  @sort-change="handleSortChange"
  @view="handleView"
  @edit="handleEdit"
  @delete="handleDelete"
/>
```

#### TokenForm
用于创建和编辑 Token 的表单组件。

```vue
<TokenForm
  :token="selectedToken"
  :loading="formLoading"
  @submit="handleSubmit"
  @cancel="handleCancel"
/>
```

### 备份相关组件

#### BackupTable
用于展示备份列表的表格组件。

```vue
<BackupTable
  :loading="loading"
  :data="backups"
  :total="total"
  :page-size="pageSize"
  :current-page="currentPage"
  @page-change="handlePageChange"
  @view="handleView"
  @restore="handleRestore"
  @delete="handleDelete"
/>
```

#### BackupForm
用于创建备份的表单组件。

```vue
<BackupForm
  :loading="formLoading"
  :tokens="selectedTokens"
  @submit="handleSubmit"
  @cancel="handleCancel"
/>
```

### 图表组件

#### StatsChart
用于展示统计数据的图表组件，支持多种图表类型。

```vue
<StatsChart
  :type="chartType"
  :data="chartData"
  :options="chartOptions"
  :loading="chartLoading"
  :height="300"
/>
```

## API 使用指南

### Token API
```javascript
import { useTokenApi } from '@/api/token';

const tokenApi = useTokenApi();

// 获取 Token 列表
const getTokens = async (page, limit, filters) => {
  try {
    const res = await tokenApi.getAll(page, limit, filters);
    return res.data;
  } catch (error) {
    console.error('Failed to get tokens:', error);
    throw error;
  }
};

// 创建 Token
const createToken = async (tokenData) => {
  try {
    const res = await tokenApi.create(tokenData);
    return res.data;
  } catch (error) {
    console.error('Failed to create token:', error);
    throw error;
  }
};
```

### 备份 API
```javascript
import { useBackupApi } from '@/api/backup';

const backupApi = useBackupApi();

// 获取备份列表
const getBackups = async (page, limit, filters) => {
  try {
    const res = await backupApi.getAll(page, limit, filters);
    return res.data;
  } catch (error) {
    console.error('Failed to get backups:', error);
    throw error;
  }
};

// 创建备份
const createBackup = async (backupData, tokenIds) => {
  try {
    const res = await backupApi.create(backupData, tokenIds);
    return res.data;
  } catch (error) {
    console.error('Failed to create backup:', error);
    throw error;
  }
};
```

## 状态管理使用指南

### Token Store
```javascript
import { useTokenStore } from '@/stores/token';

const tokenStore = useTokenStore();

// 加载 Token 列表
const loadTokens = async (page, limit, filters) => {
  await tokenStore.fetchTokens(page, limit, filters);
  return {
    tokens: tokenStore.tokens,
    total: tokenStore.total
  };
};

// 创建 Token
const createNewToken = async (tokenData) => {
  await tokenStore.createToken(tokenData);
};
```

## 国际化支持

系统支持中文和英文两种语言，可以在设置中切换。

```javascript
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

// 切换语言
const changeLanguage = (lang) => {
  locale.value = lang;
};
```

## 更新记录

### [v1.0.0] - 2023-06-15
- 初始版本发布
- 基础 Token 管理功能

### [v1.1.0] - 2023-08-01
- 添加备份管理功能
- 添加统计图表功能
- 优化用户界面

### [v1.2.0] - 2023-10-12
- 添加 Token 状态筛选功能
- 优化备份管理界面
- 添加 Token 批量操作功能
- 修复已知 BUG

### [v1.3.0] - 2023-12-20
- 添加 Token 状态列
- 完善 Token 筛选功能
- 优化 Token 备份详情查看
- 添加重置记录管理功能
- 增强数据统计和可视化功能

## 贡献

欢迎提交问题和拉取请求来帮助改进项目。

## 许可

本项目采用 MIT 许可证。 