const jwt = require('jsonwebtoken');
const config = require('../config');

/**
 * 验证JWT Token的中间件
 */
function authenticateToken(req, res, next) {
  // 从请求头获取token
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ success: false, message: '未提供认证令牌' });
  }
  
  jwt.verify(token, config.jwtSecret, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: '令牌无效或已过期' });
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    next();
  });
}

module.exports = {
  authenticateToken
}; 