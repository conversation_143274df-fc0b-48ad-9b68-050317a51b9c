import axios from 'axios';

/**
 * 新Token置入账号内订单服务
 */

// API基础URL
const apiBaseUrl = 'http://localhost:3000/api';

/**
 * 将Token置入账号内订单
 * @param {Array} tokenData - Token数据数组，每项包含 {uid, token, user}
 * @returns {Promise<Object>} - 操作结果
 */
export const placeOrder = async (tokenData) => {
  try {
    if (!tokenData || !Array.isArray(tokenData) || tokenData.length === 0) {
      return {
        success: false,
        message: '请提供有效的Token数据',
        successCount: 0,
        failCount: 0,
        totalCount: 0
      };
    }

    // 准备请求数据 - 转换为后端期望的格式
    const requestData = {
      tokens: tokenData.map(item => ({
        UID: item.uid,           // 注意大写的UID
        token: item.token,
        username: item.user || '未知用户' // 使用表格中的用户名
      }))
    };

    // 计算总数量
    const totalCount = requestData.tokens.length;

    // 使用不需要认证的测试API端点
    console.log('发送请求到后端:', apiBaseUrl + '/tokens/test/place-order');
    console.log('请求数据:', requestData);

    // 通过后端发送请求
    const response = await axios.post(`${apiBaseUrl}/tokens/test/place-order`, requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 初始请求超时时间改为1分钟
    });

    console.log('收到响应:', response.data);
    console.log('响应类型:', typeof response.data);
    
    // 确保响应数据为对象格式
    let responseData = response.data;
    if (typeof responseData === 'string') {
      try {
        responseData = JSON.parse(responseData);
        console.log('解析字符串响应为JSON:', responseData);
      } catch (e) {
        console.error('响应不是有效的JSON字符串:', e);
      }
    }
    
    console.log('处理后的响应数据:', responseData);
    console.log('响应数据结构:', JSON.stringify(responseData, null, 2));
    
    // 计算成功和失败数量 - 修正逻辑
    let failCount = 0;
    let successCount = 0;
    
    // 深入检查响应结构，找出error数组
    let errorArray = [];
    
    // 检查直接在响应中的error字段
    if (responseData.error) {
      errorArray = Array.isArray(responseData.error) 
        ? responseData.error 
        : [responseData.error];
      console.log('从顶层发现error数组:', errorArray);
    }
    // 检查嵌套在data中的error字段
    else if (responseData.data && responseData.data.error) {
      errorArray = Array.isArray(responseData.data.error) 
        ? responseData.data.error 
        : [responseData.data.error];
      console.log('从data字段发现error数组:', errorArray);
    }
    
    console.log('找到的error数组:', errorArray);
    console.log('error数组长度:', errorArray.length);
    
    // 根据error数组计算失败数量
    failCount = errorArray.length;
    // 成功数量 = 总数量 - 失败数量
    successCount = totalCount - failCount;
    
    console.log(`计算结果: 总数=${totalCount}, 失败=${failCount}, 成功=${successCount}`);
    
    // 确保successCount和failCount不会出现负数
    successCount = Math.max(0, successCount);
    failCount = Math.min(totalCount, failCount);
    
    // 根据计算的成功和失败数量决定整体操作是否成功
    const isOperationSuccessful = successCount > 0;

    // 最终计算结果输出
    console.log('最终计算结果:', {
      success: isOperationSuccessful,
      successCount,
      failCount,
      totalCount,
      successRate: totalCount > 0 ? (successCount / totalCount * 100).toFixed(2) + '%' : '0%'
    });

    // 处理响应
    if (isOperationSuccessful) {
      return {
        success: true,
        message: successCount === totalCount 
          ? '所有Token置入订单成功' 
          : `成功: ${successCount}个, 失败: ${failCount}个`,
        data: responseData,
        successCount,
        failCount,
        totalCount,
        errorArray
      };
    } else {
      // 处理错误响应
      const errorMessages = errorArray.length > 0
        ? errorArray.join('\n')
        : '操作失败';
        
      return {
        success: false,
        message: `全部失败: ${failCount}个`,
        data: responseData,
        successCount,
        failCount,
        totalCount,
        errorArray,
        errorDetails: errorMessages
      };
    }
  } catch (error) {
    console.error('Token置入账号内订单失败:', error);
    return {
      success: false,
      message: `请求失败: ${error.message || '未知错误'}`,
      error,
      successCount: 0,
      failCount: 0,
      totalCount: tokenData ? tokenData.length : 0
    };
  }
};

/**
 * 按用户名分组Token数据
 * @param {Array} tokenData - Token数据数组
 * @returns {Object} - 按用户名分组的Token
 */
const groupTokensByUser = (tokenData) => {
  const groups = {};
  
  tokenData.forEach(token => {
    const username = token.user || '未知用户';
    if (!groups[username]) {
      groups[username] = [];
    }
    groups[username].push(token);
  });
  
  return groups;
};

/**
 * 将Token数组分割成指定大小的批次
 * @param {Array} tokens - Token数组
 * @param {number} batchSize - 每批大小
 * @returns {Array} - 分批后的Token数组
 */
const batchTokens = (tokens, batchSize) => {
  const batches = [];
  for (let i = 0; i < tokens.length; i += batchSize) {
    batches.push(tokens.slice(i, i + batchSize));
  }
  return batches;
};

/**
 * 使用并发请求将Token置入账号内订单
 * @param {Array} tokenData - Token数据数组
 * @param {Object} options - 配置选项
 * @param {number} options.batchSize - 每批处理的Token数量，默认10
 * @param {number} options.maxConcurrent - 最大并发请求数，默认5
 * @param {Function} options.onProgress - 进度回调函数
 * @returns {Promise<Object>} - 汇总的操作结果
 */
export const addTokensToOrderConcurrent = async (tokenData, options = {}) => {
  try {
    if (!tokenData || !Array.isArray(tokenData) || tokenData.length === 0) {
      return {
        success: false,
        message: '请提供有效的Token数据',
        successCount: 0,
        failCount: 0,
        totalCount: 0,
        batchResults: [],
        requestsData: [],
        responsesData: []
      };
    }
    
    // 设置默认选项
    const {
      batchSize = 5,
      maxConcurrent = 3,
      onProgress = () => {},
      retryCount = 2,
      retryDelay = 1000
    } = options;
    
    // 计算总数量
    const totalCount = tokenData.length;
    
    // 按用户分组
    const userGroups = groupTokensByUser(tokenData);
    
    // 准备批次任务
    const batches = [];
    const requestsData = [];
    
    // 为每个用户组创建批次
    Object.entries(userGroups).forEach(([username, tokens]) => {
      // 将用户的tokens分成多个批次
      const userBatches = batchTokens(tokens, batchSize);
      
      // 为每个批次创建任务
      userBatches.forEach(batchTokens => {
        const requestData = {
          tokens: batchTokens.map(item => ({
            UID: item.uid,           // 注意大写的UID
            token: item.token,
            username: item.user || '未知用户' // 使用表格中的用户名
          }))
        };
        
        // 保存请求数据用于后续参考
        requestsData.push({
          username,
          batchSize: batchTokens.length,
          requestData
        });
        
        // 添加到批次列表
        batches.push({
          username,
          tokens: batchTokens,
          requestData
        });
      });
    });
    
    // 创建一个并发控制函数
    const runBatchesWithConcurrencyLimit = async (batches, limit) => {
      // 结果数组，与batches顺序对应
      const results = [];
      // 进行中的Promise数组
      const running = [];
      // 完成的批次数量
      let completedCount = 0;
      
      // 创建每个批次的处理函数
      const processBatch = async (batch, batchIndex) => {
        let retries = 0;
        let lastError = null;

        while (retries <= retryCount) {
          try {
            const result = await placeOrder(batch.tokens, {
              // 重试时移除超时限制
              timeout: retries > 0 ? 0 : 60000
            });
            
            // 分析错误
            let errorArray = [];
            
            // 检查直接在响应中的error字段
            if (result.data.error) {
              errorArray = Array.isArray(result.data.error) 
                ? result.data.error 
                : [result.data.error];
            }
            // 检查嵌套在data中的error字段
            else if (result.data.data && result.data.data.error) {
              errorArray = Array.isArray(result.data.data.error) 
                ? result.data.data.error 
                : [result.data.data.error];
            }
            
            // 计算成功和失败数量
            const batchTotalCount = batch.tokens.length;
            const failCount = errorArray.length;
            const successCount = batchTotalCount - failCount;
            
            // 构造结果对象
            const resultObject = {
              batchIndex: batchIndex,
              username: batch.username,
              success: successCount > 0,
              successCount,
              failCount,
              totalCount: batchTotalCount,
              errorArray,
              requestData: batch.requestData,
              responseData: result.data
            };
            
            // 更新进度
            completedCount++;
            const progressPercent = Math.round((completedCount / batches.length) * 100);
            onProgress({
              processed: completedCount,
              total: batches.length,
              percent: progressPercent,
              currentBatch: resultObject
            });
            
            return resultObject;
          } catch (error) {
            lastError = error;
            if (retries < retryCount) {
              console.log(`批次 ${batchIndex + 1} 第 ${retries + 1} 次重试...`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              retries++;
            } else {
              throw error;
            }
          }
        }
      };
      
      // 处理所有批次
      for (let i = 0; i < batches.length; i++) {
        // 创建处理当前批次的Promise
        const p = processBatch(batches[i], i).then(result => {
          // 将结果保存到对应位置
          results[i] = result;
          // 从运行列表中移除
          const index = running.indexOf(p);
          if (index !== -1) running.splice(index, 1);
          return result;
        });
        
        // 添加到运行列表
        running.push(p);
        
        // 如果达到并发限制，等待某个批次完成
        if (running.length >= limit) {
          await Promise.race(running);
        }
      }
      
      // 等待所有剩余的批次完成
      await Promise.all(running);
      
      return results;
    };
    
    // 运行批次处理
    console.log(`开始处理 ${batches.length} 个批次，最大并发数 ${maxConcurrent}`);
    const batchResults = await runBatchesWithConcurrencyLimit(batches, maxConcurrent);
    
    // 汇总结果
    let totalSuccessCount = 0;
    let totalFailCount = 0;
    const responsesData = [];
    
    batchResults.forEach(result => {
      totalSuccessCount += result.successCount;
      totalFailCount += result.failCount;
      
      // 保存响应数据
      responsesData.push({
        username: result.username,
        batchIndex: result.batchIndex,
        success: result.success,
        successCount: result.successCount,
        failCount: result.failCount,
        responseData: result.responseData
      });
    });
    
    // 计算每个用户的成功率
    const userResults = {};
    Object.keys(userGroups).forEach(username => {
      userResults[username] = {
        success: 0,
        fail: 0,
        total: userGroups[username].length
      };
    });
    
    // 从批次结果中更新用户结果
    batchResults.forEach(result => {
      const username = result.username;
      if (userResults[username]) {
        userResults[username].success += result.successCount;
        userResults[username].fail += result.failCount;
      }
    });
    
    // 最终结果
    const finalSuccess = totalSuccessCount > 0;
    const finalResult = {
      success: finalSuccess,
      message: finalSuccess 
        ? totalSuccessCount === totalCount 
          ? '所有Token置入订单成功' 
          : `成功: ${totalSuccessCount}个, 失败: ${totalFailCount}个`
        : `全部失败: ${totalFailCount}个`,
      successCount: totalSuccessCount,
      failCount: totalFailCount,
      totalCount,
      batchResults,
      requestsData,
      responsesData,
      userResults
    };
    
    console.log('并发处理完成:', finalResult);
    return finalResult;
    
  } catch (error) {
    console.error('并发处理过程中发生错误:', error);
    return {
      success: false,
      message: `请求失败: ${error.message || '未知错误'}`,
      error,
      successCount: 0,
      failCount: tokenData ? tokenData.length : 0,
      totalCount: tokenData ? tokenData.length : 0,
      batchResults: [],
      requestsData: [],
      responsesData: []
    };
  }
}; 