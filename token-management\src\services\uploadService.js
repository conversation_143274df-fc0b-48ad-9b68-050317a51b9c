import axios from 'axios';
import { ElMessage } from 'element-plus'; // Optional: for direct feedback in case of service-level issues
import { API_ENDPOINTS } from '../config/apiConfig';

// Define the backend API endpoint for uploading selected tokens
const UPLOAD_API_URL = '/api/upload/selected';

/**
 * Uploads selected tokens to the backend.
 * @param {Array<Object>} selectedTokens - An array of selected token objects.
 *                                        Expected format: [{ uid: '...', token: '...', user: '...', ... }]
 * @returns {Promise<Object>} - A promise that resolves to an object:
 *                              { success: boolean, message: string, uploadedCount?: number }
 */
export const uploadSelectedTokens = async (selectedTokens) => {
  if (!Array.isArray(selectedTokens) || selectedTokens.length === 0) {
    console.warn('uploadSelectedTokens called with no tokens.');
    return { success: false, message: '没有选择任何Token进行上传。' };
  }

  console.log(`[Upload Service] Preparing to upload ${selectedTokens.length} tokens...`);

  try {
    // Make the POST request to the backend endpoint
    const response = await axios.post(UPLOAD_API_URL, 
      { tokens: selectedTokens }, // Send the tokens within a 'tokens' key as expected by the backend
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 120000 // 120 seconds timeout, as backend calls external API
      }
    );

    console.log('[Upload Service] Backend response received:', response.data);

    // Check the response structure from our backend
    if (response.data && typeof response.data.success === 'boolean') {
      return {
        success: response.data.success,
        message: response.data.message || (response.data.success ? '上传成功' : '上传失败，但未收到具体信息。'),
        uploadedCount: response.data.uploadedCount
      };
    } else {
      // Handle unexpected response structure from the backend
      console.error('[Upload Service] Unexpected backend response format:', response.data);
      return { success: false, message: '后端响应格式不正确。' };
    }

  } catch (error) {
    console.error('[Upload Service] Error calling backend upload API:', error);
    let errorMessage = '上传过程中发生网络错误或服务器无响应。';

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('[Upload Service] Backend Error Status:', error.response.status);
      console.error('[Upload Service] Backend Error Data:', error.response.data);
      // Use the error message from the backend if available
      errorMessage = error.response.data?.message || `服务器错误 (状态码: ${error.response.status})。`;
    } else if (error.request) {
      // The request was made but no response was received
      console.error('[Upload Service] No response received:', error.request);
      errorMessage = '无法连接到后端服务，请检查服务是否运行或网络连接。';
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('[Upload Service] Axios setup error:', error.message);
      errorMessage = `请求设置错误: ${error.message}`;
    }

    // Optionally, display a generic error message to the user here
    // ElMessage.error(errorMessage);

    return { success: false, message: errorMessage };
  }
}; 