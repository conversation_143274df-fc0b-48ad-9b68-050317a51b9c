const db = require('../database/db');
const { logger } = require('../utils/logger');

class ActivityLog {
  /**
   * 创建新活动日志
   * @param {object} logData - 日志数据
   * @returns {Promise<number>} - 新创建的日志ID
   */
  static async create(logData) {
    try {
      const { user_id, action, module, target_id, description, ip_address, user_agent } = logData;
      
      const query = `
        INSERT INTO activity_logs 
        (user_id, action, module, target_id, description, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const [result] = await db.query(query, [
        user_id,
        action,
        module,
        target_id || null,
        description || null,
        ip_address || null,
        user_agent || null,
        new Date()
      ]);
      
      return result.insertId;
    } catch (error) {
      logger.error(`创建活动日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取活动日志列表，支持分页和筛选
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {object} filters - 筛选条件
   * @returns {Promise<{logs: Array, total: number}>} - 日志列表和总数
   */
  static async getAll(page = 1, limit = 10, filters = {}) {
    try {
      let query = `
        SELECT l.*, u.username as user_name
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE 1=1
      `;
      const params = [];

      // 应用筛选条件
      if (filters.user_id) {
        query += ' AND l.user_id = ?';
        params.push(filters.user_id);
      }

      if (filters.action) {
        query += ' AND l.action = ?';
        params.push(filters.action);
      }

      if (filters.module) {
        query += ' AND l.module = ?';
        params.push(filters.module);
      }

      if (filters.date_from) {
        query += ' AND l.created_at >= ?';
        params.push(filters.date_from);
      }

      if (filters.date_to) {
        query += ' AND l.created_at <= ?';
        params.push(filters.date_to);
      }

      // 获取总记录数
      const countQuery = query.replace('l.*, u.username as user_name', 'COUNT(*) as total');
      const [countResult] = await db.query(countQuery, params);
      const total = countResult[0].total;

      // 应用分页和排序
      query += ' ORDER BY l.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, (page - 1) * limit);

      // 执行查询
      const [rows] = await db.query(query, params);

      return {
        logs: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取活动日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取特定模块的活动日志
   * @param {string} module - 模块名称
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<{logs: Array, total: number}>} - 日志列表和总数
   */
  static async getByModule(module, page = 1, limit = 10) {
    try {
      // 获取总记录数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM activity_logs
        WHERE module = ?
      `;
      
      const [countResult] = await db.query(countQuery, [module]);
      const total = countResult[0].total;
      
      // 获取日志记录
      const query = `
        SELECT l.*, u.username as user_name
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.module = ?
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const [rows] = await db.query(query, [module, limit, (page - 1) * limit]);
      
      return {
        logs: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取模块(${module})活动日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取用户的活动日志
   * @param {number} userId - 用户ID
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<{logs: Array, total: number}>} - 日志列表和总数
   */
  static async getByUserId(userId, page = 1, limit = 10) {
    try {
      // 获取总记录数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM activity_logs
        WHERE user_id = ?
      `;
      
      const [countResult] = await db.query(countQuery, [userId]);
      const total = countResult[0].total;
      
      // 获取日志记录
      const query = `
        SELECT l.*, u.username as user_name
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.user_id = ?
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const [rows] = await db.query(query, [userId, limit, (page - 1) * limit]);
      
      return {
        logs: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取用户(ID: ${userId})活动日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取与特定目标关联的活动日志
   * @param {string} targetId - 目标ID
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<{logs: Array, total: number}>} - 日志列表和总数
   */
  static async getByTargetId(targetId, page = 1, limit = 10) {
    try {
      // 获取总记录数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM activity_logs
        WHERE target_id = ?
      `;
      
      const [countResult] = await db.query(countQuery, [targetId]);
      const total = countResult[0].total;
      
      // 获取日志记录
      const query = `
        SELECT l.*, u.username as user_name
        FROM activity_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.target_id = ?
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const [rows] = await db.query(query, [targetId, limit, (page - 1) * limit]);
      
      return {
        logs: rows,
        total: total
      };
    } catch (error) {
      logger.error(`获取目标(ID: ${targetId})活动日志失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取活动日志统计信息
   * @returns {Promise<object>} - 统计信息
   */
  static async getStats() {
    try {
      // 总日志数
      const [totalCountResult] = await db.query('SELECT COUNT(*) as total FROM activity_logs');
      
      // 各模块分布
      const [moduleStats] = await db.query(`
        SELECT module, COUNT(*) as count
        FROM activity_logs
        GROUP BY module
        ORDER BY count DESC
      `);
      
      // 各操作类型分布
      const [actionStats] = await db.query(`
        SELECT action, COUNT(*) as count
        FROM activity_logs
        GROUP BY action
        ORDER BY count DESC
      `);
      
      // 最近7天的活动趋势
      const [dailyStats] = await db.query(`
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM activity_logs
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `);
      
      // 活跃用户统计
      const [activeUsers] = await db.query(`
        SELECT user_id, COUNT(*) as activity_count
        FROM activity_logs
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY user_id
        ORDER BY activity_count DESC
        LIMIT 10
      `);
      
      // 补充用户名
      if (activeUsers.length > 0) {
        const userIds = activeUsers.map(u => u.user_id);
        const [userInfo] = await db.query(`
          SELECT id, username
          FROM users
          WHERE id IN (?)
        `, [userIds]);
        
        const userMap = {};
        userInfo.forEach(user => {
          userMap[user.id] = user.username;
        });
        
        activeUsers.forEach(user => {
          user.username = userMap[user.user_id] || '未知用户';
        });
      }
      
      return {
        total: totalCountResult[0].total,
        by_module: moduleStats,
        by_action: actionStats,
        daily_trend: dailyStats,
        active_users: activeUsers
      };
    } catch (error) {
      logger.error(`获取活动日志统计信息失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 获取过去N天的活动趋势
   * @param {number} days - 天数
   * @returns {Promise<Array>} - 趋势数据
   */
  static async getTrends(days) {
    try {
      const query = `
        SELECT 
          DATE(created_at) as date,
          module,
          COUNT(*) as count
        FROM activity_logs
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
        GROUP BY DATE(created_at), module
        ORDER BY date ASC, module
      `;
      
      const [rows] = await db.query(query, [days]);
      
      // 转换为按日期、模块组织的数据结构
      const trendsByDate = {};
      
      rows.forEach(row => {
        if (!trendsByDate[row.date]) {
          trendsByDate[row.date] = {};
        }
        trendsByDate[row.date][row.module] = row.count;
      });
      
      return trendsByDate;
    } catch (error) {
      logger.error(`获取活动趋势失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 初始化数据库表
   * @returns {Promise<boolean>} 初始化是否成功
   */
  static async initializeTable() {
    try {
      // 创建activity_logs表如果不存在
      await db.query(`
        CREATE TABLE IF NOT EXISTS activity_logs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT,
          action VARCHAR(50) NOT NULL,
          module VARCHAR(50) NOT NULL,
          target_id VARCHAR(100),
          description TEXT,
          ip_address VARCHAR(100),
          user_agent TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
          INDEX (module),
          INDEX (action),
          INDEX (target_id),
          INDEX (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
      `);
      
      logger.info('活动日志表初始化完成');
      return true;
    } catch (error) {
      logger.error(`初始化活动日志表失败: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ActivityLog; 