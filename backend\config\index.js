const dotenv = require('dotenv');

// 加载.env文件
dotenv.config();

// 配置对象
const config = {
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,
  
  // 数据库配置
  db: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'token_management'
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'token_management_jwt_secret_key',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'token_management_refresh_secret_key',
    resetSecret: process.env.JWT_RESET_SECRET || 'token_management_reset_secret_key',
    accessTokenExpiry: process.env.JWT_EXPIRES_IN || '24h',
    refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    useCookies: process.env.JWT_USE_COOKIES === 'true'
  },
  
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    credentials: true
  },
  
  // 日志配置
  logs: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/server.log'
  }
};

module.exports = config; 