import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import * as XLSX from 'xlsx'
import { parseImportedFile, processTxtData } from '@/utils/tokenParser'

/**
 * 处理Token导入的组合式函数
 * 支持Excel和TXT格式的文件导入
 */
export default function useTokenImport(options = {}) {
  // 文件输入引用
  const fileInputRef = ref(null)
  const selectedFile = ref(null)

  // 处理从Token表格组合式函数传递过来的方法
  const {
    tokens = ref([]),
    shouldResetPage = ref(true),
    processAndDeduplicateTokens,
    refreshTokens
  } = options

  // 打开文件选择对话框
  const handleImportToken = () => {
    if (fileInputRef.value) {
      fileInputRef.value.click()
    }
  }

  // 处理文件选择变化
  const handleFileInputChange = (event) => {
    const file = event.target.files[0]
    if (!file) return

    selectedFile.value = file
    processSelectedFile(file)

    // 重置文件输入，以便下次选择同一文件时也能触发事件
    event.target.value = ''
  }

  // 处理选中的文件
  const processSelectedFile = async (file) => {
    const fileName = file.name
    if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.txt')) {
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在导入数据...'
      })

      try {
        const parsedData = await parseImportedFile(file)

        if (parsedData.length === 0) {
          ElMessage.warning('文件中没有有效数据')
          loadingInstance.close()
          return
        }

        // 使用传入的去重函数处理数据
        const { newTokens, duplicateCount } = processAndDeduplicateTokens
          ? processAndDeduplicateTokens(parsedData)
          : processAndDeduplicateTokensInternal(parsedData)

        // 修改：保存当前页码
        // 设置不重置页码的标志
        if (shouldResetPage) {
          // 检查shouldResetPage是否为对象（ref对象）
          if (typeof shouldResetPage === 'object' && shouldResetPage !== null) {
            shouldResetPage.value = false
          }
          // 如果是布尔值，不做任何操作
        }

        // 更新Token列表
        tokens.value = newTokens

        let successMsg = `成功导入 ${newTokens.length} 个Token`
        if (duplicateCount > 0) {
          successMsg += `，已自动去除 ${duplicateCount} 个重复Token`
        }

        ElMessage.success(successMsg)

        // 刷新数据
        if (refreshTokens) {
          refreshTokens()
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '无效的数据格式'))
      } finally {
        loadingInstance.close()
      }
    } else {
      ElMessage.error('不支持的文件格式，仅支持Excel(.xlsx, .xls)和文本文件(.txt)')
    }
  }

  // 内部实现的去重功能，当外部未提供时使用
  const processAndDeduplicateTokensInternal = (parsedData) => {
    // 确保每个Token都有默认状态为"未知"
    const processedData = parsedData.map(token => ({
      ...token,
      status: token.status || '未知'
    }))

    // 如果现有数据为空，直接返回处理后的数组
    if (tokens.value.length === 0) {
      return {
        newTokens: processedData,
        duplicateCount: 0
      }
    }

    // 创建已存在的UID和Token值的集合，用于去重
    const existingUids = new Set(tokens.value.map(token => token.uid))
    const existingTokenValues = new Set(tokens.value.map(token => token.token))

    // 过滤重复的Token（基于UID或Token值）
    const uniqueTokens = []
    let duplicateCount = 0

    for (const token of processedData) {
      if (!existingUids.has(token.uid) && !existingTokenValues.has(token.token)) {
        uniqueTokens.push(token)
        existingUids.add(token.uid)
        existingTokenValues.add(token.token)
      } else {
        duplicateCount++
      }
    }

    // 合并现有Token和新导入的唯一Token
    return {
      newTokens: [...tokens.value, ...uniqueTokens],
      duplicateCount
    }
  }

  // 从文本字符串导入Token
  const importFromText = async (textContent) => {
    if (!textContent || textContent.trim() === '') {
      ElMessage.warning('导入内容不能为空')
      return
    }

    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在解析数据...'
    })

    try {
      // 处理文本数据
      const parsedData = processTxtData(textContent)

      if (parsedData.length === 0) {
        ElMessage.warning('文本中没有有效的Token数据')
        loadingInstance.close()
        return
      }

      // 去重处理
      const { newTokens, duplicateCount } = processAndDeduplicateTokens
        ? processAndDeduplicateTokens(parsedData)
        : processAndDeduplicateTokensInternal(parsedData)

      // 设置不重置页码
      if (shouldResetPage) {
        // 检查shouldResetPage是否为对象（ref对象）
        if (typeof shouldResetPage === 'object' && shouldResetPage !== null) {
          shouldResetPage.value = false
        }
        // 如果是布尔值，不做任何操作
      }

      // 更新Token列表
      tokens.value = newTokens

      let successMsg = `成功导入 ${parsedData.length} 个Token`
      if (duplicateCount > 0) {
        successMsg += `，已自动去除 ${duplicateCount} 个重复Token`
      }

      ElMessage.success(successMsg)

      // 刷新数据
      if (refreshTokens) {
        refreshTokens()
      }

      return {
        success: true,
        count: parsedData.length,
        newTokens
      }
    } catch (error) {
      console.error('解析文本数据失败:', error)
      ElMessage.error('导入失败: ' + (error.message || '无效的数据格式'))
      return {
        success: false,
        error
      }
    } finally {
      loadingInstance.close()
    }
  }

  // 清空所有Token数据
  const clearAllTokens = () => {
    tokens.value = []
    ElMessage.success('已清空所有Token数据')

    // 刷新数据
    if (refreshTokens) {
      refreshTokens()
    }

    return { success: true }
  }

  return {
    // 状态
    fileInputRef,
    selectedFile,

    // 方法
    handleImportToken,
    handleFileInputChange,
    processSelectedFile,
    importFromText,
    clearAllTokens
  }
}