-- 更新备份表，添加token_count列
-- 首先检查列是否存在
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'backups' AND COLUMN_NAME = 'token_count';

-- 如果列不存在，则添加
SET @query = IF(@columnExists = 0, 'ALTER TABLE backups ADD COLUMN token_count INT DEFAULT 0 AFTER task_id', 'SELECT "Column already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
