# API 配置更新说明

本文档提供了如何更新项目中 API URL 配置的指导，以便在 API 地址变更时，只需修改一处即可应用到整个系统。

## 配置文件位置

项目中有两个主要的配置文件：

1. **前端配置文件**: `token-management/src/config/apiConfig.js`
2. **后端配置文件**: `backend/config/externalApiConfig.js`

## 如何更新 API URL

当需要更改外部 API 的基础URL（如从 `http://aaaaa.pxxcp.com` 改为 `http://110.42.32.166:3080`）时，只需按照以下步骤操作：

### 步骤 1: 更新前端配置

打开前端配置文件 `token-management/src/config/apiConfig.js`，将 `BASE_API_URL` 常量的值更改为新的 URL:

```js
// 基础API URL
const BASE_API_URL = 'http://your-new-api-url.com';
```

所有依赖此基础 URL 的端点都会自动更新。

### 步骤 2: 更新后端配置

打开后端配置文件 `backend/config/externalApiConfig.js`，将 `EXTERNAL_API_BASE_URL` 的默认值更改为新的 URL:

```js
// 从环境变量获取API基础URL，如果不存在则使用默认值
const EXTERNAL_API_BASE_URL = process.env.EXTERNAL_API_BASE_URL || 'http://your-new-api-url.com';
```

### 步骤 3: 重启服务

更新配置后，需要重启前端和后端服务以使更改生效：

```bash
# 重启前端开发服务器
cd token-management
npm run dev

# 重启后端服务
cd backend
npm run start
```

## 使用环境变量（推荐）

为避免在代码中硬编码 API URL，推荐使用环境变量：

### 后端环境变量

可以在启动后端服务时设置环境变量：

```bash
# Linux/macOS
EXTERNAL_API_BASE_URL=http://your-new-api-url.com npm run start

# Windows PowerShell
$env:EXTERNAL_API_BASE_URL="http://your-new-api-url.com"; npm run start

# Windows CMD
set EXTERNAL_API_BASE_URL=http://your-new-api-url.com && npm run start
```

或者创建一个 `.env` 文件在后端根目录：

```
EXTERNAL_API_BASE_URL=http://your-new-api-url.com
EXTERNAL_API_USERNAME=your-username
EXTERNAL_API_PASSWORD=your-password
```

### 前端环境变量

对于前端，可以创建 `.env.development` 或 `.env.production` 文件：

```
VITE_API_BASE_URL=http://your-new-api-url.com
```

然后在 `apiConfig.js` 中使用：

```js
const BASE_API_URL = import.meta.env.VITE_API_BASE_URL || 'http://default-api-url.com';
```

## API 端点说明

配置文件中包含以下主要 API 端点：

1. **TOKEN.BULK_UPDATE**: 批量更新数据API（Token置入账号内订单）
2. **TOKEN.GET_UNSOLD**: 加载未卖Token
3. **TOKEN.GET_SOLD**: 加载已卖Token
4. **TOKEN.IMPORT**: 导入Token
5. **TOKEN.TRUNCATE_UNSOLD**: 清空未卖Token

如需添加新的 API 端点，只需在 `API_ENDPOINTS` 对象中添加即可。

## 故障排除

如果更新配置后遇到问题：

1. 确认配置文件中的URL是否正确，包括完整的协议（http/https）
2. 检查服务器是否可以访问新的URL（使用ping或curl测试）
3. 检查浏览器控制台是否有跨域错误
4. 确认URL末尾是否需要斜杠（某些API可能对此敏感）

若有任何问题，请联系系统管理员。 