module.exports = {
  // JWT密钥，实际生产环境应使用环境变量
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
  
  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || '',
    database: process.env.DB_NAME || 'token_management'
  },
  
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  }
}; 