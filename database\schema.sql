-- 创建数据库
CREATE DATABASE IF NOT EXISTS token_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE token_management;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Token表
CREATE TABLE IF NOT EXISTS tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uid VARCHAR(100) NOT NULL UNIQUE,
    token_value TEXT NOT NULL,
    platform VARCHAR(50) NOT NULL,
    description TEXT,
    expires_at TIMESTAMP NULL,
    status ENUM('active', 'expired', 'revoked', 'reset') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 备份表
CREATE TABLE IF NOT EXISTS backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_id VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    backup_type ENUM('manual', 'scheduled', 'automated') DEFAULT 'manual',
    source ENUM('system', 'user', 'api') DEFAULT 'user',
    status ENUM('pending', 'completed', 'failed', 'restored') DEFAULT 'pending',
    task_id VARCHAR(100) NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    scheduled_deletion_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 备份-Token关联表
CREATE TABLE IF NOT EXISTS backup_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_id INT NOT NULL,
    token_id INT NOT NULL,
    token_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE,
    FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE,
    UNIQUE KEY (backup_id, token_id)
);

-- 重置记录表
CREATE TABLE IF NOT EXISTS resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    token_id INT NOT NULL,
    old_value TEXT NOT NULL,
    new_value TEXT NOT NULL,
    reason TEXT,
    reset_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (token_id) REFERENCES tokens(id) ON DELETE CASCADE,
    FOREIGN KEY (reset_by) REFERENCES users(id)
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, setting_key)
);

-- 活动日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    details JSON,
    ip_address VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 添加默认设置
INSERT INTO settings (setting_key, setting_value, setting_type, description, is_system) VALUES
('token_expiry_days', '90', 'number', 'Token 默认过期天数', TRUE),
('enable_token_expiry', 'true', 'boolean', '是否启用 Token 过期功能', TRUE),
('max_tokens_per_user', '1000', 'number', '每个用户最大 Token 数量', TRUE),
('default_backup_retention_days', '30', 'number', '备份默认保留天数', TRUE),
('system_name', 'Token 管理系统', 'string', '系统名称', TRUE),
('enable_notifications', 'true', 'boolean', '是否启用通知功能', TRUE),
('maintenance_mode', 'false', 'boolean', '系统维护模式', TRUE),
('allowed_domains', '[]', 'json', '允许注册的邮箱域名列表 (空表示不限制)', TRUE),
('default_user_role', 'user', 'string', '默认用户角色', TRUE),
('enable_password_reset', 'true', 'boolean', '是否启用密码重置功能', TRUE);

-- 添加管理员账户
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2b$10$RnJsISTsIxnQggmPGgtg9ek8QnIFp8NVewf0hR4HgVr.mhVM4LNNq', 'admin');
-- 默认密码：admin123 