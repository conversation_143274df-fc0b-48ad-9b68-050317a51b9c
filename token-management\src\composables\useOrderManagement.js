import { ref, computed } from 'vue'
import { ElMessage, ElLoading, ElMessageBox, ElNotification } from 'element-plus'
import * as tokenService from '@/services/tokenService'
import * as orderService from '@/services/orderService'

/**
 * 处理订单管理的组合式函数
 * 包括查询、删除订单等功能
 */
export default function useOrderManagement(options = {}) {
  // 从选项中获取token表格相关的状态
  const {
    tokens = ref([]),
    selectedTokens = ref([]),
    updateTokensInfo,
    refreshTokens,
    shouldResetPage = ref(true)
  } = options

  // 订单相关状态
  const ordersDialogVisible = ref(false)
  const currentOrders = ref([])
  const currentTokenUid = ref('')
  const orderDetailDialogVisible = ref(false)
  const currentOrderDetail = ref(null)
  const orderTableRef = ref(null)
  const selectedOrdersInList = ref([])

  // 计算可删除的订单数量
  const deletableOrdersCount = computed(() => {
    return currentOrders.value.filter(order => isDeletableOrder(order.status)).length
  })

  /**
   * 查询Token订单信息
   */
  const handleQueryOrder = async () => {
    // 检查是否有选中的token
    if (selectedTokens.value.length === 0) {
      ElMessage.warning('请先选择要查询的Token')
      return
    }

    try {
      // 提示用户确认操作
      await ElMessageBox.confirm(
        `确定要查询${selectedTokens.value.length}个选中Token的订单信息吗？`,
        '查询订单信息',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 用户确认后，开始查询
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在查询订单信息...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 保存原始状态，以便在失败时恢复
      const originalTokens = JSON.parse(JSON.stringify(tokens.value))

      try {
        // 提取选中token的信息
        const selectedTokenData = selectedTokens.value.map(token => ({
          uid: token.uid,
          token: token.token
        }))

        // 调用API进行查询
        const result = await tokenService.queryTokenOrderInfo(selectedTokenData)

        if (result.success) {
          // 更新token的订单信息
          const updatedTokens = tokens.value.map(token => {
            const orderResult = result.results.find(r => r.uid === token.uid)
            if (orderResult && orderResult.success) {
              return {
                ...token,
                // 同时更新token的主status属性，使在线状态在主表格中显示
                status: orderResult.status || token.status,
                orderInfo: {
                  isOnline: orderResult.isOnline,
                  status: orderResult.status || '未知',
                  orderCount: orderResult.data?.orderCount || 0,
                  orders: orderResult.data?.orders || [],
                  message: orderResult.message
                }
              }
            }
            return {
              ...token,
              // 如果查询失败也要更新主状态
              status: orderResult?.status || token.status,
              orderInfo: orderResult ? {
                isOnline: orderResult.isOnline || false,
                status: orderResult.status || '未知',
                orderCount: 0,
                orders: [],
                message: orderResult.message || '查询失败'
              } : null
            }
          })

          // 设置不重置页码的标志
          shouldResetPage.value = false

          // 更新tokens并保持当前页码
          if (updateTokensInfo) {
            updateTokensInfo(updatedTokens)
          } else {
            tokens.value = updatedTokens
          }

          // 统计结果
          const onlineCount = result.results.filter(r => r.status === '在线').length
          const offlineCount = result.results.filter(r => r.status === '掉线').length
          const hasOrderCount = result.results.filter(r => r.success && r.status === '在线' && r.data && r.data.orderCount > 0).length

          ElNotification({
            title: '查询成功',
            message: `共处理${result.stats.total}个Token，其中${onlineCount}个在线，${hasOrderCount}个有订单`,
            type: 'success',
            duration: 5000
          })

          // 创建统计信息
          const statsHtml = `
            <div style="text-align: center; margin-top: 10px;">
              <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.stats.total} 个Token</div>
              <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                  <span style="margin-right: 5px;">在线</span>
                  <span style="font-size: 18px;">${onlineCount}</span>
                </div>
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                  <span style="margin-right: 5px;">掉线</span>
                  <span style="font-size: 18px;">${offlineCount}</span>
                </div>
                <div style="padding: 10px 15px; border-radius: 4px; background-color: #e6f2ff; color: #409EFF; font-weight: bold;">
                  <span style="margin-right: 5px;">有订单</span>
                  <span style="font-size: 18px;">${hasOrderCount}</span>
                </div>
              </div>
              <div style="font-size: 14px; color: #409EFF;">订单信息已更新到表格中，点击"订单数量"可查看详细信息</div>
            </div>
          `

          // 显示查询结果统计
          ElMessageBox.alert(statsHtml, '查询结果', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            callback: () => {
              // 刷新表格
              if (refreshTokens) {
                refreshTokens()
              }
            }
          })
        } else {
          // 查询失败，恢复原始状态
          tokens.value = originalTokens
          ElMessage.error(`查询失败: ${result.message || '未知错误'}`)
        }
      } catch (error) {
        console.error('查询订单信息异常:', error)
        // 恢复原始状态
        tokens.value = originalTokens
        ElMessage.error(`查询异常: ${error.message || '未知错误'}`)
      } finally {
        // 关闭加载提示
        loadingInstance.close()
      }
    } catch (e) {
      // 用户取消操作
      if (e !== 'cancel') {
        console.error('确认对话框异常:', e)
      }
    }
  }

  /**
   * 处理查看订单
   */
  const handleViewOrders = (row) => {
    if (row.orderInfo && row.orderInfo.orders && row.orderInfo.orders.length > 0) {
      // 为每个订单添加所属token的信息，以便支持订单详情页中的删除功能
      currentOrders.value = row.orderInfo.orders.map(order => ({
        ...order,
        uid: row.uid,
        tokenValue: row.token
      }))
      currentTokenUid.value = row.uid
      ordersDialogVisible.value = true
    } else {
      ElMessage.warning('没有找到订单信息')
    }
  }

  /**
   * 查看单个订单详情
   */
  const viewOrderDetail = (order) => {
    // 传递订单所属token的uid和token值，以便能够删除订单
    if (order) {
      const tokenInfo = order.tokenInfo || {}
      currentOrderDetail.value = {
        ...order,
        uid: order.uid || tokenInfo.uid,
        tokenValue: order.tokenValue || tokenInfo.token
      }
      orderDetailDialogVisible.value = true
    }
  }

  /**
   * 判断订单是否可删除（允许已评价、交易成功、交易已取消、未发货和退款成功的订单）
   */
  const isDeletableOrder = (status) => {
    if (!status) return false

    // 严格匹配"已评价"状态
    if (status === '已评价') {
      return true
    }

    // 严格匹配"交易成功"状态
    if (status === '交易成功') {
      return true
    }

    // 严格匹配"交易已取消"状态
    if (status === '交易已取消') {
      return true
    }

    // 严格匹配"未发货"状态
    if (status === '未发货') {
      return true
    }

    // 严格匹配"退款成功"状态
    if (status === '退款成功') {
      return true
    }

    // 包含"已评价"的状态也可能可以删除
    if (status.includes('已评价')) {
      return true
    }

    // 包含"交易成功"的状态也可能可以删除
    if (status.includes('交易成功')) {
      return true
    }

    // 包含"交易已取消"的状态也可能可以删除
    if (status.includes('交易已取消')) {
      return true
    }

    // 包含"未发货"的状态也可能可以删除
    if (status.includes('未发货')) {
      return true
    }

    // 包含"退款成功"的状态也可能可以删除
    if (status.includes('退款成功')) {
      return true
    }

    // 其他状态不可删除
    return false
  }

  /**
   * 获取可删除的订单数量
   */
  const getDeletableCount = (row) => {
    if (!row.orderInfo || !row.orderInfo.orders) return 0
    return row.orderInfo.orders.filter(order => isDeletableOrder(order.status)).length
  }

  /**
   * 批量删除选中的订单
   */
  const handleDeleteOrder = async () => {
    // 检查是否有选中的token
    if (selectedTokens.value.length === 0) {
      ElMessage.warning('请先选择要删除订单的Token')
      return
    }

    // 询问用户是否确认删除
    try {
      await ElMessageBox.confirm(
        `确定要删除选中Token的订单吗？系统将自动查询并并发删除所有可删除的订单。`,
        '批量删除订单确认',
        {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch (error) {
      return // 用户取消操作
    }

    // 显示加载中状态
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单..',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 使用并发查询和删除，不再显示进度
      const result = await orderService.concurrentQueryAndDeleteOrders(
        selectedTokens.value,
        {
          batchSize: 20, // 每批20个订单
          concurrency: 5, // 5个并发请求
        }
      )

      // 处理结果
      if (result.success) {
        // 检查是否有成功删除的订单
        const successCount = result.results.filter(r => r.success).length
        const failCount = result.results.length - successCount

        // 更新本地数据，从订单列表中移除已删除的订单
        updateLocalOrdersAfterDeletion(result.results)

        // 处理查询结果，更新UI显示
        if (result.queriedTokens && result.queriedTokens.length > 0) {
          // 更新已查询但没有可删除订单的Token状态
          updateTokensAfterQuery(result.queriedTokens)
        }

        // 使用通知显示结果摘要
        if (successCount > 0) {
          ElNotification({
            title: '删除订单完成',
            message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
            type: 'success',
            duration: 5000
          })
        } else {
          // 所有删除都失败的情
          ElNotification({
            title: '删除订单未成功',
            message: `未能成功删除任何订单${result.queriedTokens ? `，已查询 ${result.queriedTokens.length} 个Token` : ''}。请检查订单状态是否为"已评价"、"交易成功"、"交易已取消"、"未发货"或"退款成功"。`,
            type: 'warning',
            duration: 5000
          })
        }

        // 保证不重置页码的情况下刷新表格数据
        shouldResetPage.value = false
        if (refreshTokens) {
          refreshTokens()
        }
      } else {
        // API调用失败
        ElMessage.error(result.message || '查询或删除订单失败')

        // 显示详细错误信息
        ElMessageBox.alert(
          `<div style="text-align: left;">
            <p><strong>错误信息:</strong> ${result.message || '未知错误'}</p>
            <p style="margin-top: 10px; font-size: 12px; color: #909399;">
              请确认：
              <ul>
                <li>Token是否在线</li>
                <li>Token权限是否有效</li>
                <li>订单状态是否为"已评价"</li>
              </ul>
            </p>
          </div>`,
          '删除订单失败',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            type: 'error'
          }
        )
      }
    } catch (error) {
      console.error('删除订单错误:', error)

      // 更详细的错误提示
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`)

      // 显示技术细节
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <p><strong>错误信息:</strong> ${error.message || '未知错误'}</p>
          <p style="margin-top: 10px; font-size: 12px; color: #909399;">
            可能的原因：
            <ul>
              <li>网络连接问题</li>
              <li>服务器响应超时</li>
              <li>API限流或权限问题</li>
            </ul>
          </p>
        </div>`,
        '系统错误',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          type: 'error'
        }
      )

      // 更新token的订单状态为失败
      selectedTokens.value.forEach(token => {
        if (token.orderDeletionStatus) {
          token.orderDeletionStatus.inProgress = false
        }
      })
    } finally {
      // 关闭加载提示
      loadingInstance.close()
    }
  }

  /**
   * 处理单个订单删除
   */
  const handleDeleteSingleOrder = async (orderDetail) => {
    if (!orderDetail || !orderDetail.uid || !orderDetail.tokenValue || !orderDetail.orderId) {
      ElMessage.warning('订单信息不完整，无法删除')
      return
    }

    // 确认是否可删除
    if (!isDeletableOrder(orderDetail.status)) {
      ElMessage.warning(`只能删除已评价、交易成功、交易已取消、未发货或退款成功的订单，当前订单状态为"${orderDetail.status}"`)
      return
    }

    try {
      // 确认删除
      await ElMessageBox.confirm(
        `确定要删除订单 ${orderDetail.orderId} 吗？此操作不可恢复！`,
        '删除订单确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 显示loading
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在删除订单...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 调用API删除订单
        const result = await tokenService.deleteOrder(
          orderDetail.uid,
          orderDetail.tokenValue,
          orderDetail.orderId
        )

        if (result.success) {
          // 删除成功，关闭订单详情对话框
          orderDetailDialogVisible.value = false

          // 更新本地数据
          updateLocalOrdersAfterDeletion([{
            success: true,
            orderSn: orderDetail.orderId
          }])

          ElMessage.success('订单删除成功')

          // 如果主订单列表对话框也打开，更新它
          if (ordersDialogVisible.value && currentOrders.value.length > 0) {
            // 从当前列表中过滤掉已删除的订单
            currentOrders.value = currentOrders.value.filter(
              order => order.orderId !== orderDetail.orderId
            )
          }
        } else {
          // 增强错误信息显示
          const errorMsg = result.message || '删除失败'
          ElMessage.error(errorMsg)

          // 构建更详细的错误提示
          let detailsHtml = `
            <div style="text-align: left; margin: 15px 0;">
              <h3 style="margin-bottom: 10px;">删除失败详情</h3>
              <div style="color: #f56c6c;">
                <p>${errorMsg}${result.errorCode ? ` (错误码: ${result.errorCode})` : ''}</p>
              </div>

              <div style="margin-top: 15px; font-size: 14px;">
                <p>可能的原因：</p>
                <ul style="padding-left: 20px; margin-top: 5px;">
                  <li>订单状态不是"已评价"(拼多多要求必须是严格的"已评价")</li>
                  <li>Token已失效或没有足够权限</li>
                  <li>网络问题或API限流</li>
                </ul>
              </div>
          `

          if (result.responseData) {
            detailsHtml += `
              <div style="margin-top: 15px;">
                <details>
                  <summary>API响应详情</summary>
                  <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(result.responseData, null, 2)}</pre>
                </details>
              </div>
            `
          }

          detailsHtml += `</div>`

          // 显示详细错误
          ElMessageBox.alert(detailsHtml, '删除失败', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定'
          })
        }
      } finally {
        loadingInstance.close()
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除订单错误:', error)
        ElMessage.error(`操作异常: ${error.message || '未知错误'}`)
      }
    }
  }

  /**
   * 一键删除单个token的所有订单
   */
  const handleQuickDeleteOrder = async (token) => {
    // 防止重复点击
    if (token.orderDeleteLoading) return

    // 设置loading状态
    token.orderDeleteLoading = true

    try {
      // 询问用户是否确认删除
      await ElMessageBox.confirm(
        `确定要删除此Token的订单吗？将会自动查询并使用并发模式删除所有可删除的订单。`,
        '删除订单确认',
        {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 显示加载中状态
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在处理订单...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 使用并发查询和删除
        const result = await orderService.concurrentQueryAndDeleteOrders(
          [token],
          {
            batchSize: 20, // 每批20个订单
            concurrency: 5, // 5个并发请求
          }
        )

        if (result.success) {
          // 检查是否有成功删除的订单
          const successCount = result.results.filter(r => r.success).length
          const failCount = result.results.length - successCount

          // 更新本地数据
          updateLocalOrdersAfterDeletion(result.results)

          // 处理查询结果，更新UI显示
          if (result.queriedTokens && result.queriedTokens.length > 0) {
            // 更新已查询但没有可删除订单的Token状态
            updateTokensAfterQuery(result.queriedTokens)
          }

          // 使用通知显示结果
          if (successCount > 0) {
            ElNotification({
              title: '删除订单完成',
              message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
              type: 'success',
              duration: 5000
            })
          } else if (result.results.length > 0) {
            // 有订单但全部删除失败
            ElNotification({
              title: '删除订单未成功',
              message: `未能成功删除任何订单${failCount} 个删除失败`,
              type: 'warning',
              duration: 5000
            })
          } else {
            // 无可删除订单
            ElNotification({
              title: '无可删除订单',
              message: '查询完成，但没有找到可删除的订单（只有已评价、交易成功、交易已取消、未发货或退款成功的订单可以删除）',
              type: 'info',
              duration: 5000
            })
          }

          // 强制刷新表格数据
          if (refreshTokens) {
            refreshTokens()
          }
        } else {
          // API调用失败
          ElMessage.error(result.message || '操作失败')
        }
      } finally {
        loadingInstance.close()
        token.orderDeleteLoading = false
      }
    } catch (error) {
      token.orderDeleteLoading = false
      if (error !== 'cancel') {
        console.error('删除订单错误:', error)
        ElMessage.error(`操作异常: ${error.message || '未知错误'}`)
      }
    }
  }

  /**
   * 全选可删除的订单
   */
  const selectAllDeletableOrders = () => {
    // 先清除所有选择
    orderTableRef.value?.clearSelection()

    // 然后选中所有可删除的订单
    currentOrders.value.forEach(order => {
      if (isDeletableOrder(order.status)) {
        orderTableRef.value?.toggleRowSelection(order, true)
      }
    })
  }

  /**
   * 处理订单列表中的选择变化
   */
  const handleOrderSelectionChange = (selection) => {
    selectedOrdersInList.value = selection
  }

  /**
   * 批量删除选中的订单
   */
  const batchDeleteSelectedOrders = async () => {
    if (selectedOrdersInList.value.length === 0) {
      ElMessage.warning('请先选择要删除的订单')
      return
    }

    try {
      // 确认是否删除
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedOrdersInList.value.length} 个订单吗？`,
        '删除订单确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 显示loading
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在删除订单...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 构建删除请求数据
        const orderItems = selectedOrdersInList.value.map(order => ({
          uid: order.uid,
          token: order.tokenValue,
          orderSn: order.orderId
        }))

        // 调用批量删除API
        const result = await tokenService.batchDeleteOrders(orderItems)

        if (result.success) {
          // 统计结果
          const successCount = result.results.filter(r => r.success).length
          const failCount = result.results.length - successCount

          // 更新本地数据，从订单列表中移除已删除的订单
          updateLocalOrdersAfterDeletion(result.results)

          // 清除表格选择
          selectedOrdersInList.value = []
          orderTableRef.value?.clearSelection()

          // 使用通知显示详细结果
          ElNotification({
            title: '删除订单完成',
            message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
            type: 'success',
            duration: 5000
          })

          // 强制刷新表格数据
          if (refreshTokens) {
            refreshTokens()
          }
        } else {
          ElMessage.error(`批量删除失败: ${result.message || '未知错误'}`)
        }
      } finally {
        loadingInstance.close()
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除订单错误:', error)
        ElMessage.error(`操作异常: ${error.message || '未知错误'}`)
      }
    }
  }

  /**
   * 更新已查询但没有可删除订单的Token状态
   */
  const updateTokensAfterQuery = (queriedTokens) => {
    if (!queriedTokens || queriedTokens.length === 0) return

    console.log('更新已查询Token状态:', queriedTokens)

    // 设置不重置页码的标志
    shouldResetPage.value = false

    // 更新查询到的token信息
    const updatedTokens = tokens.value.map(token => {
      // 查找当前token是否在查询结果中
      const queriedToken = queriedTokens.find(qt => qt.uid === token.uid)

      if (queriedToken) {
        // 为token添加查询结果
        return {
          ...token,
          orderInfo: {
            isOnline: queriedToken.isOnline !== undefined ? queriedToken.isOnline : true,
            status: queriedToken.status || '在线',
            orderCount: queriedToken.orders?.length || 0,
            orders: queriedToken.orders || [],
            message: '查询成功'
          }
        }
      }

      return token
    })

    // 使用外部提供的更新方法或直接更新
    if (updateTokensInfo) {
      updateTokensInfo(updatedTokens)
    } else {
      tokens.value = updatedTokens
    }
  }

  /**
   * 更新本地订单数据（删除后）
   */
  const updateLocalOrdersAfterDeletion = (results) => {
    // 设置不重置页码的标志，防止删除订单后跳转到第一页
    shouldResetPage.value = false

    // 分离成功和失败的结果
    const successfullyDeletedOrders = results
      .filter(r => r.success)
      .map(r => r.orderSn)

    const failedDeletes = results.filter(r => !r.success)

    // 输出错误信息
    if (failedDeletes.length > 0) {
      console.warn('删除失败的订单:', failedDeletes)

      // 提取错误消息并统计
      const errorMessages = {}
      failedDeletes.forEach(result => {
        const msg = result.message || '未知错误'
        errorMessages[msg] = (errorMessages[msg] || 0) + 1
      })

      // 构建错误信息HTML
      let errorHtml = `
        <div style="margin-top: 15px;">
          <div style="font-weight: bold; color: #f56c6c; margin-bottom: 10px;">删除失败原因:</div>
          <ul style="text-align: left; margin: 0; padding-left: 20px;">
      `

      for (const [message, count] of Object.entries(errorMessages)) {
        errorHtml += `<li>${message} (${count}个订单)</li>`
      }

      errorHtml += `
          </ul>
          <div style="margin-top: 10px; font-size: 12px; color: #909399;">
            常见原因：订单未评价、Token已失效或者无权限操作
          </div>
        </div>
      `

      // 显示错误信息
      if (failedDeletes.length > 0) {
        ElMessageBox.alert(
          errorHtml,
          '订单删除失败详情',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            type: 'warning'
          }
        )
      }
    }

    if (successfullyDeletedOrders.length === 0) {
      // 如果没有成功删除任何订单，则显示提示并返回
      if (results.length > 0) {
        ElMessage.warning('没有成功删除任何订单，请查看失败详情')
      }
      return
    }

    console.log('开始更新本地订单数据，成功删除的订单数:', successfullyDeletedOrders.length)

    // 更新token的订单列表
    const updatedTokens = tokens.value.map(token => {
      if (token.orderInfo && token.orderInfo.orders) {
        // 计算实际被删除的订单数量（通过比较原始订单和剩余订单）
        const originalOrders = token.orderInfo.orders
        const updatedOrders = originalOrders.filter(
          order => !successfullyDeletedOrders.includes(order.orderId)
        )

        // 计算本次实际删除的订单数量
        const deletedCount = originalOrders.length - updatedOrders.length
        console.log(`Token ${token.uid}: 原始订单数量=${originalOrders.length}, 更新后订单数量=${updatedOrders.length}, 删除数量=${deletedCount}`)

        // 只在有订单被删除时更新
        if (deletedCount > 0) {
          // 确保保留原始orderInfo中的其他字段
          const updatedOrderInfo = {
            ...token.orderInfo,
            orders: updatedOrders,
            orderCount: updatedOrders.length,  // 确保orderCount为实际剩余订单数量
            // 保留以下字段确保UI显示正常
            isOnline: token.orderInfo.isOnline,
            status: token.orderInfo.status
          }

          // 更新删除状态
          const updatedDeletionStatus = {
            totalOrders: originalOrders.length,
            successCount: deletedCount,
            failCount: 0,
            inProgress: false
          }

          // 返回更新后的token对象
          return {
            ...token,
            orderInfo: updatedOrderInfo,
            orderDeletionStatus: updatedDeletionStatus,
            // 更新currentDisplayOrders和deletableOrders
            currentDisplayOrders: updatedOrders,
            deletableOrders: updatedOrders.filter(order => isDeletableOrder(order.status))
          }
        }
      }
      return token
    })

    // 更新tokens
    if (updateTokensInfo) {
      updateTokensInfo(updatedTokens)
    } else {
      tokens.value = updatedTokens
    }

    // 更新当前显示的订单列表
    if (currentOrders.value.length > 0) {
      currentOrders.value = currentOrders.value.filter(
        order => !successfullyDeletedOrders.includes(order.orderId)
      )
    }
  }

  /**
   * 获取订单状态标签类型
   */
  const getOrderStatusTag = (status) => {
    if (!status) return 'info'
    if (status.includes('待收款') || status.includes('待发货')) return 'warning'
    if (status.includes('已收款') || status.includes('已完结')) return 'success'
    if (status.includes('已取件') || status.includes('已关闭')) return 'danger'
    return 'info'
  }

  return {
    // 状态
    ordersDialogVisible,
    currentOrders,
    currentTokenUid,
    orderDetailDialogVisible,
    currentOrderDetail,
    orderTableRef,
    selectedOrdersInList,

    // 计算属性
    deletableOrdersCount,

    // 方法
    handleQueryOrder,
    handleViewOrders,
    viewOrderDetail,
    isDeletableOrder,
    getDeletableCount,
    handleDeleteOrder,
    handleDeleteSingleOrder,
    handleQuickDeleteOrder,
    selectAllDeletableOrders,
    handleOrderSelectionChange,
    batchDeleteSelectedOrders,
    getOrderStatusTag
  }
}