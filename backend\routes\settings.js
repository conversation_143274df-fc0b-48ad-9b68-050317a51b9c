const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const SettingsController = require('../controllers/settings');

// 获取系统设置
router.get('/', authenticate, SettingsController.getAllSettings);

// 获取特定设置
router.get('/:key', authenticate, SettingsController.getSettingByKey);

// 管理员：更新设置
router.put('/:key', authenticate, isAdmin, SettingsController.updateSetting);

// 管理员：批量更新设置
router.post('/batch-update', authenticate, isAdmin, SettingsController.batchUpdateSettings);

// 获取当前系统配置
router.get('/system/config', authenticate, isAdmin, SettingsController.getSystemConfig);

// 管理员：更新系统配置
router.put('/system/config', authenticate, isAdmin, SettingsController.updateSystemConfig);

// 获取用户个人设置
router.get('/user/preferences', authenticate, SettingsController.getUserPreferences);

// 更新用户个人设置
router.put('/user/preferences', authenticate, SettingsController.updateUserPreferences);

module.exports = router; 