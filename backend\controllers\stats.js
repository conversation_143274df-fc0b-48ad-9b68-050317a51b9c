const Token = require('../models/token');
const Backup = require('../models/backup');
const Reset = require('../models/reset');
const User = require('../models/user');
const ActivityLog = require('../models/activityLog');
const { logger } = require('../utils/logger');
const { exportToCSV, exportToExcel } = require('../utils/exportUtils');

class StatsController {
  /**
   * 获取仪表盘统计数据
   */
  static async getDashboardStats(req, res, next) {
    try {
      // 获取各模块的统计数据
      const tokenStats = await Token.getStats();
      const backupStats = await Backup.getStats();
      const resetStats = await Reset.getStats();
      
      // 汇总数据
      const dashboardStats = {
        tokens: {
          total: tokenStats.total,
          active: tokenStats.statusDistribution.active || 0,
          expired: tokenStats.statusDistribution.expired || 0,
          inactive: tokenStats.statusDistribution.inactive || 0,
          nextExpiry: tokenStats.nextExpiry
        },
        backups: {
          total: backupStats.total,
          byType: backupStats.typeDistribution,
          todayCount: backupStats.todayCount
        },
        resets: {
          total: resetStats.total,
          todayCount: resetStats.todayCount,
          byPlatform: resetStats.platformDistribution
        }
      };
      
      // 获取过去7天的趋势数据
      const trends = await this.getTrendsData(7);
      dashboardStats.trends = trends;
      
      res.json({
        success: true,
        data: dashboardStats
      });
    } catch (error) {
      logger.error(`获取仪表盘统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取token统计数据
   */
  static async getTokenStats(req, res, next) {
    try {
      const stats = await Token.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取token统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取备份统计数据
   */
  static async getBackupStats(req, res, next) {
    try {
      const stats = await Backup.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取备份统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取重置统计数据
   */
  static async getResetStats(req, res, next) {
    try {
      const stats = await Reset.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取重置统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取用户统计数据
   */
  static async getUserStats(req, res, next) {
    try {
      const stats = await User.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取用户统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取活动日志统计数据
   */
  static async getActivityStats(req, res, next) {
    try {
      const stats = await ActivityLog.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取活动日志统计数据失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取过去N天的活动趋势
   */
  static async getTrends(req, res, next) {
    try {
      const { days } = req.params;
      const numDays = parseInt(days) || 7;
      
      // 限制查询天数
      if (numDays <= 0 || numDays > 30) {
        return res.status(400).json({
          success: false,
          message: '天数必须在1到30之间'
        });
      }
      
      const trends = await this.getTrendsData(numDays);
      
      res.json({
        success: true,
        data: trends
      });
    } catch (error) {
      logger.error(`获取趋势数据失败: ${error.message}`);
      next(error);
    }
  }
  
  /**
   * 获取趋势数据的辅助方法
   */
  static async getTrendsData(days) {
    // 获取过去N天的日期
    const dates = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }
    
    // 获取各类数据的趋势
    const tokenTrends = await Token.getTrends(days);
    const backupTrends = await Backup.getTrends(days);
    const resetTrends = await Reset.getTrends(days);
    
    // 合并趋势数据
    return {
      dates,
      tokens: tokenTrends,
      backups: backupTrends,
      resets: resetTrends
    };
  }

  /**
   * 导出统计数据
   */
  static async exportStats(req, res, next) {
    try {
      const { type, format } = req.params;
      
      let dataToExport = [];
      let fileName = '';
      let sheetName = '';
      
      // 根据类型获取不同的统计数据
      switch (type) {
        case 'tokens':
          const tokenStats = await Token.getStats();
          dataToExport = this.formatTokenStatsForExport(tokenStats);
          fileName = 'token_stats';
          sheetName = 'Token统计';
          break;
        case 'backups':
          const backupStats = await Backup.getStats();
          dataToExport = this.formatBackupStatsForExport(backupStats);
          fileName = 'backup_stats';
          sheetName = 'Backup统计';
          break;
        case 'resets':
          const resetStats = await Reset.getStats();
          dataToExport = this.formatResetStatsForExport(resetStats);
          fileName = 'reset_stats';
          sheetName = 'Reset统计';
          break;
        case 'users':
          const userStats = await User.getStats();
          dataToExport = this.formatUserStatsForExport(userStats);
          fileName = 'user_stats';
          sheetName = 'User统计';
          break;
        case 'dashboard':
          // 获取仪表盘汇总数据
          const tokenDashStats = await Token.getStats();
          const backupDashStats = await Backup.getStats();
          const resetDashStats = await Reset.getStats();
          dataToExport = this.formatDashboardStatsForExport(tokenDashStats, backupDashStats, resetDashStats);
          fileName = 'dashboard_stats';
          sheetName = '仪表盘统计';
          break;
        default:
          return res.status(400).json({
            success: false,
            message: '不支持的统计类型'
          });
      }
      
      // 完整文件名
      fileName = `${fileName}_${new Date().toISOString().slice(0, 10)}`;
      
      // 根据格式导出
      if (format === 'csv') {
        const csv = await exportToCSV(dataToExport);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.csv`);
        return res.send(csv);
      } else if (format === 'excel') {
        const excel = await exportToExcel(dataToExport, sheetName);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        return res.send(excel);
      } else {
        return res.status(400).json({
          success: false,
          message: '不支持的导出格式，请使用csv或excel'
        });
      }
    } catch (error) {
      logger.error(`导出统计数据失败: ${error.message}`);
      next(error);
    }
  }
  
  /**
   * 格式化Token统计数据用于导出
   */
  static formatTokenStatsForExport(stats) {
    const data = [];
    
    // 基本统计
    data.push({ 类别: 'Token总数', 数量: stats.total });
    
    // 状态分布
    Object.entries(stats.statusDistribution || {}).forEach(([status, count]) => {
      data.push({ 类别: `状态: ${status}`, 数量: count });
    });
    
    // 平台分布
    Object.entries(stats.platformDistribution || {}).forEach(([platform, count]) => {
      data.push({ 类别: `平台: ${platform}`, 数量: count });
    });
    
    return data;
  }
  
  /**
   * 格式化Backup统计数据用于导出
   */
  static formatBackupStatsForExport(stats) {
    const data = [];
    
    // 基本统计
    data.push({ 类别: 'Backup总数', 数量: stats.total });
    data.push({ 类别: '今日创建', 数量: stats.todayCount });
    
    // 类型分布
    Object.entries(stats.typeDistribution || {}).forEach(([type, count]) => {
      data.push({ 类别: `类型: ${type}`, 数量: count });
    });
    
    // 来源分布
    Object.entries(stats.sourceDistribution || {}).forEach(([source, count]) => {
      data.push({ 类别: `来源: ${source}`, 数量: count });
    });
    
    return data;
  }
  
  /**
   * 格式化Reset统计数据用于导出
   */
  static formatResetStatsForExport(stats) {
    const data = [];
    
    // 基本统计
    data.push({ 类别: 'Reset总数', 数量: stats.total });
    data.push({ 类别: '今日重置', 数量: stats.todayCount });
    
    // 平台分布
    Object.entries(stats.platformDistribution || {}).forEach(([platform, count]) => {
      data.push({ 类别: `平台: ${platform}`, 数量: count });
    });
    
    return data;
  }
  
  /**
   * 格式化User统计数据用于导出
   */
  static formatUserStatsForExport(stats) {
    const data = [];
    
    // 基本统计
    data.push({ 类别: '用户总数', 数量: stats.total });
    data.push({ 类别: '今日注册', 数量: stats.todayRegistered });
    data.push({ 类别: '活跃用户(30天)', 数量: stats.activeUsers });
    
    // 角色分布
    Object.entries(stats.roleDistribution || {}).forEach(([role, count]) => {
      data.push({ 类别: `角色: ${role}`, 数量: count });
    });
    
    // 状态分布
    Object.entries(stats.statusDistribution || {}).forEach(([status, count]) => {
      data.push({ 类别: `状态: ${status}`, 数量: count });
    });
    
    return data;
  }
  
  /**
   * 格式化Dashboard统计数据用于导出
   */
  static formatDashboardStatsForExport(tokenStats, backupStats, resetStats) {
    const data = [];
    
    // Token统计
    data.push({ 板块: 'Token', 类别: '总数', 数量: tokenStats.total });
    data.push({ 板块: 'Token', 类别: '活跃', 数量: tokenStats.statusDistribution.active || 0 });
    data.push({ 板块: 'Token', 类别: '过期', 数量: tokenStats.statusDistribution.expired || 0 });
    data.push({ 板块: 'Token', 类别: '禁用', 数量: tokenStats.statusDistribution.inactive || 0 });
    
    // Backup统计
    data.push({ 板块: 'Backup', 类别: '总数', 数量: backupStats.total });
    data.push({ 板块: 'Backup', 类别: '今日创建', 数量: backupStats.todayCount });
    
    // Reset统计
    data.push({ 板块: 'Reset', 类别: '总数', 数量: resetStats.total });
    data.push({ 板块: 'Reset', 类别: '今日重置', 数量: resetStats.todayCount });
    
    return data;
  }
}

module.exports = StatsController; 