POST https://mobile.yangkeduo.com/proxy/api/api/vancouver/parent_order_cancel?pdduid=9017220962187 HTTP/1.1
Accept: application/json, text/plain, */*
Accept-Language: zh-CN,zh;q=0.9
Accesstoken: JGB6EALCANWZ2UP3XTU75GRZQV24J6UZXNMPJW46Q6ULQLFGQODQ1216a75
Cache-Control: no-cache
Connection: Keep-Alive
Content-Length: 97
Content-Type: application/json;charset=UTF-8
Host: mobile.yangkeduo.com
Origin: https://mobile.yangkeduo.com
Pragma: no-cache
Referer: https://mobile.yangkeduo.com/proxy/api/api/vancouver/parent_order_cancel?pdduid=9017220962187
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36

{"parent_order_sn":"JGB6EALCANWZ2UP3XTU75GRZQV24J6UZXNMPJW46Q6ULQLFGQODQ1216a75","cancel_type":1}


=============================返回


HTTP/1.1 403 Forbidden
Access-Control-Allow-Credentials: true
Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, x-cos-meta-object-set-callback, Anti-Content, Etag, chiru-org, user-type, host-id, client-type, corpaccesstoken, rctk, rctk-sign, ddjb-sign, ddjb-app-sign, Ws-Proxy-Uin
Access-Control-Allow-Methods: GET, POST, OPTIONS, DELETE, PUT
Access-Control-Allow-Origin: https://mobile.yangkeduo.com
Connection: keep-alive
Content-Security-Policy-Report-Only: default-src 'none';script-src 'report-sample';report-uri https://tc.pinduoduo.com/sec.gif
Content-Type: application/json;charset=UTF-8
Date: Tue, 01 Apr 2025 14:10:43 GMT
Server: nginx
Set-Cookie: api_uid=CiPQUGfr8+NgSwENk98aAg==; expires=Thu, 31-Dec-37 23:55:55 GMT; domain=.yangkeduo.com; path=/
Vary: Accept-Encoding
Vary: Accept-Encoding
Vary: Accept-Encoding
Vary: Origin
cip: **************
gw-ext: glbver=2
strict-transport-security: max-age=600
x-b3-trace: false
x-yak-request-id: 1743516643810-483c31aa35f282f896dd47d89a302d1e
yak-timeinfo: 1743516643810|5

{"server_time":1743516643,"error_code":45001,"error_msg":"","error_payload":{"view_style":"toast","view_object":{"title":"当前订单不可取消，请确认订单信息后重新提交","message":null,"detail":null,"on_show":null,"ok_label":null,"on_ok":null,"on_cancel":null,"cancel_label":null}}}