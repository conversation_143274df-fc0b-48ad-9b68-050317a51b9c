const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const { logger } = require('../utils/logger');

dotenv.config();

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'qq@666666',
  database: process.env.DB_NAME || 'token_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    logger.info('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    logger.error(`数据库连接失败: ${error.message}`);
    return false;
  }
};

// 初始化测试连接
testConnection();

module.exports = {
  pool,
  testConnection
}; 