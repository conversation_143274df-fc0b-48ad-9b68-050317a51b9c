<template>
  <div class="token-reset-container">
    <el-card class="token-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h2 class="card-title">Token重置</h2>
            <el-tag type="info" effect="plain" class="token-count">{{ filteredCount }}/{{ totalTokenCount }}</el-tag>
          </div>

          <!-- 过滤和搜索区域 -->
          <div class="header-right">
            <div class="filter-container">
              <el-select
                v-model="statusFilter"
                placeholder="状态筛选"
                clearable
                class="filter-select"
                @change="handleFilterChange"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-tag :type="getStatusType(item.value)" effect="light" size="small">
                    {{ item.label }}
                  </el-tag>
                </el-option>
              </el-select>

              <el-select
                v-model="userFilter"
                placeholder="用户筛选"
                clearable
                class="filter-select"
                filterable
                @change="handleFilterChange"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user"
                  :label="user"
                  :value="user"
                />
              </el-select>
            </div>

            <!-- 搜索区域 -->
            <div class="search-container">
            <el-input
              v-model="searchKeyword"
                placeholder="搜索UID、TOKEN/用户"
              class="search-input"
              clearable
              @clear="handleSearch"
              @input="handleSearch"
                @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><el-icon-search /></el-icon>
              </template>
            </el-input>

              <!-- 增加搜索和重置按钮 -->
              <div class="search-actions">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><el-icon-search /></el-icon>搜索
                </el-button>
                <el-button @click="resetFilters" class="reset-button">
                  <el-icon><el-icon-refresh /></el-icon>重置筛选
                </el-button>
              </div>
            </div>

            <!-- 数据统计信息 -->
            <div class="data-stats">
              <div class="stats-card info-card">
                <span class="stats-value">{{ totalTokenCount }}</span>
                <span class="stats-label">总数量</span>
              </div>
              <div class="stats-card primary-card">
                <span class="stats-value">{{ filteredCount }}</span>
                <span class="stats-label">筛选结果</span>
              </div>
              <div class="stats-card warning-card" v-if="selectedTokens.length > 0">
                <span class="stats-value">{{ selectedTokens.length }}</span>
                <span class="stats-label">已选择</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 功能按钮区域 -->
      <div class="function-buttons">
        <el-button-group>
          <el-button type="primary" @click="handleImportToken">
            <el-icon><el-icon-upload /></el-icon>导入Token
          </el-button>
          <el-button type="danger" @click="handleClearTable">
            <el-icon><el-icon-delete /></el-icon>清空表格
          </el-button>
          <UnsoldTokenLoader :current-tokens="tokens" @update:tokens="handleUnsoldTokensUpdate" />
          <SoldTokenLoader :current-tokens="tokens" @update:tokens="handleSoldTokensUpdate" />
          <OfflineTokenCleaner :tokens="tokens" @update:tokens="handleOfflineTokensUpdate" />
        </el-button-group>

        <el-button-group>
          <el-button type="info" @click="handleQueryAvatarNickname">
            <el-icon><el-icon-user /></el-icon>查询头像昵称
          </el-button>
          <el-button type="success" @click="handleQueryOnline">
            <el-icon><el-icon-connection /></el-icon>查询在线
          </el-button>
          <el-button type="warning" @click="handleQueryOrder">
            <el-icon><el-icon-goods /></el-icon>查询订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="primary" @click="handleChangeAvatar">
            <el-icon><el-icon-picture /></el-icon>修改头像
          </el-button>
          <el-button type="primary" @click="handleChangeNickname">
            <el-icon><el-icon-edit /></el-icon>修改昵称
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="success" @click="handleCreateBackup">
            <el-icon><el-icon-document-copy /></el-icon>创建备份
          </el-button>
          <el-button type="warning" @click="handleResetSelected">
            <el-icon><el-icon-refresh-right /></el-icon>重置选中
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="primary" @click="handleUploadSelected">
            <el-icon><el-icon-upload /></el-icon>选中上传后台
          </el-button>
          <el-button type="warning" @click="handleAddToOrder">
            <el-icon><el-icon-shopping-cart /></el-icon>新Token置入账号内订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="danger" @click="handleDeleteOrder">
            <el-icon><el-icon-delete /></el-icon>删除订单
          </el-button>
          <el-button type="success" @click="handleAssignToAccount">
            <el-icon><el-icon-user-filled /></el-icon>选中给指定账号
          </el-button>
        </el-button-group>
      </div>

      <!-- Token表格 -->
      <div class="table-actions">
        <el-button
          type="primary"
          size="small"
          @click="selectAllTokens"
          :disabled="tokens.length === 0"
        >
          <el-icon><el-icon-select /></el-icon>全选所有数据
        </el-button>
        <el-button
          type="info"
          size="small"
          @click="clearAllSelection"
          :disabled="selectedTokens.length === 0"
        >
          <el-icon><el-icon-close /></el-icon>清除选择
        </el-button>
        <div v-if="hasSelectionAcrossPages" class="selection-info">
          <el-tag type="warning" effect="plain">
            已选择 <strong>{{ selectedTokens.length }}</strong> 条数据（包含跨页选择）
          </el-tag>
        </div>
      </div>
      <el-table
        ref="tokenTableRef"
        :data="tableData"
        style="width: 100%"
        border
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        v-loading="loading"
        row-key="uid"
        stripe
        highlight-current-row
        class="token-table"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        <el-table-column prop="uid" label="UID" width="150" sortable />
        <el-table-column prop="token" label="Token" min-width="230" show-overflow-tooltip />
        <el-table-column prop="user" label="用户" width="120" sortable>
          <template #default="scope">
            <el-button
              type="text"
              @click="filterByUser(scope.row.user)"
              :style="{ color: userFilter === scope.row.user ? '#409EFF' : '' }"
              class="user-filter-btn"
            >
              {{ scope.row.user }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" class="token-avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        <el-table-column prop="purchaseTime" label="购买时间" width="180" sortable />
        <el-table-column prop="expiryDate" label="到期时间" width="180" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              effect="light"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderInfo" label="订单" width="280">
          <template #default="scope">
            <div v-if="scope.row.orderInfo">
              <!-- 掉线状态 -->
              <div v-if="scope.row.orderInfo.status === '掉线'" class="order-info">
                <div class="status-badge status-offline">
                  <i class="el-icon-warning-outline"></i>
                  <span>掉线</span>
                </div>
              </div>
              <!-- 在线但无订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount === 0" class="order-info">
                <div class="status-badge status-no-order">
                  <i class="el-icon-shopping-bag-1"></i>
                  <span>无订单</span>
                </div>
              </div>
              <!-- 在线且有订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount > 0" class="order-info">
                <!-- 显示删除状态和进度 -->
                <div v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.totalOrders > 0" class="order-status-container">
                  <!-- 基本订单信息和数量 -->
                  <div
                    class="order-count-badge"
                    @click="handleViewOrders(scope.row)"
                  >
                    <i class="el-icon-goods"></i>
                    <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                    <span class="order-text">订单</span>
                  </div>

                  <!-- 删除状态信息 -->
                  <div class="order-deletion-status">
                    <template v-if="scope.row.orderDeletionStatus.inProgress">
                      <el-tag type="warning" effect="plain" size="small">正在删除订单...</el-tag>
                    </template>
                    <template v-else>
                      <!-- 显示删除结果 -->
                      <div class="order-status-text">
                        <el-tag v-if="scope.row.orderDeletionStatus.successCount > 0" type="success" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-check"></i>
                          <span>已删除 {{ scope.row.orderDeletionStatus.successCount }} 条</span>
                        </el-tag>
                        <el-tag v-if="scope.row.orderDeletionStatus.failCount > 0" type="danger" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-close"></i>
                          <span>失败 {{ scope.row.orderDeletionStatus.failCount }} 条</span>
                        </el-tag>
                        <el-tag type="info" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-goods"></i>
                          <span>剩余 {{ scope.row.orderInfo.orderCount }} 条</span>
                          <span class="deletable-count">({{ getDeletableCount(scope.row) }}个可删)</span>
                        </el-tag>
                      </div>
                    </template>
                  </div>
                </div>
                <!-- 默认订单显示 -->
                <div
                  v-else
                  class="order-count-badge"
                  @click="handleViewOrders(scope.row)"
                >
                  <i class="el-icon-goods"></i>
                  <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                  <span class="order-text">订单</span>
                  <el-tag v-if="getDeletableCount(scope.row) > 0" size="small" type="success" effect="light" class="deletable-tag">
                    {{ getDeletableCount(scope.row) }}个可删
                  </el-tag>
                </div>
              </div>
              <!-- 查询失败或未知状态 -->
              <div v-else class="order-info">
                <div class="status-badge status-error">
                  <i class="el-icon-circle-close"></i>
                  <span>{{ scope.row.orderInfo.status || '查询失败' }}</span>
                </div>
              </div>
            </div>
            <div v-else class="order-info">
              <div class="status-badge status-not-queried">
                <i class="el-icon-question"></i>
                <span>未查询</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <div class="table-action-buttons">
              <el-button type="primary" size="small" text @click="handleReset(scope.row)" class="action-btn">重置</el-button>
              <el-button type="success" size="small" text @click="handleBackup(scope.row)" class="action-btn">备份</el-button>
              <el-button type="info" size="small" text @click="handleEdit(scope.row)" class="action-btn">编辑</el-button>
              <el-button type="danger" size="small" text @click="handleDelete(scope.row)" class="action-btn">删除</el-button>
              <!-- 替换原来的QuickOrderDelete组件为直接删除订单按钮 -->
              <el-button
                type="warning"
                size="small"
                text
                @click="handleQuickDeleteOrder(scope.row)"
                class="action-btn"
                :loading="scope.row.orderDeleteLoading"
              >
                删除订单
                <template v-if="getDeletableCount(scope.row) > 0">({{ getDeletableCount(scope.row) }})</template>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="filteredCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 隐藏的文件输入框 -->
    <input
      type="file"
      ref="fileInputRef"
      style="display: none;"
      @change="handleFileInputChange"
      accept=".xlsx,.xls,.txt"
    />

    <!-- 其他功能对话框 -->
    <el-dialog
      v-model="operationDialogVisible"
      :title="operationTitle"
      width="40%"
    >
      <div class="operation-dialog-content">
        {{ operationMessage }}
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="operationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmOperation">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看订单详情对话框 -->
    <el-dialog
      v-model="ordersDialogVisible"
      title="订单详情"
      width="70%"
    >
      <div class="order-details">
        <!-- 添加订单统计信息和批量操作按钮 -->
        <div class="orders-summary-bar">
          <div class="orders-stats">
            <el-tag type="info" effect="plain">
              总计 <strong>{{ currentOrders.length }}</strong> 个订单
            </el-tag>
            <el-tag type="success" effect="plain" class="ml-10">
              可删除 <strong>{{ deletableOrdersCount }}</strong> 个订单
            </el-tag>
          </div>
          <div class="batch-actions">
            <el-button
              type="primary"
              size="small"
              @click="selectAllDeletableOrders"
              :disabled="deletableOrdersCount === 0"
            >
              <el-icon><el-icon-select /></el-icon>全选可删除订单
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="batchDeleteSelectedOrders"
              :disabled="selectedOrdersInList.length === 0"
            >
              <el-icon><el-icon-delete /></el-icon>批量删除 ({{ selectedOrdersInList.length }})
            </el-button>
          </div>
        </div>

        <el-table
          :data="currentOrders"
          style="width: 100%"
          border
          v-loading="loading"
          row-key="orderId"
          stripe
          highlight-current-row
          class="order-table"
          @selection-change="handleOrderSelectionChange"
          ref="orderTableRef"
        >
          <el-table-column type="selection" width="55" :selectable="row => isDeletableOrder(row.status)" />
          <el-table-column prop="orderId" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getOrderStatusTag(scope.row.status)" effect="light">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="80">
            <template #default="scope">
              ¥{{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="goodsInfo" label="商品信息" min-width="200" show-overflow-tooltip />
          <el-table-column prop="orderTime" label="订单时间" width="180" sortable />
          <el-table-column prop="mallName" label="商家名称" width="120" show-overflow-tooltip />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <div class="order-action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewOrderDetail(scope.row)"
                text
              >
                查看详情
              </el-button>
                <el-button
                  v-if="isDeletableOrder(scope.row.status)"
                  type="danger"
                  size="small"
                  @click="handleDeleteSingleOrder(scope.row)"
                  text
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ordersDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="orderDetailDialogVisible"
      title="订单详细信息"
      width="50%"
    >
      <div v-if="currentOrderDetail" class="order-detail-content">
        <!-- 添加显示删除按钮的条件判断 -->
        <div class="order-detail-actions" v-if="isDeletableOrder(currentOrderDetail.status)">
          <el-button
            type="danger"
            size="small"
            @click="handleDeleteSingleOrder(currentOrderDetail)"
          >
            删除订单
          </el-button>
          <el-tooltip content="仅已评价的订单可删除" placement="top">
            <el-tag type="success" size="small" style="margin-left: 10px;">可删除</el-tag>
          </el-tooltip>
        </div>
        <div class="order-detail-actions" v-else>
          <el-tooltip content="该订单状态不允许删除" placement="top">
            <el-tag type="danger" size="small">不可删除</el-tag>
          </el-tooltip>
          <span class="deletion-notice">（只有已评价的订单可删除）</span>
        </div>

        <div class="order-detail-item">
          <div class="order-detail-label">订单号</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderId }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">状态</div>
          <div class="order-detail-value">
            <el-tag :type="getOrderStatusTag(currentOrderDetail.status)">
              {{ currentOrderDetail.status }}
            </el-tag>
          </div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">金额</div>
          <div class="order-detail-value">¥{{ currentOrderDetail.amount }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商品信息</div>
          <div class="order-detail-value">{{ currentOrderDetail.goodsInfo }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">订单时间</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderTime }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商家名称</div>
          <div class="order-detail-value">{{ currentOrderDetail.mallName }}</div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentOrderDetail && isDeletableOrder(currentOrderDetail.status)"
            type="danger"
            @click="handleDeleteSingleOrder(currentOrderDetail)"
          >
            删除订单
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
