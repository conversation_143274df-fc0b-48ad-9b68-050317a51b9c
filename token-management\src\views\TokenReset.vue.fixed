﻿<template>
  <div class="token-reset-container">
    <el-card class="token-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>Token鍒楄〃</span>
          <div class="token-count-indicator" v-if="totalTokenCount > 0">
            <el-tag type="success" effect="plain">
              <el-icon><el-icon-document /></el-icon>
              鍏眥{ totalTokenCount }} 涓猅oken
            </el-tag>
          </div>
          <div class="header-actions">
            <!-- 绛涢€夊姛鑳藉尯鍩?-->
            <div class="filter-container">
              <el-select
                v-model="statusFilter"
                placeholder="鐘舵€佺瓫閫?
                clearable
                class="filter-select"
                @change="handleFilterChange"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-tag :type="getStatusType(item.value)" effect="light" size="small">
                    {{ item.label }}
                  </el-tag>
                </el-option>
              </el-select>
              
              <el-select
                v-model="userFilter"
                placeholder="鐢ㄦ埛绛涢€?
                clearable
                class="filter-select"
                filterable
                @change="handleFilterChange"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user"
                  :label="user"
                  :value="user"
                />
              </el-select>
            </div>
            
            <!-- 鎼滅储鍖哄煙 -->
            <div class="search-container">
            <el-input
              v-model="searchKeyword"
                placeholder="鎼滅储UID銆乀OKEN/鐢ㄦ埛"
              class="search-input"
              clearable
              @clear="handleSearch"
              @input="handleSearch"
                @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><el-icon-search /></el-icon>
              </template>
            </el-input>
              
              <!-- 澧炲姞鎼滅储鍜岄噸缃寜閽?-->
              <div class="search-actions">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><el-icon-search /></el-icon>鎼滅储
                </el-button>
                <el-button @click="resetFilters" class="reset-button">
                  <el-icon><el-icon-refresh /></el-icon>閲嶇疆绛涢€?
                </el-button>
              </div>
            </div>
            
            <!-- 鏁版嵁缁熻淇℃伅 -->
            <div class="data-stats">
              <div class="stats-card info-card">
                <span class="stats-value">{{ totalTokenCount }}</span>
                <span class="stats-label">鎬绘暟閲?/span>
              </div>
              <div class="stats-card primary-card">
                <span class="stats-value">{{ filteredCount }}</span>
                <span class="stats-label">绛涢€夌粨鏋?/span>
              </div>
              <div class="stats-card warning-card" v-if="selectedTokens.length > 0">
                <span class="stats-value">{{ selectedTokens.length }}</span>
                <span class="stats-label">宸查€夋嫨</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 鍔熻兘鎸夐挳鍖哄煙 -->
      <div class="function-buttons">
        <el-button-group>
          <el-button type="primary" @click="handleImportToken">
            <el-icon><el-icon-upload /></el-icon>瀵煎叆Token
          </el-button>
          <el-button type="danger" @click="handleClearTable">
            <el-icon><el-icon-delete /></el-icon>娓呯┖琛ㄦ牸
          </el-button>
          <el-button type="warning" @click="handleLoadUnsold">
            <el-icon><el-icon-refresh /></el-icon>鍔犺浇鏈崠
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="info" @click="handleQueryAvatarNickname">
            <el-icon><el-icon-user /></el-icon>鏌ヨ澶村儚鏄电О
          </el-button>
          <el-button type="success" @click="handleQueryOnline">
            <el-icon><el-icon-connection /></el-icon>鏌ヨ鍦ㄧ嚎
          </el-button>
          <el-button type="warning" @click="handleQueryOrder">
            <el-icon><el-icon-goods /></el-icon>鏌ヨ璁㈠崟
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="primary" @click="handleChangeAvatar">
            <el-icon><el-icon-picture /></el-icon>淇敼澶村儚
          </el-button>
          <el-button type="primary" @click="handleChangeNickname">
            <el-icon><el-icon-edit /></el-icon>淇敼鏄电О
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="success" @click="handleCreateBackup">
            <el-icon><el-icon-document-copy /></el-icon>鍒涘缓澶囦唤
          </el-button>
          <el-button type="warning" @click="handleResetSelected">
            <el-icon><el-icon-refresh-right /></el-icon>閲嶇疆閫変腑
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="primary" @click="handleUploadSelected">
            <el-icon><el-icon-upload /></el-icon>閫変腑涓婁紶鍚庡彴
          </el-button>
          <el-button type="warning" @click="handleAddToOrder">
            <el-icon><el-icon-shopping-cart /></el-icon>鏂癟oken缃叆璐﹀彿鍐呰鍗?
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="danger" @click="handleDeleteOrder">
            <el-icon><el-icon-delete /></el-icon>鍒犻櫎璁㈠崟
          </el-button>
          <el-button type="success" @click="handleAssignToAccount">
            <el-icon><el-icon-user-filled /></el-icon>閫変腑缁欐寚瀹氳处鍙?
          </el-button>
        </el-button-group>
      </div>
      
      <!-- Token琛ㄦ牸 -->
      <div class="table-actions">
        <el-button 
          type="primary" 
          size="small" 
          @click="selectAllTokens" 
          :disabled="tokens.length === 0"
        >
          <el-icon><el-icon-select /></el-icon>鍏ㄩ€夋墍鏈夋暟鎹?
        </el-button>
        <el-button 
          type="info" 
          size="small" 
          @click="clearAllSelection" 
          :disabled="selectedTokens.length === 0"
        >
          <el-icon><el-icon-close /></el-icon>娓呴櫎閫夋嫨
        </el-button>
        <div v-if="hasSelectionAcrossPages" class="selection-info">
          <el-tag type="warning" effect="plain">
            宸查€夋嫨 <strong>{{ selectedTokens.length }}</strong> 鏉℃暟鎹紙鍖呭惈璺ㄩ〉閫夋嫨锛?
          </el-tag>
        </div>
      </div>
      <el-table
        ref="tokenTableRef"
        :data="tableData"
        style="width: 100%"
        border
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        v-loading="loading"
        row-key="uid"
        stripe
        highlight-current-row
        class="token-table"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        <el-table-column prop="uid" label="UID" width="150" sortable />
        <el-table-column prop="token" label="Token" min-width="230" show-overflow-tooltip />
        <el-table-column prop="user" label="鐢ㄦ埛" width="120" sortable>
          <template #default="scope">
            <el-button 
              type="text" 
              @click="filterByUser(scope.row.user)"
              :style="{ color: userFilter === scope.row.user ? '#409EFF' : '' }"
              class="user-filter-btn"
            >
              {{ scope.row.user }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="avatar" label="澶村儚" width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" class="token-avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="鏄电О" width="120" show-overflow-tooltip />
        <el-table-column prop="purchaseTime" label="璐拱鏃堕棿" width="180" sortable />
        <el-table-column prop="expiryDate" label="鍒版湡鏃堕棿" width="180" sortable />
        <el-table-column prop="status" label="鐘舵€? width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              effect="light"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderInfo" label="璁㈠崟" width="280">
          <template #default="scope">
            <div v-if="scope.row.orderInfo">
              <!-- 鎺夌嚎鐘舵€?-->
              <div v-if="scope.row.orderInfo.status === '鎺夌嚎'" class="order-info">
                <div class="status-badge status-offline">
                  <i class="el-icon-warning-outline"></i>
                  <span>鎺夌嚎</span>
                </div>
              </div>
              <!-- 鍦ㄧ嚎浣嗘棤璁㈠崟 -->
              <div v-else-if="scope.row.orderInfo.status === '鍦ㄧ嚎' && scope.row.orderInfo.orderCount === 0" class="order-info">
                <div class="status-badge status-no-order">
                  <i class="el-icon-shopping-bag-1"></i>
                  <span>鏃犺鍗?/span>
                </div>
              </div>
              <!-- 鍦ㄧ嚎涓旀湁璁㈠崟 -->
              <div v-else-if="scope.row.orderInfo.status === '鍦ㄧ嚎' && scope.row.orderInfo.orderCount > 0" class="order-info">
                <!-- 鏄剧ず鍒犻櫎鐘舵€佸拰杩涘害 -->
                <div v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.totalOrders > 0" class="order-status-container">
                  <!-- 鍩烘湰璁㈠崟淇℃伅鍜屾暟閲?-->
                  <div 
                    class="order-count-badge"
                    @click="handleViewOrders(scope.row)"
                  >
                    <i class="el-icon-goods"></i>
                    <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                    <span class="order-text">璁㈠崟</span>
                  </div>
                  
                  <!-- 鍒犻櫎鐘舵€佷俊鎭?-->
                  <div class="order-deletion-status">
                    <template v-if="scope.row.orderDeletionStatus.inProgress">
                      <el-tag type="warning" effect="plain" size="small">姝ｅ湪鍒犻櫎璁㈠崟...</el-tag>
                    </template>
                    <template v-else>
                      <!-- 鏄剧ず鍒犻櫎缁撴灉 -->
                      <div class="order-status-text">
                        <el-tag v-if="scope.row.orderDeletionStatus.successCount > 0" type="success" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-check"></i>
                          <span>宸插垹闄?{{ scope.row.orderDeletionStatus.successCount }} 涓?/span>
                        </el-tag>
                        <el-tag v-if="scope.row.orderDeletionStatus.failCount > 0" type="danger" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-close"></i>
                          <span>澶辫触 {{ scope.row.orderDeletionStatus.failCount }} 涓?/span>
                        </el-tag>
                        <el-tag type="info" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-goods"></i>
                          <span>鍓╀綑 {{ scope.row.orderInfo.orderCount }} 涓?/span>
                          <span class="deletable-count">({{ getDeletableCount(scope.row) }}涓彲鍒?</span>
                        </el-tag>
                      </div>
                    </template>
                  </div>
                </div>
                <!-- 榛樿璁㈠崟鏄剧ず -->
                <div 
                  v-else
                  class="order-count-badge"
                  @click="handleViewOrders(scope.row)"
                >
                  <i class="el-icon-goods"></i>
                  <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                  <span class="order-text">璁㈠崟</span>
                  <el-tag v-if="getDeletableCount(scope.row) > 0" size="small" type="success" effect="light" class="deletable-tag">
                    {{ getDeletableCount(scope.row) }}涓彲鍒?
                  </el-tag>
                </div>
              </div>
              <!-- 鏌ヨ澶辫触鎴栨湭鐭ョ姸鎬?-->
              <div v-else class="order-info">
                <div class="status-badge status-error">
                  <i class="el-icon-circle-close"></i>
                  <span>{{ scope.row.orderInfo.status || '鏌ヨ澶辫触' }}</span>
                </div>
              </div>
            </div>
            <div v-else class="order-info">
              <div class="status-badge status-not-queried">
                <i class="el-icon-question"></i>
                <span>鏈煡璇?/span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="鍒涘缓鏃堕棿" width="180" sortable />
        <el-table-column label="鎿嶄綔" width="280" fixed="right">
          <template #default="scope">
            <div class="table-action-buttons">
              <el-button type="primary" size="small" text @click="handleReset(scope.row)" class="action-btn">閲嶇疆</el-button>
              <el-button type="success" size="small" text @click="handleBackup(scope.row)" class="action-btn">澶囦唤</el-button>
              <el-button type="info" size="small" text @click="handleEdit(scope.row)" class="action-btn">缂栬緫</el-button>
              <el-button type="danger" size="small" text @click="handleDelete(scope.row)" class="action-btn">鍒犻櫎</el-button>
              <!-- 鏇挎崲鍘熸潵鐨凲uickOrderDelete缁勪欢涓虹洿鎺ュ垹闄よ鍗曟寜閽?-->
              <el-button 
                type="warning" 
                size="small" 
                text 
                @click="handleQuickDeleteOrder(scope.row)" 
                class="action-btn"
                :loading="scope.row.orderDeleteLoading"
              >
                鍒犻櫎璁㈠崟
                <template v-if="getDeletableCount(scope.row) > 0">({{ getDeletableCount(scope.row) }})</template>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 鍒嗛〉鍣?-->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="filteredCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 闅愯棌鐨勬枃浠惰緭鍏ユ -->
    <input
      type="file"
      ref="fileInputRef"
      style="display: none;"
      @change="handleFileInputChange"
      accept=".xlsx,.xls,.txt"
    />
    
    <!-- 鍏朵粬鍔熻兘瀵硅瘽妗?-->
    <el-dialog
      v-model="operationDialogVisible"
      :title="operationTitle"
      width="40%"
    >
      <div class="operation-dialog-content">
        {{ operationMessage }}
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="operationDialogVisible = false">鍙栨秷</el-button>
          <el-button type="primary" @click="confirmOperation">纭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 鏌ョ湅璁㈠崟璇︽儏瀵硅瘽妗?-->
    <el-dialog
      v-model="ordersDialogVisible"
      title="璁㈠崟璇︽儏"
      width="70%"
    >
      <div class="order-details">
        <!-- 娣诲姞璁㈠崟缁熻淇℃伅鍜屾壒閲忔搷浣滄寜閽?-->
        <div class="orders-summary-bar">
          <div class="orders-stats">
            <el-tag type="info" effect="plain">
              鎬昏 <strong>{{ currentOrders.length }}</strong> 涓鍗?
            </el-tag>
            <el-tag type="success" effect="plain" class="ml-10">
              鍙垹闄?<strong>{{ deletableOrdersCount }}</strong> 涓鍗?
            </el-tag>
          </div>
          <div class="batch-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="selectAllDeletableOrders" 
              :disabled="deletableOrdersCount === 0"
            >
              <el-icon><el-icon-select /></el-icon>鍏ㄩ€夊彲鍒犻櫎璁㈠崟
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="batchDeleteSelectedOrders" 
              :disabled="selectedOrdersInList.length === 0"
            >
              <el-icon><el-icon-delete /></el-icon>鎵归噺鍒犻櫎 ({{ selectedOrdersInList.length }})
            </el-button>
          </div>
        </div>
        
        <el-table
          :data="currentOrders"
          style="width: 100%"
          border
          v-loading="loading"
          row-key="orderId"
          stripe
          highlight-current-row
          class="order-table"
          @selection-change="handleOrderSelectionChange"
          ref="orderTableRef"
        >
          <el-table-column type="selection" width="55" :selectable="row => isDeletableOrder(row.status)" />
          <el-table-column prop="orderId" label="璁㈠崟鍙? width="180" show-overflow-tooltip />
          <el-table-column prop="status" label="鐘舵€? width="120">
            <template #default="scope">
              <el-tag :type="getOrderStatusTag(scope.row.status)" effect="light">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="閲戦" width="80">
            <template #default="scope">
              楼{{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="goodsInfo" label="鍟嗗搧淇℃伅" min-width="200" show-overflow-tooltip />
          <el-table-column prop="orderTime" label="璁㈠崟鏃堕棿" width="180" sortable />
          <el-table-column prop="mallName" label="鍟嗗鍚嶇О" width="120" show-overflow-tooltip />
          <el-table-column label="鎿嶄綔" width="120" fixed="right">
            <template #default="scope">
              <div class="order-action-buttons">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewOrderDetail(scope.row)" 
                text
              >
                鏌ョ湅璇︽儏
              </el-button>
                <el-button 
                  v-if="isDeletableOrder(scope.row.status)"
                  type="danger" 
                  size="small" 
                  @click="handleDeleteSingleOrder(scope.row)" 
                  text
                >
                  鍒犻櫎
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ordersDialogVisible = false">鍏抽棴</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 璁㈠崟璇︽儏瀵硅瘽妗?-->
    <el-dialog
      v-model="orderDetailDialogVisible"
      title="璁㈠崟璇︾粏淇℃伅"
      width="50%"
    >
      <div v-if="currentOrderDetail" class="order-detail-content">
        <!-- 娣诲姞鏄剧ず鍒犻櫎鎸夐挳鐨勬潯浠跺垽鏂?-->
        <div class="order-detail-actions" v-if="isDeletableOrder(currentOrderDetail.status)">
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDeleteSingleOrder(currentOrderDetail)"
          >
            鍒犻櫎璁㈠崟
          </el-button>
          <el-tooltip content="浠呭凡璇勪环鐨勮鍗曞彲鍒犻櫎" placement="top">
            <el-tag type="success" size="small" style="margin-left: 10px;">鍙垹闄?/el-tag>
          </el-tooltip>
        </div>
        <div class="order-detail-actions" v-else>
          <el-tooltip content="璇ヨ鍗曠姸鎬佷笉鍏佽鍒犻櫎" placement="top">
            <el-tag type="danger" size="small">涓嶅彲鍒犻櫎</el-tag>
          </el-tooltip>
          <span class="deletion-notice">锛堝彧鏈夊凡璇勪环鐨勮鍗曞彲鍒犻櫎锛?/span>
        </div>

        <div class="order-detail-item">
          <div class="order-detail-label">璁㈠崟鍙?/div>
          <div class="order-detail-value">{{ currentOrderDetail.orderId }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">鐘舵€?/div>
          <div class="order-detail-value">
            <el-tag :type="getOrderStatusTag(currentOrderDetail.status)">
              {{ currentOrderDetail.status }}
            </el-tag>
          </div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">閲戦:</div>
          <div class="order-detail-value">楼{{ currentOrderDetail.amount }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">涓嬪崟鏃堕棿:</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderTime }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">鍟嗗搧:</div>
          <div class="order-detail-value">{{ currentOrderDetail.goodsInfo }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">鐗╂祦鍗曞彿:</div>
          <div class="order-detail-value">{{ currentOrderDetail.trackingNumber || '鏆傛棤' }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">鍟嗗:</div>
          <div class="order-detail-value">{{ currentOrderDetail.mallName }}</div>
        </div>
        <el-divider />
        <div class="order-extra-info">
          <el-collapse>
            <el-collapse-item title="鏇村璁㈠崟淇℃伅" name="1">
              <pre>{{ JSON.stringify(currentOrderDetail.extraInfo, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="orderDetailDialogVisible = false">鍏抽棴</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElLoading, ElMessageBox, ElNotification } from 'element-plus'
import * as XLSX from 'xlsx'
import * as tokenService from '@/services/tokenService'
import { batchCheckOnlineStatus, applyOnlineStatusToTokens } from '@/services/onlineCheckService'
import { parseImportedFile, processTxtData } from '@/utils/tokenParser'
import { formatDateTime, validateTokenData } from '@/utils/helpers'
// 瀵煎叆缁勪欢
import * as orderService from '../services/orderService'

// 瀵硅瘽妗嗘帶浠?
const operationDialogVisible = ref(false)
const operationTitle = ref('')
const operationMessage = ref('')
const currentOperation = ref('')

// 鏂囦欢涓婁紶鐩稿叧
const fileInputRef = ref(null)
const selectedFile = ref(null)

// 鐩存帴鎵撳紑鏂囦欢閫夋嫨妗?
const handleImportToken = () => {
  fileInputRef.value.click()
}

// 鏂囦欢閫夋嫨鍙樺寲澶勭悊
const handleFileInputChange = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  selectedFile.value = file
  processSelectedFile(file)
  
  // 閲嶇疆鏂囦欢杈撳叆锛屼互渚夸笅娆￠€夋嫨鍚屼竴鏂囦欢鏃朵篃鑳借Е鍙戜簨浠?
  event.target.value = ''
}

// 澶勭悊閫変腑鐨勬枃浠?
const processSelectedFile = async (file) => {
  const fileName = file.name
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.txt')) {
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪瀵煎叆鏁版嵁...'
    })
    
    try {
      const parsedData = await parseImportedFile(file)
      
      if (parsedData.length === 0) {
        ElMessage.warning('鏂囦欢涓病鏈夋湁鏁堟暟鎹?)
        loadingInstance.close()
        return
      }
      
      const { newTokens, duplicateCount } = processAndDeduplicateTokens(parsedData)
      
      // 淇敼锛氫繚瀛樺綋鍓嶉〉鐮?
      const currentPageBeforeUpdate = currentPage.value
      
      // 璁剧疆涓嶉噸缃〉鐮佺殑鏍囧織
      shouldResetPage.value = false
      
      tokens.value = newTokens
      totalTokenCount.value = newTokens.length
      
      let successMsg = `鎴愬姛瀵煎叆 ${newTokens.length} 涓猅oken`
      if (duplicateCount > 0) {
        successMsg += `锛屽凡鑷姩鍘婚櫎 ${duplicateCount} 涓噸澶峊oken`
      }
      
      ElMessage.success(successMsg)
      loadingInstance.close()
      
      // 鎭㈠涔嬪墠鐨勯〉鐮侊紝濡傛灉椤电爜瓒呭嚭鑼冨洿锛宖ilterTokens浼氳嚜鍔ㄨ皟鏁?
      nextTick(() => {
        currentPage.value = currentPageBeforeUpdate
      })
    } catch (error) {
      console.error('瀵煎叆澶辫触:', error)
      ElMessage.error('瀵煎叆澶辫触: ' + (error.message || '鏃犳晥鐨勬暟鎹牸寮?))
      loadingInstance.close()
    }
  } else {
    ElMessage.error('涓嶆敮鎸佺殑鏂囦欢鏍煎紡锛屼粎鏀寔Excel(.xlsx, .xls)鍜屾枃鏈枃浠?.txt)')
  }
}

// 鍏叡鎿嶄綔瀵硅瘽妗?
const showOperationDialog = (title, message, operation) => {
  operationTitle.value = title
  operationMessage.value = message
  currentOperation.value = operation
  operationDialogVisible.value = true
}

// Token鍒楄〃鏁版嵁
const tokens = ref([]);
const selectedTokens = ref([]);
const loading = ref(false);
const isLocalData = ref(true); // 濮嬬粓鏍囪涓烘湰鍦版暟鎹?
const tableHeight = ref('400px'); // 杩涗竴姝ュ噺灏忛粯璁ら珮搴?
// 鍒嗛〉鐩稿叧
const currentPage = ref(1);
const pageSize = ref(100); // 榛樿姣忛〉鏄剧ず20鏉★紝鍑忚交娓叉煋鍘嬪姏
const totalTokenCount = ref(0);

// 浼樺寲琛ㄦ牸娓叉煋鍜岀瓫閫夋€ц兘
// 缂撳瓨宸茬瓫閫夌殑鏁版嵁锛岄伩鍏嶉噸澶嶈绠?
const filteredTokensCache = ref([]);
const isFiltering = ref(false);

// 鏇存敼涓烘柟娉曡€屼笉鏄绠楀睘鎬э紝瀹炵幇寤惰繜璁＄畻
const filterTokens = () => {
  // 濡傛灉姝ｅ湪绛涢€変腑锛屼笉閲嶅鎵ц
  if (isFiltering.value) return;
  
  isFiltering.value = true;
  loading.value = true;
  
  // 浣跨敤setTimeout灏嗙瓫閫夋搷浣滅Щ鍒颁笅涓€涓簨浠跺惊鐜紝閬垮厤闃诲UI
  setTimeout(() => {
    try {
      // 鍏堢瓫閫夋暟鎹?
      let result = [...tokens.value]; // 鍒涘缓鎷疯礉锛岄伩鍏嶇洿鎺ヤ慨鏀瑰師鏁扮粍
      
      // 鎸夌姸鎬佺瓫閫?
      if (statusFilter.value) {
        result = result.filter(token => token.status === statusFilter.value);
      }
      
      // 鎸夌敤鎴风瓫閫?
      if (userFilter.value) {
        result = result.filter(token => token.user === userFilter.value);
      }
      
      // 鎸夊叧閿瓧鎼滅储
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(token => 
          (token.uid && token.uid.toLowerCase().includes(keyword)) ||
          (token.user && token.user.toLowerCase().includes(keyword)) ||
          (token.nickname && token.nickname && token.nickname.toLowerCase().includes(keyword)) ||
          (token.token && token.token.toLowerCase().includes(keyword)) ||
          (token.status && token.status.toLowerCase().includes(keyword))
        );
      }
      
      // 鏇存柊缂撳瓨鐨勭瓫閫夌粨鏋?
      filteredTokensCache.value = result;
      
      // 鏇存柊鎬昏
      totalTokenCount.value = tokens.value.length;
      
      // 澶勭悊绛涢€夊畬鎴愬悗鐨勬搷浣?
      nextTick(() => {
        // 閲嶇疆鍒扮涓€椤靛鏋滃綋鍓嶉〉瓒呭嚭鑼冨洿
        const totalPages = Math.ceil(filteredTokensCache.value.length / pageSize.value) || 1;
        if (currentPage.value > totalPages) {
          currentPage.value = 1;
        } 
        // 绉婚櫎浠ヤ笅浠ｇ爜鍧楋紝涓嶅啀鍩轰簬shouldResetPage鑷姩閲嶇疆椤电爜
        // else if (shouldResetPage.value) {
        //   // 浠呭綋闇€瑕侀噸缃〉鐮佹椂鎵嶉噸缃?
        //   //   currentPage.value = 1;
        // }
        
        // 閲嶆柊搴旂敤閫夋嫨鐘舵€侊紝纭繚閫変腑鐘舵€佷笌鏁版嵁涓€鑷?
        applyTableSelectionStatus();
        
        // 涓嶅啀鍦ㄨ繖閲岄噸缃爣蹇楋紝浠ヤ繚鎸佸綋鍓嶈缃?
        // shouldResetPage.value = true;
      });
    } catch (error) {
      console.error('鏁版嵁绛涢€夐敊璇?', error);
    } finally {
      isFiltering.value = false;
      loading.value = false;
    }
  }, 0);
};

// 浼樺寲琛ㄦ牸鏁版嵁璁＄畻灞炴€?
const tableData = computed(() => {
  // 璁＄畻褰撳墠椤垫暟
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = Math.min(startIndex + pageSize.value, filteredTokensCache.value.length);
  
  // 杩斿洖褰撳墠椤电殑鏁版嵁
  return filteredTokensCache.value.slice(startIndex, endIndex);
});

// 绛涢€夊悗鐨勬暟鎹€婚噺璁＄畻灞炴€?
const filteredCount = computed(() => filteredTokensCache.value.length);

// 鎼滅储
const searchKeyword = ref('');

// 绛涢€夊姛鑳界浉鍏?
const statusFilter = ref('');
const userFilter = ref('');

// 鐘舵€侀€夐」
const statusOptions = [
  { value: '鏈煡', label: '鏈煡' },
  { value: '鍦ㄧ嚎', label: '鍦ㄧ嚎' },
  { value: '鎺夌嚎', label: '鎺夌嚎' },
  { value: '姝ｅ父', label: '姝ｅ父' },
  { value: '杩囨湡', label: '杩囨湡' },
  { value: '寰呮縺娲?, label: '寰呮縺娲? },
  { value: '宸查噸缃?, label: '宸查噸缃? }
];

// 鐢ㄦ埛閫夐」锛堜粠Token鏁版嵁涓彁鍙栵級
const userOptions = computed(() => {
  // 鎻愬彇鎵€鏈変笉鍚岀殑鐢ㄦ埛
  const userSet = new Set();
  tokens.value.forEach(token => {
    if (token.user) {
      userSet.add(token.user);
    }
  });
  return Array.from(userSet).sort();
});

// 琛ㄦ牸寮曠敤
const tokenTableRef = ref(null);

// 搴旂敤琛ㄦ牸閫夋嫨鐘舵€?
const applyTableSelection = () => {
  try {
    // 娓呴櫎鎵€鏈夐€夋嫨
    tokenTableRef.value?.clearSelection();
    
    // 鑾峰彇褰撳墠椤甸潰涓婂簲璇ヨ閫変腑鐨勮
    nextTick(() => {
      // 瀵逛簬褰撳墠椤电殑姣忎釜閫変腑椤癸紝鎵嬪姩璁剧疆閫変腑
      tableData.value.forEach(token => {
        if (selectedTokens.value.some(selected => selected.uid === token.uid)) {
          tokenTableRef.value?.toggleRowSelection(token, true);
        }
      });
    });
  } catch (error) {
    console.error('搴旂敤閫夋嫨鐘舵€侀敊璇?', error);
  }
};

// 澶勭悊绛涢€夊彉鍖?
const handleFilterChange = () => {
  // 绛涢€夊彉鍖栨椂閲嶇疆鍒扮涓€椤?
  // 鍙湁鐢ㄦ埛鏄庣‘杩涜绛涢€夋搷浣滄椂鎵嶉噸缃〉鐮?
  shouldResetPage.value = true;
  
  // 鎵ц绛涢€?
  filterTokens();
};

// 鎼滅储澶勭悊
const handleSearch = () => {
  // 鎼滅储鏃堕噸缃埌绗竴椤?
  // 鍙湁鐢ㄦ埛鏄庣‘杩涜鎼滅储鎿嶄綔鏃舵墠閲嶇疆椤电爜
  shouldResetPage.value = true;
  
  // 鎵ц绛涢€?
  filterTokens();
};

// 閫氳繃鐢ㄦ埛鍚嶇瓫閫?
const filterByUser = (username) => {
  if (userFilter.value === username) {
    // 濡傛灉宸茬粡鏄瓫閫夎繖涓敤鎴凤紝鐐瑰嚮鍒欏彇娑堢瓫閫?
    userFilter.value = '';
  } else {
    // 鍚﹀垯璁剧疆涓虹瓫閫夎繖涓敤鎴?
    userFilter.value = username;
  }
  
  // 鐢ㄦ埛鏄庣‘绛涢€夋椂鎵嶉噸缃埌绗竴椤?
  shouldResetPage.value = true;
  
  // 鎵ц绛涢€?
  filterTokens();
};

// 澧炲姞鎼滅储鍜岄噸缃寜閽?
const resetFilters = () => {
  statusFilter.value = '';
  userFilter.value = '';
  searchKeyword.value = '';
  // 鐢ㄦ埛鏄庣‘閲嶇疆绛涢€夋椂鎵嶉噸缃〉鐮?
  shouldResetPage.value = true;
  
  // 鎵ц绛涢€?
  filterTokens();
};

// 鍒锋柊Token鍒楄〃 - 浼樺寲
const refreshTokens = () => {
  // 濡傛灉姝ｅ湪鍒锋柊涓紝閬垮厤閲嶅鎵ц
  if (loading.value) return;
  
  // 鏄剧ず鍔犺浇鐘舵€?
  loading.value = true;
  
  // 鍒锋柊鏃朵笉閲嶇疆椤电爜锛屼繚鎸佸綋鍓嶉〉
  // shouldResetPage.value = true;
  
  // 閲嶆柊绛涢€夋暟鎹?
  filterTokens();
  
  // 纭繚鍦ㄥ埛鏂板畬鎴愬悗閲嶆柊搴旂敤閫夋嫨鐘舵€?
  setTimeout(() => {
    applyTableSelectionStatus();
    loading.value = false;
  }, 50);
};

// 鐩戝惉瀵煎叆鏁版嵁鐘舵€侊紝浼樺寲澶ф暟鎹泦澶勭悊
watch(tokens, (newTokens) => {
  if (newTokens.length > 1000) {
    // 鏁版嵁閲忓ぇ鏃惰嚜鍔ㄨ皟鏁撮〉澶у皬
    if (pageSize.value < 50) {
      pageSize.value = 50;
      ElMessage.info('鏁版嵁閲忚緝澶э紝宸茶嚜鍔ㄨ皟鏁翠负姣忛〉鏄剧ず50鏉?);
    }
  }
  
  // 涓嶅啀寮哄埗閲嶇疆鍒扮涓€椤?
  // shouldResetPage.value = true;
  
  // 鎵ц绛涢€?
  filterTokens();
}, { deep: false });

// 鏇存柊閫変腑鐨凾oken淇℃伅
const updateTokensInfo = (updatedTokens) => {
  if (!updatedTokens || updatedTokens.length === 0) return
  
  // 鏇存柊鏈湴鏁版嵁
  tokens.value = tokens.value.map(token => {
    const updated = updatedTokens.find(t => t.uid === token.uid)
    return updated ? { ...token, ...updated } : token
  })
  
  // 璁剧疆涓嶉噸缃〉鐮佹爣蹇?
  shouldResetPage.value = false;
  
  // 搴旂敤绛涢€変絾涓嶆敼鍙橀〉鐮?
  filterTokens();
}

// 澶勭悊鍗曚釜Token鎿嶄綔
const handleReset = async (token) => {
  ElMessageBox.confirm(
    '纭瑕侀噸缃Token鍚楋紵',
    '鎿嶄綔纭',
    {
      confirmButtonText: '閲嶇疆',
      cancelButtonText: '鍙栨秷',
      type: 'warning'
    }
  ).then(() => {
    // 妯℃嫙閲嶇疆鏁堟灉
    const index = tokens.value.findIndex(t => t.uid === token.uid);
    if (index !== -1) {
      const newToken = { ...token };
      newToken.token = 'NEW_' + Math.random().toString(36).substring(2, 10);
      newToken.status = '宸查噸缃?;
      newToken.resetTime = formatDateTime(new Date());
      tokens.value[index] = newToken;
      ElMessage.success('Token宸查噸缃?);
    }
  }).catch(() => {});
};

const handleBackup = async (token) => {
  ElMessage.info('鏈湴妯″紡锛氬浠藉姛鑳戒粎鍦ㄦ湇鍔″櫒妯″紡涓嬪彲鐢?);
};

const handleEdit = (token) => {
  ElMessage.info('缂栬緫鍔熻兘灏氭湭瀹炵幇');
};

const handleDelete = async (token) => {
  ElMessageBox.confirm(
    '纭瑕佸垹闄ゆToken鍚楋紵',
    '鎿嶄綔纭',
    {
      confirmButtonText: '鍒犻櫎',
      cancelButtonText: '鍙栨秷',
      type: 'warning'
    }
  ).then(() => {
    // 浠庢暟缁勪腑绉婚櫎
    tokens.value = tokens.value.filter(t => t.uid !== token.uid);
    totalTokenCount.value = tokens.value.length;
    ElMessage.success('Token宸插垹闄?);
  }).catch(() => {});
};

// 琛ㄦ牸鎿嶄綔
const handleSelectionChange = (selection) => {
  // 鐩存帴鏇挎崲鏁翠釜宸查€夋嫨鍒楄〃
  // 娉ㄦ剰锛氳繖鍙兘浼氬鑷磋法椤甸€夋嫨涓㈠け锛屽洜姝ゆ垜浠渶瑕佺壒娈婂鐞?
  try {
    // 鑾峰彇褰撳墠椤甸潰鐨勬墍鏈塙ID
    const currentPageUids = new Set(tableData.value.map(item => item.uid));
    
    // 鏋勫缓鏂扮殑閫夋嫨鍒楄〃
    // 1. 淇濈暀涓嶅湪褰撳墠椤甸潰鐨勪箣鍓嶉€夋嫨鐨勯」
    // 2. 娣诲姞褰撳墠椤甸潰鏂伴€夋嫨鐨勯」
    const newSelection = [
      // 淇濈暀涓嶅湪褰撳墠椤甸潰鐨勯€夋嫨椤?
      ...selectedTokens.value.filter(item => !currentPageUids.has(item.uid)),
      // 娣诲姞褰撳墠椤甸潰鐨勯€夋嫨椤?
      ...selection
    ];
    
    // 鏇存柊閫夋嫨鍒楄〃
    selectedTokens.value = newSelection;
  } catch (error) {
    console.error('閫夋嫨鍙樺寲澶勭悊閿欒:', error);
  }
};

// 椤甸潰鍒囨崲澶勭悊
const handleSizeChange = (size) => {
  // 璁颁綇涔嬪墠鐨勯€夋嫨
  const previousSelection = [...selectedTokens.value];
  
  // 鏇存柊椤靛ぇ灏?
  pageSize.value = size;
  
  // 寤舵椂澶勭悊锛岄伩鍏岲OM鏇存柊鍐茬獊
  setTimeout(() => {
    try {
      // 娓呴櫎琛ㄦ牸UI涓婄殑閫夋嫨鐘舵€?
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }
      
      // 濡傛灉褰撳墠椤电爜瓒呭嚭鑼冨洿鍒欓噸缃?
      const totalPages = Math.ceil(filteredTokensCache.value.length / size) || 1;
      if (currentPage.value > totalPages) {
        currentPage.value = 1;
      }
      
      // 閲嶆柊璁剧疆閫変腑鐘舵€?
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 涓哄綋鍓嶉〉鐨勬瘡琛屾鏌ユ槸鍚﹀湪閫変腑鍒楄〃涓?
          tableData.value.forEach(row => {
            // 鏌ユ壘褰撳墠琛屾槸鍚﹀湪閫変腑鍒楄〃涓?
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 鎵嬪姩璁剧疆閫変腑鐘舵€?
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('椤甸潰澶у皬鍙樺寲閿欒:', error);
    }
  }, 50);
};

const handleCurrentChange = (page) => {
  // 璁颁綇涔嬪墠鐨勯€夋嫨
  const previousSelection = [...selectedTokens.value];
  
  // 鏇存柊椤电爜
  currentPage.value = page;
  
  // 寤舵椂澶勭悊锛岄伩鍏岲OM鏇存柊鍐茬獊
  setTimeout(() => {
    try {
      // 娓呴櫎琛ㄦ牸UI涓婄殑閫夋嫨鐘舵€?
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }
      
      // 閲嶆柊璁剧疆閫変腑鐘舵€?
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 涓哄綋鍓嶉〉鐨勬瘡琛屾鏌ユ槸鍚﹀湪閫変腑鍒楄〃涓?
          tableData.value.forEach(row => {
            // 鏌ユ壘褰撳墠琛屾槸鍚﹀湪閫変腑鍒楄〃涓?
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 鎵嬪姩璁剧疆閫変腑鐘舵€?
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('椤甸潰鍒囨崲閿欒:', error);
    }
  }, 50);
};

// 涓撻棬鐢ㄤ簬搴旂敤琛ㄦ牸閫夋嫨鐘舵€佺殑鍑芥暟
const applyTableSelectionStatus = () => {
  // 闃叉鏈垵濮嬪寲鏃惰皟鐢?
  if (!tokenTableRef.value) return;
  
  try {
    // 鍏堟竻闄よ〃鏍肩殑鎵€鏈夐€変腑鐘舵€侊紝閬垮厤UI鐘舵€佹贩涔?
    tokenTableRef.value.clearSelection();
    
    // 寤惰繜鍒颁笅涓€涓猼ick纭繚琛ㄦ牸宸叉洿鏂?
    nextTick(() => {
      // 閲嶆柊涓哄綋鍓嶉〉鐨勬瘡涓€琛屾鏌ユ槸鍚﹀簲璇ラ€変腑
      tableData.value.forEach(row => {
        const shouldBeSelected = selectedTokens.value.some(selected => selected.uid === row.uid);
        if (shouldBeSelected) {
          tokenTableRef.value?.toggleRowSelection(row, true);
        }
      });
    });
  } catch (error) {
    console.error('搴旂敤閫夋嫨鐘舵€侀敊璇?', error);
  }
};

// 閫氳繃鎼滅储鍏抽敭瀛楄繃婊ょ殑Token鍒楄〃
const filteredTokens = computed(() => tokens.value);

// 澶勭悊鍘婚噸鍜岃缃粯璁ょ姸鎬佺殑鍑芥暟
const processAndDeduplicateTokens = (parsedData) => {
  // 纭繚姣忎釜Token閮芥湁榛樿鐘舵€佷负"鏈煡"
  const processedData = parsedData.map(token => ({
    ...token,
    status: token.status || '鏈煡'
  }));
  
  // 濡傛灉鐜版湁鏁版嵁涓虹┖锛岀洿鎺ヨ繑鍥炲鐞嗗悗鐨勬暟缁?
  if (tokens.value.length === 0) {
    return { 
      newTokens: processedData,
      duplicateCount: 0
    };
  }
  
  // 鍒涘缓宸插瓨鍦ㄧ殑UID鍜孴oken鍊肩殑闆嗗悎锛岀敤浜庡幓閲?
  const existingUids = new Set(tokens.value.map(token => token.uid));
  const existingTokenValues = new Set(tokens.value.map(token => token.token));
  
  // 杩囨护閲嶅鐨凾oken锛堝熀浜嶶ID鎴朤oken鍊硷級
  const uniqueTokens = [];
  let duplicateCount = 0;
  
  for (const token of processedData) {
    if (!existingUids.has(token.uid) && !existingTokenValues.has(token.token)) {
      uniqueTokens.push(token);
      existingUids.add(token.uid);
      existingTokenValues.add(token.token);
    } else {
      duplicateCount++;
    }
  }
  
  // 鍚堝苟鐜版湁Token鍜屾柊瀵煎叆鐨勫敮涓€Token
  return {
    newTokens: [...tokens.value, ...uniqueTokens],
    duplicateCount
  };
};

// 鑾峰彇鐘舵€佸搴旂殑绫诲瀷
const getStatusType = (status) => {
  const statusMap = {
    '鍦ㄧ嚎': 'success',
    '鎺夌嚎': 'danger',
    '鏈煡': 'info',
    '姝ｅ父': 'success',
    '杩囨湡': 'danger',
    '寰呮縺娲?: 'warning',
    '宸查噸缃?: 'info'
  };
  return statusMap[status] || 'info';
};

// 椤甸潰鍔犺浇鏃跺彧璋冩暣琛ㄦ牸楂樺害
onMounted(() => {
  adjustTableHeight();
  
  // 鐩戝惉绐楀彛澶у皬鍙樺寲锛岃皟鏁磋〃鏍奸珮搴?
  window.addEventListener('resize', adjustTableHeight);
  
  // 鍒濆鍖栫瓫閫夋暟鎹?
  filterTokens();
});

// 鍏ㄩ€夋墍鏈夋暟鎹殑鍔熻兘
const selectAllTokens = () => {
  try {
    // 鍏堟樉绀哄姞杞戒腑
    loading.value = true;
    
    // 娓呴櫎琛ㄦ牸UI涓婄殑閫夋嫨鐘舵€?
    tokenTableRef.value?.clearSelection();
    
    // 鐩存帴灏嗘墍鏈夌瓫閫夌粨鏋滀綔涓洪€変腑椤?
    selectedTokens.value = JSON.parse(JSON.stringify(filteredTokensCache.value));
    
    // 璁剧疆瀹氭椂鍣紝纭繚鍚庣画鎿嶄綔鍦―OM鏇存柊鍚庢墽琛?
    setTimeout(() => {
      // 寮哄埗涓哄綋鍓嶉〉鐨勬瘡涓€琛岄兘璁剧疆閫変腑鐘舵€?
      if (tokenTableRef.value) {
        tableData.value.forEach(row => {
          tokenTableRef.value.toggleRowSelection(row, true);
        });
      }
      
      // 鏄剧ず鎴愬姛娑堟伅
      ElMessage.success(`宸查€変腑鍏ㄩ儴 ${selectedTokens.value.length} 鏉℃暟鎹甡);
      loading.value = false;
    }, 100);
  } catch (error) {
    console.error('鍏ㄩ€夋搷浣滈敊璇?', error);
    loading.value = false;
    ElMessage.error('鍏ㄩ€夋搷浣滃け璐ワ紝璇烽噸璇?);
  }
};

// 娓呴櫎鎵€鏈夐€夋嫨
const clearAllSelection = () => {
  try {
    // 璁板綍褰撳墠椤电爜锛岀‘淇濇竻闄ら€夋嫨鍚庝笉浼氭敼鍙橀〉鐮?
    const currentPageBeforeClearing = currentPage.value;
    
    // 娓呴櫎琛ㄦ牸UI涓婄殑閫夋嫨鐘舵€?
    tokenTableRef.value?.clearSelection();
    
    // 娓呴櫎閫夋嫨鏁版嵁
    selectedTokens.value = [];
    
    // 涓嶅啀閲嶇疆杩欎簺鍊硷紝鍙竻闄ら€夋嫨
    // totalTokenCount.value = 0;
    // filteredTokensCache.value = [];
    
    // 涓嶅啀閲嶇疆椤电爜
    // currentPage.value = 1;
    
    // 纭繚琛ㄦ牸閫夋嫨鐘舵€佽娓呴櫎
    if (tokenTableRef.value) {
      tokenTableRef.value.clearSelection();
    }
    
    // 璁剧疆涓嶉噸缃〉鐮佺殑鏍囧織
    shouldResetPage.value = false;
    
    // 杞婚噺绾у埛鏂帮紝鍙埛鏂拌〃鏍奸€夋嫨鐘舵€侊紝涓嶉噸缃〉鐮佹垨杩囨护鏉′欢
    nextTick(() => {
      // 鎭㈠鍘熼〉鐮?
      currentPage.value = currentPageBeforeClearing;
      // 搴旂敤琛ㄦ牸閫夋嫨鐘舵€?
      applyTableSelectionStatus();
    });
    
    ElMessage.success('宸叉竻闄ゆ墍鏈夐€夋嫨');
  } catch (error) {
    console.error('娓呴櫎閫夋嫨閿欒:', error);
    ElMessage.error('娓呴櫎閫夋嫨澶辫触锛岃閲嶈瘯');
  }
};

// 妫€鏌ユ槸鍚︽湁璺ㄩ〉閫夋嫨
const hasSelectionAcrossPages = computed(() => {
  return selectedTokens.value.length > tableData.value.length;
});

// 璋冩暣琛ㄦ牸楂樺害
const adjustTableHeight = () => {
  const windowHeight = window.innerHeight;
  // 杩涗竴姝ュ鍔犲噺鍘荤殑绌洪棿
  tableHeight.value = `${windowHeight - 400}px`; // 鍑忓幓鏇村绌洪棿缁欏垎椤靛櫒鍜屽叾浠栧厓绱?
};

// 鏂板鐨勮鐐瑰嚮澶勭悊鍑芥暟
const handleRowClick = (row, column) => {
  // 蹇界暐鐐瑰嚮閫夋嫨妗嗗拰鎿嶄綔鍒楃殑鎯呭喌锛屽洜涓鸿繖浜涘垪鏈夎嚜宸辩殑鐐瑰嚮琛屼负
  if (column.type === 'selection' || column.label === '鎿嶄綔') {
    return;
  }
  
  // 鍒囨崲琛岀殑閫夋嫨鐘舵€?
  if (tokenTableRef.value) {
    // 妫€鏌ュ綋鍓嶈鏄惁宸茶閫変腑
    const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
    
    // 鍒囨崲閫変腑鐘舵€?
    tokenTableRef.value.toggleRowSelection(row, !isSelected);
    
    // 鐢变簬toggleRowSelection浼氳Е鍙憇election-change浜嬩欢锛屾墍浠ヤ笉闇€瑕佸湪杩欓噷鎵嬪姩鏇存柊selectedTokens
  }
};

// 鏌ヨ鍦ㄧ嚎鐘舵€?
const handleQueryOnline = async () => {
  // 妫€鏌ユ槸鍚︽湁閫変腑鐨則oken
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('璇峰厛閫夋嫨瑕佹煡璇㈢殑Token');
    return;
  }

  // 纭瀵硅瘽妗?
  try {
    await ElMessageBox.confirm(
      `纭畾瑕佹煡璇㈤€変腑${selectedTokens.value.length}涓猅oken鐨勫湪绾跨姸鎬佸悧锛熸鎿嶄綔灏嗛€氳繃鍚庣骞跺彂澶勭悊锛岄€熷害鏇村揩銆俙,
      '鎿嶄綔纭',
      {
        confirmButtonText: '纭畾',
        cancelButtonText: '鍙栨秷',
        type: 'info'
      }
    );
    
    // 鐢ㄦ埛纭鍚庯紝寮€濮嬫煡璇?
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪鏌ヨ鍦ㄧ嚎鐘舵€?.',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 淇濆瓨褰撳墠椤电爜
    const currentPageBeforeQuery = currentPage.value;
    // 璁剧疆涓嶉噸缃〉鐮佹爣蹇?
    shouldResetPage.value = false;
    
    // 淇濆瓨鍘熷鐘舵€侊紝浠ヤ究鍦ㄥけ璐ユ椂鎭㈠
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));
    
    try {
      // 鎻愬彇閫変腑token鐨勪俊鎭?
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));
      
      // 璋冪敤鍚庣浼樺寲API杩涜鏌ヨ
      const result = await tokenService.queryOnlineStatusOptimized(selectedTokenData);
      
      if (result.success) {
        // 鏇存柊token鐘舵€?
        const updatedTokens = applyOnlineStatusToTokens(tokens.value, result.results);
        
        // 璁剧疆涓嶉噸缃〉鐮?
        shouldResetPage.value = false;
        tokens.value = updatedTokens;
        
        // 缁熻缁撴灉
        const onlineCount = result.results.filter(r => r.status === '鍦ㄧ嚎').length;
        const offlineCount = result.results.filter(r => r.status === '鎺夌嚎').length;
        
        // 鍒涘缓缁熻淇℃伅
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">鏌ヨ瀹屾垚锛屽叡澶勭悊 ${result.results.length} 涓猅oken</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">鍦ㄧ嚎</span> 
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">鎺夌嚎</span> 
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">鐘舵€佸凡鏇存柊鍒拌〃鏍间腑</div>
          </div>
        `;
        
        // 鏄剧ず鏌ヨ缁撴灉缁熻
        ElMessageBox.alert(statsHtml, '鏌ヨ缁撴灉', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '纭畾',
          callback: () => {
            // 鍒锋柊琛ㄦ牸鏃朵笉閲嶇疆椤电爜
            shouldResetPage.value = false;
            refreshTokens();
            
            // 纭繚鍦ㄥ埛鏂板悗淇濇寔鍦ㄥ師椤电爜
            nextTick(() => {
              currentPage.value = currentPageBeforeQuery;
            });
          }
        });
        
        ElMessage.success('鏌ヨ瀹屾垚锛岀姸鎬佸凡鏇存柊');
      } else {
        // 鏌ヨ澶辫触锛屾仮澶嶅師濮嬬姸鎬?
        tokens.value = originalTokens;
        ElMessage.error(`鏌ヨ澶辫触: ${result.error || '鏈煡閿欒'}`);
      }
    } catch (error) {
      console.error('鏌ヨ鍦ㄧ嚎鐘舵€佸紓甯?', error);
      // 鎭㈠鍘熷鐘舵€?
      tokens.value = originalTokens;
      ElMessage.error(`鏌ヨ寮傚父: ${error.message || '鏈煡閿欒'}`);
    } finally {
      // 鍏抽棴鍔犺浇鎻愮ず
      loadingInstance.close();
      
      // 纭繚淇濇寔鍦ㄥ師椤电爜
      nextTick(() => {
        currentPage.value = currentPageBeforeQuery;
      });
    }
  } catch (e) {
    // 鐢ㄦ埛鍙栨秷鎿嶄綔
    if (e !== 'cancel') {
      console.error('纭瀵硅瘽妗嗗紓甯?', e);
    }
  }
};

// 鏌ヨ澶村儚鏄电О
const handleQueryAvatarNickname = async () => {
  // 妫€鏌ユ槸鍚︽湁閫変腑鐨則oken
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('璇峰厛閫夋嫨瑕佹煡璇㈢殑Token');
    return;
  }

  // 纭瀵硅瘽妗?
  try {
    await ElMessageBox.confirm(
      `纭畾瑕佹煡璇㈤€変腑${selectedTokens.value.length}涓猅oken鐨勫ご鍍忓拰鏄电О鍚楋紵姝ゆ搷浣滃皢閫氳繃鍚庣骞跺彂澶勭悊锛岄€熷害鏇村揩銆俙,
      '鎿嶄綔纭',
      {
        confirmButtonText: '纭畾',
        cancelButtonText: '鍙栨秷',
        type: 'info'
      }
    );
    
    // 鐢ㄦ埛纭鍚庯紝寮€濮嬫煡璇?
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪鏌ヨ澶村儚鍜屾樀绉?.',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 淇濆瓨鍘熷鐘舵€侊紝浠ヤ究鍦ㄥけ璐ユ椂鎭㈠
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));
    
    try {
      // 鎻愬彇閫変腑token鐨勪俊鎭?
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));
      
      // 璋冪敤鍚庣API杩涜鏌ヨ
      const result = await tokenService.queryTokenUserInfo(selectedTokenData);
      
      if (result.success) {
        // 鏇存柊token鐨勫ご鍍忓拰鏄电О
        const updatedTokens = tokens.value.map(token => {
          const userInfo = result.results.find(r => r.uid === token.uid);
          if (userInfo && userInfo.success && userInfo.data) {
            return {
              ...token,
              avatar: userInfo.data.avatar || token.avatar,
              nickname: userInfo.data.nickname || token.nickname,
              // 濡傛灉token鍦ㄧ嚎锛屼篃鏇存柊鐘舵€?
              status: userInfo.data.isOnline ? '鍦ㄧ嚎' : token.status
            };
          }
          return token;
        });
        
        tokens.value = updatedTokens;
        
        // 缁熻缁撴灉
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;
        
        // 鍒涘缓缁熻淇℃伅
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">鏌ヨ瀹屾垚锛屽叡澶勭悊 ${result.results.length} 涓猅oken</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">鎴愬姛</span> 
                <span style="font-size: 18px;">${successCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">澶辫触</span> 
                <span style="font-size: 18px;">${failCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">澶村儚鍜屾樀绉颁俊鎭凡鏇存柊鍒拌〃鏍间腑</div>
          </div>
        `;
        
        // 鏄剧ず鏌ヨ缁撴灉缁熻
        ElMessageBox.alert(statsHtml, '鏌ヨ缁撴灉', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '纭畾',
          callback: () => {
            // 鍒锋柊琛ㄦ牸
            refreshTokens();
          }
        });
        
        ElMessage.success('鏌ヨ瀹屾垚锛屽ご鍍忓拰鏄电О宸叉洿鏂?);
      } else {
        // 鏌ヨ澶辫触锛屾仮澶嶅師濮嬬姸鎬?
        tokens.value = originalTokens;
        ElMessage.error(`鏌ヨ澶辫触: ${result.error || '鏈煡閿欒'}`);
      }
    } catch (error) {
      console.error('鏌ヨ澶村儚鏄电О寮傚父:', error);
      // 鎭㈠鍘熷鐘舵€?
      tokens.value = originalTokens;
      ElMessage.error(`鏌ヨ寮傚父: ${error.message || '鏈煡閿欒'}`);
    } finally {
      // 鍏抽棴鍔犺浇鎻愮ず
      loadingInstance.close();
    }
  } catch (e) {
    // 鐢ㄦ埛鍙栨秷鎿嶄綔
    if (e !== 'cancel') {
      console.error('纭瀵硅瘽妗嗗紓甯?', e);
    }
  }
};

// 鏌ヨ璁㈠崟淇℃伅
const handleQueryOrder = async () => {
  // 妫€鏌ユ槸鍚︽湁閫変腑鐨則oken
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('璇峰厛閫夋嫨瑕佹煡璇㈢殑Token');
    return;
  }
  
  try {
    // 鎻愮ず鐢ㄦ埛纭鎿嶄綔
    await ElMessageBox.confirm(
      `纭畾瑕佹煡璇?{selectedTokens.value.length}涓€変腑Token鐨勮鍗曚俊鎭悧锛焋,
      '鏌ヨ璁㈠崟淇℃伅',
      {
        confirmButtonText: '纭畾',
        cancelButtonText: '鍙栨秷',
        type: 'warning'
      }
    );
    
    // 鐢ㄦ埛纭鍚庯紝寮€濮嬫煡璇?
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪鏌ヨ璁㈠崟淇℃伅...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 淇濆瓨鍘熷鐘舵€侊紝浠ヤ究鍦ㄥけ璐ユ椂鎭㈠
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));
    
    try {
      // 鎻愬彇閫変腑token鐨勪俊鎭?
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));
      
      // 璋冪敤API杩涜鏌ヨ
      const result = await tokenService.queryTokenOrderInfo(selectedTokenData);
      
      if (result.success) {
        // 鏇存柊token鐨勮鍗曚俊鎭?
        const updatedTokens = tokens.value.map(token => {
          const orderResult = result.results.find(r => r.uid === token.uid);
          if (orderResult && orderResult.success) {
            return {
              ...token,
              // 鍚屾椂鏇存柊token鐨勪富status灞炴€э紝浣垮湪绾跨姸鎬佸湪涓昏〃鏍间腑鏄剧ず
              status: orderResult.status || token.status,
              orderInfo: {
                isOnline: orderResult.isOnline,
                status: orderResult.status || '鏈煡',
                orderCount: orderResult.data?.orderCount || 0,
                orders: orderResult.data?.orders || [],
                message: orderResult.message
              }
            };
          }
          return {
            ...token,
            // 濡傛灉鏌ヨ澶辫触涔熻鏇存柊涓荤姸鎬?
            status: orderResult?.status || token.status,
            orderInfo: orderResult ? {
              isOnline: orderResult.isOnline || false,
              status: orderResult.status || '鏈煡',
              orderCount: 0,
              orders: [],
              message: orderResult.message || '鏌ヨ澶辫触'
            } : null
          };
        });
        
        // 璁剧疆涓嶉噸缃〉鐮佺殑鏍囧織
        shouldResetPage.value = false;
        
        // 鏇存柊tokens骞朵繚鎸佸綋鍓嶉〉鐮?
        tokens.value = updatedTokens;
        
        // 缁熻缁撴灉
        const onlineCount = result.results.filter(r => r.status === '鍦ㄧ嚎').length;
        const offlineCount = result.results.filter(r => r.status === '鎺夌嚎').length;
        const hasOrderCount = result.results.filter(r => r.success && r.status === '鍦ㄧ嚎' && r.data && r.data.orderCount > 0).length;
        
        ElNotification({
          title: '鏌ヨ鎴愬姛',
          message: `鍏卞鐞?{result.stats.total}涓猅oken锛屽叾涓?{onlineCount}涓湪绾匡紝${hasOrderCount}涓湁璁㈠崟`,
          type: 'success',
          duration: 5000
        });
        
        // 鍒涘缓缁熻淇℃伅
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">鏌ヨ瀹屾垚锛屽叡澶勭悊 ${result.stats.total} 涓猅oken</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">鍦ㄧ嚎</span> 
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">鎺夌嚎</span> 
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #e6f2ff; color: #409EFF; font-weight: bold;">
                <span style="margin-right: 5px;">鏈夎鍗?/span> 
                <span style="font-size: 18px;">${hasOrderCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">璁㈠崟淇℃伅宸叉洿鏂板埌琛ㄦ牸涓紝鐐瑰嚮"璁㈠崟鏁伴噺"鍙煡鐪嬭缁嗕俊鎭?/div>
          </div>
        `;
        
        // 鏄剧ず鏌ヨ缁撴灉缁熻
        ElMessageBox.alert(statsHtml, '鏌ヨ缁撴灉', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '纭畾',
          callback: () => {
            // 鍒锋柊琛ㄦ牸
            refreshTokens();
          }
        });
      } else {
        // 鏌ヨ澶辫触锛屾仮澶嶅師濮嬬姸鎬?
        tokens.value = originalTokens;
        ElMessage.error(`鏌ヨ澶辫触: ${result.message || '鏈煡閿欒'}`);
      }
    } catch (error) {
      console.error('鏌ヨ璁㈠崟淇℃伅寮傚父:', error);
      // 鎭㈠鍘熷鐘舵€?
      tokens.value = originalTokens;
      ElMessage.error(`鏌ヨ寮傚父: ${error.message || '鏈煡閿欒'}`);
    } finally {
      // 鍏抽棴鍔犺浇鎻愮ず
      loadingInstance.close();
    }
  } catch (e) {
    // 鐢ㄦ埛鍙栨秷鎿嶄綔
    if (e !== 'cancel') {
      console.error('纭瀵硅瘽妗嗗紓甯?', e);
    }
  }
};

// 鑾峰彇璁㈠崟鐘舵€佹爣绛剧被
const getOrderStatusTag = (status) => {
  if (!status) return 'info';
  if (status.includes('寰呮敹娆?) || status.includes('寰呭彂璐?)) return 'warning';
  if (status.includes('宸叉敹娆?) || status.includes('宸插畬缁?)) return 'success';
  if (status.includes('宸插彇浠?) || status.includes('宸插叧闂?)) return 'danger';
  return 'info';
};

// 鏍煎紡鍖栬鍗旾D
const formatOrderId = (id) => {
  if (id.length > 10) {
    return id.slice(0, 5) + '...' + id.slice(-5);
  }
  return id;
};

// 鑾峰彇璁㈠崟宸ュ叿鎻愮ず
const getOrderTooltip = (orderInfo) => {
  if (orderInfo.detail) {
    return orderInfo.detail;
  }
  return orderInfo.id ? orderInfo.id : '鏃犺鍗曚俊鎭?;
};

// 鏌ョ湅璁㈠崟璇︽儏
const ordersDialogVisible = ref(false);
const currentOrders = ref([]);
const currentTokenUid = ref('');
const orderDetailDialogVisible = ref(false);
const currentOrderDetail = ref(null);

// 澶勭悊鏌ョ湅璁㈠崟
const handleViewOrders = (row) => {
  if (row.orderInfo && row.orderInfo.orders && row.orderInfo.orders.length > 0) {
    // 涓烘瘡涓鍗曟坊鍔犳墍灞瀟oken鐨勪俊鎭紝浠ヤ究鏀寔璁㈠崟璇︽儏椤典腑鐨勫垹闄ゅ姛鑳?
    currentOrders.value = row.orderInfo.orders.map(order => ({
      ...order,
      uid: row.uid,
      tokenValue: row.token
    }));
    currentTokenUid.value = row.uid;
    ordersDialogVisible.value = true;
  } else {
    ElMessage.warning('娌℃湁鎵惧埌璁㈠崟淇℃伅');
  }
};

// 鏌ョ湅鍗曚釜璁㈠崟璇︽儏
const viewOrderDetail = (order) => {
  // 浼犻€掕鍗曟墍灞瀟oken鐨剈id鍜宼oken鍊硷紝浠ヤ究鑳藉鍒犻櫎璁㈠崟
  if (order) {
    const tokenInfo = order.tokenInfo || {};
    currentOrderDetail.value = {
      ...order,
      uid: order.uid || tokenInfo.uid,
      tokenValue: order.tokenValue || tokenInfo.token
    };
  orderDetailDialogVisible.value = true;
  }
};

// 鏍煎紡鍖栬鍗曟椂闂?
const formatOrderTime = (time) => {
  if (!time) return '鏈煡';
  return time;
};

// 鍒犻櫎璁㈠崟淇℃伅
const handleDeleteOrder = async () => {
  // 妫€鏌ユ槸鍚︽湁閫変腑鐨則oken
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('璇峰厛閫夋嫨瑕佸垹闄よ鍗曠殑Token');
    return;
  }

  // 璇㈤棶鐢ㄦ埛鏄惁纭鍒犻櫎
  try {
    await ElMessageBox.confirm(
      `纭畾瑕佸垹闄ら€変腑Token鐨勮鍗曞悧锛熺郴缁熷皢鑷姩鏌ヨ骞跺苟鍙戝垹闄ゆ墍鏈夊彲鍒犻櫎鐨勮鍗曘€俙,
      '鎵归噺鍒犻櫎璁㈠崟纭',
      {
        confirmButtonText: '纭鍒犻櫎',
        cancelButtonText: '鍙栨秷',
        type: 'warning'
      }
    );
  } catch (error) {
    return; // 鐢ㄦ埛鍙栨秷鎿嶄綔
  }

  // 浣跨敤ElLoading鏇夸唬杩涘害瀵硅瘽妗?
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '姝ｅ湪鍒犻櫎璁㈠崟锛岃绋嶇瓑..',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  // 淇濆瓨褰撳墠椤电爜
  const currentPageBeforeDelete = currentPage.value;
  // 璁剧疆涓嶉噸缃〉鐮佹爣蹇?
  shouldResetPage.value = false;
  
  try {
    // 浣跨敤骞跺彂鏌ヨ鍜屽垹闄わ紝涓嶅啀鏄剧ず杩涘害
    const result = await orderService.concurrentQueryAndDeleteOrders(
      selectedTokens.value,
      {
        batchSize: 20, // 姣忔壒20涓鍗?
        concurrency: 5, // 5涓苟鍙戣姹?
        // 涓嶅啀闇€瑕佽繘搴﹀洖浼?
      }
    );
    
    // 澶勭悊缁撴灉
    if (result.success) {
      // 妫€鏌ユ槸鍚︽湁鎴愬姛鍒犻櫎鐨勮鍗?
      const successCount = result.results.filter(r => r.success).length;
      const failCount = result.results.length - successCount;
      
      // 鏇存柊鏈湴鏁版嵁锛屼粠璁㈠崟鍒楄〃涓Щ闄ゅ凡鍒犻櫎鐨勮鍗?
      updateLocalOrdersAfterDeletion(result.results);
      
      // 澶勭悊鏌ヨ缁撴灉锛屾洿鏂癠I鏄剧ず
      if (result.queriedTokens && result.queriedTokens.length > 0) {
        // 鏇存柊宸叉煡璇絾娌℃湁鍙垹闄よ鍗曠殑Token鐘舵€?
        updateTokensAfterQuery(result.queriedTokens);
      }
      
      // 浣跨敤閫氱煡鏄剧ず缁撴灉鎽樿
      if (successCount > 0) {
        ElNotification({
          title: '鍒犻櫎璁㈠崟瀹屾垚',
          message: `鎴愬姛鍒犻櫎 ${successCount} 涓鍗?{failCount > 0 ? `锛屽け璐?${failCount} 涓猔 : ''}`,
          type: 'success',
          duration: 5000
        });
      } else {
        // 鎵€鏈夊垹闄ら兘澶辫触鐨勬儏
        ElNotification({
          title: '鍒犻櫎璁㈠崟鏈垚鍔?,
          message: `鏈兘鎴愬姛鍒犻櫎浠讳綍璁㈠崟${result.queriedTokens ? `锛屽凡鏌ヨ ${result.queriedTokens.length} 涓猅oken` : ''}銆傝妫€鏌ヨ鍗曠姸鎬佹槸鍚︿负"宸茶瘎浠?銆俙,
          type: 'warning',
          duration: 5000
        });
      }
      
      // 淇濊瘉涓嶉噸缃〉鐮佺殑鎯呭喌涓嬪埛鏂拌〃鏍兼暟鎹?
      shouldResetPage.value = false;
      refreshTokens();
      
      // 纭繚鍦ㄥ埛鏂板悗淇濇寔椤电爜
      nextTick(() => {
        currentPage.value = currentPageBeforeDelete;
      });
    } else {
      // API璋冪敤澶辫触
      ElMessage.error(result.message || '鏌ヨ鎴栧垹闄よ鍗曞け璐?);
      
      // 鏄剧ず璇︾粏閿欒淇℃伅
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <p><strong>閿欒淇℃伅:</strong> ${result.message || '鏈煡閿欒'}</p>
          <p style="margin-top: 10px; font-size: 12px; color: #909399;">
            璇风‘璁わ細
            <ul>
              <li>Token鏄惁鍦ㄧ嚎</li>
              <li>Token鏉冮檺鏄惁鏈夋晥</li>
              <li>璁㈠崟鐘舵€佹槸鍚︿负"宸茶瘎浠?</li>
            </ul>
          </p>
        </div>`,
        '鍒犻櫎璁㈠崟澶辫触',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '鎴戠煡閬撲簡',
          type: 'error'
        }
      );
    }
  } catch (error) {
    console.error('鍒犻櫎璁㈠崟閿欒:', error);
    
    // 鏇磋缁嗙殑閿欒鎻愮ず
    ElMessage.error(`鎿嶄綔寮傚父: ${error.message || '鏈煡閿欒'}`);
    
    // 鏄剧ず鎶€鏈粏鑺?
    ElMessageBox.alert(
      `<div style="text-align: left;">
        <p><strong>閿欒淇℃伅:</strong> ${error.message || '鏈煡閿欒'}</p>
        <p style="margin-top: 10px; font-size: 12px; color: #909399;">
          鍙兘鐨勫師鍥狅細
          <ul>
            <li>缃戠粶杩炴帴闂</li>
            <li>鏈嶅姟鍣ㄥ搷搴旇秴鏃?/li>
            <li>API闄愭祦鎴栨潈闄愰棶棰?/li>
          </ul>
        </p>
      </div>`,
      '绯荤粺閿欒',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '鎴戠煡閬撲簡',
        type: 'error'
      }
    );
    
    // 鏇存柊token鐨勮鍗曠姸鎬佷负澶辫触
    selectedTokens.value.forEach(token => {
      if (token.orderDeletionStatus) {
        token.orderDeletionStatus.inProgress = false;
      }
    });
  } finally {
    // 鍏抽棴鍔犺浇鎻愮ず
    loadingInstance.close();
    
    // 纭繚淇濇寔鍦ㄥ師椤电爜
    nextTick(() => {
      currentPage.value = currentPageBeforeDelete;
    });
  }
};

// 鏂板锛氭洿鏂板凡鏌ヨ浣嗘病鏈夊彲鍒犻櫎璁㈠崟鐨凾oken鐘舵€?
const updateTokensAfterQuery = (queriedTokens) => {
  if (!queriedTokens || queriedTokens.length === 0) return;
  
  console.log('鏇存柊宸叉煡璇oken鐘舵€?', queriedTokens);
  
  // 璁剧疆涓嶉噸缃〉鐮佺殑鏍囧織
  shouldResetPage.value = false;
  
  // 鏇存柊鏌ヨ鍒扮殑token淇℃伅
  tokens.value = tokens.value.map(token => {
    // 鏌ユ壘褰撳墠token鏄惁鍦ㄦ煡璇㈢粨鏋滀腑
    const queriedToken = queriedTokens.find(qt => qt.uid === token.uid);
    
    if (queriedToken) {
      // 涓簍oken娣诲姞鏌ヨ缁撴灉
      return {
        ...token,
        orderInfo: {
          isOnline: queriedToken.isOnline !== undefined ? queriedToken.isOnline : true,
          status: queriedToken.status || '鍦ㄧ嚎',
          orderCount: queriedToken.orders?.length || 0,
          orders: queriedToken.orders || [],
          message: '鏌ヨ鎴愬姛'
        }
      };
    }
    
    return token;
  });
};

// 鏇存柊鏈湴璁㈠崟鏁版嵁锛堝垹闄ゅ悗锛?
const updateLocalOrdersAfterDeletion = (results) => {
  // 璁剧疆涓嶉噸缃〉鐮佺殑鏍囧織锛岄槻姝㈠垹闄よ鍗曞悗璺宠浆鍒扮涓€椤?
  shouldResetPage.value = false;
  
  // 淇濆瓨褰撳墠椤电爜
  const currentPageBeforeUpdate = currentPage.value;
  
  // 鍒嗙鎴愬姛鍜屽け璐ョ殑缁撴灉
  const successfullyDeletedOrders = results
    .filter(r => r.success)
    .map(r => r.orderSn);
  
  const failedDeletes = results.filter(r => !r.success);
  
  // 杈撳嚭閿欒淇℃伅
  if (failedDeletes.length > 0) {
    console.warn('鍒犻櫎澶辫触鐨勮鍗?', failedDeletes);
    
    // 鎻愬彇閿欒娑堟伅骞剁粺璁?
    const errorMessages = {};
    failedDeletes.forEach(result => {
      const msg = result.message || '鏈煡閿欒';
      errorMessages[msg] = (errorMessages[msg] || 0) + 1;
    });
    
    // 鏋勫缓閿欒淇℃伅HTML
    let errorHtml = `
      <div style="margin-top: 15px;">
        <div style="font-weight: bold; color: #f56c6c; margin-bottom: 10px;">鍒犻櫎澶辫触鍘熷洜:</div>
        <ul style="text-align: left; margin: 0; padding-left: 20px;">
    `;
    
    for (const [message, count] of Object.entries(errorMessages)) {
      errorHtml += `<li>${message} (${count}涓鍗?</li>`;
    }
    
    errorHtml += `
        </ul>
        <div style="margin-top: 10px; font-size: 12px; color: #909399;">
          甯歌鍘熷洜锛氳鍗曟湭璇勪环銆乀oken宸插け鏁堟垨鑰呮棤鏉冮檺鎿嶄綔
        </div>
      </div>
    `;
    
    // 鏄剧ず閿欒淇℃伅
    if (failedDeletes.length > 0) {
      ElMessageBox.alert(
        errorHtml,
        '璁㈠崟鍒犻櫎澶辫触璇︽儏',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '鎴戠煡閬撲簡',
          type: 'warning'
        }
      );
    }
  }
  
  if (successfullyDeletedOrders.length === 0) {
    // 濡傛灉娌℃湁鎴愬姛鍒犻櫎浠讳綍璁㈠崟锛屽垯鏄剧ず鎻愮ず骞惰繑鍥?
    if (results.length > 0) {
      ElMessage.warning('娌℃湁鎴愬姛鍒犻櫎浠讳綍璁㈠崟锛岃鏌ョ湅澶辫触璇︽儏');
    }
    return;
  }
  
  console.log('寮€濮嬫洿鏂版湰鍦拌鍗曟暟鎹紝鎴愬姛鍒犻櫎鐨勮鍗曟暟:', successfullyDeletedOrders.length);
  
  // 鏇存柊token鐨勮鍗曞垪
  tokens.value = tokens.value.map(token => {
    if (token.orderInfo && token.orderInfo.orders) {
      // 璁＄畻瀹為檯琚垹闄ょ殑璁㈠崟鏁伴噺锛堥€氳繃姣旇緝鍘熷璁㈠崟鍜屽墿浣欒鍗曪級
      const originalOrders = token.orderInfo.orders;
      const updatedOrders = originalOrders.filter(
        order => !successfullyDeletedOrders.includes(order.orderId)
      );
      
      // 璁＄畻鏈瀹為檯鍒犻櫎鐨勮鍗曟暟閲?
      const deletedCount = originalOrders.length - updatedOrders.length;
      console.log(`Token ${token.uid}: 鍘熷璁㈠崟鏁伴噺=${originalOrders.length}, 鏇存柊鍚庤鍗曟暟閲?${updatedOrders.length}, 鍒犻櫎鏁伴噺=${deletedCount}`);
      
      // 鍙湪鏈夎鍗曡鍒犻櫎鏃舵洿鏂?
      if (deletedCount > 0) {
        // 纭繚淇濈暀鍘熷orderInfo涓殑鍏朵粬瀛楁
        const updatedOrderInfo = {
          ...token.orderInfo,
          orders: updatedOrders,
          orderCount: updatedOrders.length,  // 纭繚orderCount涓哄疄闄呭墿浣欒鍗曟暟閲?
          // 淇濈暀浠ヤ笅瀛楁纭繚UI鏄剧ず姝ｅ父
          isOnline: token.orderInfo.isOnline,
          status: token.orderInfo.status
        };
        
        // 鏇存柊鍒犻櫎鐘舵€?
        const updatedDeletionStatus = {
          totalOrders: originalOrders.length,
          successCount: deletedCount,
          failCount: 0,
          inProgress: false
        };
        
        // 杩斿洖鏇存柊鍚庣殑token瀵硅薄
        return {
          ...token,
          orderInfo: updatedOrderInfo,
          orderDeletionStatus: updatedDeletionStatus,
          // 鏇存柊currentDisplayOrders鍜宒eletableOrders
          currentDisplayOrders: updatedOrders,
          deletableOrders: updatedOrders.filter(order => isDeletableOrder(order.status))
        };
      }
    }
    return token;
  });
  
  // 鏇存柊褰撳墠鏄剧ず鐨勮鍗曞垪
  if (currentOrders.value.length > 0) {
    currentOrders.value = currentOrders.value.filter(
      order => !successfullyDeletedOrders.includes(order.orderId)
    );
  }
  
  // 鏇存柊鍙垹闄よ鍗曞垪
  deletableOrders.value = deletableOrders.value.filter(
    order => !successfullyDeletedOrders.includes(order.orderId)
  );
  
  // 寮哄埗鍒锋柊琛ㄦ牸鏄剧ず
  nextTick(() => {
    // 淇濊瘉涓嶉噸缃〉鐮?
    currentPage.value = currentPageBeforeUpdate;
    
    // 閫氱煡琛ㄦ牸鏁版嵁宸茬粡鏀瑰彉
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
    }
    // 搴旂敤琛ㄦ牸閫夋嫨鐘舵€?
    applyTableSelectionStatus();
  });
};

// 鏄剧ず鍒犻櫎缁撴灉璇︽儏 - 淇敼涓轰粎鏄剧ず绠€鍗曟秷鎭?
const showDeletionResultDetails = (results) => {
  const successResults = results.filter(r => r.success);
  const failResults = results.filter(r => !r.success);
  
  // 鏄剧ず绠€鍗曠殑鎴愬姛娑堟伅锛屼笉鍐嶅脊鍑鸿缁嗙粨鏋滃璇濇
  if (successResults.length > 0) {
    ElMessage.success(`鎴愬姛鍒犻櫎 ${successResults.length} 涓鍗?{failResults.length > 0 ? `锛屽け璐?${failResults.length} 涓猔 : ''}`);
  } else if (failResults.length > 0) {
    ElMessage.warning(`鍏ㄩ儴 ${failResults.length} 涓鍗曞垹闄ゅけ璐);
  }
};

// 娓呯┖琛ㄦ牸
const handleClearTable = () => {
  ElMessageBox.confirm(
    '纭畾瑕佹竻绌哄綋鍓嶈〃鏍间腑鐨勬墍鏈夋暟鎹悧锛熸鎿嶄綔涓嶅彲鎭㈠锛?,
    '娓呯┖琛ㄦ牸纭',
    {
      confirmButtonText: '纭畾娓呯┖',
      cancelButtonText: '鍙栨秷',
      type: 'warning',
      distinguishCancelAndClose: true,
      customClass: 'warning-confirm-dialog'
    }
  )
    .then(() => {
      // 娓呯┖鏁版嵁鏁扮粍
      tokens.value = [];
      // 閲嶇疆鐩稿叧鐘舵€?
      selectedTokens.value = [];
      totalTokenCount.value = 0;
      filteredTokensCache.value = [];
      
      // 娓呯┖琛ㄦ牸鏄敤鎴锋槑纭殑鎿嶄綔锛屾墍浠ュ湪姝ゆ儏鍐典笅閲嶇疆椤电爜鏄悎鐞嗙殑
      currentPage.value = 1;
      
      // 娓呴櫎琛ㄦ牸閫夋嫨鐘舵€?
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }
      
      // 鍒锋柊绛涢€夌姸鎬?
      filterTokens();
      
      ElMessage.success('琛ㄦ牸鏁版嵁宸插叏閮ㄦ竻绌?);
    })
    .catch(() => {
      // 鐢ㄦ埛鍙栨秷鎿嶄綔锛屼笉鍋氫换浣曞鐞?
    });
};

// 澶勭悊鍗曚釜璁㈠崟鍒犻櫎
const handleDeleteSingleOrder = async (orderDetail) => {
  if (!orderDetail || !orderDetail.uid || !orderDetail.tokenValue || !orderDetail.orderId) {
    ElMessage.warning('璁㈠崟淇℃伅涓嶅畬鏁达紝鏃犳硶鍒犻櫎');
    return;
  }

  // 纭鏄惁鍙垹闄?
  if (!isDeletableOrder(orderDetail.status)) {
    ElMessage.warning(`鍙兘鍒犻櫎宸茶瘎浠风殑璁㈠崟锛屽綋鍓嶈鍗曠姸鎬佷负"${orderDetail.status}"`);
    return;
  }

  try {
    // 纭鍒犻櫎
    await ElMessageBox.confirm(
      `纭畾瑕佸垹闄よ鍗?${orderDetail.orderId} 鍚楋紵姝ゆ搷浣滀笉鍙仮澶嶏紒`,
      '鍒犻櫎璁㈠崟纭',
      {
        confirmButtonText: '纭畾鍒犻櫎',
        cancelButtonText: '鍙栨秷',
        type: 'warning'
      }
    );
    
    // 鏄剧ずloading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪鍒犻櫎璁㈠崟...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
      // 璋冪敤API鍒犻櫎璁㈠崟
      const result = await tokenService.deleteOrder(
        orderDetail.uid,
        orderDetail.tokenValue,
        orderDetail.orderId
      );
      
      if (result.success) {
        // 鍒犻櫎鎴愬姛锛屽叧闂鍗曡鎯呭璇濇
        orderDetailDialogVisible.value = false;
        
        // 鏇存柊鏈湴鏁版嵁
        updateLocalOrdersAfterDeletion([{
          success: true,
          orderSn: orderDetail.orderId
        }]);
        
        ElMessage.success('璁㈠崟鍒犻櫎鎴愬姛');
        
        // 濡傛灉涓昏鍗曞垪琛ㄥ璇濇涔熸墦寮€锛屾洿鏂板畠
        if (ordersDialogVisible.value && currentOrders.value.length > 0) {
          // 浠庡綋鍓嶅垪琛ㄤ腑杩囨护鎺夊凡鍒犻櫎鐨勮鍗?
          currentOrders.value = currentOrders.value.filter(
            order => order.orderId !== orderDetail.orderId
          );
        }
      } else {
        // 澧炲己閿欒淇℃伅鏄剧ず
        const errorMsg = result.message || '鍒犻櫎澶辫触';
        ElMessage.error(errorMsg);
        
        // 鏋勫缓鏇磋缁嗙殑閿欒鎻愮ず
        let detailsHtml = `
          <div style="text-align: left; margin: 15px 0;">
            <h3 style="margin-bottom: 10px;">鍒犻櫎澶辫触璇︽儏</h3>
            <div style="color: #f56c6c;">
              <p>${errorMsg}${result.errorCode ? ` (閿欒鐮? ${result.errorCode})` : ''}</p>
            </div>
            
            <div style="margin-top: 15px; font-size: 14px;">
              <p>鍙兘鐨勫師鍥狅細</p>
              <ul style="padding-left: 20px; margin-top: 5px;">
                <li>璁㈠崟鐘舵€佷笉鏄?宸茶瘎浠?(鎷煎澶氳姹傚繀椤绘槸涓ユ牸鐨?宸茶瘎浠?)</li>
                <li>Token宸插け鏁堟垨娌℃湁瓒冲鏉冮檺</li>
                <li>缃戠粶闂鎴朅PI闄愭祦</li>
              </ul>
            </div>
        `;
        
        if (result.responseData) {
          detailsHtml += `
            <div style="margin-top: 15px;">
              <details>
                <summary>API鍝嶅簲璇︽儏</summary>
                <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(result.responseData, null, 2)}</pre>
              </details>
            </div>
          `;
        }
        
        detailsHtml += `</div>`;
        
        // 鏄剧ず璇︾粏閿欒
        ElMessageBox.alert(detailsHtml, '鍒犻櫎澶辫触', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '纭畾'
        });
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('鍒犻櫎璁㈠崟閿欒:', error);
      ElMessage.error(`鎿嶄綔寮傚父: ${error.message || '鏈煡閿欒'}`);
    }
  }
};

// 璁㈠崟鍒楄〃琛ㄦ牸閫夋嫨鐩稿叧
const orderTableRef = ref(null);
const selectedOrdersInList = ref([]);
const deletableOrdersCount = computed(() => {
  return currentOrders.value.filter(order => isDeletableOrder(order.status)).length;
});

// 澶勭悊璁㈠崟鍒楄〃琛ㄦ牸鐨勯€夋嫨鍙樺寲
const handleOrderSelectionChange = (selection) => {
  selectedOrdersInList.value = selection;
};

// 鍏ㄩ€夊彲鍒犻櫎鐨勮鍗?
const selectAllDeletableOrders = () => {
  // 鍏堟竻闄ゆ墍鏈夐€夋嫨
  orderTableRef.value?.clearSelection();
  
  // 鐒跺悗閫変腑鎵€鏈夊彲鍒犻櫎鐨勮鍗? currentOrders.value.forEach(order => {
  currentOrders.value.forEach(order => {
    if (isDeletableOrder(order.status)) {
      orderTableRef.value?.toggleRowSelection(order, true);
    }
  });
};

// 鎵归噺鍒犻櫎閫変腑鐨勮鍗?
const batchDeleteSelectedOrders = async () => {
  if (selectedOrdersInList.value.length === 0) {
    ElMessage.warning('璇峰厛閫夋嫨瑕佸垹闄ょ殑璁㈠崟');
    return;
  }

  try {
    // 纭鏄惁鍒犻櫎
    await ElMessageBox.confirm(
      `纭畾瑕佸垹闄ら€変腑鐨?${selectedOrdersInList.value.length} 涓鍗曞悧锛焋,
      '鍒犻櫎璁㈠崟纭',
      {
        confirmButtonText: '纭畾鍒犻櫎',
        cancelButtonText: '鍙栨秷',
        type: 'warning'
      }
    );
    
    // 鏄剧ずloading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪鍒犻櫎璁㈠崟...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
      // 鏋勫缓鍒犻櫎璇锋眰鏁版嵁
      const orderItems = selectedOrdersInList.value.map(order => ({
        uid: order.uid,
        token: order.tokenValue,
        orderSn: order.orderId
      }));
      
      // 璋冪敤鎵归噺鍒犻櫎API
      const result = await tokenService.batchDeleteOrders(orderItems);
      
      if (result.success) {
        // 缁熻缁撴灉
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;
        
        // 鏇存柊鏈湴鏁版嵁锛屼粠璁㈠崟鍒楄〃涓Щ闄ゅ凡鍒犻櫎鐨勮鍗?
        updateLocalOrdersAfterDeletion(result.results);
        
        // 娓呴櫎琛ㄦ牸閫夋嫨
        selectedOrdersInList.value = [];
        orderTableRef.value?.clearSelection();
        
        // 浣跨敤閫氱煡鏄剧ず璇︾粏缁撴灉
        ElNotification({
          title: '鍒犻櫎璁㈠崟瀹屾垚',
          message: `鎴愬姛鍒犻櫎 ${successCount} 涓鍗?{failCount > 0 ? `锛?{failCount} 涓垹闄ゅけ璐 : ''}`,
          type: 'success',
          duration: 5000
        });
        
        // 寮哄埗鍒锋柊琛ㄦ牸鏁版嵁
        refreshTokens();
      } else {
        ElMessage.error(`鎵归噺鍒犻櫎澶辫触: ${result.message || '鏈煡閿欒'}`);
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('鎵归噺鍒犻櫎璁㈠崟閿欒:', error);
      ElMessage.error(`鎿嶄綔寮傚父: ${error.message || '鏈煡閿欒'}`);
    }
  }
};

// 鍒ゆ柇璁㈠崟鏄惁鍙垹闄わ紙鍙厑璁稿凡璇勪环鐨勮鍗曪級
const isDeletableOrder = (status) => {
  if (!status) return false;
  
  // 璁板綍璁㈠崟鐘舵€侊紝甯姪鎴戜滑浜嗚В涓嶅悓绫诲瀷鐨勭姸鎬?
  console.log('妫€鏌ヨ鍗曟槸鍚﹀彲鍒犻櫎锛岀姸鎬?, status);
  
  // 涓ユ牸鍖归厤"宸茶瘎浠?鐘舵€?
  // 鍙湁瀹屽叏鍖归厤"宸茶瘎浠?鐨勮鍗曟墠鑳藉垹闄わ紝鏍规嵁鎷煎澶欰PI鐨勮姹?
  if (status === '宸茶瘎浠?) {
    return true;
  }
  
  // 鍖呭惈"宸茶瘎浠?鐨勭姸鎬佷篃鍙兘鍙互鍒犻櫎
  if (status.includes('宸茶瘎浠?)) {
    return true;
  }
  
  // 鍏朵粬鐘舵€佷笉鍙垹闄?
  return false;
};

// 鑾峰彇鍙垹闄ょ殑璁㈠崟鏁伴噺
const getDeletableCount = (row) => {
  if (!row.orderInfo || !row.orderInfo.orders) return 0;
  return row.orderInfo.orders.filter(order => isDeletableOrder(order.status)).length;
};

// 娣诲姞deletableOrders鍙橀噺
const deletableOrders = ref([]);

// 澶勭悊璁㈠崟琚垹闄ょ殑鍥炶皟
const handleOrderDeleted = (data) => {
  // 浣跨敤鐜版湁鐨剈pdateLocalOrdersAfterDeletion鏂规硶鏇存柊鏈湴鏁版嵁
  if (data && data.results) {
    updateLocalOrdersAfterDeletion(data.results);
  }
};

// 涓€閿垹闄よ鍗曞鐞嗗嚱鏁?
const handleQuickDeleteOrder = async (token) => {
  // 闃叉閲嶅鐐瑰嚮
  if (token.orderDeleteLoading) return;
  
  // 璁剧疆loading鐘舵€?
  token.orderDeleteLoading = true;
  
  try {
    // 璇㈤棶鐢ㄦ埛鏄惁纭鍒犻櫎
    await ElMessageBox.confirm(
      `纭畾瑕佸垹闄ゆToken鐨勮鍗曞悧锛熷皢浼氳嚜鍔ㄦ煡璇㈠苟浣跨敤骞跺彂妯″紡鍒犻櫎鎵€鏈夊彲鍒犻櫎鐨勮鍗曘€俙,
      '鍒犻櫎璁㈠崟纭',
      {
        confirmButtonText: '纭鍒犻櫎',
        cancelButtonText: '鍙栨秷',
        type: 'warning'
      }
    );
    
    // 鏄剧ず鍔犺浇涓姸鎬?
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '姝ｅ湪澶勭悊璁㈠崟...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
      // 浣跨敤骞跺彂鏌ヨ鍜屽垹闄?
      const result = await orderService.concurrentQueryAndDeleteOrders(
        [token],
        {
          batchSize: 20, // 姣忔壒20涓鍗?
          concurrency: 5, // 5涓苟鍙戣姹?
        }
      );
      
      if (result.success) {
        // 妫€鏌ユ槸鍚︽湁鎴愬姛鍒犻櫎鐨勮鍗?
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;
        
        // 鏇存柊鏈湴鏁版嵁
        updateLocalOrdersAfterDeletion(result.results);
        
        // 澶勭悊鏌ヨ缁撴灉锛屾洿鏂癠I鏄剧ず
        if (result.queriedTokens && result.queriedTokens.length > 0) {
          // 鏇存柊宸叉煡璇絾娌℃湁鍙垹闄よ鍗曠殑Token鐘舵€?
          updateTokensAfterQuery(result.queriedTokens);
        }
        
        // 浣跨敤閫氱煡鏄剧ず缁撴灉
        if (successCount > 0) {
          ElNotification({
            title: '鍒犻櫎璁㈠崟瀹屾垚',
            message: `鎴愬姛鍒犻櫎 ${successCount} 涓鍗?{failCount > 0 ? `锛?{failCount} 涓垹闄ゅけ璐 : ''}`,
            type: 'success',
            duration: 5000
          });
        } else if (result.results.length > 0) {
          // 鏈夎鍗曚絾鍏ㄩ儴鍒犻櫎澶辫触
          ElNotification({
            title: '鍒犻櫎璁㈠崟鏈垚鍔?,
            message: `鏈兘鎴愬姛鍒犻櫎浠讳綍璁㈠崟${failCount} 涓垹闄ゅけ璐,
            type: 'warning',
            duration: 5000
          });
        } else {
          // 鏃犲彲鍒犻櫎璁㈠崟
          ElNotification({
            title: '鏃犲彲鍒犻櫎璁㈠崟',
            message: '鏌ヨ瀹屾垚锛屼絾娌℃湁鎵惧埌鍙垹闄ょ殑璁㈠崟锛堝彧鏈夊凡璇勪环鐨勮鍗曞彲浠ュ垹闄わ級',
            type: 'info',
            duration: 5000
          });
        }
        
        // 寮哄埗鍒锋柊琛ㄦ牸鏁版嵁
        refreshTokens();
      } else {
        // API璋冪敤澶辫触
        ElMessage.error(result.message || '鎿嶄綔澶辫触');
      }
    } finally {
      loadingInstance.close();
      token.orderDeleteLoading = false;
    }
  } catch (error) {
    token.orderDeleteLoading = false;
    if (error !== 'cancel') {
      console.error('鍒犻櫎璁㈠崟閿欒:', error);
      ElMessage.error(`鎿嶄綔寮傚父: ${error.message || '鏈煡閿欒'}`);
    }
  }
};

// 娣诲姞涓€涓爣蹇楁潵鎺у埗鏄惁闇€瑕侀噸缃〉鐮?
const shouldResetPage = ref(true);</script>

<style scoped>
.token-reset-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  /* 绉婚櫎min-height: 100vh锛岄伩鍏嶅己鍒跺鍣ㄨ繃澶?/
  height: auto;
  overflow: visible; /* 淇敼涓簐isible锛岀‘淇濆唴瀹逛笉琚鍓?/
}

.token-list-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible;
  max-height: 80vh; /* 闄愬埗鏈€澶ч珮搴︿负瑙嗗彛楂樺害锟?0% */
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.token-count-indicator {
  display: flex;
  align-items: center;
  margin-right: auto;
  margin-left: 15px;
}

.function-buttons {
  margin: 0 0 20px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.function-buttons .el-button-group {
  margin-right: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 10px;
}

.filter-select {
  width: 130px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 280px;
}

.data-stats {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.stats-card {
  min-width: 70px;
  height: 43px;
  border-radius: 4px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  text-align: center;
  cursor: default;
}

.info-card {
  background-color: #909399;
}

.primary-card {
  background-color: #409EFF;
}

.warning-card {
  background-color: #E6A23C;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.2;
  display: block;
}

.stats-label {
  font-size: 12px;
  opacity: 0.9;
  display: block;
  margin-top: 2px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.selection-info {
  margin-left: auto;
}

/* 琛ㄦ牸鏍峰紡浼樺寲 */
.token-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.token-table :deep(.el-table__header) {
  font-weight: 600;
  color: #303133;
}

.token-table :deep(.el-table__row) {
  height: 60px;
  cursor: pointer; /* 娣诲姞鎵嬪瀷鎸囬拡锛屾彁绀哄彲鐐瑰嚮 */
}

.token-table :deep(.el-table__row:hover) {
  border: 2px solid #fff;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 30px; /* 澧炲姞搴曢儴杈硅窛 */
  padding: 15px 0; /* 娣诲姞鍐呰竟璺?*/
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px; /* 鍦嗚杈规 */
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 瀵煎叆瀵硅瘽妗嗘牸寮忚鏄庢牱?*/
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th, 
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 鏂囨湰瀵煎叆鏍峰紡 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 鐢ㄦ埛澶村儚鏍峰紡 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 璁㈠崟淇℃伅鏍峰紡 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.order-count-badge {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  background: linear-gradient(135deg, #e8f4ff, #c5e3ff);
  color: #1a4b8c;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 110, 255, 0.12);
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 179, 255, 0.2);
  font-size: 13px;
  min-width: 60px;
  justify-content: center;
  height: 32px;
  font-weight: 500;
}

.order-count-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 110, 255, 0.15);
  background: linear-gradient(135deg, #d1e9ff, #b3d9ff);
}

.order-number {
  font-weight: 600;
  font-size: 15px;
  margin-right: 3px;
  color: #0960d0;
}

.order-text {
  font-size: 13px;
  color: #5a8dc8;
}

.order-deletable {
  font-size: 12px;
  color: #67c23a;
  margin-left: 5px;
  font-weight: normal;
}

.deletable-tag {
  margin-left: 8px;
  font-size: 11px;
  height: 22px;
  line-height: 20px;
  padding: 0 6px;
}

.deletable-count {
  font-size: 11px;
  color: #67c23a;
  margin-left: 4px;
}

/* 鐘舵€佹爣绛鹃€氱敤鏍峰紡 */
.status-badge {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.3s ease;
  gap: 4px;
  height: 24px;
}

.status-badge i {
  font-size: 14px;
}

/* 鎺夌嚎鐘舵€佹牱寮?*/
.status-offline {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.1);
}

/* 鏃犺鍗曠姸鎬佹牱寮?*/
.status-no-order {
  background-color: #f5f7fa;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 鏌ヨ澶辫触鐘舵€佹牱寮?*/
.status-error {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid rgba(230, 162, 60, 0.1);
}

/* 鏈煡璇㈢姸鎬佹牱寮?*/
.status-not-queried {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 璁㈠崟璇︽儏瀵硅瘽妗嗘牱寮?*/
.order-details {
  margin-top: 10px;
}

.order-table {
  margin-bottom: 15px;
}

.order-table .el-table__row:hover {
  background-color: #f0f7ff;
}

.order-detail-item {
  display: flex;
  margin-bottom: 8px;
}

.order-detail-label {
  font-weight: bold;
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

.order-detail-value {
  flex: 1;
}

.order-status-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.order-deletion-status {
  margin-top: 10px;
}

.order-status-text {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-tag i {
  font-size: 12px;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 瀵煎叆瀵硅瘽妗嗘牸寮忚鏄庢牱寮?*/
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th, 
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 鏂囨湰瀵煎叆鏍峰紡 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 鐢ㄦ埛澶村儚鏍峰紡 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 璁㈠崟淇℃伅鏍峰紡 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

</style> 
</template>
