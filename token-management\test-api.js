const axios = require('axios');

// 测试API是否可用
async function testAPI() {
  try {
    console.log('尝试连接: http://localhost:3000/api/test');
    const testResponse = await axios.get('http://localhost:3000/api/test');
    console.log('连接成功! 响应:', testResponse.data);
    return true;
  } catch (error) {
    console.error('连接测试失败:', error.message);
    return false;
  }
}

// 测试POST请求
async function testPostRequest() {
  try {
    // 准备测试数据
    const testPostData = {
      tokens: [
        { UID: "test123", token: "testtoken123", username: "testuser" }
      ]
    };
    
    console.log('尝试POST请求到: http://localhost:3000/api/tokens/place-order');
    console.log('发送数据:', JSON.stringify(testPostData));
    
    const orderResponse = await axios.post('http://localhost:3000/api/tokens/place-order', testPostData);
    console.log('POST请求成功! 响应:', orderResponse.data);
  } catch (error) {
    console.error('POST请求失败:', error.message);
  }
}

// 测试API服务是否正常运行
async function testApi() {
  try {
    console.log('开始测试API...');
    // 测试简单的GET请求
    console.log('尝试连接: http://localhost:3000/api/test');
    const testResponse = await axios.get('http://localhost:3000/api/test');
    console.log('测试API响应:', testResponse.data);
    
    // 如果上面的请求成功，尝试一个简单的POST请求
    const testPostData = {
      tokens: [
        {
          UID: 'test123456',
          token: 'testtoken123',
          username: 'testuser'
        }
      ]
    };
    
    console.log('尝试POST请求到: http://localhost:3000/api/tokens/place-order');
    console.log('请求数据:', JSON.stringify(testPostData));
    
    try {
      const orderResponse = await axios.post('http://localhost:3000/api/tokens/place-order', testPostData);
      console.log('订单API响应:', orderResponse.data);
    } catch (orderError) {
      console.error('订单API错误:', orderError.message);
      console.error('完整错误信息:', orderError);
      if (orderError.response) {
        console.error('错误状态码:', orderError.response.status);
        console.error('错误详情:', orderError.response.data);
      }
    }
    
  } catch (error) {
    console.error('API测试失败:', error.message);
    console.error('完整错误信息:', error);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误详情:', error.response.data);
    }
  }
}

// 执行测试
testApi(); 