const Reset = require('../models/reset');
const Token = require('../models/token');
const { logger } = require('../utils/logger');
const { exportToCSV, exportToExcel } = require('../utils/exportUtils');

class ResetController {
  /**
   * 获取所有重置记录
   */
  static async getAllResets(req, res, next) {
    try {
      const { page = 1, limit = 10, token_id, platform, status, date_from, date_to } = req.query;
      
      const filters = {};
      if (token_id) filters.token_id = token_id;
      if (platform) filters.platform = platform;
      if (status) filters.status = status;
      if (date_from) filters.date_from = new Date(date_from);
      if (date_to) filters.date_to = new Date(date_to);
      
      const result = await Reset.getAll(parseInt(page), parseInt(limit), filters);
      
      res.json({
        success: true,
        data: {
          resets: result.resets,
          pagination: {
            total: result.total,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(result.total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      logger.error(`获取所有重置记录失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 根据ID获取单个重置记录
   */
  static async getResetById(req, res, next) {
    try {
      const { id } = req.params;
      const reset = await Reset.getById(id);
      
      if (!reset) {
        return res.status(404).json({
          success: false,
          message: '重置记录不存在'
        });
      }
      
      res.json({
        success: true,
        data: reset
      });
    } catch (error) {
      logger.error(`获取重置记录(ID: ${req.params.id})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取与特定token关联的重置历史
   */
  static async getResetsByToken(req, res, next) {
    try {
      const { tokenId } = req.params;
      const { page = 1, limit = 10 } = req.query;
      
      // 检查token是否存在
      const token = await Token.getById(tokenId);
      if (!token) {
        return res.status(404).json({
          success: false,
          message: 'Token不存在'
        });
      }
      
      const result = await Reset.getByTokenId(tokenId, parseInt(page), parseInt(limit));
      
      res.json({
        success: true,
        data: {
          token: {
            id: token.id,
            platform: token.platform,
            uid: token.uid
          },
          resets: result.resets,
          pagination: {
            total: result.total,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(result.total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      logger.error(`获取token的重置历史(Token ID: ${req.params.tokenId})失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：批量创建重置记录
   */
  static async batchCreateReset(req, res, next) {
    try {
      const { resets } = req.body;
      
      // 验证请求格式
      if (!Array.isArray(resets) || resets.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供有效的重置记录数组'
        });
      }
      
      // 验证每个重置记录的必填字段
      for (const reset of resets) {
        if (!reset.token_id || !reset.old_value || !reset.new_value) {
          return res.status(400).json({
            success: false,
            message: '每条重置记录必须包含token_id、old_value和new_value字段'
          });
        }
      }
      
      // 准备重置数据
      const resetsData = resets.map(reset => ({
        token_id: reset.token_id,
        platform: reset.platform,
        uid: reset.uid,
        old_value: reset.old_value,
        new_value: reset.new_value,
        reason: reset.reason || '管理员手动创建',
        reset_by: req.user.id,
        reset_at: new Date()
      }));
      
      const result = await Reset.bulkCreate(resetsData);
      logger.info(`管理员 ${req.user.username} (ID: ${req.user.id}) 批量创建了 ${result.length} 条重置记录`);
      
      res.status(201).json({
        success: true,
        message: `成功创建 ${result.length} 条重置记录`,
        data: {
          resets: result
        }
      });
    } catch (error) {
      logger.error(`批量创建重置记录失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取重置统计信息
   */
  static async getResetStats(req, res, next) {
    try {
      const stats = await Reset.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error(`获取重置统计信息失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 导出重置记录
   */
  static async exportResets(req, res, next) {
    try {
      const { format } = req.params;
      const { token_id, platform, date_from, date_to } = req.query;
      
      // 构建查询条件
      const filters = {};
      if (token_id) filters.token_id = token_id;
      if (platform) filters.platform = platform;
      if (date_from) filters.date_from = new Date(date_from);
      if (date_to) filters.date_to = new Date(date_to);
      
      // 获取所有重置记录（不分页）
      const result = await Reset.getAll(1, 100000, filters);
      
      // 准备导出数据
      const resets = result.resets.map(reset => ({
        ID: reset.id,
        TokenID: reset.token_id,
        平台: reset.platform,
        UID: reset.uid,
        旧值: reset.old_value,
        新值: reset.new_value,
        原因: reset.reason || '',
        重置时间: new Date(reset.reset_at).toLocaleString(),
        重置人: reset.reset_by_username || String(reset.reset_by)
      }));
      
      // 导出文件名
      const fileName = `resets_export_${new Date().toISOString().slice(0, 10)}`;
      
      if (format === 'csv') {
        const csv = await exportToCSV(resets);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.csv`);
        return res.send(csv);
      } else if (format === 'excel') {
        const excel = await exportToExcel(resets, 'Resets');
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
        return res.send(excel);
      } else {
        return res.status(400).json({
          success: false,
          message: '不支持的导出格式，请使用csv或excel'
        });
      }
    } catch (error) {
      logger.error(`导出重置记录失败: ${error.message}`);
      next(error);
    }
  }
}

module.exports = ResetController; 