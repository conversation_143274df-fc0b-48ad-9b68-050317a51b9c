import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'

/**
 * 处理各种对话框的组合式函数
 * 用于管理操作确认、编辑、详情等对话框状态
 */
export default function useDialogs() {
  // 通用操作对话框
  const operationDialogVisible = ref(false)
  const operationTitle = ref('')
  const operationMessage = ref('')
  const currentOperation = ref('')
  
  // 显示通用操作对话框
  const showOperationDialog = (title, message, operation) => {
    operationTitle.value = title
    operationMessage.value = message
    currentOperation.value = operation
    operationDialogVisible.value = true
    
    return new Promise((resolve, reject) => {
      // 保存当前的resolve和reject函数，以便在确认或取消时调用
      showOperationDialog.currentResolve = resolve
      showOperationDialog.currentReject = reject
    })
  }
  
  // 确认操作
  const confirmOperation = () => {
    // 关闭对话框
    operationDialogVisible.value = false
    
    // 如果有待处理的Promise，则解析它
    if (showOperationDialog.currentResolve) {
      showOperationDialog.currentResolve({
        success: true,
        operation: currentOperation.value
      })
      
      // 清理解析器
      showOperationDialog.currentResolve = null
      showOperationDialog.currentReject = null
    }
    
    return currentOperation.value
  }
  
  // 取消操作
  const cancelOperation = () => {
    // 关闭对话框
    operationDialogVisible.value = false
    
    // 如果有待处理的Promise，则拒绝它
    if (showOperationDialog.currentReject) {
      showOperationDialog.currentReject({
        success: false,
        operation: currentOperation.value,
        reason: 'cancel'
      })
      
      // 清理解析器
      showOperationDialog.currentResolve = null
      showOperationDialog.currentReject = null
    }
  }
  
  /**
   * 使用Element Plus对话框进行确认
   * @param {string} message 确认消息
   * @param {string} title 对话框标题
   * @param {Object} options 额外选项
   * @returns {Promise} 确认结果的Promise
   */
  const confirm = (message, title = '操作确认', options = {}) => {
    return ElMessageBox.confirm(
      message,
      title,
      {
        confirmButtonText: options.confirmButtonText || '确认',
        cancelButtonText: options.cancelButtonText || '取消',
        type: options.type || 'warning',
        ...options
      }
    )
  }
  
  /**
   * 使用HTML内容的确认对话框
   * @param {string} htmlContent HTML内容
   * @param {string} title 对话框标题
   * @param {Object} options 额外选项
   * @returns {Promise} 确认结果的Promise
   */
  const confirmWithHtml = (htmlContent, title = '操作确认', options = {}) => {
    return ElMessageBox.confirm(
      htmlContent,
      title,
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: options.confirmButtonText || '确认',
        cancelButtonText: options.cancelButtonText || '取消',
        type: options.type || 'warning',
        ...options
      }
    )
  }
  
  /**
   * 使用Element Plus对话框进行警告
   * @param {string} message 警告消息
   * @param {string} title 对话框标题
   * @param {Object} options 额外选项
   * @returns {Promise} 确认结果的Promise
   */
  const alert = (message, title = '提示', options = {}) => {
    return ElMessageBox.alert(
      message,
      title,
      {
        confirmButtonText: options.confirmButtonText || '确定',
        type: options.type || 'info',
        ...options
      }
    )
  }
  
  /**
   * 使用HTML内容的警告对话框
   * @param {string} htmlContent HTML内容
   * @param {string} title 对话框标题
   * @param {Object} options 额外选项
   * @returns {Promise} 确认结果的Promise
   */
  const alertWithHtml = (htmlContent, title = '提示', options = {}) => {
    return ElMessageBox.alert(
      htmlContent,
      title,
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: options.confirmButtonText || '确定',
        type: options.type || 'info',
        ...options
      }
    )
  }
  
  return {
    // 状态
    operationDialogVisible,
    operationTitle,
    operationMessage,
    currentOperation,
    
    // 方法
    showOperationDialog,
    confirmOperation,
    cancelOperation,
    confirm,
    confirmWithHtml,
    alert,
    alertWithHtml
  }
} 