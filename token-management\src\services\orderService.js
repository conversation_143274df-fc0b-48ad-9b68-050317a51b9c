import * as tokenService from './tokenService';
import { ElMessage } from 'element-plus';

/**
 * 自动查询并删除指定Token的订单
 * 这个方法会先查询订单信息，然后自动删除可删除的订单
 * @param {Object} token Token对象
 * @returns {Promise<Object>} 删除结果
 */
export const autoQueryAndDeleteOrders = async (token) => {
  if (!token || !token.uid || !token.token) {
    return {
      success: false,
      message: 'Token信息不完整',
      results: []
    };
  }

  try {
    // 1. 先查询订单信息
    const queryResult = await tokenService.queryTokenOrderInfo([{
      uid: token.uid,
      token: token.token
    }]);

    if (!queryResult.success) {
      return {
        success: false,
        message: `查询订单失败: ${queryResult.message || '未知错误'}`,
        results: []
      };
    }

    // 2. 获取查询结果中的订单信息
    const orderResult = queryResult.results.find(r => r.uid === token.uid);

    if (!orderResult || !orderResult.success || !orderResult.data) {
      return {
        success: false,
        message: '未能获取订单信息',
        results: []
      };
    }

    // 3. 获取可删除的订单
    const orders = orderResult.data.orders || [];
    const deletableOrders = orders.filter(order =>
      order.status && (
        order.status.includes('已评价') ||
        order.status.includes('交易成功') ||
        order.status.includes('交易已取消') ||
        order.status.includes('未发货') ||
        order.status.includes('退款成功')
      )
    );

    if (deletableOrders.length === 0) {
      return {
        success: false,
        message: '没有可删除的订单，只有已评价、交易成功、交易已取消、未发货或退款成功的订单可以删除',
        results: []
      };
    }

    // 4. 准备删除请求数据
    const orderItems = deletableOrders.map(order => {
      // 获取订单编号 - 考虑多种可能的字段名
      let orderNumber = null;

      // 检查可能的字段名
      if (order.orderId) {
        orderNumber = order.orderId;
      } else if (order.orderSn) {
        orderNumber = order.orderSn;
      } else if (order.order_sn) {
        orderNumber = order.order_sn;
      } else if (order.order_id) {
        orderNumber = order.order_id;
      } else if (order.id) {
        orderNumber = order.id;
      } else if (order.orderNo) {
        orderNumber = order.orderNo;
      } else if (order.order_no) {
        orderNumber = order.order_no;
      } else if (order.number) {
        orderNumber = order.number;
      }

      // 记录订单编号获取情况
      if (!orderNumber) {
        console.warn('无法获取订单编号:', {
          orderStatus: order.status,
          availableFields: Object.keys(order),
          orderData: order
        });
      }

      return {
        uid: token.uid,
        token: token.token,
        orderSn: orderNumber
      };
    });

    // 检查是否有无效的订单项（缺少orderSn）
    const invalidOrderItems = orderItems.filter(item => !item.orderSn);
    if (invalidOrderItems.length > 0) {
      console.error(`准备删除的订单项中有${invalidOrderItems.length}/${orderItems.length}项缺少orderSn`);

      // 记录详细信息
      invalidOrderItems.forEach((item, index) => {
        console.error(`无效订单项 #${index + 1}:`, {
          uid: item.uid,
          hasToken: !!item.token,
          orderData: deletableOrders.find(order =>
            (order.orderId && item.uid === token.uid) ||
            (order.id && item.uid === token.uid)
          )
        });
      });

      // 过滤掉无效的订单项
      const validOrderItems = orderItems.filter(item => item.orderSn);
      console.log(`过滤后剩余${validOrderItems.length}个有效订单项`);

      // 如果没有有效的订单项，返回错误
      if (validOrderItems.length === 0) {
        return {
          success: false,
          message: '所有订单项都缺少必要参数(orderSn)，无法删除',
          results: []
        };
      }

      // 使用有效的订单项继续
      orderItems = validOrderItems;
    }

    // 5. 调用批量删除API
    console.log(`准备调用批量删除API，订单项数量: ${orderItems.length}`);
    console.log('订单项示例:', orderItems.slice(0, 2));

    const deleteResult = await tokenService.batchDeleteOrders(orderItems);

    // 记录删除结果
    if (deleteResult.success) {
      const successCount = deleteResult.results.filter(r => r.success).length;
      console.log(`删除结果: 成功=${successCount}/${deleteResult.results.length}`);
    } else {
      console.error('删除失败:', deleteResult.message);
    }

    // 6. 返回删除结果
    return deleteResult;

  } catch (error) {
    console.error('自动查询和删除订单出错:', error);
    return {
      success: false,
      message: error.message || '操作异常',
      results: []
    };
  }
};

/**
 * 根据查询到的订单信息，计算可删除的订单数量
 * @param {Object} token Token对象
 * @returns {Number} 可删除的订单数量
 */
export const countDeletableOrders = (token) => {
  if (!token || !token.orderInfo || !token.orderInfo.orders) {
    return 0;
  }

  return token.orderInfo.orders.filter(order =>
    order.status && (
      order.status.includes('已评价') ||
      order.status.includes('交易成功') ||
      order.status.includes('交易已取消') ||
      order.status.includes('未发货') ||
      order.status.includes('退款成功')
    )
  ).length;
};

/**
 * 格式化订单状态显示文本
 * @param {Object} token Token对象
 * @returns {String} 格式化后的状态文本
 */
export const formatOrderStatusText = (token) => {
  if (!token || !token.orderInfo) {
    return '未查询';
  }

  if (token.orderInfo.status === '掉线') {
    return '掉线';
  }

  if (token.orderInfo.status === '在线' && token.orderInfo.orderCount === 0) {
    return '无订单';
  }

  if (token.orderInfo.status === '在线' && token.orderInfo.orderCount > 0) {
    const deletableCount = countDeletableOrders(token);
    return `${token.orderInfo.orderCount}个订单 (${deletableCount}个可删)`;
  }

  return token.orderInfo.status || '查询失败';
};

/**
 * 并发删除多个订单
 * @param {Array} orderItems 订单数据数组，每项包含uid, token, orderSn
 * @param {Object} options 配置选项
 * @returns {Promise<Object>} 删除结果
 */
export const concurrentDeleteOrders = async (orderItems, options = {}) => {
  if (!orderItems || !orderItems.length) {
    return {
      success: true,
      message: '没有订单需要删除',
      results: []
    };
  }

  // 验证每个订单项是否包含必要的字段
  let invalidItems = orderItems.filter(item => !item.uid || !item.token || !item.orderSn);
  if (invalidItems.length > 0) {
    console.error('发现无效的订单项:', invalidItems);
    console.warn(`订单项中有${invalidItems.length}/${orderItems.length}项缺少必要参数`);

    // 记录详细的错误信息，帮助诊断问题
    invalidItems.forEach((item, index) => {
      console.error(`无效订单项 #${index + 1}:`, {
        uid: item.uid || '缺失',
        token: item.token ? '存在' : '缺失',
        orderSn: item.orderSn || '缺失',
        allFields: Object.keys(item)
      });
    });

    // 尝试修复无效的订单项
    const fixedItems = [];
    const stillInvalidItems = [];

    invalidItems.forEach(item => {
      // 只处理缺少orderSn的情况，其他情况无法修复
      if (item.uid && item.token && !item.orderSn) {
        // 尝试从其他字段获取订单编号
        let orderNumber = null;

        // 检查可能的字段名
        if (item.orderId) {
          orderNumber = item.orderId;
        } else if (item.order_id) {
          orderNumber = item.order_id;
        } else if (item.id) {
          orderNumber = item.id;
        } else if (item.orderNo) {
          orderNumber = item.orderNo;
        } else if (item.order_no) {
          orderNumber = item.order_no;
        } else if (item.number) {
          orderNumber = item.number;
        }

        if (orderNumber) {
          // 成功修复
          console.log(`成功修复订单项，使用字段值作为orderSn:`, {
            uid: item.uid,
            orderNumber
          });

          fixedItems.push({
            ...item,
            orderSn: orderNumber
          });
        } else {
          // 无法修复
          stillInvalidItems.push(item);
        }
      } else {
        // 其他情况无法修复
        stillInvalidItems.push(item);
      }
    });

    // 更新无效项列表
    invalidItems = stillInvalidItems;

    // 将修复的项目添加回订单项列表
    if (fixedItems.length > 0) {
      console.log(`成功修复 ${fixedItems.length} 个订单项`);
      orderItems = [...orderItems.filter(item => item.uid && item.token && item.orderSn), ...fixedItems];
    } else {
      // 过滤掉无效的订单项
      orderItems = orderItems.filter(item => item.uid && item.token && item.orderSn);
    }

    if (orderItems.length === 0) {
      return {
        success: false,
        message: '所有订单项都缺少必要参数(uid, token, orderSn)，无法修复',
        results: invalidItems.map(item => ({
          success: false,
          orderSn: item.orderSn || '未知',
          message: '参数不完整，需要提供uid、token和orderSn'
        }))
      };
    }
  }

  // 配置选项 - 增加批处理大小和并发数
  const batchSize = options.batchSize || 50; // 从20增加到50
  const concurrency = options.concurrency || 10; // 从5增加到10

  // 分批处理
  const batches = [];
  for (let i = 0; i < orderItems.length; i += batchSize) {
    batches.push(orderItems.slice(i, i + batchSize));
  }

  console.log(`订单已分成${batches.length}批，每批${batchSize}个，并发数${concurrency}`);

  // 结果数组
  let allResults = [];
  let completedBatches = 0;

  // 使用并发删除
  try {
    // 使用并发控制
    const throttledRequests = [];

    for (let i = 0; i < batches.length; i++) {
      // 限制最大并发数
      while (throttledRequests.filter(p => p.status === 'pending').length >= concurrency) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 创建一个带状态的Promise
      const request = {
        status: 'pending',
        promise: null
      };

      // 创建Promise
      request.promise = (async () => {
        try {
          // 输出当前批次的第一个订单项示例
          if (batches[i].length > 0) {
            console.log(`批次${i+1}示例:`, batches[i][0]);
          }

          // 批量删除API调用
          const batchResult = await tokenService.batchDeleteOrders(batches[i]);

          // 更新完成批次数
          completedBatches++;

          // 检查是否是系统繁忙错误
          if (!batchResult.success && (batchResult.error_code === 46047 || batchResult.systemBusy)) {
            console.warn(`批次${i + 1}删除时系统繁忙，将跳过此批次:`, batchResult.message);

            // 添加系统繁忙的结果，但不视为错误
            allResults.push({
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true
            });

            // 请求完成
            request.status = 'fulfilled';

            return {
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true,
              results: []
            };
          }

          // 记录结果
          if (batchResult && batchResult.success && batchResult.results) {
            allResults = allResults.concat(batchResult.results);
          }

          // 请求完成
          request.status = 'fulfilled';

          return batchResult;
        } catch (error) {
          // 检查是否是系统繁忙错误
          const isSystemBusy = error.error_code === 46047 ||
                              error.systemBusy ||
                              (error.message && error.message.includes('系统繁忙')) ||
                              (error.response && error.response.data && error.response.data.error_code === 46047);

          if (isSystemBusy) {
            console.warn(`批次${i + 1}删除时系统繁忙，将跳过此批次:`, error.message || '系统繁忙');

            // 添加系统繁忙的结果，但不视为错误
            allResults.push({
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true
            });

            // 请求完成，但标记为系统繁忙
            request.status = 'busy';

            return {
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true,
              results: []
            };
          }

          // 其他错误，正常处理
          request.status = 'rejected';
          console.error(`批次${i + 1}删除失败:`, error);
          throw error;
        }
      })();

      throttledRequests.push(request);
    }

    // 等待所有请求完成
    await Promise.all(throttledRequests.map(r => r.promise.catch(e => e)));

    return {
      success: true,
      message: `成功删除${allResults.filter(r => r.success).length}个订单`,
      results: allResults
    };
  } catch (error) {
    console.error('并发删除订单出错:', error);
    return {
      success: false,
      message: error.message || '并发删除订单出错',
      results: allResults
    };
  }
};

/**
 * 并发查询并删除多个Token的订单，支持大批量处理
 * @param {Array} tokens Token对象数组
 * @param {Object} options 配置选项
 * @returns {Promise<Object>} 删除结果
 */
export const concurrentQueryAndDeleteOrders = async (tokens, options = {}) => {
  if (!tokens || !tokens.length) {
    return {
      success: true,
      message: '没有Token需要处理',
      results: []
    };
  }

  console.log('开始执行并发查询和删除订单，tokens总数量:', tokens.length);

  // 设置批次大小，后端限制一次最多处理100个Token
  const tokenBatchSize = options.tokenBatchSize || 90; // 设置为90，留一些余量
  const batchSize = options.batchSize || 50; // 从20增加到50
  const concurrency = options.concurrency || 10; // 从5增加到10
  const parallelBatches = options.parallelBatches || 3; // 新增：并行处理的批次数
  const onProgress = options.onProgress || (() => {}); // 进度回调函数

  // 分批处理tokens
  const tokenBatches = [];
  for (let i = 0; i < tokens.length; i += tokenBatchSize) {
    tokenBatches.push(tokens.slice(i, i + tokenBatchSize));
  }

  console.log(`Token已分成${tokenBatches.length}批，每批最多${tokenBatchSize}个`);

  // 结果数组
  let allResults = [];
  let queriedTokens = [];
  let processedBatches = 0;

  // 改进分批处理逻辑：并行处理多批token
  for (let i = 0; i < tokenBatches.length; i += parallelBatches) {
    // 获取当前要并行处理的批次
    const currentParallelBatches = tokenBatches.slice(i, i + parallelBatches);
    console.log(`并行处理${currentParallelBatches.length}批Token，批次索引: ${i} 到 ${i + currentParallelBatches.length - 1}`);

    // 并行处理多批token
    const batchPromises = currentParallelBatches.map(async (batch, batchIndex) => {
      const batchNumber = i + batchIndex;
      console.log(`处理第${batchNumber + 1}/${tokenBatches.length}批Token，数量: ${batch.length}`);

      try {
        // 调用原有的处理逻辑，处理当前批次的token
        const batchResult = await processSingleTokenBatch(batch, {
          batchSize,
          concurrency
        });

        return {
          batchNumber,
          result: batchResult
        };
      } catch (error) {
        // 检查是否是系统繁忙错误
        const isSystemBusy = error.error_code === 46047 ||
                            error.systemBusy ||
                            (error.message && error.message.includes('系统繁忙')) ||
                            (error.response && error.response.data && error.response.data.error_code === 46047);

        if (isSystemBusy) {
          console.warn(`处理第${batchNumber + 1}批Token时系统繁忙，将跳过此批次:`, error.message || '系统繁忙');
          return {
            batchNumber,
            systemBusy: true,
            result: {
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true,
              results: [],
              queriedTokens: []
            }
          };
        } else {
          console.error(`处理第${batchNumber + 1}批Token时出错:`, error);
          return {
            batchNumber,
            error: error,
            result: {
              success: false,
              message: error.message || '处理批次出错',
              results: [],
              queriedTokens: []
            }
          };
        }
      }
    });

    // 等待所有并行批次完成
    const batchResults = await Promise.all(batchPromises);

    // 处理结果
    for (const { result, error } of batchResults) {
      if (!error && result) {
        // 合并结果
        if (result.results && result.results.length > 0) {
          allResults = allResults.concat(result.results);
        }

        if (result.queriedTokens && result.queriedTokens.length > 0) {
          queriedTokens = queriedTokens.concat(result.queriedTokens);
        }
      }

      // 更新进度
      processedBatches++;
      const progress = Math.floor((processedBatches / tokenBatches.length) * 100);
      onProgress({
        current: processedBatches,
        total: tokenBatches.length,
        progress,
        message: `已处理 ${processedBatches}/${tokenBatches.length} 批Token (${progress}%)`
      });
    }

    // 批次组之间添加短暂延迟，避免请求过于密集
    if (i + parallelBatches < tokenBatches.length) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 从1000ms减少到500ms
    }
  }

  // 统计总体结果
  const totalSuccess = allResults.filter(r => r.success).length;
  const totalFailed = allResults.length - totalSuccess;
  console.log(`订单处理总结果: 成功=${totalSuccess}, 失败=${totalFailed}, 总数=${allResults.length}`);
  console.log(`已查询Token数量: ${queriedTokens.length}`);

  return {
    success: true,
    message: allResults.length > 0
      ? `处理了${allResults.length}个订单，成功删除${totalSuccess}个`
      : '没有找到可删除的订单，只有已评价、交易成功、交易已取消、未发货或退款成功的订单可以删除',
    results: allResults,
    queriedTokens,
    batchCount: tokenBatches.length,
    processedBatchCount: processedBatches
  };
};

/**
 * 处理单批Token的订单查询和删除
 * 这个函数包含原来concurrentQueryAndDeleteOrders的核心逻辑
 * @param {Array} tokens Token对象数组
 * @param {Object} options 配置选项
 * @returns {Promise<Object>} 处理结果
 */
const processSingleTokenBatch = async (tokens, options = {}) => {
  // 结果数组
  let allResults = [];
  // 保存已查询的Token信息，即使没有可删除的订单
  let queriedTokens = [];

  // 创建uid到token的映射，用于处理API返回结果中缺少token的情况
  const tokenMap = {};
  tokens.forEach(token => {
    if (token.uid && token.token) {
      tokenMap[token.uid] = token.token;
    }
  });

  console.log('创建的uid到token映射包含', Object.keys(tokenMap).length, '个条目');

  try {
    // 1. 区分已查询和未查询的token
    const tokensWithOrderInfo = tokens.filter(
      token => token.orderInfo && token.orderInfo.orders && token.orderInfo.orders.length > 0
    );

    const tokensWithoutOrderInfo = tokens.filter(
      token => !token.orderInfo || !token.orderInfo.orders
    );

    console.log(`已有订单信息的tokens: ${tokensWithOrderInfo.length}, 未查询的tokens: ${tokensWithoutOrderInfo.length}`);

    // 2. 处理已有订单信息的token
    if (tokensWithOrderInfo.length > 0) {
      // 准备要删除的订单项
      let orderItemsToDelete = [];

      tokensWithOrderInfo.forEach(token => {
        const deletableOrders = token.orderInfo.orders.filter(order =>
          order.status && (
            order.status.includes('已评价') ||
            order.status.includes('交易成功') ||
            order.status.includes('交易已取消') ||
            order.status.includes('未发货') ||
            order.status.includes('退款成功')
          )
        );

        console.log(`Token ${token.uid}: 总订单数=${token.orderInfo.orders.length}, 可删除订单数=${deletableOrders.length}`);

        // 记录可删除订单的详细信息，特别是"交易成功"状态的订单
        if (deletableOrders.length > 0) {
          const successOrders = deletableOrders.filter(order => order.status && order.status.includes('交易成功'));
          const evaluatedOrders = deletableOrders.filter(order => order.status && order.status.includes('已评价'));

          console.log(`Token ${token.uid}: 可删除订单中，"已评价"状态=${evaluatedOrders.length}个，"交易成功"状态=${successOrders.length}个`);

          // 记录第一个"交易成功"状态订单的完整数据结构
          if (successOrders.length > 0) {
            console.log('交易成功状态订单示例数据结构:', JSON.stringify(successOrders[0]));
            console.log('交易成功状态订单字段列表:', Object.keys(successOrders[0]));
          }

          // 记录第一个"已评价"状态订单的完整数据结构（用于比较）
          if (evaluatedOrders.length > 0) {
            console.log('已评价状态订单示例数据结构:', JSON.stringify(evaluatedOrders[0]));
            console.log('已评价状态订单字段列表:', Object.keys(evaluatedOrders[0]));
          }
        }

        // 添加到已查询Token列表
        queriedTokens.push({
          uid: token.uid,
          token: token.token || tokenMap[token.uid], // 使用token或从映射获取
          isOnline: true,
          status: '在线',
          orders: token.orderInfo.orders || []
        });

        deletableOrders.forEach(order => {
          // 获取token值，优先使用token.token，如果不存在则从映射中获取
          const tokenValue = token.token || tokenMap[token.uid];

          // 获取订单编号 - 考虑多种可能的字段名
          let orderNumber = null;

          // 检查可能的字段名
          if (order.orderId) {
            orderNumber = order.orderId;
          } else if (order.orderSn) {
            orderNumber = order.orderSn;
          } else if (order.order_sn) {
            orderNumber = order.order_sn;
          } else if (order.order_id) {
            orderNumber = order.order_id;
          } else if (order.id) {
            orderNumber = order.id;
          } else if (order.orderNo) {
            orderNumber = order.orderNo;
          } else if (order.order_no) {
            orderNumber = order.order_no;
          } else if (order.number) {
            orderNumber = order.number;
          } else if (order.sn) {
            orderNumber = order.sn;
          } else if (order.code) {
            orderNumber = order.code;
          }

          // 检查并确保需要的字段都存在
          if (!token.uid || !tokenValue || !orderNumber) {
            console.warn('订单数据缺少必要字段:', {
              hasUid: !!token.uid,
              hasToken: !!tokenValue,
              hasOrderNumber: !!orderNumber,
              possibleOrderIdFields: {
                orderId: order.orderId,
                orderSn: order.orderSn,
                order_sn: order.order_sn,
                order_id: order.order_id,
                id: order.id
              },
              orderStatus: order.status,
              orderData: order
            });
            return; // 跳过无效的订单
          }

          orderItemsToDelete.push({
            uid: token.uid,
            token: tokenValue, // 使用获取的token值
            orderSn: orderNumber // 使用获取的订单编号
          });
        });
      });

      if (orderItemsToDelete.length > 0) {
        console.log(`准备删除已知订单，总数: ${orderItemsToDelete.length}`);
        // 检查orderItemsToDelete结构
        console.log('删除已知订单 - 参数示例:', {
          sample: orderItemsToDelete.slice(0, 2),
          allFields: orderItemsToDelete.length > 0 ? Object.keys(orderItemsToDelete[0]) : []
        });

        // 不再需要进度回调
        // 并发删除订单
        const deleteResult = await concurrentDeleteOrders(
          orderItemsToDelete,
          {
            ...options,
            // 移除进度回调
          }
        );

        if (deleteResult.success) {
          const successCount = deleteResult.results.filter(r => r.success).length;
          console.log(`已知订单删除结果: 成功=${successCount}/${deleteResult.results.length}`);
          allResults = allResults.concat(deleteResult.results);
        } else {
          // 检查是否是系统繁忙错误
          if (deleteResult.error_code === 46047 || deleteResult.systemBusy) {
            console.warn('系统繁忙，跳过当前token，继续处理其他token');
            // 添加一个系统繁忙的结果，但不阻止继续处理
            allResults.push({
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true
            });
          } else {
            console.error('删除已知订单失败:', deleteResult.message);
          }
        }
      } else {
        console.log('没有找到可删除的已知订单');
      }
    }

    // 3. 对于未查询过的token，先查询订单再删除
    if (tokensWithoutOrderInfo.length > 0) {
      // 获取未查询过的token的信息
      const tokenItemsToQuery = tokensWithoutOrderInfo.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      console.log(`开始查询未知订单信息，tokens数量: ${tokenItemsToQuery.length}`);

      // 使用并发查询
      const queryResponse = await tokenService.queryTokenOrderInfo(tokenItemsToQuery);

      // 修复：正确处理queryTokenOrderInfo的返回值结构
      if (!queryResponse || !queryResponse.success) {
        console.error('查询订单信息失败:', queryResponse?.message || '未知错误');
        return {
          success: false,
          message: queryResponse?.message || '查询订单信息失败',
          results: allResults,
          queriedTokens
        };
      }

      // 确保results是一个数组
      const queryResults = queryResponse.results || [];
      if (!Array.isArray(queryResults)) {
        console.error('查询结果不是数组:', queryResults);
        return {
          success: false,
          message: '查询结果格式错误',
          results: allResults,
          queriedTokens
        };
      }

      console.log(`订单查询完成，获取到${queryResults.length}个结果`);

      // 提取可删除的订单
      let newOrderItemsToDelete = [];

      // 遍历查询结果 - 使用正确的结果数组
      queryResults.forEach(result => {
        if (result.success && result.data) {
          // 获取token值：优先使用result.token，如果不存在则从映射中获取
          const tokenValue = result.token || tokenMap[result.uid];

          // 记录token值来源
          if (!result.token && tokenMap[result.uid]) {
            console.log(`Token ${result.uid}: 使用映射中的token值代替缺失的API响应token`);
          } else if (!result.token && !tokenMap[result.uid]) {
            console.warn(`Token ${result.uid}: API响应和映射中都缺少token值`);
          }

          // 如果无法获取token值，记录警告并跳过这个Token
          if (!tokenValue) {
            console.warn(`Token ${result.uid} 无法获取token值，跳过处理`);
            return;
          }

          // 将所有查询成功的Token添加到queriedTokens中，即使没有可删除的订单
          queriedTokens.push({
            uid: result.uid,
            token: tokenValue, // 使用从映射获取的token值
            isOnline: result.isOnline !== undefined ? result.isOnline : true,
            status: result.status || '在线',
            orders: result.data.orders || []
          });

          if (result.data.orders) {
            const allOrders = result.data.orders;
            const deletableOrders = allOrders.filter(order =>
              order.status && (
                order.status.includes('已评价') ||
                order.status.includes('交易成功') ||
                order.status.includes('交易已取消') ||
                order.status.includes('未发货') ||
                order.status.includes('退款成功')
              )
            );

            console.log(`Token ${result.uid}: 查询到${allOrders.length}个订单，其中${deletableOrders.length}个可删除`);

            // 记录可删除订单的详细信息，特别是"交易成功"状态的订单
            if (deletableOrders.length > 0) {
              const successOrders = deletableOrders.filter(order => order.status && order.status.includes('交易成功'));
              const evaluatedOrders = deletableOrders.filter(order => order.status && order.status.includes('已评价'));

              console.log(`Token ${result.uid}: 可删除订单中，"已评价"状态=${evaluatedOrders.length}个，"交易成功"状态=${successOrders.length}个`);

              // 记录第一个"交易成功"状态订单的完整数据结构
              if (successOrders.length > 0) {
                console.log('新查询 - 交易成功状态订单示例数据结构:', JSON.stringify(successOrders[0]));
                console.log('新查询 - 交易成功状态订单字段列表:', Object.keys(successOrders[0]));
              }

              // 记录第一个"已评价"状态订单的完整数据结构（用于比较）
              if (evaluatedOrders.length > 0) {
                console.log('新查询 - 已评价状态订单示例数据结构:', JSON.stringify(evaluatedOrders[0]));
                console.log('新查询 - 已评价状态订单字段列表:', Object.keys(evaluatedOrders[0]));
              }
            }

            // 如果有可删除的订单，记录token值
            if (deletableOrders.length > 0) {
              console.log(`Token ${result.uid}: 使用token值 ${tokenValue ? '已获取' : '缺失'} 来删除订单`);
            }

            // 将可删除的订单添加到待删除列表
            deletableOrders.forEach(order => {
              // 获取订单编号 - 考虑多种可能的字段名
              let orderNumber = null;

              // 检查可能的字段名
              if (order.orderId) {
                orderNumber = order.orderId;
              } else if (order.orderSn) {
                orderNumber = order.orderSn;
              } else if (order.order_sn) {
                orderNumber = order.order_sn;
              } else if (order.order_id) {
                orderNumber = order.order_id;
              } else if (order.id) {
                orderNumber = order.id;
              } else if (order.orderNo) {
                orderNumber = order.orderNo;
              } else if (order.order_no) {
                orderNumber = order.order_no;
              } else if (order.number) {
                orderNumber = order.number;
              } else if (order.sn) {
                orderNumber = order.sn;
              } else if (order.code) {
                orderNumber = order.code;
              }

              // 检查并确保需要的字段都存在
              if (!result.uid || !tokenValue || !orderNumber) {
                console.warn('新查询的订单数据缺少必要字段:', {
                  hasUid: !!result.uid,
                  hasToken: !!tokenValue,
                  hasOrderNumber: !!orderNumber,
                  possibleOrderIdFields: {
                    orderId: order.orderId,
                    orderSn: order.orderSn,
                    order_sn: order.order_sn,
                    order_id: order.order_id,
                    id: order.id
                  },
                  orderStatus: order.status,
                  orderData: order
                });
                return; // 跳过无效的订单
              }

              newOrderItemsToDelete.push({
                uid: result.uid,
                token: tokenValue, // 使用获取的token值
                orderSn: orderNumber // 使用获取的订单编号
              });
            });
          }
        } else {
          console.warn(`Token ${result.uid} 查询失败或无订单:`, result);
        }
      });

      // 并发删除新查询到的订单
      if (newOrderItemsToDelete.length > 0) {
        console.log(`准备删除新查询到的订单，总数: ${newOrderItemsToDelete.length}`);
        // 检查newOrderItemsToDelete结构
        console.log('删除新查询订单 - 参数示例:', {
          sample: newOrderItemsToDelete.slice(0, 2),
          allFields: newOrderItemsToDelete.length > 0 ? Object.keys(newOrderItemsToDelete[0]) : []
        });

        // 不再需要进度回调
        const newDeleteResult = await concurrentDeleteOrders(
          newOrderItemsToDelete,
          {
            ...options,
            // 移除进度回调
          }
        );

        if (newDeleteResult.success) {
          const successCount = newDeleteResult.results.filter(r => r.success).length;
          console.log(`新查询订单删除结果: 成功=${successCount}/${newDeleteResult.results.length}`);
          allResults = allResults.concat(newDeleteResult.results);
        } else {
          // 检查是否是系统繁忙错误
          if (newDeleteResult.error_code === 46047 || newDeleteResult.systemBusy) {
            console.warn('系统繁忙，跳过当前token，继续处理其他token');
            // 添加一个系统繁忙的结果，但不阻止继续处理
            allResults.push({
              success: false,
              message: '系统繁忙，请稍后重试！',
              error_code: 46047,
              systemBusy: true
            });
          } else {
            console.error('删除新查询订单失败:', newDeleteResult.message);
          }
        }
      } else {
        console.log('没有找到可删除的新查询订单');
      }
    }

    // 返回结果
    return {
      success: true,
      results: allResults,
      queriedTokens
    };
  } catch (error) {
    console.error('处理单批Token出错:', error);
    return {
      success: false,
      message: error.message || '处理单批Token出错',
      results: allResults,
      queriedTokens
    };
  }
};