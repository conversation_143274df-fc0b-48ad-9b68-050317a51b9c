const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/user');
const { logger } = require('../utils/logger');
const config = require('../config');

class AuthController {
  /**
   * 注册新用户
   */
  static async register(req, res, next) {
    try {
      const { username, password, email, fullName } = req.body;

      // 验证必填字段
      if (!username || !password || !email) {
        return res.status(400).json({
          success: false,
          message: '用户名、密码和邮箱为必填项'
        });
      }

      // 检查用户名是否已存在
      const existingUser = await User.getByUsername(username);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: '用户名已存在'
        });
      }

      // 检查邮箱是否已存在
      const existingEmail = await User.getByEmail(email);
      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: '邮箱已被注册'
        });
      }

      // 创建用户（使用明文密码）
      const userData = {
        username,
        password: password, // 直接使用明文密码
        email,
        full_name: fullName || username,
        role: 'user', // 默认角色
        status: 'active',
        created_at: new Date()
      };

      const userId = await User.create(userData);
      logger.info(`新用户注册成功: ${username} (ID: ${userId})`);

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          id: userId,
          username,
          email,
          fullName: userData.full_name,
          role: userData.role
        }
      });
    } catch (error) {
      logger.error(`用户注册失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 用户登录
   */
  static async login(req, res, next) {
    try {
      const { username, password } = req.body;

      // 验证必填字段
      if (!username || !password) {
        return res.status(400).json({
          success: false,
          message: '用户名和密码为必填项'
        });
      }

      // 获取用户
      const user = await User.getByUsername(username);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 直接比较明文密码
      const isPasswordValid = password === user.password;
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 生成JWT令牌
      const tokenPayload = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

      const accessToken = jwt.sign(
        tokenPayload,
        config.jwt.secret,
        { expiresIn: config.jwt.accessTokenExpiry }
      );

      const refreshToken = jwt.sign(
        { id: user.id },
        config.jwt.refreshSecret,
        { expiresIn: config.jwt.refreshTokenExpiry }
      );

      // 更新用户的最后登录时间
      await User.updateLastLogin(user.id);
      logger.info(`用户登录成功: ${username} (ID: ${user.id})`);

      // 设置令牌到cookie（如果需要）
      if (config.jwt.useCookies) {
        res.cookie('accessToken', accessToken, {
          httpOnly: true,
          secure: config.env === 'production',
          maxAge: 60 * 60 * 1000 // 1小时
        });
        
        res.cookie('refreshToken', refreshToken, {
          httpOnly: true,
          secure: config.env === 'production',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
        });
      }

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.full_name,
            role: user.role
          },
          tokens: {
            accessToken,
            refreshToken,
            expiresIn: config.jwt.accessTokenExpiry
          }
        }
      });
    } catch (error) {
      logger.error(`用户登录失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 用户登出
   */
  static async logout(req, res, next) {
    try {
      // 清除cookie（如果使用）
      if (config.jwt.useCookies) {
        res.clearCookie('accessToken');
        res.clearCookie('refreshToken');
      }
      
      // 记录登出操作
      logger.info(`用户登出: ${req.user.username} (ID: ${req.user.id})`);

      res.json({
        success: true,
        message: '登出成功'
      });
    } catch (error) {
      logger.error(`用户登出失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 刷新令牌
   */
  static async refreshToken(req, res, next) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: '缺少刷新令牌'
        });
      }

      // 验证刷新令牌
      let decoded;
      try {
        decoded = jwt.verify(refreshToken, config.jwt.refreshSecret);
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: '无效的刷新令牌'
        });
      }

      // 获取用户
      const user = await User.getById(decoded.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 检查用户状态
      if (user.status !== 'active') {
        return res.status(403).json({
          success: false,
          message: '账户已被禁用，请联系管理员'
        });
      }

      // 生成新的访问令牌
      const tokenPayload = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

      const accessToken = jwt.sign(
        tokenPayload,
        config.jwt.secret,
        { expiresIn: config.jwt.accessTokenExpiry }
      );

      // 生成新的刷新令牌
      const newRefreshToken = jwt.sign(
        { id: user.id },
        config.jwt.refreshSecret,
        { expiresIn: config.jwt.refreshTokenExpiry }
      );

      logger.info(`令牌刷新成功: ${user.username} (ID: ${user.id})`);

      // 设置新令牌到cookie（如果需要）
      if (config.jwt.useCookies) {
        res.cookie('accessToken', accessToken, {
          httpOnly: true,
          secure: config.env === 'production',
          maxAge: 60 * 60 * 1000 // 1小时
        });
        
        res.cookie('refreshToken', newRefreshToken, {
          httpOnly: true,
          secure: config.env === 'production',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
        });
      }

      res.json({
        success: true,
        message: '令牌刷新成功',
        data: {
          tokens: {
            accessToken,
            refreshToken: newRefreshToken,
            expiresIn: config.jwt.accessTokenExpiry
          }
        }
      });
    } catch (error) {
      logger.error(`令牌刷新失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(req, res, next) {
    try {
      const userId = req.user.id;
      const user = await User.getById(userId);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role,
          status: user.status,
          createdAt: user.created_at,
          lastLogin: user.last_login
        }
      });
    } catch (error) {
      logger.error(`获取当前用户信息失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 修改密码
   */
  static async changePassword(req, res, next) {
    try {
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;

      // 验证必填字段
      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: '当前密码和新密码为必填项'
        });
      }

      // 获取用户
      const user = await User.getById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 直接比较明文密码
      const isPasswordValid = currentPassword === user.password;
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: '当前密码错误'
        });
      }

      // 更新密码（直接使用明文）
      await User.updatePassword(userId, newPassword);
      
      logger.info(`用户更新密码成功: ${user.username} (ID: ${userId})`);

      res.json({
        success: true,
        message: '密码修改成功'
      });
    } catch (error) {
      logger.error(`修改密码失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 请求重置密码
   */
  static async requestPasswordReset(req, res, next) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({
          success: false,
          message: '邮箱为必填项'
        });
      }

      // 获取用户
      const user = await User.getByEmail(email);
      if (!user) {
        // 为了安全，即使用户不存在也返回成功
        return res.json({
          success: true,
          message: '如果邮箱存在，重置密码的链接已发送到您的邮箱'
        });
      }

      // 生成重置令牌
      const resetToken = jwt.sign(
        { id: user.id, email: user.email },
        config.jwt.resetSecret,
        { expiresIn: '1h' }
      );

      // 保存重置令牌
      await User.saveResetToken(user.id, resetToken);

      // 发送重置邮件
      // TODO: 实现邮件发送功能
      logger.info(`用户请求重置密码: ${user.username} (ID: ${user.id})`);

      res.json({
        success: true,
        message: '如果邮箱存在，重置密码的链接已发送到您的邮箱'
      });
    } catch (error) {
      logger.error(`请求重置密码失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 重置密码
   */
  static async resetPassword(req, res, next) {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        return res.status(400).json({
          success: false,
          message: '令牌和新密码为必填项'
        });
      }

      // 验证令牌
      let decoded;
      try {
        decoded = jwt.verify(token, config.jwt.resetSecret);
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: '无效或已过期的重置令牌'
        });
      }

      // 获取用户
      const user = await User.getById(decoded.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 验证令牌是否与用户保存的令牌匹配
      const isTokenValid = await User.validateResetToken(user.id, token);
      if (!isTokenValid) {
        return res.status(401).json({
          success: false,
          message: '无效的重置令牌'
        });
      }

      // 更新密码
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await User.updatePassword(user.id, hashedPassword);
      
      // 清除重置令牌
      await User.clearResetToken(user.id);
      
      logger.info(`用户重置密码成功: ${user.username} (ID: ${user.id})`);

      res.json({
        success: true,
        message: '密码重置成功，请使用新密码登录'
      });
    } catch (error) {
      logger.error(`重置密码失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：获取所有用户
   */
  static async getAllUsers(req, res, next) {
    try {
      const { page = 1, limit = 10, status, role, search } = req.query;

      const filters = {};
      if (status) filters.status = status;
      if (role) filters.role = role;
      if (search) filters.search = search;

      const result = await User.getAll(parseInt(page), parseInt(limit), filters);

      res.json({
        success: true,
        data: {
          users: result.users,
          pagination: {
            total: result.total,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(result.total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      logger.error(`获取所有用户失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：更新用户角色
   */
  static async updateUserRole(req, res, next) {
    try {
      const { id } = req.params;
      const { role } = req.body;

      if (!role || !['user', 'admin'].includes(role)) {
        return res.status(400).json({
          success: false,
          message: '角色必须为 user 或 admin'
        });
      }

      // 验证用户是否存在
      const user = await User.getById(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 不允许管理员修改自己的角色
      if (parseInt(id) === req.user.id) {
        return res.status(403).json({
          success: false,
          message: '不能修改自己的角色'
        });
      }

      // 更新角色
      await User.updateRole(id, role);
      logger.info(`管理员更新用户角色: ${user.username} (ID: ${id}) 角色更新为 ${role}`);

      res.json({
        success: true,
        message: '用户角色更新成功',
        data: {
          id: parseInt(id),
          role
        }
      });
    } catch (error) {
      logger.error(`更新用户角色失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 管理员：禁用用户
   */
  static async disableUser(req, res, next) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status || !['active', 'disabled'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: '状态必须为 active 或 disabled'
        });
      }

      // 验证用户是否存在
      const user = await User.getById(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 不允许管理员禁用自己
      if (parseInt(id) === req.user.id) {
        return res.status(403).json({
          success: false,
          message: '不能修改自己的状态'
        });
      }

      // 更新状态
      await User.updateStatus(id, status);
      logger.info(`管理员更新用户状态: ${user.username} (ID: ${id}) 状态更新为 ${status}`);

      res.json({
        success: true,
        message: `用户已${status === 'active' ? '启用' : '禁用'}`,
        data: {
          id: parseInt(id),
          status
        }
      });
    } catch (error) {
      logger.error(`更新用户状态失败: ${error.message}`);
      next(error);
    }
  }

  /**
   * 更新用户个人资料
   */
  static async updateProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const { username, email, fullName } = req.body;
      
      // 获取当前用户信息
      const currentUser = await User.getById(userId);
      if (!currentUser) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }
      
      // 准备更新数据
      const userData = {};
      
      // 如果更新用户名，需要检查是否已存在
      if (username && username !== currentUser.username) {
        const existingUser = await User.getByUsername(username);
        if (existingUser && existingUser.id !== userId) {
          return res.status(409).json({
            success: false,
            message: '用户名已存在'
          });
        }
        userData.username = username;
      }
      
      // 如果更新邮箱，需要检查是否已存在
      if (email && email !== currentUser.email) {
        const existingEmail = await User.getByEmail(email);
        if (existingEmail && existingEmail.id !== userId) {
          return res.status(409).json({
            success: false,
            message: '邮箱已被注册'
          });
        }
        userData.email = email;
      }
      
      // 更新全名
      if (fullName) {
        userData.full_name = fullName;
      }
      
      // 如果没有任何需要更新的字段
      if (Object.keys(userData).length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有提供要更新的字段'
        });
      }
      
      // 更新用户信息
      await User.updateProfile(userId, userData);
      logger.info(`用户更新个人资料成功: ${currentUser.username} (ID: ${userId})`);
      
      // 获取更新后的用户信息
      const updatedUser = await User.getById(userId);
      
      res.json({
        success: true,
        message: '个人资料更新成功',
        data: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          fullName: updatedUser.full_name,
          role: updatedUser.role
        }
      });
    } catch (error) {
      logger.error(`更新用户个人资料失败: ${error.message}`);
      next(error);
    }
  }
}

module.exports = AuthController; 