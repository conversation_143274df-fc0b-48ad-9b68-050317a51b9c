-- Token管理系统数据库更新脚本
-- 用于更新已存在的数据库结构，添加缺失的字段
-- 创建日期：2024年05月

USE token_management;

-- 检查并添加backups表中缺失的字段
ALTER TABLE backups
  ADD COLUMN IF NOT EXISTS token_count INT DEFAULT 0 AFTER task_id,
  ADD COLUMN IF NOT EXISTS size VARCHAR(20) AFTER token_count,
  ADD COLUMN IF NOT EXISTS type VARCHAR(20) AFTER size,
  ADD COLUMN IF NOT EXISTS remark TEXT AFTER description,
  ADD COLUMN IF NOT EXISTS delete_status BOOLEAN DEFAULT FALSE AFTER status,
  ADD COLUMN IF NOT EXISTS scheduled_delete_time DATETIME AFTER delete_status,
  ADD COLUMN IF NOT EXISTS backup_time DATETIME AFTER scheduled_delete_time;

-- 确保所有必要的索引存在
CREATE INDEX IF NOT EXISTS idx_backups_backup_id ON backups(backup_id);
CREATE INDEX IF NOT EXISTS idx_backups_backup_type ON backups(backup_type);
CREATE INDEX IF NOT EXISTS idx_backups_task_id ON backups(task_id);
CREATE INDEX IF NOT EXISTS idx_backups_status ON backups(status);
CREATE INDEX IF NOT EXISTS idx_backups_delete_status ON backups(delete_status);
CREATE INDEX IF NOT EXISTS idx_backups_created_by ON backups(created_by);
CREATE INDEX IF NOT EXISTS idx_backups_scheduled_delete_time ON backups(scheduled_delete_time);

-- 检查并创建备份Token关联表的字段和索引
CREATE INDEX IF NOT EXISTS idx_backup_tokens_backup_id ON backup_tokens(backup_id);
CREATE INDEX IF NOT EXISTS idx_backup_tokens_token_id ON backup_tokens(token_id);

-- 检查并创建token表上的索引
CREATE INDEX IF NOT EXISTS idx_tokens_uid ON tokens(uid);
CREATE INDEX IF NOT EXISTS idx_tokens_status ON tokens(status);
CREATE INDEX IF NOT EXISTS idx_tokens_purchase_time ON tokens(purchase_time);
CREATE INDEX IF NOT EXISTS idx_tokens_expiry_date ON tokens(expiry_date);
CREATE INDEX IF NOT EXISTS idx_tokens_platform_status ON tokens(platform, status);

-- 检查并创建备份表相关的表
CREATE TABLE IF NOT EXISTS backup_tokens_complete (
  id INT AUTO_INCREMENT PRIMARY KEY,
  backup_id INT NOT NULL,
  uid VARCHAR(255) NOT NULL,
  token VARCHAR(1000) NOT NULL,
  username VARCHAR(255),
  nickname VARCHAR(255),
  avatar VARCHAR(255),
  status VARCHAR(50),
  platform VARCHAR(50),
  purchase_time DATETIME,
  expiry_time DATETIME,
  order_id VARCHAR(255),
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 为完整备份表添加索引(如果不存在)
CREATE INDEX IF NOT EXISTS idx_backup_tokens_complete_backup_id ON backup_tokens_complete(backup_id);
CREATE INDEX IF NOT EXISTS idx_backup_tokens_complete_uid ON backup_tokens_complete(uid);

-- 检查并创建字符串类型Token ID备份表
CREATE TABLE IF NOT EXISTS backup_tokens_string (
  id INT AUTO_INCREMENT PRIMARY KEY,
  backup_id INT NOT NULL,
  token_id_string VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (backup_id) REFERENCES backups(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 为字符串Token ID表添加索引
CREATE INDEX IF NOT EXISTS idx_backup_tokens_string_backup_id ON backup_tokens_string(backup_id);

-- 检查并创建通知设置表
CREATE TABLE IF NOT EXISTS notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_enabled BOOLEAN DEFAULT TRUE,
    expiry_notification JSON,
    backup_notification BOOLEAN DEFAULT TRUE,
    system_events JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 检查并创建自动重置任务表
CREATE TABLE IF NOT EXISTS auto_reset_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    name VARCHAR(100),
    status VARCHAR(20) DEFAULT '待执行',
    schedule_type VARCHAR(20),
    scheduled_time DATETIME,
    load_days INT,
    platform VARCHAR(50),
    token_count INT DEFAULT 0,
    before_backup_id VARCHAR(50),
    after_backup_id VARCHAR(50),
    created_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at DATETIME,
    remarks TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 为自动重置任务表添加索引
CREATE INDEX IF NOT EXISTS idx_auto_reset_tasks_task_id ON auto_reset_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_auto_reset_tasks_status ON auto_reset_tasks(status);
CREATE INDEX IF NOT EXISTS idx_auto_reset_tasks_scheduled_time ON auto_reset_tasks(scheduled_time);

-- 确保设置表中有必要的基础设置
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('system_name', 'Token 管理系统', '系统名称'),
('theme', 'default', '系统主题'),
('language', 'zh-CN', '系统语言'),
('page_size', '20', '默认分页大小'),
('backup_retention_days', '30', '备份保留天数'),
('token_cleanup_days', '60', '过期Token清理天数'); 