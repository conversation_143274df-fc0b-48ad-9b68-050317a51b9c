﻿<template>
  <div class="token-reset-container">
    <el-card class="token-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>Token列表</span>
          <div class="token-count-indicator" v-if="totalTokenCount > 0">
            <el-tag type="success" effect="plain">
              <el-icon><el-icon-document /></el-icon>
              共{{ totalTokenCount }} 个Token
            </el-tag>
          </div>
          <div class="header-actions">
            <!-- 筛选功能区域 -->
            <div class="filter-container">
              <el-select
                v-model="statusFilter"
                placeholder="状态筛选"
                clearable
                class="filter-select"
                @change="handleFilterChange"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-tag :type="getStatusType(item.value)" effect="light" size="small">
                    {{ item.label }}
                  </el-tag>
                </el-option>
              </el-select>

              <el-select
                v-model="userFilter"
                placeholder="用户筛选"
                clearable
                class="filter-select"
                filterable
                @change="handleFilterChange"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user"
                  :label="user"
                  :value="user"
                />
              </el-select>
            </div>

            <!-- 搜索区域 -->
            <div class="search-container">
            <el-input
              v-model="searchKeyword"
                placeholder="搜索UID、TOKEN/用户"
              class="search-input"
              clearable
              @clear="handleSearch"
              @input="handleSearch"
                @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><el-icon-search /></el-icon>
              </template>
            </el-input>

              <!-- 增加搜索和重置按钮 -->
              <div class="search-actions">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><el-icon-search /></el-icon>搜索
                </el-button>
                <el-button @click="resetFilters" class="reset-button">
                  <el-icon><el-icon-refresh /></el-icon>重置筛选
                </el-button>
              </div>
            </div>

            <!-- 数据统计信息 -->
            <div class="data-stats">
              <div class="stats-card info-card">
                <span class="stats-value">{{ totalTokenCount }}</span>
                <span class="stats-label">总数量</span>
              </div>
              <div class="stats-card primary-card">
                <span class="stats-value">{{ filteredCount }}</span>
                <span class="stats-label">筛选结果</span>
              </div>
              <div class="stats-card warning-card" v-if="selectedTokens.length > 0">
                <span class="stats-value">{{ selectedTokens.length }}</span>
                <span class="stats-label">已选择</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 功能按钮区域 -->
      <div class="function-buttons">
        <el-button-group>
          <TokenImport
            :tokens="tokens"
            :shouldResetPage="shouldResetPage"
            :totalTokenCount="totalTokenCount"
            :currentPage="currentPage"
            @update:tokens="tokens = $event"
            @import-success="handleImportSuccess"
            @import-error="handleImportError"
          />
          <el-button type="danger" @click="handleClearTable">
            <el-icon><el-icon-delete /></el-icon>清空表格
          </el-button>
          <el-button type="warning" @click="handleLoadUnsold">
            <el-icon><el-icon-refresh /></el-icon>加载未卖
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="info" @click="handleQueryAvatarNickname">
            <el-icon><el-icon-user /></el-icon>查询头像昵称
          </el-button>
          <el-button type="success" @click="handleQueryOnline">
            <el-icon><el-icon-connection /></el-icon>查询在线
          </el-button>
          <el-button type="warning" @click="handleQueryOrder">
            <el-icon><el-icon-goods /></el-icon>查询订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="primary" @click="handleChangeAvatar">
            <el-icon><el-icon-picture /></el-icon>修改头像
          </el-button>
          <el-button type="primary" @click="handleChangeNickname">
            <el-icon><el-icon-edit /></el-icon>修改昵称
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="success" @click="handleBackup">
            <el-icon><el-icon-document-copy /></el-icon>创建备份
          </el-button>
          <el-button type="warning" @click="handleResetSelected">
            <el-icon><el-icon-refresh-right /></el-icon>重置选中
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="primary" @click="handleUploadSelected">
            <el-icon><el-icon-upload /></el-icon>选中上传后台
          </el-button>
          <el-button type="warning" @click="handleAddToOrder">
            <el-icon><el-icon-shopping-cart /></el-icon>新Token置入账号内订单
          </el-button>
        </el-button-group>

        <el-button-group>
          <el-button type="danger" @click="handleDeleteOrder">
            <el-icon><el-icon-delete /></el-icon>删除订单
          </el-button>
          <el-button type="success" @click="handleAssignToAccount">
            <el-icon><el-icon-user-filled /></el-icon>选中给指定账号
          </el-button>
        </el-button-group>
      </div>

      <!-- Token表格 -->
      <div class="table-actions">
        <el-button
          type="primary"
          size="small"
          @click="selectAllTokens"
          :disabled="tokens.length === 0"
        >
          <el-icon><el-icon-select /></el-icon>全选所有数据
        </el-button>
        <el-button
          type="info"
          size="small"
          @click="clearAllSelection"
          :disabled="selectedTokens.length === 0"
        >
          <el-icon><el-icon-close /></el-icon>清除选择
        </el-button>
        <div v-if="hasSelectionAcrossPages" class="selection-info">
          <el-tag type="warning" effect="plain">
            已选择 <strong>{{ selectedTokens.length }}</strong> 条数据（包含跨页选择）
          </el-tag>
        </div>
      </div>
      <el-table
        ref="tokenTableRef"
        :data="tableData"
        style="width: 100%"
        border
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        v-loading="loading"
        row-key="uid"
        stripe
        highlight-current-row
        class="token-table"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true" />
        <el-table-column prop="uid" label="UID" width="150" sortable />
        <el-table-column prop="token" label="Token" min-width="230" show-overflow-tooltip />
        <el-table-column prop="user" label="用户" width="120" sortable>
          <template #default="scope">
            <el-button
              type="text"
              @click="filterByUser(scope.row.user)"
              :style="{ color: userFilter === scope.row.user ? '#409EFF' : '' }"
              class="user-filter-btn"
            >
              {{ scope.row.user }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="scope">
            <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" class="token-avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" width="120" show-overflow-tooltip />
        <el-table-column prop="purchaseTime" label="购买时间" width="180" sortable />
        <el-table-column prop="expiryDate" label="到期时间" width="180" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              effect="light"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderInfo" label="订单" width="280">
          <template #default="scope">
            <div v-if="scope.row.orderInfo">
              <!-- 掉线状态 -->
              <div v-if="scope.row.orderInfo.status === '掉线'" class="order-info">
                <div class="status-badge status-offline">
                  <i class="el-icon-warning-outline"></i>
                  <span>掉线</span>
                </div>
              </div>
              <!-- 在线但无订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount === 0" class="order-info">
                <div class="status-badge status-no-order">
                  <i class="el-icon-shopping-bag-1"></i>
                  <span>无订单</span>
                </div>
              </div>
              <!-- 在线且有订单 -->
              <div v-else-if="scope.row.orderInfo.status === '在线' && scope.row.orderInfo.orderCount > 0" class="order-info">
                <!-- 显示删除状态和进度 -->
                <div v-if="scope.row.orderDeletionStatus && scope.row.orderDeletionStatus.totalOrders > 0" class="order-status-container">
                  <!-- 基本订单信息和数量 -->
                  <div
                    class="order-count-badge"
                    @click="handleViewOrders(scope.row)"
                  >
                    <i class="el-icon-goods"></i>
                    <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                    <span class="order-text">订单</span>
                  </div>

                  <!-- 删除状态信息 -->
                  <div class="order-deletion-status">
                    <template v-if="scope.row.orderDeletionStatus.inProgress">
                      <el-tag type="warning" effect="plain" size="small">正在删除订单...</el-tag>
                    </template>
                    <template v-else>
                      <!-- 显示删除结果 -->
                      <div class="order-status-text">
                        <el-tag v-if="scope.row.orderDeletionStatus.successCount > 0" type="success" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-check"></i>
                          <span>已删除 {{ scope.row.orderDeletionStatus.successCount }} 个</span>
                        </el-tag>
                        <el-tag v-if="scope.row.orderDeletionStatus.failCount > 0" type="danger" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-close"></i>
                          <span>失败 {{ scope.row.orderDeletionStatus.failCount }} 个</span>
                        </el-tag>
                        <el-tag type="info" size="small" effect="plain" class="status-tag">
                          <i class="el-icon-goods"></i>
                          <span>剩余 {{ scope.row.orderInfo.orderCount }} 个</span>
                          <span class="deletable-count">({{ getDeletableCount(scope.row) }}个可删)</span>
                        </el-tag>
                      </div>
                    </template>
                  </div>
                </div>
                <!-- 默认订单显示 -->
                <div
                  v-else
                  class="order-count-badge"
                  @click="handleViewOrders(scope.row)"
                >
                  <i class="el-icon-goods"></i>
                  <span class="order-number">{{ scope.row.orderInfo.orderCount }}</span>
                  <span class="order-text">订单</span>
                  <el-tag v-if="getDeletableCount(scope.row) > 0" size="small" type="success" effect="light" class="deletable-tag">
                    {{ getDeletableCount(scope.row) }}个可删
                  </el-tag>
                </div>
              </div>
              <!-- 查询失败或未知状态 -->
              <div v-else class="order-info">
                <div class="status-badge status-error">
                  <i class="el-icon-circle-close"></i>
                  <span>{{ scope.row.orderInfo.status || '查询失败' }}</span>
                </div>
              </div>
            </div>
            <div v-else class="order-info">
              <div class="status-badge status-not-queried">
                <i class="el-icon-question"></i>
                <span>未查询</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <div class="table-action-buttons">
              <el-button type="primary" size="small" text @click="handleReset(scope.row)" class="action-btn">重置</el-button>
              <el-button type="success" size="small" text @click="handleBackup(scope.row)" class="action-btn">备份</el-button>
              <el-button type="info" size="small" text @click="handleEdit(scope.row)" class="action-btn">编辑</el-button>
              <el-button type="danger" size="small" text @click="handleDelete(scope.row)" class="action-btn">删除</el-button>
              <!-- 替换原来的QuickOrderDelete组件为直接删除订单按钮 -->
              <el-button
                type="warning"
                size="small"
                text
                @click="handleQuickDeleteOrder(scope.row)"
                class="action-btn"
                :loading="scope.row.orderDeleteLoading"
              >
                删除订单
                <template v-if="getDeletableCount(scope.row) > 0">({{ getDeletableCount(scope.row) }})</template>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="filteredCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- TokenImport组件已包含文件输入框 -->

    <!-- 其他功能对话框 -->
    <el-dialog
      v-model="operationDialogVisible"
      :title="operationTitle"
      width="40%"
    >
      <div class="operation-dialog-content">
        {{ operationMessage }}
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="operationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmOperation">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看订单详情对话框 -->
    <el-dialog
      v-model="ordersDialogVisible"
      title="订单详情"
      width="70%"
    >
      <div class="order-details">
        <!-- 添加订单统计信息和批量操作按钮 -->
        <div class="orders-summary-bar">
          <div class="orders-stats">
            <el-tag type="info" effect="plain">
              总计 <strong>{{ currentOrders.length }}</strong> 个订单
            </el-tag>
            <el-tag type="success" effect="plain" class="ml-10">
              可删除 <strong>{{ deletableOrdersCount }}</strong> 个订单
            </el-tag>
          </div>
          <div class="batch-actions">
            <el-button
              type="primary"
              size="small"
              @click="selectAllDeletableOrders"
              :disabled="deletableOrdersCount === 0"
            >
              <el-icon><el-icon-select /></el-icon>全选可删除订单
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="batchDeleteSelectedOrders"
              :disabled="selectedOrdersInList.length === 0"
            >
              <el-icon><el-icon-delete /></el-icon>批量删除 ({{ selectedOrdersInList.length }})
            </el-button>
          </div>
        </div>

        <el-table
          :data="currentOrders"
          style="width: 100%"
          border
          v-loading="loading"
          row-key="orderId"
          stripe
          highlight-current-row
          class="order-table"
          @selection-change="handleOrderSelectionChange"
          ref="orderTableRef"
        >
          <el-table-column type="selection" width="55" :selectable="row => isDeletableOrder(row.status)" />
          <el-table-column prop="orderId" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getOrderStatusTag(scope.row.status)" effect="light">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="80">
            <template #default="scope">
              ¥{{ scope.row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="goodsInfo" label="商品信息" min-width="200" show-overflow-tooltip />
          <el-table-column prop="orderTime" label="订单时间" width="180" sortable />
          <el-table-column prop="mallName" label="商家名称" width="120" show-overflow-tooltip />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <div class="order-action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewOrderDetail(scope.row)"
                text
              >
                查看详情
              </el-button>
                <el-button
                  v-if="isDeletableOrder(scope.row.status)"
                  type="danger"
                  size="small"
                  @click="handleDeleteSingleOrder(scope.row)"
                  text
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ordersDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="orderDetailDialogVisible"
      title="订单详细信息"
      width="50%"
    >
      <div v-if="currentOrderDetail" class="order-detail-content">
        <!-- 添加显示删除按钮的条件判断 -->
        <div class="order-detail-actions" v-if="isDeletableOrder(currentOrderDetail.status)">
          <el-button
            type="danger"
            size="small"
            @click="handleDeleteSingleOrder(currentOrderDetail)"
          >
            删除订单
          </el-button>
          <el-tooltip content="仅已评价的订单可删除" placement="top">
            <el-tag type="success" size="small" style="margin-left: 10px;">可删除</el-tag>
          </el-tooltip>
        </div>
        <div class="order-detail-actions" v-else>
          <el-tooltip content="该订单状态不允许删除" placement="top">
            <el-tag type="danger" size="small">不可删除</el-tag>
          </el-tooltip>
          <span class="deletion-notice">（只有已评价的订单可删除）</span>
        </div>

        <div class="order-detail-item">
          <div class="order-detail-label">订单号</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderId }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">状态</div>
          <div class="order-detail-value">
            <el-tag :type="getOrderStatusTag(currentOrderDetail.status)">
              {{ currentOrderDetail.status }}
            </el-tag>
          </div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">金额:</div>
          <div class="order-detail-value">¥{{ currentOrderDetail.amount }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">下单时间:</div>
          <div class="order-detail-value">{{ currentOrderDetail.orderTime }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商品:</div>
          <div class="order-detail-value">{{ currentOrderDetail.goodsInfo }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">物流单号:</div>
          <div class="order-detail-value">{{ currentOrderDetail.trackingNumber || '暂无' }}</div>
        </div>
        <div class="order-detail-item">
          <div class="order-detail-label">商家:</div>
          <div class="order-detail-value">{{ currentOrderDetail.mallName }}</div>
        </div>
        <el-divider />
        <div class="order-extra-info">
          <el-collapse>
            <el-collapse-item title="更多订单信息" name="1">
              <pre>{{ JSON.stringify(currentOrderDetail.extraInfo, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="orderDetailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElLoading, ElMessageBox, ElNotification } from 'element-plus'
import * as tokenService from '@/services/tokenService'
import { applyOnlineStatusToTokens } from '@/services/onlineCheckService'
import { formatDateTime } from '@/utils/helpers'
// 导入token相关的函数已移动到TokenImport.vue组件中
// 导入组件
import TokenImport from '@/components/TokenImport.vue'
import * as orderService from '../services/orderService'

// 对话框控件
const operationDialogVisible = ref(false)
const operationTitle = ref('')
const operationMessage = ref('')
const currentOperation = ref('')

// 处理导入成功事件
const handleImportSuccess = (result) => {
  console.log('导入成功:', result)
  // 刷新数据
  refreshTokens()
}

// 处理导入失败事件
const handleImportError = (error) => {
  console.error('导入失败:', error)
}

// 公共操作对话框
const showOperationDialog = (title, message, operation) => {
  operationTitle.value = title
  operationMessage.value = message
  currentOperation.value = operation
  operationDialogVisible.value = true
}

// Token列表数据
const tokens = ref([]);
const selectedTokens = ref([]);
const loading = ref(false);
const isLocalData = ref(true); // 始终标记为本地数据
const tableHeight = ref('400px'); // 进一步减小默认高度
// 分页相关
const currentPage = ref(1);
const pageSize = ref(100); // 默认每页显示20条，减轻渲染压力
const totalTokenCount = ref(0);

// 优化表格渲染和筛选性能
// 缓存已筛选的数据，避免重复计算
const filteredTokensCache = ref([]);
const isFiltering = ref(false);

// 更改为方法而不是计算属性，实现延迟计算
const filterTokens = () => {
  // 如果正在筛选中，不重复执行
  if (isFiltering.value) return;

  isFiltering.value = true;
  loading.value = true;

  // 使用setTimeout将筛选操作移到下一个事件循环，避免阻塞UI
  setTimeout(() => {
    try {
      // 先筛选数据
      let result = [...tokens.value]; // 创建拷贝，避免直接修改原数组

      // 按状态筛选
      if (statusFilter.value) {
        result = result.filter(token => token.status === statusFilter.value);
      }

      // 按用户筛选
      if (userFilter.value) {
        result = result.filter(token => token.user === userFilter.value);
      }

      // 按关键字搜索
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        result = result.filter(token =>
          (token.uid && token.uid.toLowerCase().includes(keyword)) ||
          (token.user && token.user.toLowerCase().includes(keyword)) ||
          (token.nickname && token.nickname && token.nickname.toLowerCase().includes(keyword)) ||
          (token.token && token.token.toLowerCase().includes(keyword)) ||
          (token.status && token.status.toLowerCase().includes(keyword))
        );
      }

      // 更新缓存的筛选结果
      filteredTokensCache.value = result;

      // 更新总计
      totalTokenCount.value = tokens.value.length;

      // 处理筛选完成后的操作
      nextTick(() => {
        // 重置到第一页如果当前页超出范围
        const totalPages = Math.ceil(filteredTokensCache.value.length / pageSize.value) || 1;
        if (currentPage.value > totalPages) {
          currentPage.value = 1;
        }
        // 移除以下代码块，不再基于shouldResetPage自动重置页码
        // else if (shouldResetPage.value) {
        //   // 仅当需要重置页码时才重置
        //   //   currentPage.value = 1;
        // }

        // 重新应用选择状态，确保选中状态与数据一致
        applyTableSelectionStatus();

        // 不再在这里重置标志，以保持当前设置
        // shouldResetPage.value = true;
      });
    } catch (error) {
      console.error('数据筛选错误:', error);
    } finally {
      isFiltering.value = false;
      loading.value = false;
    }
  }, 0);
};

// 优化表格数据计算属性
const tableData = computed(() => {
  // 计算当前页数
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = Math.min(startIndex + pageSize.value, filteredTokensCache.value.length);

  // 返回当前页的数据
  return filteredTokensCache.value.slice(startIndex, endIndex);
});

// 筛选后的数据总量计算属性
const filteredCount = computed(() => filteredTokensCache.value.length);

// 搜索
const searchKeyword = ref('');

// 筛选功能相关
const statusFilter = ref('');
const userFilter = ref('');

// 状态选项
const statusOptions = [
  { value: '未知', label: '未知' },
  { value: '在线', label: '在线' },
  { value: '掉线', label: '掉线' },
  { value: '正常', label: '正常' },
  { value: '过期', label: '过期' },
  { value: '待激活', label: '待激活' },
  { value: '已重置', label: '已重置' }
];

// 用户选项（从Token数据中提取）
const userOptions = computed(() => {
  // 提取所有不同的用户
  const userSet = new Set();
  tokens.value.forEach(token => {
    if (token.user) {
      userSet.add(token.user);
    }
  });
  return Array.from(userSet).sort();
});

// 表格引用
const tokenTableRef = ref(null);

// 应用表格选择状态
const applyTableSelection = () => {
  try {
    // 清除所有选择
    tokenTableRef.value?.clearSelection();

    // 获取当前页面上应该被选中的行
    nextTick(() => {
      // 对于当前页的每个选中项，手动设置选中
      tableData.value.forEach(token => {
        if (selectedTokens.value.some(selected => selected.uid === token.uid)) {
          tokenTableRef.value?.toggleRowSelection(token, true);
        }
      });
    });
  } catch (error) {
    console.error('应用选择状态错误:', error);
  }
};

// 处理筛选变化
const handleFilterChange = () => {
  // 筛选变化时重置到第一页
  // 只有用户明确进行筛选操作时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 搜索处理
const handleSearch = () => {
  // 搜索时重置到第一页
  // 只有用户明确进行搜索操作时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 通过用户名筛选
const filterByUser = (username) => {
  if (userFilter.value === username) {
    // 如果已经是筛选这个用户，点击则取消筛选
    userFilter.value = '';
  } else {
    // 否则设置为筛选这个用户
    userFilter.value = username;
  }

  // 用户明确筛选时才重置到第一页
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 增加搜索和重置按钮
const resetFilters = () => {
  statusFilter.value = '';
  userFilter.value = '';
  searchKeyword.value = '';
  // 用户明确重置筛选时才重置页码
  shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
};

// 刷新Token列表 - 优化
const refreshTokens = () => {
  // 如果正在刷新中，避免重复执行
  if (loading.value) return;

  // 显示加载状态
  loading.value = true;

  // 刷新时不重置页码，保持当前页
  // shouldResetPage.value = true;

  // 重新筛选数据
  filterTokens();

  // 确保在刷新完成后重新应用选择状态
  setTimeout(() => {
    applyTableSelectionStatus();
    loading.value = false;
  }, 50);
};

// 监听导入数据状态，优化大数据集处理
watch(tokens, (newTokens) => {
  if (newTokens.length > 1000) {
    // 数据量大时自动调整页大小
    if (pageSize.value < 50) {
      pageSize.value = 50;
      ElMessage.info('数据量较大，已自动调整为每页显示50条');
    }
  }

  // 不再强制重置到第一页
  // shouldResetPage.value = true;

  // 执行筛选
  filterTokens();
}, { deep: false });

// 更新选中的Token信息
const updateTokensInfo = (updatedTokens) => {
  if (!updatedTokens || updatedTokens.length === 0) return

  // 更新本地数据
  tokens.value = tokens.value.map(token => {
    const updated = updatedTokens.find(t => t.uid === token.uid)
    return updated ? { ...token, ...updated } : token
  })

  // 设置不重置页码标志
  shouldResetPage.value = false;

  // 应用筛选但不改变页码
  filterTokens();
}

// 处理单个Token操作
const handleReset = async (token) => {
  ElMessageBox.confirm(
    '确认要重置此Token吗？',
    '操作确认',
    {
      confirmButtonText: '重置',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟重置效果
    const index = tokens.value.findIndex(t => t.uid === token.uid);
    if (index !== -1) {
      const newToken = { ...token };
      newToken.token = 'NEW_' + Math.random().toString(36).substring(2, 10);
      newToken.status = '已重置';
      newToken.resetTime = formatDateTime(new Date());
      tokens.value[index] = newToken;
      ElMessage.success('Token已重置');
    }
  }).catch(() => {});
};

const handleBackup = async (token) => {
  // 如果传入了具体的token，则备份该token
  // 如果没有传入，则备份选中的所有token
  if (!token && selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要备份的Token');
    return;
  }

  const tokensToBackup = token ? [token] : selectedTokens.value;
  ElMessage.info(`本地模式：备份功能仅在服务器模式下可用，已选择 ${tokensToBackup.length} 个Token`);
};

const handleEdit = (token) => {
  if (token) {
    ElMessage.info(`编辑Token: ${token.uid} - 编辑功能尚未实现`);
  } else {
    ElMessage.info('编辑功能尚未实现');
  }
};

const handleDelete = async (token) => {
  ElMessageBox.confirm(
    '确认要删除此Token吗？',
    '操作确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 从数组中移除
    tokens.value = tokens.value.filter(t => t.uid !== token.uid);
    totalTokenCount.value = tokens.value.length;
    ElMessage.success('Token已删除');
  }).catch(() => {});
};

// 表格操作
const handleSelectionChange = (selection) => {
  // 直接替换整个已选择列表
  // 注意：这可能会导致跨页选择丢失，因此我们需要特殊处理
  try {
    // 获取当前页面的所有UID
    const currentPageUids = new Set(tableData.value.map(item => item.uid));

    // 构建新的选择列表
    // 1. 保留不在当前页面的之前选择的项
    // 2. 添加当前页面新选择的项
    const newSelection = [
      // 保留不在当前页面的选择项
      ...selectedTokens.value.filter(item => !currentPageUids.has(item.uid)),
      // 添加当前页面的选择项
      ...selection
    ];

    // 更新选择列表
    selectedTokens.value = newSelection;
  } catch (error) {
    console.error('选择变化处理错误:', error);
  }
};

// 页面切换处理
const handleSizeChange = (size) => {
  // 记住之前的选择
  const previousSelection = [...selectedTokens.value];

  // 更新页大小
  pageSize.value = size;

  // 延时处理，避免DOM更新冲突
  setTimeout(() => {
    try {
      // 清除表格UI上的选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 如果当前页码超出范围则重置
      const totalPages = Math.ceil(filteredTokensCache.value.length / size) || 1;
      if (currentPage.value > totalPages) {
        currentPage.value = 1;
      }

      // 重新设置选中状态
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 为当前页的每行检查是否在选中列表中
          tableData.value.forEach(row => {
            // 查找当前行是否在选中列表中
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 手动设置选中状态
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('页面大小变化错误:', error);
    }
  }, 50);
};

const handleCurrentChange = (page) => {
  // 记住之前的选择
  const previousSelection = [...selectedTokens.value];

  // 更新页码
  currentPage.value = page;

  // 延时处理，避免DOM更新冲突
  setTimeout(() => {
    try {
      // 清除表格UI上的选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 重新设置选中状态
      if (selectedTokens.value.length > 0) {
        setTimeout(() => {
          // 为当前页的每行检查是否在选中列表中
          tableData.value.forEach(row => {
            // 查找当前行是否在选中列表中
            const isSelected = selectedTokens.value.some(item => item.uid === row.uid);
            if (isSelected && tokenTableRef.value) {
              // 手动设置选中状态
              tokenTableRef.value.toggleRowSelection(row, true);
            }
          });
        }, 50);
      }
    } catch (error) {
      console.error('页面切换错误:', error);
    }
  }, 50);
};

// 专门用于应用表格选择状态的函数
const applyTableSelectionStatus = () => {
  // 防止未初始化时调用
  if (!tokenTableRef.value) return;

  try {
    // 先清除表格的所有选中状态，避免UI状态混乱
    tokenTableRef.value.clearSelection();

    // 延迟到下一个tick确保表格已更新
    nextTick(() => {
      // 重新为当前页的每一行检查是否应该选中
      tableData.value.forEach(row => {
        const shouldBeSelected = selectedTokens.value.some(selected => selected.uid === row.uid);
        if (shouldBeSelected) {
          tokenTableRef.value?.toggleRowSelection(row, true);
        }
      });
    });
  } catch (error) {
    console.error('应用选择状态错误:', error);
  }
};

// 通过搜索关键字过滤的Token列表
const filteredTokens = computed(() => tokens.value);

// 处理去重和设置默认状态的函数已移动到TokenImport.vue组件中

// 获取状态对应的类型
const getStatusType = (status) => {
  const statusMap = {
    '在线': 'success',
    '掉线': 'danger',
    '未知': 'info',
    '正常': 'success',
    '过期': 'danger',
    '待激活': 'warning',
    '已重置': 'info'
  };
  return statusMap[status] || 'info';
};

// 页面加载时只调整表格高度
onMounted(() => {
  adjustTableHeight();

  // 监听窗口大小变化，调整表格高度
  window.addEventListener('resize', adjustTableHeight);

  // 初始化筛选数据
  filterTokens();
});

// 全选所有数据的功能
const selectAllTokens = () => {
  try {
    // 先显示加载中
    loading.value = true;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 直接将所有筛选结果作为选中项
    selectedTokens.value = JSON.parse(JSON.stringify(filteredTokensCache.value));

    // 设置定时器，确保后续操作在DOM更新后执行
    setTimeout(() => {
      // 强制为当前页的每一行都设置选中状态
      if (tokenTableRef.value) {
        tableData.value.forEach(row => {
          tokenTableRef.value.toggleRowSelection(row, true);
        });
      }

      // 显示成功消息
      ElMessage.success(`已选中全部 ${selectedTokens.value.length} 条数据`);
      loading.value = false;
    }, 100);
  } catch (error) {
    console.error('全选操作错误:', error);
    loading.value = false;
    ElMessage.error('全选操作失败，请重试');
  }
};

// 清除所有选择
const clearAllSelection = () => {
  try {
    // 记录当前页码，确保清除选择后不会改变页码
    const currentPageBeforeClearing = currentPage.value;

    // 清除表格UI上的选择状态
    tokenTableRef.value?.clearSelection();

    // 清除选择数据
    selectedTokens.value = [];

    // 不再重置这些值，只清除选择
    // totalTokenCount.value = 0;
    // filteredTokensCache.value = [];

    // 不再重置页码
    // currentPage.value = 1;

    // 确保表格选择状态被清除
    if (tokenTableRef.value) {
      tokenTableRef.value.clearSelection();
    }

    // 设置不重置页码的标志
    shouldResetPage.value = false;

    // 轻量级刷新，只刷新表格选择状态，不重置页码或过滤条件
    nextTick(() => {
      // 恢复原页码
      currentPage.value = currentPageBeforeClearing;
      // 应用表格选择状态
      applyTableSelectionStatus();
    });

    ElMessage.success('已清除所有选择');
  } catch (error) {
    console.error('清除选择错误:', error);
    ElMessage.error('清除选择失败，请重试');
  }
};

// 检查是否有跨页选择
const hasSelectionAcrossPages = computed(() => {
  return selectedTokens.value.length > tableData.value.length;
});

// 调整表格高度
const adjustTableHeight = () => {
  const windowHeight = window.innerHeight;
  // 进一步增加减去的空间
  tableHeight.value = `${windowHeight - 400}px`; // 减去更多空间给分页器和其他元素
};

// 新增的行点击处理函数
const handleRowClick = (row, column) => {
  // 忽略点击选择框和操作列的情况，因为这些列有自己的点击行为
  if (column.type === 'selection' || column.label === '操作') {
    return;
  }

  // 切换行的选择状态
  if (tokenTableRef.value) {
    // 检查当前行是否已被选中
    const isSelected = selectedTokens.value.some(item => item.uid === row.uid);

    // 切换选中状态
    tokenTableRef.value.toggleRowSelection(row, !isSelected);

    // 由于toggleRowSelection会触发selection-change事件，所以不需要在这里手动更新selectedTokens
  }
};

// 查询在线状态
const handleQueryOnline = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确定要查询选中${selectedTokens.value.length}个Token的在线状态吗？此操作将通过后端并发处理，速度更快。`,
      '操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询在线状态..',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存当前页码
    const currentPageBeforeQuery = currentPage.value;
    // 设置不重置页码标志
    shouldResetPage.value = false;

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用后端优化API进行查询
      const result = await tokenService.queryOnlineStatusOptimized(selectedTokenData);

      if (result.success) {
        // 更新token状态
        const updatedTokens = applyOnlineStatusToTokens(tokens.value, result.results);

        // 设置不重置页码
        shouldResetPage.value = false;
        tokens.value = updatedTokens;

        // 统计结果
        const onlineCount = result.results.filter(r => r.status === '在线').length;
        const offlineCount = result.results.filter(r => r.status === '掉线').length;

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span>
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span>
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">状态已更新到表格中</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格时不重置页码
            shouldResetPage.value = false;
            refreshTokens();

            // 确保在刷新后保持在原页码
            nextTick(() => {
              currentPage.value = currentPageBeforeQuery;
            });
          }
        });

        ElMessage.success('查询完成，状态已更新');
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询在线状态异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();

      // 确保保持在原页码
      nextTick(() => {
        currentPage.value = currentPageBeforeQuery;
      });
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 查询头像昵称
const handleQueryAvatarNickname = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确定要查询选中${selectedTokens.value.length}个Token的头像和昵称吗？此操作将通过后端并发处理，速度更快。`,
      '操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询头像和昵称..',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用后端API进行查询
      const result = await tokenService.queryTokenUserInfo(selectedTokenData);

      if (result.success) {
        // 更新token的头像和昵称
        const updatedTokens = tokens.value.map(token => {
          const userInfo = result.results.find(r => r.uid === token.uid);
          if (userInfo && userInfo.success && userInfo.data) {
            return {
              ...token,
              avatar: userInfo.data.avatar || token.avatar,
              nickname: userInfo.data.nickname || token.nickname,
              // 如果token在线，也更新状态
              status: userInfo.data.isOnline ? '在线' : token.status
            };
          }
          return token;
        });

        tokens.value = updatedTokens;

        // 统计结果
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.results.length} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">成功</span>
                <span style="font-size: 18px;">${successCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">失败</span>
                <span style="font-size: 18px;">${failCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">头像和昵称信息已更新到表格中</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格
            refreshTokens();
          }
        });

        ElMessage.success('查询完成，头像和昵称已更新');
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询头像昵称异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 查询订单信息
const handleQueryOrder = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要查询的Token');
    return;
  }

  try {
    // 提示用户确认操作
    await ElMessageBox.confirm(
      `确定要查询${selectedTokens.value.length}个选中Token的订单信息吗？`,
      '查询订单信息',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 用户确认后，开始查询
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在查询订单信息...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 保存原始状态，以便在失败时恢复
    const originalTokens = JSON.parse(JSON.stringify(tokens.value));

    try {
      // 提取选中token的信息
      const selectedTokenData = selectedTokens.value.map(token => ({
        uid: token.uid,
        token: token.token
      }));

      // 调用API进行查询
      const result = await tokenService.queryTokenOrderInfo(selectedTokenData);

      if (result.success) {
        // 更新token的订单信息
        const updatedTokens = tokens.value.map(token => {
          const orderResult = result.results.find(r => r.uid === token.uid);
          if (orderResult && orderResult.success) {
            return {
              ...token,
              // 同时更新token的主status属性，使在线状态在主表格中显示
              status: orderResult.status || token.status,
              orderInfo: {
                isOnline: orderResult.isOnline,
                status: orderResult.status || '未知',
                orderCount: orderResult.data?.orderCount || 0,
                orders: orderResult.data?.orders || [],
                message: orderResult.message
              }
            };
          }
          return {
            ...token,
            // 如果查询失败也要更新主状态
            status: orderResult?.status || token.status,
            orderInfo: orderResult ? {
              isOnline: orderResult.isOnline || false,
              status: orderResult.status || '未知',
              orderCount: 0,
              orders: [],
              message: orderResult.message || '查询失败'
            } : null
          };
        });

        // 设置不重置页码的标志
        shouldResetPage.value = false;

        // 更新tokens并保持当前页码
        tokens.value = updatedTokens;

        // 统计结果
        const onlineCount = result.results.filter(r => r.status === '在线').length;
        const offlineCount = result.results.filter(r => r.status === '掉线').length;
        const hasOrderCount = result.results.filter(r => r.success && r.status === '在线' && r.data && r.data.orderCount > 0).length;

        ElNotification({
          title: '查询成功',
          message: `共处理${result.stats.total}个Token，其中${onlineCount}个在线，${hasOrderCount}个有订单`,
          type: 'success',
          duration: 5000
        });

        // 创建统计信息
        const statsHtml = `
          <div style="text-align: center; margin-top: 10px;">
            <div style="font-size: 16px; margin-bottom: 10px;">查询完成，共处理 ${result.stats.total} 个Token</div>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 15px;">
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #f0f9eb; color: #67c23a; font-weight: bold;">
                <span style="margin-right: 5px;">在线</span>
                <span style="font-size: 18px;">${onlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #fef0f0; color: #f56c6c; font-weight: bold;">
                <span style="margin-right: 5px;">掉线</span>
                <span style="font-size: 18px;">${offlineCount}</span>
              </div>
              <div style="padding: 10px 15px; border-radius: 4px; background-color: #e6f2ff; color: #409EFF; font-weight: bold;">
                <span style="margin-right: 5px;">有订单</span>
                <span style="font-size: 18px;">${hasOrderCount}</span>
              </div>
            </div>
            <div style="font-size: 14px; color: #409EFF;">订单信息已更新到表格中，点击"订单数量"可查看详细信息</div>
          </div>
        `;

        // 显示查询结果统计
        ElMessageBox.alert(statsHtml, '查询结果', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          callback: () => {
            // 刷新表格
            refreshTokens();
          }
        });
      } else {
        // 查询失败，恢复原始状态
        tokens.value = originalTokens;
        ElMessage.error(`查询失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('查询订单信息异常:', error);
      // 恢复原始状态
      tokens.value = originalTokens;
      ElMessage.error(`查询异常: ${error.message || '未知错误'}`);
    } finally {
      // 关闭加载提示
      loadingInstance.close();
    }
  } catch (e) {
    // 用户取消操作
    if (e !== 'cancel') {
      console.error('确认对话框异常:', e);
    }
  }
};

// 获取订单状态标签类
const getOrderStatusTag = (status) => {
  if (!status) return 'info';
  if (status.includes('待收款') || status.includes('待发货')) return 'warning';
  if (status.includes('已收款') || status.includes('已完结')) return 'success';
  if (status.includes('已取件') || status.includes('已关闭')) return 'danger';
  return 'info';
};

// 格式化订单ID
const formatOrderId = (id) => {
  if (id.length > 10) {
    return id.slice(0, 5) + '...' + id.slice(-5);
  }
  return id;
};

// 获取订单工具提示
const getOrderTooltip = (orderInfo) => {
  if (orderInfo.detail) {
    return orderInfo.detail;
  }
  return orderInfo.id ? orderInfo.id : '无订单信息';
};

// 查看订单详情
const ordersDialogVisible = ref(false);
const currentOrders = ref([]);
const currentTokenUid = ref('');
const orderDetailDialogVisible = ref(false);
const currentOrderDetail = ref(null);

// 处理查看订单
const handleViewOrders = (row) => {
  if (row.orderInfo && row.orderInfo.orders && row.orderInfo.orders.length > 0) {
    // 为每个订单添加所属token的信息，以便支持订单详情页中的删除功能
    currentOrders.value = row.orderInfo.orders.map(order => ({
      ...order,
      uid: row.uid,
      tokenValue: row.token
    }));
    currentTokenUid.value = row.uid;
    ordersDialogVisible.value = true;
  } else {
    ElMessage.warning('没有找到订单信息');
  }
};

// 查看单个订单详情
const viewOrderDetail = (order) => {
  // 传递订单所属token的uid和token值，以便能够删除订单
  if (order) {
    const tokenInfo = order.tokenInfo || {};
    currentOrderDetail.value = {
      ...order,
      uid: order.uid || tokenInfo.uid,
      tokenValue: order.tokenValue || tokenInfo.token
    };
  orderDetailDialogVisible.value = true;
  }
};

// 格式化订单时间
const formatOrderTime = (time) => {
  if (!time) return '未知';
  return time;
};

// 删除订单信息
const handleDeleteOrder = async () => {
  // 检查是否有选中的token
  if (selectedTokens.value.length === 0) {
    ElMessage.warning('请先选择要删除订单的Token');
    return;
  }

  // 询问用户是否确认删除
  try {
    await ElMessageBox.confirm(
      `确定要删除选中Token的订单吗？系统将自动查询并并发删除所有可删除的订单。`,
      '批量删除订单确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
  } catch (error) {
    return; // 用户取消操作
  }

  // 使用ElLoading替代进度对话框
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '正在删除订单，请稍等..',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  // 保存当前页码
  const currentPageBeforeDelete = currentPage.value;
  // 设置不重置页码标志
  shouldResetPage.value = false;

  try {
    // 使用并发查询和删除，不再显示进度
    const result = await orderService.concurrentQueryAndDeleteOrders(
      selectedTokens.value,
      {
        batchSize: 20, // 每批20个订单
        concurrency: 5, // 5个并发请求
        // 不再需要进度回传
      }
    );

    // 处理结果
    if (result.success) {
      // 检查是否有成功删除的订单
      const successCount = result.results.filter(r => r.success).length;
      const failCount = result.results.length - successCount;

      // 更新本地数据，从订单列表中移除已删除的订单
      updateLocalOrdersAfterDeletion(result.results);

      // 处理查询结果，更新UI显示
      if (result.queriedTokens && result.queriedTokens.length > 0) {
        // 更新已查询但没有可删除订单的Token状态
        updateTokensAfterQuery(result.queriedTokens);
      }

      // 使用通知显示结果摘要
      if (successCount > 0) {
        ElNotification({
          title: '删除订单完成',
          message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
          type: 'success',
          duration: 5000
        });
      } else {
        // 所有删除都失败的情
        ElNotification({
          title: '删除订单未成功',
          message: `未能成功删除任何订单${result.queriedTokens ? `，已查询 ${result.queriedTokens.length} 个Token` : ''}。请检查订单状态是否为"已评价"。`,
          type: 'warning',
          duration: 5000
        });
      }

      // 保证不重置页码的情况下刷新表格数据
      shouldResetPage.value = false;
      refreshTokens();

      // 确保在刷新后保持页码
      nextTick(() => {
        currentPage.value = currentPageBeforeDelete;
      });
    } else {
      // API调用失败
      ElMessage.error(result.message || '查询或删除订单失败');

      // 显示详细错误信息
      ElMessageBox.alert(
        `<div style="text-align: left;">
          <p><strong>错误信息:</strong> ${result.message || '未知错误'}</p>
          <p style="margin-top: 10px; font-size: 12px; color: #909399;">
            请确认：
            <ul>
              <li>Token是否在线</li>
              <li>Token权限是否有效</li>
              <li>订单状态是否为"已评价"</li>
            </ul>
          </p>
        </div>`,
        '删除订单失败',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          type: 'error'
        }
      );
    }
  } catch (error) {
    console.error('删除订单错误:', error);

    // 更详细的错误提示
    ElMessage.error(`操作异常: ${error.message || '未知错误'}`);

    // 显示技术细节
    ElMessageBox.alert(
      `<div style="text-align: left;">
        <p><strong>错误信息:</strong> ${error.message || '未知错误'}</p>
        <p style="margin-top: 10px; font-size: 12px; color: #909399;">
          可能的原因：
          <ul>
            <li>网络连接问题</li>
            <li>服务器响应超时</li>
            <li>API限流或权限问题</li>
          </ul>
        </p>
      </div>`,
      '系统错误',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了',
        type: 'error'
      }
    );

    // 更新token的订单状态为失败
    selectedTokens.value.forEach(token => {
      if (token.orderDeletionStatus) {
        token.orderDeletionStatus.inProgress = false;
      }
    });
  } finally {
    // 关闭加载提示
    loadingInstance.close();

    // 确保保持在原页码
    nextTick(() => {
      currentPage.value = currentPageBeforeDelete;
    });
  }
};

// 新增：更新已查询但没有可删除订单的Token状态
const updateTokensAfterQuery = (queriedTokens) => {
  if (!queriedTokens || queriedTokens.length === 0) return;

  console.log('更新已查询Token状态:', queriedTokens);

  // 设置不重置页码的标志
  shouldResetPage.value = false;

  // 更新查询到的token信息
  tokens.value = tokens.value.map(token => {
    // 查找当前token是否在查询结果中
    const queriedToken = queriedTokens.find(qt => qt.uid === token.uid);

    if (queriedToken) {
      // 为token添加查询结果
      return {
        ...token,
        orderInfo: {
          isOnline: queriedToken.isOnline !== undefined ? queriedToken.isOnline : true,
          status: queriedToken.status || '在线',
          orderCount: queriedToken.orders?.length || 0,
          orders: queriedToken.orders || [],
          message: '查询成功'
        }
      };
    }

    return token;
  });
};

// 更新本地订单数据（删除后）
const updateLocalOrdersAfterDeletion = (results) => {
  // 设置不重置页码的标志，防止删除订单后跳转到第一页
  shouldResetPage.value = false;

  // 保存当前页码
  const currentPageBeforeUpdate = currentPage.value;

  // 分离成功和失败的结果
  const successfullyDeletedOrders = results
    .filter(r => r.success)
    .map(r => r.orderSn);

  const failedDeletes = results.filter(r => !r.success);

  // 输出错误信息
  if (failedDeletes.length > 0) {
    console.warn('删除失败的订单:', failedDeletes);

    // 提取错误消息并统计
    const errorMessages = {};
    failedDeletes.forEach(result => {
      const msg = result.message || '未知错误';
      errorMessages[msg] = (errorMessages[msg] || 0) + 1;
    });

    // 构建错误信息HTML
    let errorHtml = `
      <div style="margin-top: 15px;">
        <div style="font-weight: bold; color: #f56c6c; margin-bottom: 10px;">删除失败原因:</div>
        <ul style="text-align: left; margin: 0; padding-left: 20px;">
    `;

    for (const [message, count] of Object.entries(errorMessages)) {
      errorHtml += `<li>${message} (${count}个订单)</li>`;
    }

    errorHtml += `
        </ul>
        <div style="margin-top: 10px; font-size: 12px; color: #909399;">
          常见原因：订单未评价、Token已失效或者无权限操作
        </div>
      </div>
    `;

    // 显示错误信息
    if (failedDeletes.length > 0) {
      ElMessageBox.alert(
        errorHtml,
        '订单删除失败详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '我知道了',
          type: 'warning'
        }
      );
    }
  }

  if (successfullyDeletedOrders.length === 0) {
    // 如果没有成功删除任何订单，则显示提示并返回
    if (results.length > 0) {
      ElMessage.warning('没有成功删除任何订单，请查看失败详情');
    }
    return;
  }

  console.log('开始更新本地订单数据，成功删除的订单数:', successfullyDeletedOrders.length);

  // 更新token的订单列
  tokens.value = tokens.value.map(token => {
    if (token.orderInfo && token.orderInfo.orders) {
      // 计算实际被删除的订单数量（通过比较原始订单和剩余订单）
      const originalOrders = token.orderInfo.orders;
      const updatedOrders = originalOrders.filter(
        order => !successfullyDeletedOrders.includes(order.orderId)
      );

      // 计算本次实际删除的订单数量
      const deletedCount = originalOrders.length - updatedOrders.length;
      console.log(`Token ${token.uid}: 原始订单数量=${originalOrders.length}, 更新后订单数量=${updatedOrders.length}, 删除数量=${deletedCount}`);

      // 只在有订单被删除时更新
      if (deletedCount > 0) {
        // 确保保留原始orderInfo中的其他字段
        const updatedOrderInfo = {
          ...token.orderInfo,
          orders: updatedOrders,
          orderCount: updatedOrders.length,  // 确保orderCount为实际剩余订单数量
          // 保留以下字段确保UI显示正常
          isOnline: token.orderInfo.isOnline,
          status: token.orderInfo.status
        };

        // 更新删除状态
        const updatedDeletionStatus = {
          totalOrders: originalOrders.length,
          successCount: deletedCount,
          failCount: 0,
          inProgress: false
        };

        // 返回更新后的token对象
        return {
          ...token,
          orderInfo: updatedOrderInfo,
          orderDeletionStatus: updatedDeletionStatus,
          // 更新currentDisplayOrders和deletableOrders
          currentDisplayOrders: updatedOrders,
          deletableOrders: updatedOrders.filter(order => isDeletableOrder(order.status))
        };
      }
    }
    return token;
  });

  // 更新当前显示的订单列
  if (currentOrders.value.length > 0) {
    currentOrders.value = currentOrders.value.filter(
      order => !successfullyDeletedOrders.includes(order.orderId)
    );
  }

  // 更新可删除订单列
  deletableOrders.value = deletableOrders.value.filter(
    order => !successfullyDeletedOrders.includes(order.orderId)
  );

  // 强制刷新表格显示
  nextTick(() => {
    // 保证不重置页码
    currentPage.value = currentPageBeforeUpdate;

    // 通知表格数据已经改变
    if (tokenTableRef.value) {
      tokenTableRef.value.$forceUpdate();
    }
    // 应用表格选择状态
    applyTableSelectionStatus();
  });
};

// 显示删除结果详情 - 修改为仅显示简单消息
const showDeletionResultDetails = (results) => {
  const successResults = results.filter(r => r.success);
  const failResults = results.filter(r => !r.success);

  // 显示简单的成功消息，不再弹出详细结果对话框
  if (successResults.length > 0) {
    ElMessage.success(`成功删除 ${successResults.length} 个订单${failResults.length > 0 ? `，失败 ${failResults.length} 个` : ''}`);
  } else if (failResults.length > 0) {
    ElMessage.warning(`全部 ${failResults.length} 个订单删除失败`);
  }
};

// 清空表格
const handleClearTable = () => {
  ElMessageBox.confirm(
    '确定要清空当前表格中的所有数据吗？此操作不可恢复！',
    '清空表格确认',
    {
      confirmButtonText: '确定清空',
      cancelButtonText: '取消',
      type: 'warning',
      distinguishCancelAndClose: true,
      customClass: 'warning-confirm-dialog'
    }
  )
    .then(() => {
      // 清空数据数组
      tokens.value = [];
      // 重置相关状态
      selectedTokens.value = [];
      totalTokenCount.value = 0;
      filteredTokensCache.value = [];

      // 清空表格是用户明确的操作，所以在此情况下重置页码是合理的
      currentPage.value = 1;

      // 清除表格选择状态
      if (tokenTableRef.value) {
        tokenTableRef.value.clearSelection();
      }

      // 刷新筛选状态
      filterTokens();

      ElMessage.success('表格数据已全部清空');
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    });
};

// 处理单个订单删除
const handleDeleteSingleOrder = async (orderDetail) => {
  if (!orderDetail || !orderDetail.uid || !orderDetail.tokenValue || !orderDetail.orderId) {
    ElMessage.warning('订单信息不完整，无法删除');
    return;
  }

  // 确认是否可删除
  if (!isDeletableOrder(orderDetail.status)) {
    ElMessage.warning(`只能删除已评价的订单，当前订单状态为"${orderDetail.status}"`);
    return;
  }

  try {
    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除订单 ${orderDetail.orderId} 吗？此操作不可恢复！`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 调用API删除订单
      const result = await tokenService.deleteOrder(
        orderDetail.uid,
        orderDetail.tokenValue,
        orderDetail.orderId
      );

      if (result.success) {
        // 删除成功，关闭订单详情对话框
        orderDetailDialogVisible.value = false;

        // 更新本地数据
        updateLocalOrdersAfterDeletion([{
          success: true,
          orderSn: orderDetail.orderId
        }]);

        ElMessage.success('订单删除成功');

        // 如果主订单列表对话框也打开，更新它
        if (ordersDialogVisible.value && currentOrders.value.length > 0) {
          // 从当前列表中过滤掉已删除的订单
          currentOrders.value = currentOrders.value.filter(
            order => order.orderId !== orderDetail.orderId
          );
        }
      } else {
        // 增强错误信息显示
        const errorMsg = result.message || '删除失败';
        ElMessage.error(errorMsg);

        // 构建更详细的错误提示
        let detailsHtml = `
          <div style="text-align: left; margin: 15px 0;">
            <h3 style="margin-bottom: 10px;">删除失败详情</h3>
            <div style="color: #f56c6c;">
              <p>${errorMsg}${result.errorCode ? ` (错误码: ${result.errorCode})` : ''}</p>
            </div>

            <div style="margin-top: 15px; font-size: 14px;">
              <p>可能的原因：</p>
              <ul style="padding-left: 20px; margin-top: 5px;">
                <li>订单状态不是"已评价"(拼多多要求必须是严格的"已评价")</li>
                <li>Token已失效或没有足够权限</li>
                <li>网络问题或API限流</li>
              </ul>
            </div>
        `;

        if (result.responseData) {
          detailsHtml += `
            <div style="margin-top: 15px;">
              <details>
                <summary>API响应详情</summary>
                <pre style="max-height: 150px; overflow: auto; background: #f8f8f8; padding: 8px; margin-top: 5px; border-radius: 4px;">${JSON.stringify(result.responseData, null, 2)}</pre>
              </details>
            </div>
          `;
        }

        detailsHtml += `</div>`;

        // 显示详细错误
        ElMessageBox.alert(detailsHtml, '删除失败', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        });
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 订单列表表格选择相关
const orderTableRef = ref(null);
const selectedOrdersInList = ref([]);
const deletableOrdersCount = computed(() => {
  return currentOrders.value.filter(order => isDeletableOrder(order.status)).length;
});

// 处理订单列表表格的选择变化
const handleOrderSelectionChange = (selection) => {
  selectedOrdersInList.value = selection;
};

// 全选可删除的订单
const selectAllDeletableOrders = () => {
  // 先清除所有选择
  orderTableRef.value?.clearSelection();

  // 然后选中所有可删除的订单  currentOrders.value.forEach(order => {
  currentOrders.value.forEach(order => {
    if (isDeletableOrder(order.status)) {
      orderTableRef.value?.toggleRowSelection(order, true);
    }
  });
};

// 批量删除选中的订单
const batchDeleteSelectedOrders = async () => {
  if (selectedOrdersInList.value.length === 0) {
    ElMessage.warning('请先选择要删除的订单');
    return;
  }

  try {
    // 确认是否删除
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedOrdersInList.value.length} 个订单吗？`,
      '删除订单确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示loading
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在删除订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 构建删除请求数据
      const orderItems = selectedOrdersInList.value.map(order => ({
        uid: order.uid,
        token: order.tokenValue,
        orderSn: order.orderId
      }));

      // 调用批量删除API
      const result = await tokenService.batchDeleteOrders(orderItems);

      if (result.success) {
        // 统计结果
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 更新本地数据，从订单列表中移除已删除的订单
        updateLocalOrdersAfterDeletion(result.results);

        // 清除表格选择
        selectedOrdersInList.value = [];
        orderTableRef.value?.clearSelection();

        // 使用通知显示详细结果
        ElNotification({
          title: '删除订单完成',
          message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
          type: 'success',
          duration: 5000
        });

        // 强制刷新表格数据
        refreshTokens();
      } else {
        ElMessage.error(`批量删除失败: ${result.message || '未知错误'}`);
      }
    } finally {
      loadingInstance.close();
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};

// 判断订单是否可删除（只允许已评价的订单）
const isDeletableOrder = (status) => {
  if (!status) return false;

  // 严格匹配"已评价"状态
  // 只有完全匹配"已评价"的订单才能删除，根据拼多多API的要求
  if (status === '已评价') {
    return true;
  }

  // 包含"已评价"的状态也可能可以删除
  if (status.includes('已评价')) {
    return true;
  }

  // 其他状态不可删除
  return false;
};

// 获取可删除的订单数量
const getDeletableCount = (row) => {
  if (!row.orderInfo || !row.orderInfo.orders) return 0;
  return row.orderInfo.orders.filter(order => isDeletableOrder(order.status)).length;
};

// 添加deletableOrders变量
const deletableOrders = ref([]);

// 处理订单被删除的回调
const handleOrderDeleted = (data) => {
  // 使用现有的updateLocalOrdersAfterDeletion方法更新本地数据
  if (data && data.results) {
    updateLocalOrdersAfterDeletion(data.results);
  }
};

// 一键删除订单处理函数
const handleQuickDeleteOrder = async (token) => {
  // 防止重复点击
  if (token.orderDeleteLoading) return;

  // 设置loading状态
  token.orderDeleteLoading = true;

  try {
    // 询问用户是否确认删除
    await ElMessageBox.confirm(
      `确定要删除此Token的订单吗？将会自动查询并使用并发模式删除所有可删除的订单。`,
      '删除订单确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 显示加载中状态
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在处理订单...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 使用并发查询和删除
      const result = await orderService.concurrentQueryAndDeleteOrders(
        [token],
        {
          batchSize: 20, // 每批20个订单
          concurrency: 5, // 5个并发请求
        }
      );

      if (result.success) {
        // 检查是否有成功删除的订单
        const successCount = result.results.filter(r => r.success).length;
        const failCount = result.results.length - successCount;

        // 更新本地数据
        updateLocalOrdersAfterDeletion(result.results);

        // 处理查询结果，更新UI显示
        if (result.queriedTokens && result.queriedTokens.length > 0) {
          // 更新已查询但没有可删除订单的Token状态
          updateTokensAfterQuery(result.queriedTokens);
        }

        // 使用通知显示结果
        if (successCount > 0) {
          ElNotification({
            title: '删除订单完成',
            message: `成功删除 ${successCount} 个订单${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
            type: 'success',
            duration: 5000
          });
        } else if (result.results.length > 0) {
          // 有订单但全部删除失败
          ElNotification({
            title: '删除订单未成功',
            message: `未能成功删除任何订单${failCount} 个删除失败`,
            type: 'warning',
            duration: 5000
          });
        } else {
          // 无可删除订单
          ElNotification({
            title: '无可删除订单',
            message: '查询完成，但没有找到可删除的订单（只有已评价的订单可以删除）',
            type: 'info',
            duration: 5000
          });
        }

        // 强制刷新表格数据
        refreshTokens();
      } else {
        // API调用失败
        ElMessage.error(result.message || '操作失败');
      }
    } finally {
      loadingInstance.close();
      token.orderDeleteLoading = false;
    }
  } catch (error) {
    token.orderDeleteLoading = false;
    if (error !== 'cancel') {
      console.error('删除订单错误:', error);
      ElMessage.error(`操作异常: ${error.message || '未知错误'}`);
    }
  }
};


// 添加一个标志来控制是否需要重置页码
const shouldResetPage = ref(true);
</script>

<style scoped>
.token-reset-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  /* 移除min-height: 100vh，避免强制容器过大*/
  height: auto;
  overflow: visible; /* 修改为visible，确保内容不被裁剪*/
}

.token-list-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible;
  max-height: 80vh; /* 限制最大高度为视口高度�?0% */
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.token-count-indicator {
  display: flex;
  align-items: center;
  margin-right: auto;
  margin-left: 15px;
}

.function-buttons {
  margin: 0 0 20px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.function-buttons .el-button-group {
  margin-right: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 10px;
}

.filter-select {
  width: 130px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 280px;
}

.data-stats {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.stats-card {
  min-width: 70px;
  height: 43px;
  border-radius: 4px;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  text-align: center;
  cursor: default;
}

.info-card {
  background-color: #909399;
}

.primary-card {
  background-color: #409EFF;
}

.warning-card {
  background-color: #E6A23C;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.2;
  display: block;
}

.stats-label {
  font-size: 12px;
  opacity: 0.9;
  display: block;
  margin-top: 2px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.selection-info {
  margin-left: auto;
}

/* 表格样式优化 */
.token-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.token-table :deep(.el-table__header) {
  font-weight: 600;
  color: #303133;
}

.token-table :deep(.el-table__row) {
  height: 60px;
  cursor: pointer; /* 添加手型指针，提示可点击 */
}

.token-table :deep(.el-table__row:hover) {
  border: 2px solid #fff;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 30px; /* 增加底部边距 */
  padding: 15px 0; /* 添加内边距 */
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px; /* 圆角边框 */
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 导入对话框格式说明样?*/
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th,
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 文本导入样式 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 用户头像样式 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.order-count-badge {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  background: linear-gradient(135deg, #e8f4ff, #c5e3ff);
  color: #1a4b8c;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 110, 255, 0.12);
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 179, 255, 0.2);
  font-size: 13px;
  min-width: 60px;
  justify-content: center;
  height: 32px;
  font-weight: 500;
}

.order-count-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 110, 255, 0.15);
  background: linear-gradient(135deg, #d1e9ff, #b3d9ff);
}

.order-number {
  font-weight: 600;
  font-size: 15px;
  margin-right: 3px;
  color: #0960d0;
}

.order-text {
  font-size: 13px;
  color: #5a8dc8;
}

.order-deletable {
  font-size: 12px;
  color: #67c23a;
  margin-left: 5px;
  font-weight: normal;
}

.deletable-tag {
  margin-left: 8px;
  font-size: 11px;
  height: 22px;
  line-height: 20px;
  padding: 0 6px;
}

.deletable-count {
  font-size: 11px;
  color: #67c23a;
  margin-left: 4px;
}

/* 状态标签通用样式 */
.status-badge {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.3s ease;
  gap: 4px;
  height: 24px;
}

.status-badge i {
  font-size: 14px;
}

/* 掉线状态样式 */
.status-offline {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.1);
}

/* 无订单状态样式 */
.status-no-order {
  background-color: #f5f7fa;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 查询失败状态样式 */
.status-error {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid rgba(230, 162, 60, 0.1);
}

/* 未查询状态样式 */
.status-not-queried {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.1);
}

/* 订单详情对话框样式 */
.order-details {
  margin-top: 10px;
}

.order-table {
  margin-bottom: 15px;
}

.order-table .el-table__row:hover {
  background-color: #f0f7ff;
}

.order-detail-item {
  display: flex;
  margin-bottom: 8px;
}

.order-detail-label {
  font-weight: bold;
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

.order-detail-value {
  flex: 1;
}

.order-status-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.order-deletion-status {
  margin-top: 10px;
}

.order-status-text {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-tag i {
  font-size: 12px;
}

.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn {
  transition: all 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.user-filter-btn {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.user-filter-btn:hover {
  background-color: #f0f9ff;
}

.table-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-area {
  width: 100%;
}

.operation-dialog-content {
  padding: 20px 0;
  text-align: center;
  font-size: 16px;
}

/* 导入对话框格式说明样式 */
.format-guide {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.format-guide p {
  margin: 10px 0;
  line-height: 1.5;
}

.format-guide ul {
  padding-left: 20px;
  margin: 10px 0;
}

.format-guide li {
  margin: 8px 0;
  line-height: 1.5;
}

.format-guide pre {
  background-color: #f1f1f1;
  padding: 12px;
  border-radius: 4px;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  margin: 10px 0;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.example-table {
  margin: 15px 0;
  overflow-x: auto;
}

.example-table table {
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #dcdfe6;
}

.example-table th,
.example-table td {
  border: 1px solid #dcdfe6;
  padding: 10px;
  text-align: left;
}

.example-table th {
  background-color: #f2f6fc;
  font-weight: 600;
}

.example-table tr:nth-child(even) {
  background-color: #f9fafc;
}

/* 文本导入样式 */
.text-import-container {
  padding: 15px 0;
}

.text-import-tip {
  margin-bottom: 15px;
  color: #606266;
}

.import-textarea {
  margin-bottom: 20px;
}

.text-import-actions {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
}

.local-mode-tip {
  display: none;
}

.local-mode-note {
  display: none;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-button {
  margin-left: 0;
}

/* 用户头像样式 */
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 30px; /* 增加底部边距 */
  padding: 15px 0; /* 添加内边距 */
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px; /* 圆角边框 */
}

</style>
