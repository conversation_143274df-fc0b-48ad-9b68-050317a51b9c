/**
 * Excel导出工具函数
 * 用于将数据导出为Excel文件
 */
import * as XLSX from 'xlsx'

/**
 * 将数据导出为Excel文件
 * @param {Array} data - 要导出的数据数组
 * @param {string} fileName - 导出的文件名（不包含扩展名）
 * @param {string} sheetName - 工作表名称
 * @returns {Promise<void>}
 */
export const exportToExcel = async (data, fileName = 'export', sheetName = 'Sheet1') => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('导出数据不能为空')
  }

  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    
    // 将数据转换为工作表
    const worksheet = XLSX.utils.json_to_sheet(data)
    
    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
    
    // 将工作簿写入文件并下载
    XLSX.writeFile(workbook, `${fileName}.xlsx`)
    
    return true
  } catch (error) {
    console.error('导出Excel时出错:', error)
    throw error
  }
}

/**
 * 将数据导出为CSV文件
 * @param {Array} data - 要导出的数据数组
 * @param {string} fileName - 导出的文件名（不包含扩展名）
 * @returns {Promise<void>}
 */
export const exportToCSV = async (data, fileName = 'export') => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    throw new Error('导出数据不能为空')
  }

  try {
    // 确保有数据
    if (data.length === 0) {
      throw new Error('没有可导出的数据')
    }
    
    // 获取表头（对象的所有键）
    const headers = Object.keys(data[0])
    
    // 创建CSV内容
    let csvContent = headers.join(',') + '\n'
    
    // 添加数据行
    data.forEach(item => {
      const row = headers.map(header => {
        // 处理可能包含逗号、引号等特殊字符的字段
        let cell = item[header] === null || item[header] === undefined ? '' : item[header].toString()
        if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
          cell = `"${cell.replace(/"/g, '""')}"`
        }
        return cell
      })
      csvContent += row.join(',') + '\n'
    })
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    // 设置下载属性
    link.setAttribute('href', url)
    link.setAttribute('download', `${fileName}.csv`)
    link.style.visibility = 'hidden'
    
    // 添加到文档并点击
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('导出CSV时出错:', error)
    throw error
  }
}

/**
 * 将数据导出为JSON文件
 * @param {Array} data - 要导出的数据数组
 * @param {string} fileName - 导出的文件名（不包含扩展名）
 * @param {boolean} prettyPrint - 是否格式化JSON
 * @returns {Promise<void>}
 */
export const exportToJSON = async (data, fileName = 'export', prettyPrint = true) => {
  if (!data) {
    throw new Error('导出数据不能为空')
  }

  try {
    // 转换为JSON字符串
    const jsonString = prettyPrint 
      ? JSON.stringify(data, null, 2) 
      : JSON.stringify(data)
    
    // 创建Blob对象
    const blob = new Blob([jsonString], { type: 'application/json' })
    
    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    // 设置下载属性
    link.setAttribute('href', url)
    link.setAttribute('download', `${fileName}.json`)
    link.style.visibility = 'hidden'
    
    // 添加到文档并点击
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('导出JSON时出错:', error)
    throw error
  }
} 