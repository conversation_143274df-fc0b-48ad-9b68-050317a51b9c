const express = require('express');
const router = express.Router();
const { authenticate, isAdmin } = require('../middleware/auth');
const ResetController = require('../controllers/reset');

// 获取所有重置记录
router.get('/', authenticate, ResetController.getAllResets);

// 获取单个重置记录详情
router.get('/:id', authenticate, ResetController.getResetById);

// 获取与特定token关联的重置历史
router.get('/token/:tokenId', authenticate, ResetController.getResetsByToken);

// 获取重置统计信息
router.get('/stats/overview', authenticate, ResetController.getResetStats);

// 管理员：批量创建重置记录（通常由系统自动创建，这里提供管理接口）
router.post('/batch', authenticate, isAdmin, ResetController.batchCreateReset);

// 导出重置记录（CSV/Excel）
router.get('/export/:format', authenticate, ResetController.exportResets);

module.exports = router; 